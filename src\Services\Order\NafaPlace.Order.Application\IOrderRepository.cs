using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace NafaPlace.Order.Application
{
    public interface IOrderRepository
    {
        Task<IEnumerable<Domain.Order>> GetAllOrdersAsync();
        Task<Domain.Order> GetOrderByIdAsync(int id);
        Task<IEnumerable<Domain.Order>> GetOrdersByUserIdAsync(string userId);
        Task<Domain.Order> CreateOrderAsync(Domain.Order order);
        Task<Domain.Order> UpdateOrderAsync(Domain.Order order);
        Task<IEnumerable<Domain.Order>> GetOrdersWithFiltersAsync(
            string? searchTerm = null,
            string? status = null,
            string? paymentStatus = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            int pageNumber = 1,
            int pageSize = 20);
        Task<IEnumerable<Domain.Order>> GetOrdersBySellerIdAsync(
            int sellerId,
            string? searchTerm = null,
            string? status = null,
            string? paymentStatus = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            int pageNumber = 1,
            int pageSize = 20);
        Task<int> GetOrdersCountBySellerIdAsync(int sellerId);
    }
}