# Script de déploiement Docker pour NafaPlace
# Utilisation: .\scripts\deploy-docker.ps1

Write-Host "🚀 Déploiement de NafaPlace avec Docker" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green

# Vérifier que Docker est en cours d'exécution
Write-Host "🔍 Vérification de Docker..." -ForegroundColor Yellow
try {
    docker --version | Out-Null
    docker-compose --version | Out-Null
    Write-Host "✅ Docker et Docker Compose sont installés" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker ou Docker Compose n'est pas installé ou en cours d'exécution" -ForegroundColor Red
    exit 1
}

# Nettoyer les conteneurs et volumes existants
Write-Host "🧹 Nettoyage des conteneurs existants..." -ForegroundColor Yellow
docker-compose down -v --remove-orphans

# Supprimer les images existantes pour forcer la reconstruction
Write-Host "🗑️ Suppression des images existantes..." -ForegroundColor Yellow
docker rmi $(docker images "lamyas92/nafaplace-*" -q) -f 2>$null

# Construire et démarrer tous les services
Write-Host "🔨 Construction et démarrage des services..." -ForegroundColor Yellow
docker-compose up --build -d

# Attendre que les services soient prêts
Write-Host "⏳ Attente du démarrage des services..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# Vérifier l'état des services
Write-Host "🔍 Vérification de l'état des services..." -ForegroundColor Yellow
docker-compose ps

# Tester les endpoints de santé
Write-Host "🏥 Test des endpoints de santé..." -ForegroundColor Yellow

$services = @(
    @{Name="API Gateway"; Url="http://localhost:5000/health"},
    @{Name="Identity API"; Url="http://localhost:5155/health"},
    @{Name="Catalog API"; Url="http://localhost:5243/health"},
    @{Name="Cart API"; Url="http://localhost:5003/health"},
    @{Name="Order API"; Url="http://localhost:5004/health"},
    @{Name="Payment API"; Url="http://localhost:5005/health"},
    @{Name="Reviews API"; Url="http://localhost:5006/health"},
    @{Name="Notifications API"; Url="http://localhost:5007/health"}
)

foreach ($service in $services) {
    try {
        $response = Invoke-WebRequest -Uri $service.Url -TimeoutSec 10 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ $($service.Name) - OK" -ForegroundColor Green
        } else {
            Write-Host "⚠️ $($service.Name) - Status: $($response.StatusCode)" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "❌ $($service.Name) - Non accessible" -ForegroundColor Red
    }
}

# Afficher les URLs importantes
Write-Host "`n🌐 URLs importantes:" -ForegroundColor Cyan
Write-Host "API Gateway: http://localhost:5000" -ForegroundColor White
Write-Host "API Gateway Swagger: http://localhost:5000/swagger" -ForegroundColor White
Write-Host "Web App: http://localhost:8080" -ForegroundColor White
Write-Host "Admin Portal: http://localhost:8081" -ForegroundColor White
Write-Host "Seller Portal: http://localhost:8082" -ForegroundColor White
Write-Host "PgAdmin: http://localhost:5050 (<EMAIL> / admin)" -ForegroundColor White

# Afficher les informations de base de données
Write-Host "`n💾 Bases de données:" -ForegroundColor Cyan
Write-Host "PostgreSQL Catalog: localhost:5432" -ForegroundColor White
Write-Host "PostgreSQL Identity: localhost:5433" -ForegroundColor White
Write-Host "PostgreSQL Order: localhost:5434" -ForegroundColor White
Write-Host "PostgreSQL Reviews: localhost:5435" -ForegroundColor White
Write-Host "PostgreSQL Notifications: localhost:5436" -ForegroundColor White
Write-Host "Redis: localhost:6379" -ForegroundColor White

# Afficher les logs en cas de problème
Write-Host "`n📋 Pour voir les logs:" -ForegroundColor Cyan
Write-Host "docker-compose logs -f [service-name]" -ForegroundColor White
Write-Host "Exemple: docker-compose logs -f api-gateway" -ForegroundColor White

Write-Host "`n🎉 Déploiement terminé!" -ForegroundColor Green
Write-Host "La plateforme NafaPlace est maintenant accessible avec toutes les fonctionnalités en GNF" -ForegroundColor Green
