using Microsoft.Extensions.Configuration;
using NafaPlace.Order.API.DTOs;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;

namespace NafaPlace.Order.API.Services
{
    public class CartService : ICartService
    {
        private readonly HttpClient _httpClient;
        private readonly string? _cartApiUrl;

        public CartService(HttpClient httpClient, IConfiguration configuration)
        {
            _httpClient = httpClient;
            _cartApiUrl = configuration["ServiceUrls:CartApi"];
        }

        public async Task<ShoppingCartDto?> GetCartAsync(string userId)
        {
            if (string.IsNullOrEmpty(_cartApiUrl))
            {
                return null;
            }
            var response = await _httpClient.GetAsync($"{_cartApiUrl}/api/cart/{userId}");
            response.EnsureSuccessStatusCode();
            var content = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<ShoppingCartDto>(content, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
        }

        public async Task ClearCartAsync(string userId)
        {
            var response = await _httpClient.DeleteAsync($"{_cartApiUrl}/api/cart/{userId}");
            response.EnsureSuccessStatusCode();
        }
    }
}