@page "/account/orders"
@using NafaPlace.Web.Models.Order
@using NafaPlace.Web.Services
@using Microsoft.AspNetCore.Components.Authorization
@using System.Security.Claims
@inject IOrderService OrderService
@inject NavigationManager NavigationManager

<PageTitle>Mes Commandes - NafaPlace</PageTitle>

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-shopping-bag me-2"></i>Mes Commandes</h2>
                <a href="/" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Continuer mes achats
                </a>
            </div>

            @if (_loading)
            {
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                    <p class="mt-2">Chargement de vos commandes...</p>
                </div>
            }
            else if (_orders == null || !_orders.Any())
            {
                <div class="text-center py-5">
                    <i class="fas fa-shopping-bag text-muted" style="font-size: 4rem;"></i>
                    <h4 class="mt-3 text-muted">Aucune commande trouvée</h4>
                    <p class="text-muted">Vous n'avez pas encore passé de commande.</p>
                    <a href="/" class="btn btn-primary">
                        <i class="fas fa-shopping-cart me-2"></i>Commencer mes achats
                    </a>
                </div>
            }
            else
            {
                <div class="row">
                    @foreach (var order in _orders.OrderByDescending(o => o.CreatedAt))
                    {
                        <div class="col-12 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <div class="row align-items-center">
                                        <div class="col-md-3">
                                            <strong>Commande #@order.Id.ToString().PadLeft(8, '0')</strong>
                                        </div>
                                        <div class="col-md-3">
                                            <small class="text-muted">@order.CreatedAt.ToString("dd/MM/yyyy HH:mm")</small>
                                        </div>
                                        <div class="col-md-3">
                                            <span class="badge @GetStatusBadgeClass(order.Status)">
                                                @GetStatusText(order.Status)
                                            </span>
                                        </div>
                                        <div class="col-md-3 text-end">
                                            <strong>@order.TotalAmount.ToString("N0") GNF</strong>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <h6>Adresse de livraison:</h6>
                                            <p class="mb-2">
                                                @order.ShippingAddress?.FullName<br>
                                                @order.ShippingAddress?.Address<br>
                                                @order.ShippingAddress?.City, @order.ShippingAddress?.Country
                                            </p>
                                            
                                            <h6>Statut de paiement:</h6>
                                            <span class="badge @GetPaymentStatusBadgeClass(order.PaymentStatus)">
                                                @GetPaymentStatusText(order.PaymentStatus)
                                            </span>
                                            
                                            @if (!string.IsNullOrEmpty(order.TransactionId))
                                            {
                                                <br><small class="text-muted">Transaction: @order.TransactionId</small>
                                            }
                                        </div>
                                        <div class="col-md-4 text-end">
                                            <div class="btn-group-vertical w-100">
                                                <button class="btn btn-outline-primary btn-sm mb-2" 
                                                        @onclick="() => ViewOrderDetails(order.Id)">
                                                    <i class="fas fa-eye me-2"></i>Voir détails
                                                </button>
                                                
                                                @if (order.PaymentStatus != "Completed")
                                                {
                                                    <button class="btn btn-warning btn-sm mb-2" 
                                                            @onclick="() => GoToPayment(order.Id)">
                                                        <i class="fas fa-credit-card me-2"></i>Finaliser le paiement
                                                    </button>
                                                }
                                                
                                                @if (order.Status == "Delivered")
                                                {
                                                    <button class="btn btn-success btn-sm" disabled>
                                                        <i class="fas fa-check me-2"></i>Livré
                                                    </button>
                                                }
                                                else if (order.Status == "Shipped")
                                                {
                                                    <button class="btn btn-info btn-sm">
                                                        <i class="fas fa-truck me-2"></i>Suivre
                                                    </button>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            }
        </div>
    </div>
</div>

@code {
    private IEnumerable<OrderDto>? _orders;
    private bool _loading = true;
    private string _userId = string.Empty;

    [CascadingParameter]
    private Task<AuthenticationState> AuthenticationStateTask { get; set; } = null!;

    protected override async Task OnInitializedAsync()
    {
        try
        {
            var authState = await AuthenticationStateTask;
            var user = authState.User;
            
            if (user.Identity?.IsAuthenticated == true)
            {
                _userId = user.FindFirst(c => c.Type == ClaimTypes.NameIdentifier)?.Value ?? string.Empty;
                
                if (!string.IsNullOrEmpty(_userId))
                {
                    _orders = await OrderService.GetOrdersByUserIdAsync(_userId);
                }
            }
            else
            {
                NavigationManager.NavigateTo("/auth/login");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des commandes: {ex.Message}");
            _orders = new List<OrderDto>();
        }
        finally
        {
            _loading = false;
        }
    }

    private void ViewOrderDetails(int orderId)
    {
        NavigationManager.NavigateTo($"/account/orders/{orderId}");
    }

    private void GoToPayment(int orderId)
    {
        NavigationManager.NavigateTo($"/payment/simple/{orderId}");
    }

    private string GetStatusText(string? status)
    {
        return status switch
        {
            "Pending" => "En attente",
            "Confirmed" => "Confirmée",
            "Processing" => "En préparation",
            "Shipped" => "Expédiée",
            "Delivered" => "Livrée",
            "Cancelled" => "Annulée",
            _ => "Statut inconnu"
        };
    }

    private string GetStatusBadgeClass(string? status)
    {
        return status switch
        {
            "Pending" => "bg-warning",
            "Confirmed" => "bg-info",
            "Processing" => "bg-primary",
            "Shipped" => "bg-secondary",
            "Delivered" => "bg-success",
            "Cancelled" => "bg-danger",
            _ => "bg-secondary"
        };
    }

    private string GetPaymentStatusText(string? status)
    {
        return status switch
        {
            "Pending" => "En attente",
            "Completed" => "Payé",
            "Failed" => "Échoué",
            "Cancelled" => "Annulé",
            _ => "Inconnu"
        };
    }

    private string GetPaymentStatusBadgeClass(string? status)
    {
        return status switch
        {
            "Pending" => "bg-warning",
            "Completed" => "bg-success",
            "Failed" => "bg-danger",
            "Cancelled" => "bg-secondary",
            _ => "bg-secondary"
        };
    }
}
