# Script de test de performance NafaPlace
# Auteur: Assistant IA
# Date: 2025-01-28

param(
    [int]$Requests = 100,
    [int]$Concurrent = 10,
    [int]$Duration = 60,
    [string]$Service = "all",
    [switch]$Detailed
)

Write-Host "⚡ Tests de Performance NafaPlace" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green

# Configuration des services
$services = @{
    "Gateway" = "http://localhost:5000"
    "Catalog" = "http://localhost:5243"
    "Order" = "http://localhost:5004"
    "Notification" = "http://localhost:5005"
    "Analytics" = "http://localhost:5006"
    "Chat" = "http://localhost:5007"
    "Recommendation" = "http://localhost:5008"
    "Loyalty" = "http://localhost:5009"
    "Localization" = "http://localhost:5010"
    "Search" = "http://localhost:5011"
    "Inventory" = "http://localhost:5012"
}

# Endpoints de test pour chaque service
$testEndpoints = @{
    "Gateway" = @("/health", "/api/products", "/api/categories")
    "Catalog" = @("/health", "/api/products", "/api/categories")
    "Order" = @("/health", "/api/orders/user/test-user")
    "Notification" = @("/health", "/api/notifications/user/test-user")
    "Analytics" = @("/health", "/api/analytics/kpis")
    "Chat" = @("/health", "/api/faq")
    "Recommendation" = @("/health", "/api/recommendations/trending")
    "Loyalty" = @("/health", "/api/loyalty/account/test-user")
    "Localization" = @("/health", "/api/languages")
    "Search" = @("/health", "/api/search/suggestions")
    "Inventory" = @("/health", "/api/inventory/alerts")
}

# Classe pour stocker les résultats de performance
class PerformanceResult {
    [string]$Service
    [string]$Endpoint
    [int]$TotalRequests
    [int]$SuccessfulRequests
    [int]$FailedRequests
    [double]$AverageResponseTime
    [double]$MinResponseTime
    [double]$MaxResponseTime
    [double]$RequestsPerSecond
    [double]$SuccessRate
    [DateTime]$TestTime
}

# Fonction pour tester un endpoint spécifique
function Test-EndpointPerformance {
    param(
        [string]$ServiceName,
        [string]$BaseUrl,
        [string]$Endpoint,
        [int]$RequestCount,
        [int]$ConcurrentUsers
    )
    
    Write-Host "🎯 Test: $ServiceName$Endpoint ($RequestCount requêtes, $ConcurrentUsers concurrent)" -ForegroundColor Cyan
    
    $url = "$BaseUrl$Endpoint"
    $results = @()
    $startTime = Get-Date
    
    # Créer des jobs pour les requêtes concurrentes
    $jobs = @()
    $requestsPerJob = [math]::Ceiling($RequestCount / $ConcurrentUsers)
    
    for ($i = 0; $i -lt $ConcurrentUsers; $i++) {
        $job = Start-Job -ScriptBlock {
            param($Url, $RequestsPerJob, $JobId)
            
            $jobResults = @()
            for ($j = 0; $j -lt $RequestsPerJob; $j++) {
                $requestStart = Get-Date
                try {
                    $response = Invoke-RestMethod -Uri $Url -Method Get -TimeoutSec 30
                    $requestEnd = Get-Date
                    $responseTime = ($requestEnd - $requestStart).TotalMilliseconds
                    
                    $jobResults += @{
                        Success = $true
                        ResponseTime = $responseTime
                        StatusCode = 200
                    }
                }
                catch {
                    $requestEnd = Get-Date
                    $responseTime = ($requestEnd - $requestStart).TotalMilliseconds
                    
                    $jobResults += @{
                        Success = $false
                        ResponseTime = $responseTime
                        Error = $_.Exception.Message
                    }
                }
            }
            return $jobResults
        } -ArgumentList $url, $requestsPerJob, $i
        
        $jobs += $job
    }
    
    # Attendre que tous les jobs se terminent
    Write-Host "⏳ Exécution des requêtes..." -ForegroundColor Yellow
    $allResults = @()
    
    foreach ($job in $jobs) {
        $jobResults = Receive-Job -Job $job -Wait
        $allResults += $jobResults
        Remove-Job -Job $job
    }
    
    $endTime = Get-Date
    $totalDuration = ($endTime - $startTime).TotalSeconds
    
    # Calculer les statistiques
    $successfulRequests = ($allResults | Where-Object { $_.Success }).Count
    $failedRequests = $allResults.Count - $successfulRequests
    $responseTimes = $allResults | ForEach-Object { $_.ResponseTime }
    
    $avgResponseTime = if ($responseTimes.Count -gt 0) { ($responseTimes | Measure-Object -Average).Average } else { 0 }
    $minResponseTime = if ($responseTimes.Count -gt 0) { ($responseTimes | Measure-Object -Minimum).Minimum } else { 0 }
    $maxResponseTime = if ($responseTimes.Count -gt 0) { ($responseTimes | Measure-Object -Maximum).Maximum } else { 0 }
    $requestsPerSecond = if ($totalDuration -gt 0) { $allResults.Count / $totalDuration } else { 0 }
    $successRate = if ($allResults.Count -gt 0) { ($successfulRequests / $allResults.Count) * 100 } else { 0 }
    
    # Créer l'objet résultat
    $result = [PerformanceResult]::new()
    $result.Service = $ServiceName
    $result.Endpoint = $Endpoint
    $result.TotalRequests = $allResults.Count
    $result.SuccessfulRequests = $successfulRequests
    $result.FailedRequests = $failedRequests
    $result.AverageResponseTime = [math]::Round($avgResponseTime, 2)
    $result.MinResponseTime = [math]::Round($minResponseTime, 2)
    $result.MaxResponseTime = [math]::Round($maxResponseTime, 2)
    $result.RequestsPerSecond = [math]::Round($requestsPerSecond, 2)
    $result.SuccessRate = [math]::Round($successRate, 2)
    $result.TestTime = $startTime
    
    # Afficher les résultats
    $statusColor = if ($successRate -ge 95) { "Green" } elseif ($successRate -ge 80) { "Yellow" } else { "Red" }
    Write-Host "📊 Résultats:" -ForegroundColor White
    Write-Host "   • Succès: $successfulRequests/$($allResults.Count) ($($result.SuccessRate)%)" -ForegroundColor $statusColor
    Write-Host "   • Temps moyen: $($result.AverageResponseTime)ms" -ForegroundColor White
    Write-Host "   • Req/sec: $($result.RequestsPerSecond)" -ForegroundColor White
    Write-Host "   • Min/Max: $($result.MinResponseTime)ms / $($result.MaxResponseTime)ms" -ForegroundColor White
    
    if ($Detailed -and $failedRequests -gt 0) {
        Write-Host "❌ Erreurs détectées:" -ForegroundColor Red
        $errors = $allResults | Where-Object { -not $_.Success } | Group-Object Error | Select-Object Name, Count
        foreach ($error in $errors) {
            Write-Host "   • $($error.Name): $($error.Count) fois" -ForegroundColor Red
        }
    }
    
    return $result
}

# Fonction pour tester tous les services
function Test-AllServicesPerformance {
    Write-Host "🚀 Test de performance de tous les services..." -ForegroundColor Green
    
    $allResults = @()
    
    foreach ($service in $services.GetEnumerator()) {
        if ($testEndpoints.ContainsKey($service.Key)) {
            Write-Host "`n🔧 Service: $($service.Key)" -ForegroundColor Magenta
            Write-Host "----------------------------------------" -ForegroundColor Magenta
            
            foreach ($endpoint in $testEndpoints[$service.Key]) {
                try {
                    $result = Test-EndpointPerformance $service.Key $service.Value $endpoint $Requests $Concurrent
                    $allResults += $result
                }
                catch {
                    Write-Host "❌ Erreur lors du test de $($service.Key)$endpoint : $($_.Exception.Message)" -ForegroundColor Red
                }
                
                Start-Sleep -Seconds 1
            }
        }
    }
    
    return $allResults
}

# Fonction pour tester un service spécifique
function Test-SpecificServicePerformance {
    param([string]$ServiceName)
    
    if (-not $services.ContainsKey($ServiceName)) {
        Write-Host "❌ Service '$ServiceName' non trouvé" -ForegroundColor Red
        Write-Host "Services disponibles: $($services.Keys -join ', ')" -ForegroundColor Yellow
        return @()
    }
    
    Write-Host "🎯 Test de performance du service: $ServiceName" -ForegroundColor Green
    
    $results = @()
    $baseUrl = $services[$ServiceName]
    $endpoints = $testEndpoints[$ServiceName]
    
    foreach ($endpoint in $endpoints) {
        try {
            $result = Test-EndpointPerformance $ServiceName $baseUrl $endpoint $Requests $Concurrent
            $results += $result
        }
        catch {
            Write-Host "❌ Erreur lors du test de $ServiceName$endpoint : $($_.Exception.Message)" -ForegroundColor Red
        }
        
        Start-Sleep -Seconds 1
    }
    
    return $results
}

# Fonction pour générer un rapport de performance
function New-PerformanceReport {
    param([array]$Results)
    
    Write-Host "`n📋 RAPPORT DE PERFORMANCE" -ForegroundColor Magenta
    Write-Host "=========================" -ForegroundColor Magenta
    
    if ($Results.Count -eq 0) {
        Write-Host "❌ Aucun résultat à afficher" -ForegroundColor Red
        return
    }
    
    # Statistiques globales
    $totalRequests = ($Results | Measure-Object TotalRequests -Sum).Sum
    $totalSuccessful = ($Results | Measure-Object SuccessfulRequests -Sum).Sum
    $totalFailed = ($Results | Measure-Object FailedRequests -Sum).Sum
    $avgSuccessRate = ($Results | Measure-Object SuccessRate -Average).Average
    $avgResponseTime = ($Results | Measure-Object AverageResponseTime -Average).Average
    $totalRPS = ($Results | Measure-Object RequestsPerSecond -Sum).Sum
    
    Write-Host "`n🌐 Statistiques Globales:" -ForegroundColor Cyan
    Write-Host "   • Total requêtes: $totalRequests" -ForegroundColor White
    Write-Host "   • Succès: $totalSuccessful ($([math]::Round($avgSuccessRate, 2))%)" -ForegroundColor Green
    Write-Host "   • Échecs: $totalFailed" -ForegroundColor $(if ($totalFailed -eq 0) { "Green" } else { "Red" })
    Write-Host "   • Temps moyen: $([math]::Round($avgResponseTime, 2))ms" -ForegroundColor White
    Write-Host "   • Débit total: $([math]::Round($totalRPS, 2)) req/sec" -ForegroundColor White
    
    # Top 5 des services les plus rapides
    Write-Host "`n🏆 Top 5 - Services les plus rapides:" -ForegroundColor Cyan
    $fastestServices = $Results | Sort-Object AverageResponseTime | Select-Object -First 5
    foreach ($service in $fastestServices) {
        Write-Host "   • $($service.Service)$($service.Endpoint): $($service.AverageResponseTime)ms" -ForegroundColor Green
    }
    
    # Services avec problèmes
    $problematicServices = $Results | Where-Object { $_.SuccessRate -lt 95 }
    if ($problematicServices.Count -gt 0) {
        Write-Host "`n⚠️ Services avec problèmes (< 95% succès):" -ForegroundColor Yellow
        foreach ($service in $problematicServices) {
            Write-Host "   • $($service.Service)$($service.Endpoint): $($service.SuccessRate)%" -ForegroundColor Red
        }
    }
    
    # Recommandations
    Write-Host "`n💡 Recommandations:" -ForegroundColor Cyan
    if ($avgResponseTime -gt 2000) {
        Write-Host "   ⚠️ Temps de réponse élevé (>2s) - Optimisation nécessaire" -ForegroundColor Yellow
    } else {
        Write-Host "   ✅ Temps de réponse acceptable (<2s)" -ForegroundColor Green
    }
    
    if ($avgSuccessRate -lt 95) {
        Write-Host "   ⚠️ Taux de succès faible (<95%) - Vérifier la stabilité" -ForegroundColor Yellow
    } else {
        Write-Host "   ✅ Taux de succès excellent (≥95%)" -ForegroundColor Green
    }
    
    if ($totalRPS -lt 50) {
        Write-Host "   ⚠️ Débit faible (<50 req/sec) - Optimisation possible" -ForegroundColor Yellow
    } else {
        Write-Host "   ✅ Débit satisfaisant (≥50 req/sec)" -ForegroundColor Green
    }
    
    # Sauvegarder le rapport
    $reportPath = "logs/performance-report-$(Get-Date -Format 'yyyyMMdd-HHmmss').csv"
    $Results | Export-Csv -Path $reportPath -NoTypeInformation -Encoding UTF8
    Write-Host "`n💾 Rapport sauvegardé: $reportPath" -ForegroundColor Blue
}

# Fonction pour afficher l'aide
function Show-PerformanceHelp {
    Write-Host "📖 Aide - Tests de Performance" -ForegroundColor Cyan
    Write-Host "===============================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Syntaxe:" -ForegroundColor Yellow
    Write-Host "  .\performance-test.ps1 [options]" -ForegroundColor White
    Write-Host ""
    Write-Host "Options:" -ForegroundColor Yellow
    Write-Host "  -Requests <n>     Nombre de requêtes par endpoint (défaut: 100)" -ForegroundColor White
    Write-Host "  -Concurrent <n>   Utilisateurs concurrents (défaut: 10)" -ForegroundColor White
    Write-Host "  -Duration <n>     Durée du test en secondes (défaut: 60)" -ForegroundColor White
    Write-Host "  -Service <nom>    Tester un service spécifique (défaut: all)" -ForegroundColor White
    Write-Host "  -Detailed         Affichage détaillé des erreurs" -ForegroundColor White
    Write-Host ""
    Write-Host "Exemples:" -ForegroundColor Yellow
    Write-Host "  .\performance-test.ps1                           # Test standard" -ForegroundColor White
    Write-Host "  .\performance-test.ps1 -Requests 500 -Concurrent 20  # Test intensif" -ForegroundColor White
    Write-Host "  .\performance-test.ps1 -Service Analytics        # Test Analytics uniquement" -ForegroundColor White
    Write-Host "  .\performance-test.ps1 -Detailed                 # Avec détails des erreurs" -ForegroundColor White
}

# Fonction principale
function Start-PerformanceTest {
    Write-Host "⚡ Configuration du test:" -ForegroundColor Yellow
    Write-Host "   • Requêtes par endpoint: $Requests" -ForegroundColor White
    Write-Host "   • Utilisateurs concurrents: $Concurrent" -ForegroundColor White
    Write-Host "   • Service(s): $Service" -ForegroundColor White
    Write-Host "   • Mode détaillé: $Detailed" -ForegroundColor White
    
    # Créer le dossier de logs s'il n'existe pas
    if (-not (Test-Path "logs")) {
        New-Item -ItemType Directory -Path "logs" | Out-Null
    }
    
    $results = @()
    
    if ($Service -eq "all") {
        $results = Test-AllServicesPerformance
    } else {
        $results = Test-SpecificServicePerformance $Service
    }
    
    if ($results.Count -gt 0) {
        New-PerformanceReport $results
    }
}

# Vérifier les paramètres d'aide
if ($args -contains "-help" -or $args -contains "--help" -or $args -contains "/?") {
    Show-PerformanceHelp
    return
}

# Exécuter les tests
Start-PerformanceTest

Write-Host "`n🏁 Tests de performance terminés !" -ForegroundColor Green
