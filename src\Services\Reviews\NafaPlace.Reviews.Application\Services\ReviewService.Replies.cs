using NafaPlace.Reviews.Application.DTOs;
using NafaPlace.Reviews.Application.Interfaces;
using NafaPlace.Reviews.Application.Services;
using NafaPlace.Reviews.Domain.Models;
using NafaPlace.Reviews.DTOs;

namespace NafaPlace.Reviews.Application.Services;

public partial class ReviewService : IReviewService
{
    // Reply operations
    
    public async Task<ReplyDto?> GetReplyByIdAsync(int id)
    {
        var reply = await _reviewRepository.GetReplyByIdAsync(id);
        return reply == null ? null : MapToReplyDto(reply);
    }
    
    public async Task<List<ReplyDto>> GetRepliesByReviewIdAsync(int reviewId)
    {
        var replies = await _reviewRepository.GetRepliesByReviewIdAsync(reviewId);
        return replies.Select(MapToReplyDto).ToList();
    }
    
    public async Task<ReplyDto> CreateReplyAsync(CreateReplyRequest request, string userId, string userName)
    {
        var reply = new Reply
        {
            ReviewId = request.ReviewId,
            UserId = userId,
            UserName = userName,
            Content = request.Content,
            IsFromSeller = request.IsFromSeller,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };
        
        var createdReply = await _reviewRepository.CreateReplyAsync(reply);
        
        // Get the review to send notification to its author
        var review = await _reviewRepository.GetByIdAsync(request.ReviewId);
        if (review != null && review.UserId != userId)
        {
            await _notificationService.SendReviewReplyNotificationAsync(createdReply.Id, request.ReviewId, review.UserId);
        }
        
        return MapToReplyDto(createdReply);
    }
    
    public async Task<ReplyDto> UpdateReplyAsync(int id, UpdateReplyRequest request, string userId)
    {
        var reply = await _reviewRepository.GetReplyByIdAsync(id);
        if (reply == null)
        {
            throw new ArgumentException("Reply not found");
        }
        
        if (reply.UserId != userId)
        {
            throw new UnauthorizedAccessException("User can only update their own replies");
        }
        
        reply.Content = request.Content;
        reply.UpdatedAt = DateTime.UtcNow;
        
        var updatedReply = await _reviewRepository.UpdateReplyAsync(reply);
        return MapToReplyDto(updatedReply);
    }
    
    public async Task DeleteReplyAsync(int id, string userId)
    {
        var reply = await _reviewRepository.GetReplyByIdAsync(id);
        if (reply == null)
        {
            throw new ArgumentException("Reply not found");
        }
        
        if (reply.UserId != userId)
        {
            throw new UnauthorizedAccessException("User can only delete their own replies");
        }
        
        await _reviewRepository.DeleteReplyAsync(id);
    }
    
    private static ReplyDto MapToReplyDto(Reply reply)
    {
        return new ReplyDto
        {
            Id = reply.Id,
            ReviewId = reply.ReviewId,
            UserId = reply.UserId,
            UserName = reply.UserName,
            Content = reply.Content,
            CreatedAt = reply.CreatedAt,
            UpdatedAt = reply.UpdatedAt,
            IsFromSeller = reply.IsFromSeller
        };
    }
}
