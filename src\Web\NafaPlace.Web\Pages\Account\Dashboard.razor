@page "/account/dashboard"
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@using Microsoft.AspNetCore.Components.Authorization
@inject AuthenticationStateProvider AuthStateProvider
@inject NavigationManager NavigationManager

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-body">
                    <AuthorizeView>
                        <Authorized>
                            <h2 class="mb-4">Bienvenue, @context.User.Identity?.Name!</h2>
                            <p class="lead">Vous êtes maintenant connecté à votre compte NafaPlace.</p>
                            
                            <div class="row mt-5">
                                <div class="col-md-4 mb-4">
                                    <div class="card h-100 border-0 shadow-sm">
                                        <div class="card-body text-center py-4">
                                            <i class="fas fa-user-circle text-primary mb-3" style="font-size: 3rem;"></i>
                                            <h4>Mon Profil</h4>
                                            <p class="text-muted">G<PERSON><PERSON> vos informations personnelles</p>
                                            <a href="/account/profile" class="btn btn-outline-primary">Voir mon profil</a>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-4 mb-4">
                                    <div class="card h-100 border-0 shadow-sm">
                                        <div class="card-body text-center py-4">
                                            <i class="fas fa-shopping-bag text-primary mb-3" style="font-size: 3rem;"></i>
                                            <h4>Mes Commandes</h4>
                                            <p class="text-muted">Suivez vos commandes récentes</p>
                                            <a href="/account/orders" class="btn btn-outline-primary">Voir mes commandes</a>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-4 mb-4">
                                    <div class="card h-100 border-0 shadow-sm">
                                        <div class="card-body text-center py-4">
                                            <i class="fas fa-star text-primary mb-3" style="font-size: 3rem;"></i>
                                            <h4>Mes Avis</h4>
                                            <p class="text-muted">Gérez vos avis produits</p>
                                            <a href="/account/my-reviews" class="btn btn-outline-primary">Voir mes avis</a>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-4 mb-4">
                                    <div class="card h-100 border-0 shadow-sm">
                                        <div class="card-body text-center py-4">
                                            <i class="fas fa-map-marker-alt text-primary mb-3" style="font-size: 3rem;"></i>
                                            <h4>Mes Adresses</h4>
                                            <p class="text-muted">Gérez vos adresses de livraison</p>
                                            <a href="/account/addresses" class="btn btn-outline-primary">Voir mes adresses</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </Authorized>
                        <NotAuthorized>
                            <div class="text-center py-5">
                                <h3 class="text-danger">Accès non autorisé</h3>
                                <p class="lead">Vous devez être connecté pour accéder à cette page.</p>
                                <a href="/auth/login" class="btn btn-primary mt-3">Se connecter</a>
                            </div>
                        </NotAuthorized>
                    </AuthorizeView>
                </div>
            </div>
        </div>
    </div>
</div>
