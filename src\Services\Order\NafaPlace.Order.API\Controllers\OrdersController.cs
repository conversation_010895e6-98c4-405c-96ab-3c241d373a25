using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using NafaPlace.Order.Application;
using NafaPlace.Order.API.Services;
using NafaPlace.Order.API.Models;
using System;
using System.Linq;
using System.Threading.Tasks;
using System.Text.Json;
using System.Collections.Generic;

namespace NafaPlace.Order.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class OrdersController : ControllerBase
    {
        private readonly IOrderRepository _repository;
        private readonly ICartService _cartService;
        private readonly HttpClient _httpClient;
        private readonly IConfiguration _configuration;
        private readonly IDeliveryIntegrationService _deliveryService;

        public OrdersController(IOrderRepository repository, ICartService cartService, HttpClient httpClient, IConfiguration configuration, IDeliveryIntegrationService deliveryService)
        {
            _repository = repository;
            _cartService = cartService;
            _httpClient = httpClient;
            _configuration = configuration;
            _deliveryService = deliveryService;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<Domain.Order>>> GetAllOrders(
            [FromQuery] string? searchTerm = null,
            [FromQuery] string? status = null,
            [FromQuery] string? paymentStatus = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 20)
        {
            var orders = await _repository.GetOrdersWithFiltersAsync(
                searchTerm, status, paymentStatus, startDate, endDate, pageNumber, pageSize);
            return Ok(orders);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<Domain.Order>> GetOrder(int id)
        {
            var order = await _repository.GetOrderByIdAsync(id);
            if (order == null)
            {
                return NotFound();
            }
            return Ok(order);
        }

        [HttpGet("user/{userId}")]
        public async Task<ActionResult<Domain.Order>> GetOrdersByUser(string userId)
        {
            var orders = await _repository.GetOrdersByUserIdAsync(userId);
            return Ok(orders);
        }

        [HttpPost]
        public async Task<ActionResult<Domain.Order>> CreateOrder(Domain.Order order)
        {
            var createdOrder = await _repository.CreateOrderAsync(order);
            return CreatedAtAction(nameof(GetOrder), new { id = createdOrder.Id }, createdOrder);
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateOrder(int id, Domain.Order order)
        {
            if (id != order.Id)
            {
                return BadRequest();
            }

            await _repository.UpdateOrderAsync(order);

            return NoContent();
        }

        [HttpPost("checkout/{userId}")]
        public async Task<ActionResult<Domain.Order>> Checkout(string userId, [FromBody] CheckoutRequest request)
        {
            var cart = await _cartService.GetCartAsync(userId);
            if (cart == null || cart.Items.Count == 0)
            {
                return BadRequest("Cart is empty");
            }

            // Calculer le total complet avec frais de livraison et taxes
            var subTotal = cart.Items.Sum(i => i.UnitPrice * i.Quantity);

            // Appliquer la réduction de coupon si présente
            var couponDiscount = request.CouponDiscount;
            var discountedSubTotal = Math.Max(0, subTotal - couponDiscount);

            // Recalculer les frais de livraison et taxes sur le montant après réduction
            var shippingFee = discountedSubTotal > 500000 ? 0 : 25000; // Livraison gratuite > 500,000 GNF
            var tax = discountedSubTotal * 0.18m; // 18% TVA
            var totalAmount = discountedSubTotal + shippingFee + tax;

            var order = new Domain.Order
            {
                UserId = userId,
                OrderDate = DateTime.UtcNow,
                TotalAmount = totalAmount,
                Currency = "GNF", // Franc Guinéen
                Status = Domain.OrderStatus.Pending,
                PaymentMethod = request.PaymentMethod,
                PaymentStatus = Domain.PaymentStatus.Pending,
                ShippingAddress = request.ShippingAddress != null ? new Domain.ShippingAddress
                {
                    FullName = request.ShippingAddress.FullName,
                    Address = request.ShippingAddress.Address,
                    City = request.ShippingAddress.City,
                    Country = request.ShippingAddress.Country,
                    PostalCode = request.ShippingAddress.PostalCode,
                    PhoneNumber = request.ShippingAddress.PhoneNumber
                } : null,
                OrderItems = await CreateOrderItemsWithSellerIdAsync(cart.Items)
            };

            var createdOrder = await _repository.CreateOrderAsync(order);

            // Enregistrer l'utilisation du coupon si un coupon a été appliqué
            if (request.CouponId.HasValue && couponDiscount > 0)
            {
                await RecordCouponUsageAsync(request.CouponId.Value, userId, createdOrder.Id.ToString(), couponDiscount);
            }

            // Créer automatiquement l'ordre de livraison si une adresse de livraison est fournie
            if (createdOrder.ShippingAddress != null)
            {
                try
                {
                    var deliveryOrder = await _deliveryService.CreateDeliveryOrderAsync(createdOrder);
                    if (deliveryOrder != null)
                    {
                        // Optionnel: Mettre à jour la commande avec le numéro de suivi
                        // createdOrder.TrackingNumber = deliveryOrder.TrackingNumber;
                        // await _repository.UpdateOrderAsync(createdOrder);
                    }
                }
                catch (Exception ex)
                {
                    // Log l'erreur mais ne pas faire échouer la commande
                    Console.WriteLine($"Erreur lors de la création de l'ordre de livraison: {ex.Message}");
                }
            }

            // Ne pas vider le panier ici - il sera vidé après un paiement réussi
            // await _cartService.ClearCartAsync(userId);

            return CreatedAtAction(nameof(GetOrder), new { id = createdOrder.Id }, createdOrder);
        }

        [HttpPost("{orderId}/initiate-payment")]
        public async Task<ActionResult> InitiatePayment(int orderId, [FromBody] InitiatePaymentRequest request)
        {
            var order = await _repository.GetOrderByIdAsync(orderId);
            if (order == null)
            {
                return NotFound("Order not found");
            }

            if (order.PaymentStatus != Domain.PaymentStatus.Pending)
            {
                return BadRequest("Payment already processed");
            }

            try
            {
                switch (order.PaymentMethod)
                {
                    case Domain.PaymentMethod.OrangeMoney:
                        // TODO: Appeler l'API Orange Money
                        return Ok(new {
                            message = "Orange Money payment initiated",
                            paymentUrl = $"http://localhost:8080/payment/orange-money?orderId={orderId}",
                            orderId = orderId
                        });

                    case Domain.PaymentMethod.Stripe:
                        // TODO: Appeler l'API Stripe
                        return Ok(new {
                            message = "Stripe payment initiated",
                            paymentUrl = $"http://localhost:8080/payment/stripe?orderId={orderId}",
                            orderId = orderId
                        });

                    case Domain.PaymentMethod.CashOnDelivery:
                        return Ok(new {
                            message = "Cash on delivery - no payment required",
                            orderId = orderId
                        });

                    default:
                        return BadRequest("Unsupported payment method");
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = "Error initiating payment", details = ex.Message });
            }
        }

        [HttpPost("{orderId}/update-payment-status")]
        public async Task<ActionResult> UpdatePaymentStatus(int orderId, [FromBody] UpdatePaymentStatusRequest request)
        {
            var order = await _repository.GetOrderByIdAsync(orderId);
            if (order == null)
            {
                return NotFound("Order not found");
            }

            order.PaymentStatus = request.PaymentStatus;
            order.PaymentTransactionId = request.TransactionId;

            if (request.PaymentStatus == Domain.PaymentStatus.Completed)
            {
                order.PaymentDate = DateTime.UtcNow;
                order.Status = Domain.OrderStatus.Paid;

                // Vider le panier après un paiement réussi
                try
                {
                    await _cartService.ClearCartAsync(order.UserId);
                    Console.WriteLine($"Panier vidé pour l'utilisateur {order.UserId} après paiement réussi de la commande {orderId}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Erreur lors du vidage du panier pour l'utilisateur {order.UserId}: {ex.Message}");
                    // Ne pas faire échouer la mise à jour du paiement pour un problème de panier
                }
            }

            await _repository.UpdateOrderAsync(order);

            return Ok(new { message = "Payment status updated", orderId = orderId });
        }

        [HttpPut("{orderId}/status")]
        public async Task<ActionResult<UpdateOrderStatusResponse>> UpdateOrderStatus(int orderId, [FromBody] UpdateOrderStatusRequest request)
        {
            var order = await _repository.GetOrderByIdAsync(orderId);
            if (order == null)
            {
                return NotFound("Order not found");
            }

            // Convertir le statut string en enum
            if (Enum.TryParse<Domain.OrderStatus>(request.Status, out var orderStatus))
            {
                order.Status = orderStatus;
            }
            else
            {
                return BadRequest($"Invalid order status: {request.Status}");
            }

            // Mettre à jour les champs optionnels si fournis
            if (!string.IsNullOrEmpty(request.TrackingNumber))
            {
                // Ajouter le numéro de suivi si le domaine le supporte
                // Pour l'instant, nous pouvons l'ignorer ou l'ajouter comme note
            }

            await _repository.UpdateOrderAsync(order);

            var response = new UpdateOrderStatusResponse
            {
                Id = order.Id,
                Status = order.Status.ToString(),
                Message = "Order status updated successfully"
            };

            return Ok(response);
        }

        [HttpGet("seller/{sellerId}")]
        [Authorize]
        public async Task<ActionResult<IEnumerable<Domain.Order>>> GetOrdersBySeller(
            int sellerId,
            [FromQuery] string? searchTerm = null,
            [FromQuery] string? status = null,
            [FromQuery] string? paymentStatus = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 20)
        {
            var orders = await _repository.GetOrdersBySellerIdAsync(
                sellerId, searchTerm, status, paymentStatus, startDate, endDate, pageNumber, pageSize);
            return Ok(orders);
        }

        [HttpGet("seller/{sellerId}/count")]
        public async Task<ActionResult<int>> GetOrdersCountBySeller(int sellerId)
        {
            var count = await _repository.GetOrdersCountBySellerIdAsync(sellerId);
            return Ok(count);
        }

        private async Task<List<Domain.OrderItem>> CreateOrderItemsWithSellerIdAsync(List<NafaPlace.Order.API.DTOs.CartItemDto> cartItems)
        {
            var orderItems = new List<Domain.OrderItem>();

            foreach (var item in cartItems)
            {
                var sellerId = await GetProductSellerIdAsync(item.ProductId);

                orderItems.Add(new Domain.OrderItem
                {
                    ProductId = item.ProductId,
                    ProductName = item.ProductName,
                    UnitPrice = item.UnitPrice,
                    Quantity = item.Quantity,
                    ImageUrl = item.ImageUrl,
                    SellerId = sellerId
                });
            }

            return orderItems;
        }

        private async Task<int> GetProductSellerIdAsync(int productId)
        {
            try
            {
                var catalogApiUrl = _configuration["ServiceUrls:CatalogApi"];
                Console.WriteLine($"[GetProductSellerIdAsync] CatalogApi URL: {catalogApiUrl}");

                if (string.IsNullOrEmpty(catalogApiUrl))
                {
                    Console.WriteLine("❌ CatalogApi URL not configured");
                    return 1; // Default seller ID
                }

                var url = $"{catalogApiUrl}/api/v1/products/{productId}";
                Console.WriteLine($"[GetProductSellerIdAsync] Calling: {url}");

                var response = await _httpClient.GetAsync(url);
                Console.WriteLine($"[GetProductSellerIdAsync] Response status: {response.StatusCode}");

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"[GetProductSellerIdAsync] Response content: {content}");

                    var product = JsonSerializer.Deserialize<ProductDto>(content, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
                    var sellerId = product?.SellerId ?? 1;
                    Console.WriteLine($"[GetProductSellerIdAsync] Product SellerId: {sellerId}");
                    return sellerId;
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"❌ API call failed: {response.StatusCode} - {errorContent}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error getting product seller ID: {ex.Message}");
            }

            Console.WriteLine("⚠️ Returning default SellerId = 1");
            return 1; // Default seller ID if error
        }

        private async Task RecordCouponUsageAsync(int couponId, string userId, string orderId, decimal discountAmount)
        {
            try
            {
                var couponApiUrl = _configuration.GetValue<string>("ApiSettings:CouponApiUrl") ?? "http://coupon-api";
                var requestData = new
                {
                    CouponId = couponId,
                    UserId = userId,
                    OrderId = orderId,
                    DiscountAmount = discountAmount
                };

                var json = JsonSerializer.Serialize(requestData);
                var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{couponApiUrl}/api/coupon/record-usage", content);

                if (response.IsSuccessStatusCode)
                {
                    Console.WriteLine($"✅ Coupon usage recorded successfully for coupon {couponId}");
                }
                else
                {
                    Console.WriteLine($"⚠️ Failed to record coupon usage: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error recording coupon usage: {ex.Message}");
            }
        }

        [HttpGet("delivery/calculate-fee")]
        public async Task<ActionResult<decimal>> CalculateDeliveryFee([FromQuery] string address, [FromQuery] decimal orderAmount, [FromQuery] decimal? weight = null)
        {
            try
            {
                var fee = await _deliveryService.CalculateDeliveryFeeAsync(address, orderAmount, weight);
                return Ok(fee);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erreur lors du calcul des frais de livraison: {ex.Message}");
            }
        }

        [HttpPost("delivery/quotes")]
        public async Task<ActionResult<List<DeliveryQuoteDto>>> GetDeliveryQuotes([FromBody] DeliveryQuoteRequest request)
        {
            try
            {
                var quotes = await _deliveryService.GetDeliveryQuotesAsync(
                    request.Address,
                    request.OrderAmount,
                    request.Weight,
                    request.Volume
                );
                return Ok(quotes);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erreur lors de l'obtention des devis de livraison: {ex.Message}");
            }
        }

        [HttpGet("{orderId}/delivery/tracking")]
        public async Task<ActionResult<List<DeliveryTrackingDto>>> GetOrderDeliveryTracking(int orderId)
        {
            try
            {
                var order = await _repository.GetOrderByIdAsync(orderId);
                if (order == null)
                    return NotFound();

                // Assuming we store tracking number in order or have a way to get it
                // For now, we'll use order ID as tracking number
                var trackingNumber = $"NP{DateTime.UtcNow:yyyyMMdd}{orderId:D4}";
                var tracking = await _deliveryService.GetDeliveryTrackingAsync(trackingNumber);

                return Ok(tracking);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erreur lors de l'obtention du suivi de livraison: {ex.Message}");
            }
        }

        [HttpGet("health")]
        public IActionResult Health()
        {
            return Ok(new { status = "healthy", service = "order-api", timestamp = DateTime.UtcNow });
        }
    }

    public class ProductDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public int SellerId { get; set; }
    }

    public class DeliveryQuoteRequest
    {
        public string Address { get; set; } = string.Empty;
        public decimal OrderAmount { get; set; }
        public decimal? Weight { get; set; }
        public decimal? Volume { get; set; }
    }
}