using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using NafaPlace.Catalog.Application.DTOs.Category;

namespace NafaPlace.Catalog.Application.Common.Interfaces
{
    public interface ICategoryService
    {
        Task<CategoryDto> CreateCategoryAsync(CreateCategoryRequest request);
        Task<CategoryDto> UpdateCategoryAsync(int id, UpdateCategoryRequest request);
        Task<bool> DeleteCategoryAsync(int id);
        Task<CategoryDto> GetCategoryByIdAsync(int id);
        Task<IEnumerable<CategoryDto>> GetAllCategoriesAsync();
        
        // Méthodes additionnelles utilisées dans le contrôleur
        Task<IEnumerable<CategoryDto>> GetMainCategoriesAsync();
        Task<IEnumerable<CategoryDto>> GetSubcategoriesAsync(int parentId);
        Task<IEnumerable<CategoryDto>> SearchCategoriesAsync(string searchTerm);
        Task<string> UploadCategoryImageAsync(string base64Image);
    }
}
