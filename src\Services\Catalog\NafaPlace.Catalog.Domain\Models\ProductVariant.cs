using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using NafaPlace.Catalog.Domain.Common;

namespace NafaPlace.Catalog.Domain.Models;

public class ProductVariant : BaseEntity
{
    [Required]
    [MaxLength(100)]
    public required string Name { get; set; }

    [Required]
    [MaxLength(50)]
    public required string Sku { get; set; }

    [Required]
    [Range(0, double.MaxValue)]
    public decimal Price { get; set; }

    [Required]
    [Range(0, int.MaxValue)]
    public int StockQuantity { get; set; }

    [Range(0, double.MaxValue)]
    public decimal Weight { get; set; }

    [MaxLength(50)]
    public string? Dimensions { get; set; }

    public bool IsActive { get; set; } = true;

    public int ProductId { get; set; }
    public Product? Product { get; set; }

    public List<ProductVariantAttribute> Attributes { get; set; } = new();

    public new DateTime CreatedAt { get; set; }
    public new DateTime UpdatedAt { get; set; }
}
