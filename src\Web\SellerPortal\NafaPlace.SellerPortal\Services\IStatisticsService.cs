using NafaPlace.SellerPortal.Models.Statistics;

namespace NafaPlace.SellerPortal.Services;

public interface IStatisticsService
{
    Task<DashboardStatistics> GetDashboardStatisticsAsync(StatisticsRequest request);
    Task<SalesStatistics> GetSalesStatisticsAsync(StatisticsRequest request);
    Task<List<TopProduct>> GetTopProductsAsync(StatisticsRequest request, int limit = 10);
    Task<List<RegionSales>> GetSalesByRegionAsync(StatisticsRequest request);
    Task<List<CategorySales>> GetSalesByCategoryAsync(StatisticsRequest request);
    Task<List<SalesChartData>> GetSalesChartDataAsync(StatisticsRequest request);
    Task<List<SalesReport>> GetSalesReportAsync(StatisticsRequest request);
    Task<byte[]> ExportStatisticsAsync(ExportRequest request);
}
