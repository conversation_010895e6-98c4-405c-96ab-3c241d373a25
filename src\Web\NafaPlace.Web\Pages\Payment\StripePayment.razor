@page "/payment/stripe/{OrderId:int}"
@using NafaPlace.Web.Models.Order
@inject NavigationManager NavigationManager
@inject IOrderService OrderService
@inject IJSRuntime JSRuntime

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header bg-primary text-white text-center">
                    <h4><i class="fab fa-cc-stripe me-2"></i>Paiement par Carte</h4>
                </div>
                <div class="card-body">
                    @if (_loading)
                    {
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Chargement...</span>
                            </div>
                            <p class="mt-2">Initialisation du paiement...</p>
                        </div>
                    }
                    else if (_order != null)
                    {
                        <div class="text-center mb-4">
                            <h5>Commande #@_order.Id.ToString().PadLeft(8, '0')</h5>
                            <p class="text-muted">Montant à payer: <strong>@_order.TotalAmount.ToString("N0") GNF</strong></p>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-credit-card me-2"></i>
                            <strong>Paiement sécurisé par Stripe</strong>
                            <p class="mt-2 mb-0">Vos informations de carte sont cryptées et sécurisées.</p>
                        </div>

                        <!-- Stripe Checkout Button -->
                        <div id="stripe-checkout">
                            <div class="alert alert-info mb-4">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Paiement sécurisé Stripe</strong>
                                <p class="mb-0 mt-2">Cliquez sur le bouton ci-dessous pour procéder au paiement sécurisé via Stripe.</p>
                            </div>

                            <div class="card border-primary mb-4">
                                <div class="card-body text-center">
                                    <h5 class="card-title">Récapitulatif du paiement</h5>
                                    <p class="card-text">
                                        <strong>Commande :</strong> #@_order.Id.ToString().PadLeft(8, '0')<br>
                                        <strong>Montant :</strong> <span class="text-primary fs-4">@_order.TotalAmount.ToString("N0") GNF</span>
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="text-center">
                            <button class="btn btn-success btn-lg me-2" @onclick="CreateStripeCheckoutSession" disabled="@_processing">
                                @if (_processing)
                                {
                                    <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                    <span>Redirection vers Stripe...</span>
                                }
                                else
                                {
                                    <i class="fab fa-cc-stripe me-2"></i>
                                    <span>Payer @_order.TotalAmount.ToString("N0") GNF avec Stripe</span>
                                }
                            </button>
                            <button class="btn btn-outline-secondary" @onclick="CancelPayment" disabled="@_processing">
                                <i class="fas fa-times me-2"></i>Annuler
                            </button>
                        </div>

                        <div class="mt-3 text-center">
                            <small class="text-muted">
                                <i class="fas fa-shield-alt me-1"></i>
                                Paiement sécurisé SSL 256-bit
                            </small>
                        </div>

                        <!-- Mode développement -->
                        <div class="mt-4 p-3 bg-light rounded">
                            <h6 class="text-muted">Mode Développement</h6>
                            <p class="small mb-2">Utilisez ces numéros de test:</p>
                            <ul class="small mb-2">
                                <li><strong>4242 4242 4242 4242</strong> - Visa (succès)</li>
                                <li><strong>4000 0000 0000 0002</strong> - Carte déclinée</li>
                            </ul>
                            <button class="btn btn-sm btn-success" @onclick="SimulateStripePayment">
                                <i class="fas fa-play me-1"></i>Simuler paiement réussi
                            </button>
                        </div>
                    }
                    else
                    {
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Erreur lors du chargement de la commande.
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    [Parameter] public int OrderId { get; set; }
    
    private OrderDto? _order;
    private bool _loading = true;
    private bool _processing = false;

    protected override async Task OnInitializedAsync()
    {
        try
        {
            _order = await OrderService.GetOrderByIdAsync(OrderId);
            
            // Initialiser Stripe Elements
            await JSRuntime.InvokeVoidAsync("initializeStripe");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement de la commande: {ex.Message}");
        }
        finally
        {
            _loading = false;
        }
    }

    private async Task CreateStripeCheckoutSession()
    {
        try
        {
            _processing = true;

            // Calculer les composants du total
            var subTotal = _order.OrderItems.Sum(item => item.UnitPrice * item.Quantity);
            var shippingFee = subTotal > 500000 ? 0 : 25000;
            var tax = subTotal * 0.18m;

            // Créer la liste des articles pour Stripe (produits + frais + taxes)
            var stripeItems = _order.OrderItems.Select(item => new
            {
                Name = item.ProductName,
                UnitAmount = (long)item.UnitPrice, // GNF n'utilise pas de centimes
                Quantity = item.Quantity,
                ImageUrl = item.ImageUrl
            }).ToList();

            // Ajouter les frais de livraison si applicable
            if (shippingFee > 0)
            {
                stripeItems.Add(new
                {
                    Name = "Frais de livraison",
                    UnitAmount = (long)shippingFee,
                    Quantity = 1,
                    ImageUrl = ""
                });
            }

            // Ajouter les taxes
            if (tax > 0)
            {
                stripeItems.Add(new
                {
                    Name = "TVA (18%)",
                    UnitAmount = (long)tax,
                    Quantity = 1,
                    ImageUrl = ""
                });
            }

            // Appeler l'API Payment via JavaScript pour éviter les problèmes CORS
            var checkoutRequest = new
            {
                Items = stripeItems,
                Currency = "gnf",
                SuccessUrl = $"http://localhost:8080/payment/success?orderId={OrderId}",
                CancelUrl = $"http://localhost:8080/payment/cancel?orderId={OrderId}",
                OrderId = OrderId.ToString()
            };

            // Utiliser JavaScript pour faire l'appel API
            var sessionUrl = await JSRuntime.InvokeAsync<string>("createStripeCheckoutSession", checkoutRequest);

            if (!string.IsNullOrEmpty(sessionUrl))
            {
                // Rediriger vers Stripe Checkout
                NavigationManager.NavigateTo(sessionUrl, forceLoad: true);
            }
            else
            {
                Console.WriteLine("Erreur: URL de session Stripe non reçue");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la création de la session Stripe: {ex.Message}");
        }
        finally
        {
            _processing = false;
        }
    }

    private async Task SimulateStripePayment()
    {
        try
        {
            _processing = true;
            
            // Simuler un délai de traitement
            await Task.Delay(2000);
            
            // Rediriger vers la confirmation
            NavigationManager.NavigateTo($"/order-confirmation/{OrderId}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la simulation: {ex.Message}");
        }
        finally
        {
            _processing = false;
        }
    }

    private void CancelPayment()
    {
        NavigationManager.NavigateTo("/cart");
    }
}
