namespace NafaPlace.Identity.Domain.Models;

public class AuthenticationResult
{
    public bool Succeeded { get; set; }
    public string? AccessToken { get; set; }
    public string? RefreshToken { get; set; }
    public IEnumerable<string> Errors { get; set; } = Array.Empty<string>();
    public User? User { get; set; }
    
    public static AuthenticationResult Success(string accessToken, string refreshToken, User user) =>
        new() { Succeeded = true, AccessToken = accessToken, RefreshToken = refreshToken, User = user };
    
    public static AuthenticationResult Failure(IEnumerable<string> errors) =>
        new() { Succeeded = false, Errors = errors };
}
