// Fonctions pour gérer les modals Bootstrap
window.showModal = function (modalId) {
    var modalElement = document.getElementById(modalId);
    if (modalElement) {
        var modal = new bootstrap.Modal(modalElement);
        modal.show();
    }
};

window.hideModal = function (modalId) {
    var modalElement = document.getElementById(modalId);
    if (modalElement) {
        var modal = bootstrap.Modal.getInstance(modalElement);
        if (modal) {
            modal.hide();
        }
    }
};

// Fonction pour afficher des notifications toast
window.showToast = function (type, title, message) {
    // Vérifier si Toastr est disponible
    if (typeof toastr !== 'undefined') {
        toastr.options = {
            closeButton: true,
            progressBar: true,
            positionClass: "toast-top-right",
            timeOut: 5000
        };

        switch (type) {
            case 'success':
                toastr.success(message, title);
                break;
            case 'error':
                toastr.error(message, title);
                break;
            case 'warning':
                toastr.warning(message, title);
                break;
            case 'info':
                toastr.info(message, title);
                break;
            default:
                toastr.info(message, title);
                break;
        }
    } else {
        // Fallback si Toastr n'est pas disponible
        alert(title + ": " + message);
    }
};

// Fonction pour initialiser les dropdowns Bootstrap
window.initializeDropdowns = function () {
    // Sélectionner tous les éléments avec data-bs-toggle="dropdown"
    var dropdownElementList = document.querySelectorAll('[data-bs-toggle="dropdown"]');
    
    // Créer une instance Dropdown pour chaque élément
    dropdownElementList.forEach(function (dropdownToggleEl) {
        try {
            // Vérifier si l'élément a déjà une instance Dropdown
            var existingDropdown = bootstrap.Dropdown.getInstance(dropdownToggleEl);
            if (!existingDropdown) {
                // Créer une nouvelle instance Dropdown si elle n'existe pas déjà
                new bootstrap.Dropdown(dropdownToggleEl);
            }
        } catch (e) {
            // Erreur lors de l'initialisation du dropdown
        }
    });
    
    // Ajouter des gestionnaires d'événements pour les clics sur les éléments dropdown
    document.addEventListener('click', function (event) {
        var target = event.target;
        // Vérifier si l'élément cliqué ou un de ses parents a l'attribut data-bs-toggle="dropdown"
        while (target && target !== document) {
            if (target.getAttribute('data-bs-toggle') === 'dropdown') {
                try {
                    var dropdown = bootstrap.Dropdown.getInstance(target);
                    if (dropdown) {
                        dropdown.toggle();
                    } else {
                        dropdown = new bootstrap.Dropdown(target);
                        dropdown.toggle();
                    }
                    event.preventDefault();
                    return;
                } catch (e) {
                    // Erreur lors du toggle du dropdown
                }
            }
            target = target.parentNode;
        }
    });
};

// Initialiser les dropdowns au chargement de la page
document.addEventListener('DOMContentLoaded', function () {
    window.initializeDropdowns();
});

// Fonction pour réinitialiser les dropdowns (à appeler après des mises à jour dynamiques)
window.reinitializeDropdowns = function () {
    window.initializeDropdowns();
};

// Dashboard Charts Initialization
window.initializeDashboardCharts = function() {
    // Vérifier si Chart.js est disponible
    if (typeof Chart === 'undefined') {
        console.error('Chart.js n\'est pas chargé');
        return;
    }

    // Configuration par défaut pour tous les graphiques
    Chart.defaults.font.family = "'Inter', sans-serif";
    Chart.defaults.color = '#6c757d';
    Chart.defaults.plugins.legend.display = true;

    // Graphique des ventes (ligne)
    const salesCtx = document.getElementById('salesChart');
    if (salesCtx) {
        new Chart(salesCtx, {
            type: 'line',
            data: {
                labels: ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'],
                datasets: [{
                    label: 'Ventes (GNF)',
                    data: [120000, 190000, 300000, 500000, 200000, 300000, 450000],
                    borderColor: '#E73C30',
                    backgroundColor: 'rgba(231, 60, 48, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#E73C30',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: '#E73C30',
                        borderWidth: 1,
                        callbacks: {
                            label: function(context) {
                                return 'Ventes: ' + context.parsed.y.toLocaleString() + ' GNF';
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        },
                        ticks: {
                            callback: function(value) {
                                return (value / 1000) + 'K';
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            }
        });
    }

    // Graphique des catégories (donut)
    const categoriesCtx = document.getElementById('categoriesChart');
    if (categoriesCtx) {
        new Chart(categoriesCtx, {
            type: 'doughnut',
            data: {
                labels: ['Électronique', 'Vêtements', 'Maison & Jardin', 'Sport & Loisirs', 'Livres'],
                datasets: [{
                    data: [35, 25, 20, 15, 5],
                    backgroundColor: [
                        '#E73C30',
                        '#F96302',
                        '#28a745',
                        '#17a2b8',
                        '#6f42c1'
                    ],
                    borderWidth: 0,
                    hoverBorderWidth: 3,
                    hoverBorderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '60%',
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            font: {
                                size: 12
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: '#E73C30',
                        borderWidth: 1,
                        callbacks: {
                            label: function(context) {
                                return context.label + ': ' + context.parsed + '%';
                            }
                        }
                    }
                }
            }
        });
    }

    console.log('Graphiques du tableau de bord initialisés avec succès');
};
