using NafaPlace.Web.Models.Wishlist;

namespace NafaPlace.Web.Services;

public interface IWishlistService
{
    // Wishlist operations
    Task<WishlistDto> GetUserWishlistAsync(string userId);
    Task<WishlistSummaryDto> GetWishlistSummaryAsync(string userId);
    Task<int> GetWishlistItemCountAsync(string userId);
    Task<List<WishlistItemDto>> GetWishlistItemsAsync(string userId, int page = 1, int pageSize = 20);

    // Wishlist items operations
    Task<WishlistItemDto> AddToWishlistAsync(string userId, AddToWishlistRequest request);
    Task<bool> RemoveFromWishlistAsync(string userId, int productId);
    Task<bool> IsProductInWishlistAsync(string userId, int productId);
    Task<WishlistItemDto?> GetWishlistItemAsync(string userId, int productId);
    Task<bool> ClearWishlistAsync(string userId);

    // Utility operations
    Task<bool> MoveToCartAsync(string userId, int productId);
    Task<List<WishlistItemDto>> GetRecentlyAddedItemsAsync(string userId, int count = 5);

    // Guest operations
    Task<WishlistDto> GetGuestWishlistAsync(string guestId);
    Task<WishlistItemDto> AddToGuestWishlistAsync(string guestId, AddToWishlistRequest request);
}
