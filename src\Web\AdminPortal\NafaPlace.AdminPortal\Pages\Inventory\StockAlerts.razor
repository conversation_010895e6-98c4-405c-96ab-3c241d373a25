@page "/inventory/alerts"
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.JSInterop
@using NafaPlace.AdminPortal.Services
@using NafaPlace.AdminPortal.Models.Inventory
@attribute [Authorize]
@inject InventoryService InventoryService
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime
@inject NotificationService NotificationService

<h1 class="visually-hidden">Alert<PERSON> de Stock - NafaPlace Admin</h1>

<div class="container-fluid px-4">
    <h1 class="mt-4">Alertes de Stock</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="/dashboard">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="/inventory">Inventaire</a></li>
        <li class="breadcrumb-item active">Alertes</li>
    </ol>

    <!-- Filtres -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="bi bi-funnel me-1"></i>
            Filtres
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">Type d'alerte</label>
                    <select class="form-select" @bind="_selectedAlertType" @bind:after="OnFilterChanged">
                        <option value="">Tous les types</option>
                        <option value="@((int)AlertType.LowStock)">Stock Faible</option>
                        <option value="@((int)AlertType.OutOfStock)">Rupture de Stock</option>
                        <option value="@((int)AlertType.OverStock)">Surstock</option>
                        <option value="@((int)AlertType.StockMovement)">Mouvement Important</option>
                        <option value="@((int)AlertType.ReservationExpiry)">Réservation Expirée</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Sévérité</label>
                    <select class="form-select" @bind="_selectedSeverity" @bind:after="OnFilterChanged">
                        <option value="">Toutes les sévérités</option>
                        <option value="@((int)AlertSeverity.Info)">Information</option>
                        <option value="@((int)AlertSeverity.Warning)">Avertissement</option>
                        <option value="@((int)AlertSeverity.Critical)">Critique</option>
                        <option value="@((int)AlertSeverity.Emergency)">Urgence</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Statut</label>
                    <select class="form-select" @bind="_selectedStatus" @bind:after="OnFilterChanged">
                        <option value="">Tous les statuts</option>
                        <option value="pending">En attente</option>
                        <option value="acknowledged">Acquittées</option>
                        <option value="active">Actives</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Actions</label>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-secondary" @onclick="ClearFilters">
                            <i class="bi bi-x-circle me-1"></i>
                            Effacer
                        </button>
                        <button class="btn btn-primary" @onclick="LoadAlerts">
                            <i class="bi bi-arrow-clockwise me-1"></i>
                            Actualiser
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Liste des alertes -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <i class="bi bi-bell me-1"></i>
                Alertes de Stock (@_filteredAlerts.Count)
            </div>
            <div>
                @if (_filteredAlerts.Any(a => !a.IsAcknowledged))
                {
                    <button class="btn btn-sm btn-success me-2" @onclick="AcknowledgeAllPending">
                        <i class="bi bi-check-all me-1"></i>
                        Acquitter Toutes
                    </button>
                }
                <button class="btn btn-sm btn-outline-primary" @onclick="LoadAlerts">
                    <i class="bi bi-arrow-clockwise me-1"></i>
                    Actualiser
                </button>
            </div>
        </div>
        <div class="card-body">
            @if (_isLoading)
            {
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                </div>
            }
            else if (!_filteredAlerts.Any())
            {
                <div class="text-center text-muted py-4">
                    <i class="bi bi-check-circle fs-1"></i>
                    <p class="mt-2">Aucune alerte trouvée</p>
                </div>
            }
            else
            {
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" class="form-check-input" @onchange="ToggleSelectAll" />
                                </th>
                                <th>Sévérité</th>
                                <th>Type</th>
                                <th>Produit</th>
                                <th>Vendeur</th>
                                <th>Stock Actuel</th>
                                <th>Seuil</th>
                                <th>Message</th>
                                <th>Date</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var alert in _filteredAlerts)
                            {
                                <tr class="@GetAlertRowClass(alert.Severity)">
                                    <td>
                                        <input type="checkbox" class="form-check-input" 
                                               @onchange="(e) => ToggleAlertSelection(alert.Id, (bool)e.Value!)" />
                                    </td>
                                    <td>
                                        <span class="badge @GetSeverityBadgeClass(alert.Severity)">
                                            @GetSeverityText(alert.Severity)
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge @GetAlertTypeBadgeClass(alert.Type)">
                                            @GetAlertTypeText(alert.Type)
                                        </span>
                                    </td>
                                    <td>
                                        <strong>@alert.ProductName</strong>
                                        <br />
                                        <small class="text-muted">ID: @alert.ProductId</small>
                                    </td>
                                    <td>@alert.SellerName</td>
                                    <td>
                                        <span class="@GetStockClass(alert.CurrentStock, alert.ThresholdValue)">
                                            @alert.CurrentStock
                                        </span>
                                    </td>
                                    <td>@alert.ThresholdValue</td>
                                    <td>
                                        <small>@alert.Message</small>
                                    </td>
                                    <td>
                                        @alert.CreatedAt.ToString("dd/MM/yyyy")
                                        <br />
                                        <small class="text-muted">@alert.CreatedAt.ToString("HH:mm")</small>
                                    </td>
                                    <td>
                                        @if (alert.IsAcknowledged)
                                        {
                                            <span class="badge bg-success">
                                                <i class="bi bi-check-circle me-1"></i>
                                                Acquittée
                                            </span>
                                            @if (!string.IsNullOrEmpty(alert.AcknowledgedBy))
                                            {
                                                <br />
                                                <small class="text-muted">par @alert.AcknowledgedBy</small>
                                            }
                                        }
                                        else
                                        {
                                            <span class="badge bg-warning">
                                                <i class="bi bi-clock me-1"></i>
                                                En attente
                                            </span>
                                        }
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            @if (!alert.IsAcknowledged)
                                            {
                                                <button class="btn btn-outline-success" 
                                                        @onclick="() => AcknowledgeAlert(alert.Id)"
                                                        title="Acquitter">
                                                    <i class="bi bi-check"></i>
                                                </button>
                                            }
                                            <button class="btn btn-outline-primary" 
                                                    @onclick="() => NavigateToProduct(alert.ProductId)"
                                                    title="Voir le produit">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-info" 
                                                    @onclick="() => ViewAlertDetails(alert)"
                                                    title="Détails">
                                                <i class="bi bi-info-circle"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>

                <!-- Actions en lot -->
                @if (_selectedAlerts.Any())
                {
                    <div class="mt-3 p-3 bg-light rounded">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>@_selectedAlerts.Count alerte(s) sélectionnée(s)</span>
                            <div>
                                <button class="btn btn-sm btn-success me-2" @onclick="AcknowledgeSelectedAlerts">
                                    <i class="bi bi-check-all me-1"></i>
                                    Acquitter la sélection
                                </button>
                                <button class="btn btn-sm btn-outline-secondary" @onclick="ClearSelection">
                                    <i class="bi bi-x me-1"></i>
                                    Annuler la sélection
                                </button>
                            </div>
                        </div>
                    </div>
                }
            }
        </div>
    </div>
</div>

@code {
    private List<StockAlertDto> _alerts = new();
    private List<StockAlertDto> _filteredAlerts = new();
    private HashSet<int> _selectedAlerts = new();
    private bool _isLoading = true;

    // Filtres
    private string _selectedAlertType = "";
    private string _selectedSeverity = "";
    private string _selectedStatus = "";

    protected override async Task OnInitializedAsync()
    {
        await LoadAlerts();
    }

    private async Task LoadAlerts()
    {
        _isLoading = true;
        try
        {
            _alerts = await InventoryService.GetActiveAlertsAsync();
            ApplyFilters();
        }
        catch (Exception ex)
        {
            NotificationService.Error($"Erreur lors du chargement des alertes: {ex.Message}");
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    private void ApplyFilters()
    {
        _filteredAlerts = _alerts.Where(alert =>
        {
            if (!string.IsNullOrEmpty(_selectedAlertType) && 
                (int)alert.Type != int.Parse(_selectedAlertType))
                return false;

            if (!string.IsNullOrEmpty(_selectedSeverity) && 
                (int)alert.Severity != int.Parse(_selectedSeverity))
                return false;

            if (!string.IsNullOrEmpty(_selectedStatus))
            {
                return _selectedStatus switch
                {
                    "pending" => !alert.IsAcknowledged,
                    "acknowledged" => alert.IsAcknowledged,
                    "active" => alert.IsActive,
                    _ => true
                };
            }

            return true;
        }).OrderByDescending(a => a.Severity)
          .ThenByDescending(a => a.CreatedAt)
          .ToList();

        StateHasChanged();
    }

    private Task OnFilterChanged()
    {
        ApplyFilters();
        return Task.CompletedTask;
    }

    private void ClearFilters()
    {
        _selectedAlertType = "";
        _selectedSeverity = "";
        _selectedStatus = "";
        ApplyFilters();
    }

    private async Task AcknowledgeAlert(int alertId)
    {
        try
        {
            var success = await InventoryService.AcknowledgeAlertAsync(alertId);
            if (success)
            {
                await LoadAlerts();
                NotificationService.Success("Alerte acquittée avec succès");
            }
            else
            {
                NotificationService.Error("Erreur lors de l'acquittement de l'alerte");
            }
        }
        catch (Exception ex)
        {
            NotificationService.Error($"Erreur: {ex.Message}");
        }
    }

    private async Task AcknowledgeAllPending()
    {
        var pendingAlerts = _filteredAlerts.Where(a => !a.IsAcknowledged).ToList();
        if (!pendingAlerts.Any()) return;

        try
        {
            var tasks = pendingAlerts.Select(alert => InventoryService.AcknowledgeAlertAsync(alert.Id));
            await Task.WhenAll(tasks);
            
            await LoadAlerts();
            NotificationService.Success($"{pendingAlerts.Count} alerte(s) acquittée(s) avec succès");
        }
        catch (Exception ex)
        {
            NotificationService.Error($"Erreur lors de l'acquittement en lot: {ex.Message}");
        }
    }

    private async Task AcknowledgeSelectedAlerts()
    {
        if (!_selectedAlerts.Any()) return;

        try
        {
            var tasks = _selectedAlerts.Select(alertId => InventoryService.AcknowledgeAlertAsync(alertId));
            await Task.WhenAll(tasks);
            
            await LoadAlerts();
            ClearSelection();
            NotificationService.Success($"{_selectedAlerts.Count} alerte(s) acquittée(s) avec succès");
        }
        catch (Exception ex)
        {
            NotificationService.Error($"Erreur lors de l'acquittement de la sélection: {ex.Message}");
        }
    }

    private void ToggleSelectAll(ChangeEventArgs e)
    {
        var isChecked = (bool)e.Value!;
        if (isChecked)
        {
            _selectedAlerts = _filteredAlerts.Select(a => a.Id).ToHashSet();
        }
        else
        {
            _selectedAlerts.Clear();
        }
        StateHasChanged();
    }

    private void ToggleAlertSelection(int alertId, bool isSelected)
    {
        if (isSelected)
        {
            _selectedAlerts.Add(alertId);
        }
        else
        {
            _selectedAlerts.Remove(alertId);
        }
        StateHasChanged();
    }

    private void ClearSelection()
    {
        _selectedAlerts.Clear();
        StateHasChanged();
    }

    private void NavigateToProduct(int productId)
    {
        NavigationManager.NavigateTo($"/products/edit/{productId}");
    }

    private void ViewAlertDetails(StockAlertDto alert)
    {
        // TODO: Implémenter la modal de détails
        NotificationService.Info($"Détails de l'alerte: {alert.Message}");
    }

    // Méthodes d'aide pour les styles
    private string GetAlertRowClass(AlertSeverity severity)
    {
        return severity switch
        {
            AlertSeverity.Emergency => "table-danger",
            AlertSeverity.Critical => "table-danger",
            AlertSeverity.Warning => "table-warning",
            _ => ""
        };
    }

    private string GetSeverityBadgeClass(AlertSeverity severity)
    {
        return severity switch
        {
            AlertSeverity.Emergency => "bg-danger",
            AlertSeverity.Critical => "bg-danger",
            AlertSeverity.Warning => "bg-warning",
            AlertSeverity.Info => "bg-info",
            _ => "bg-secondary"
        };
    }

    private string GetAlertTypeBadgeClass(AlertType type)
    {
        return type switch
        {
            AlertType.OutOfStock => "bg-danger",
            AlertType.LowStock => "bg-warning",
            AlertType.OverStock => "bg-info",
            AlertType.StockMovement => "bg-primary",
            AlertType.ReservationExpiry => "bg-secondary",
            _ => "bg-secondary"
        };
    }

    private string GetStockClass(int currentStock, int? threshold)
    {
        if (currentStock == 0) return "text-danger fw-bold";
        if (threshold.HasValue && currentStock <= threshold.Value) return "text-warning fw-bold";
        return "";
    }

    private string GetSeverityText(AlertSeverity severity)
    {
        return severity switch
        {
            AlertSeverity.Emergency => "Urgence",
            AlertSeverity.Critical => "Critique",
            AlertSeverity.Warning => "Avertissement",
            AlertSeverity.Info => "Info",
            _ => severity.ToString()
        };
    }

    private string GetAlertTypeText(AlertType type)
    {
        return type switch
        {
            AlertType.OutOfStock => "Rupture",
            AlertType.LowStock => "Stock Faible",
            AlertType.OverStock => "Surstock",
            AlertType.StockMovement => "Mouvement",
            AlertType.ReservationExpiry => "Réservation",
            _ => type.ToString()
        };
    }
}
