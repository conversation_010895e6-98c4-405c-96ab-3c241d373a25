using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;
using NafaPlace.Catalog.Infrastructure.Persistence;
using System;
using System.IO;

namespace NafaPlace.Catalog.Infrastructure
{
    public class DesignTimeDbContextFactory : IDesignTimeDbContextFactory<CatalogDbContext>
    {
        public CatalogDbContext CreateDbContext(string[] args)
        {
            var connectionString = "Host=localhost;Port=5432;Database=NafaPlace.Catalog;Username=postgres;Password=*****************";
            var optionsBuilder = new DbContextOptionsBuilder<CatalogDbContext>();
            optionsBuilder.UseNpgsql(connectionString);

            return new CatalogDbContext(optionsBuilder.Options);
        }
    }
}
