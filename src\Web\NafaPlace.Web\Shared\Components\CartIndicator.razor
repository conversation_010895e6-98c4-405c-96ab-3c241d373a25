@using NafaPlace.Web.Services
@using System.Security.Claims
@using Microsoft.AspNetCore.Components.Authorization
@inject ICartService CartService
@inject CartNotificationService NotificationService
@inject IAuthService AuthService
@inject IJSRuntime JSRuntime
@implements IDisposable

<a href="/cart" class="action-item">
    <i class="bi bi-cart3 action-icon"></i>
    <span class="action-label">Panier</span>
    <span class="action-badge">@_itemCount</span>
</a>

@code {
    private int _itemCount = 0;
    private string? _userId;
    private Timer? _refreshTimer;

    [CascadingParameter]
    private Task<AuthenticationState>? AuthenticationStateTask { get; set; }

    protected override async Task OnInitializedAsync()
    {
        try
        {
            await UpdateUserIdFromAuthState();
            await UpdateCartCount();

            // S'abonner aux notifications de mise à jour du panier
            NotificationService.CartUpdated += OnCartUpdated;

            // S'abonner aux changements d'état d'authentification
            AuthService.AuthenticationStateChanged += OnAuthenticationStateChanged;

            // Mettre à jour le compteur toutes les 60 secondes (backup)
            _refreshTimer = new Timer(async _ => await UpdateCartCount(), null, TimeSpan.Zero, TimeSpan.FromSeconds(60));
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de l'initialisation du CartIndicator: {ex.Message}");
        }
    }

    private async Task UpdateUserIdFromAuthState()
    {
        try
        {
            if (AuthenticationStateTask != null)
            {
                var authState = await AuthenticationStateTask;
                var user = authState.User;
                if (user.Identity?.IsAuthenticated == true)
                {
                    _userId = user.FindFirst(c => c.Type == ClaimTypes.NameIdentifier)?.Value;
                    Console.WriteLine($"🔍 DEBUG CartIndicator: UserId récupéré = '{_userId}'");

                    // Debug de toutes les claims
                    Console.WriteLine("🔍 DEBUG CartIndicator: Toutes les claims:");
                    foreach (var claim in user.Claims)
                    {
                        Console.WriteLine($"  - {claim.Type}: {claim.Value}");
                    }
                }
                else
                {
                    Console.WriteLine("❌ DEBUG CartIndicator: Utilisateur non authentifié");
                    _userId = null; // Reset userId pour forcer la récupération de l'ID invité
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la mise à jour de l'ID utilisateur: {ex.Message}");
        }
    }

    private async Task UpdateCartCount()
    {
        Console.WriteLine($"🔍 DEBUG CartIndicator: UpdateCartCount appelé - UserId: {_userId}");

        // Toujours mettre à jour l'ID utilisateur d'abord
        await UpdateUserIdFromAuthState();

        if (string.IsNullOrEmpty(_userId))
        {
            // Essayer de récupérer l'ID invité
            _userId = await GetOrCreateGuestUserId();
            Console.WriteLine($"🔍 DEBUG CartIndicator: ID invité récupéré: {_userId}");
        }

        try
        {
            Console.WriteLine($"📡 DEBUG CartIndicator: Appel GetCartSummaryAsync avec UserId: {_userId}");
            var summary = await CartService.GetCartSummaryAsync(_userId);
            var newCount = summary?.ItemCount ?? 0;

            Console.WriteLine($"📊 DEBUG CartIndicator: Ancien count: {_itemCount}, Nouveau count: {newCount}");

            if (newCount != _itemCount)
            {
                _itemCount = newCount;
                Console.WriteLine($"✅ DEBUG CartIndicator: Mise à jour du compteur vers {_itemCount}");
                await InvokeAsync(StateHasChanged);
            }
            else
            {
                Console.WriteLine($"ℹ️ DEBUG CartIndicator: Pas de changement, compteur reste à {_itemCount}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ DEBUG CartIndicator: Erreur complète: {ex}");
            Console.WriteLine($"Erreur lors de la mise à jour du compteur de panier: {ex.Message}");
        }
    }

    private async void OnCartUpdated()
    {
        Console.WriteLine("🔔 DEBUG CartIndicator: OnCartUpdated déclenché !");
        await UpdateCartCount();
    }

    private async void OnAuthenticationStateChanged()
    {
        Console.WriteLine("🔔 DEBUG CartIndicator: OnAuthenticationStateChanged déclenché !");

        // Forcer la mise à jour de l'état d'authentification
        if (AuthenticationStateTask != null)
        {
            // Recréer la tâche d'authentification pour forcer la mise à jour
            AuthenticationStateTask = AuthenticationStateTask.ContinueWith(async _ =>
            {
                // Attendre un peu pour que l'état se propage
                await Task.Delay(50);
                return await AuthenticationStateTask;
            }).Unwrap();
        }

        await UpdateUserIdFromAuthState();
        await UpdateCartCount();

        // Forcer le re-rendu du composant
        await InvokeAsync(StateHasChanged);
    }

    public async Task RefreshCartCount()
    {
        await UpdateCartCount();
    }

    private async Task<string> GetOrCreateGuestUserId()
    {
        // Utiliser le localStorage pour stocker l'ID de session invité
        var guestId = await JSRuntime.InvokeAsync<string>("localStorage.getItem", "guestUserId");

        if (string.IsNullOrEmpty(guestId))
        {
            guestId = $"guest_{Random.Shared.Next(1, int.MaxValue)}";
            await JSRuntime.InvokeVoidAsync("localStorage.setItem", "guestUserId", guestId);
        }

        return guestId;
    }

    public void Dispose()
    {
        NotificationService.CartUpdated -= OnCartUpdated;
        AuthService.AuthenticationStateChanged -= OnAuthenticationStateChanged;
        _refreshTimer?.Dispose();
    }
}
