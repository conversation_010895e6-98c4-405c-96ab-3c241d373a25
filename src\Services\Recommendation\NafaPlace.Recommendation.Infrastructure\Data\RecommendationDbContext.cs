using Microsoft.EntityFrameworkCore;
using NafaPlace.Recommendation.Domain.Entities;

namespace NafaPlace.Recommendation.Infrastructure.Data;

public class RecommendationDbContext : DbContext
{
    public RecommendationDbContext(DbContextOptions<RecommendationDbContext> options) : base(options)
    {
    }

    public DbSet<UserInteraction> UserInteractions { get; set; }
    public DbSet<ProductSimilarity> ProductSimilarities { get; set; }
    public DbSet<UserPreference> UserPreferences { get; set; }
    public DbSet<RecommendationModel> RecommendationModels { get; set; }
    public DbSet<RecommendationResult> RecommendationResults { get; set; }
    public DbSet<ABTestGroup> ABTestGroups { get; set; }
    public DbSet<UserABTestAssignment> UserABTestAssignments { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configuration UserInteractions
        modelBuilder.Entity<UserInteraction>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.UserId).HasMaxLength(100).IsRequired();
            entity.Property(e => e.ProductId).IsRequired();
            entity.Property(e => e.SessionId).HasMaxLength(100);
            entity.Property(e => e.Weight).HasDefaultValue(1.0);
            entity.Property(e => e.Context).HasColumnType("jsonb").HasDefaultValue("{}");
            entity.Property(e => e.Timestamp).HasDefaultValueSql("CURRENT_TIMESTAMP");

            entity.HasIndex(e => new { e.UserId, e.Timestamp });
            entity.HasIndex(e => new { e.ProductId, e.Type });
            entity.HasIndex(e => e.SessionId);
        });

        // Configuration ProductSimilarities
        modelBuilder.Entity<ProductSimilarity>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.ProductId1).IsRequired();
            entity.Property(e => e.ProductId2).IsRequired();
            entity.Property(e => e.SimilarityScore).HasColumnType("decimal(5,4)");
            entity.Property(e => e.Algorithm).HasMaxLength(50).IsRequired();
            entity.Property(e => e.Metadata).HasColumnType("jsonb").HasDefaultValue("{}");
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

            entity.HasIndex(e => new { e.ProductId1, e.Algorithm });
            entity.HasIndex(e => new { e.ProductId2, e.Algorithm });
            entity.HasIndex(e => e.SimilarityScore);
        });

        // Configuration UserPreferences
        modelBuilder.Entity<UserPreference>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.UserId).HasMaxLength(100).IsRequired();
            entity.Property(e => e.PreferenceType).HasMaxLength(50).IsRequired();
            entity.Property(e => e.PreferenceValue).HasMaxLength(200).IsRequired();
            entity.Property(e => e.Score).HasColumnType("decimal(5,4)");
            entity.Property(e => e.Confidence).HasColumnType("decimal(5,4)");
            entity.Property(e => e.Source).HasMaxLength(50);
            entity.Property(e => e.Metadata).HasColumnType("jsonb").HasDefaultValue("{}");
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

            entity.HasIndex(e => new { e.UserId, e.PreferenceType });
            entity.HasIndex(e => e.Score);
        });

        // Configuration RecommendationModels
        modelBuilder.Entity<RecommendationModel>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).HasMaxLength(100).IsRequired();
            entity.Property(e => e.Algorithm).HasMaxLength(50).IsRequired();
            entity.Property(e => e.Version).HasMaxLength(20).IsRequired();
            entity.Property(e => e.Parameters).HasColumnType("jsonb").HasDefaultValue("{}");
            entity.Property(e => e.ModelData).HasColumnType("jsonb").HasDefaultValue("{}");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.Accuracy).HasColumnType("decimal(5,4)");
            entity.Property(e => e.Precision).HasColumnType("decimal(5,4)");
            entity.Property(e => e.Recall).HasColumnType("decimal(5,4)");
            entity.Property(e => e.F1Score).HasColumnType("decimal(5,4)");
            entity.Property(e => e.TrainingData).HasColumnType("jsonb").HasDefaultValue("{}");
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

            entity.HasIndex(e => new { e.Algorithm, e.IsActive });
            entity.HasIndex(e => e.Accuracy);
        });

        // Configuration RecommendationResults
        modelBuilder.Entity<RecommendationResult>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.UserId).HasMaxLength(100).IsRequired();
            entity.Property(e => e.RecommendedProducts).HasColumnType("jsonb").HasDefaultValue("[]");
            entity.Property(e => e.Algorithm).HasMaxLength(50).IsRequired();
            entity.Property(e => e.Context).HasColumnType("jsonb").HasDefaultValue("{}");
            entity.Property(e => e.Scores).HasColumnType("jsonb").HasDefaultValue("{}");
            entity.Property(e => e.Metadata).HasColumnType("jsonb").HasDefaultValue("{}");
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

            entity.HasIndex(e => new { e.UserId, e.CreatedAt });
            entity.HasIndex(e => e.Algorithm);
            entity.HasIndex(e => e.ModelId);

            entity.HasOne<RecommendationModel>()
                .WithMany()
                .HasForeignKey(e => e.ModelId)
                .OnDelete(DeleteBehavior.SetNull);
        });

        // Configuration ABTestGroups
        modelBuilder.Entity<ABTestGroup>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).HasMaxLength(100).IsRequired();
            entity.Property(e => e.Algorithm).HasMaxLength(50).IsRequired();
            entity.Property(e => e.Parameters).HasColumnType("jsonb").HasDefaultValue("{}");
            entity.Property(e => e.TrafficPercentage).HasColumnType("decimal(5,2)");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.Metrics).HasColumnType("jsonb").HasDefaultValue("{}");
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

            entity.HasIndex(e => new { e.IsActive, e.StartDate, e.EndDate });
        });

        // Configuration UserABTestAssignments
        modelBuilder.Entity<UserABTestAssignment>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.UserId).HasMaxLength(100).IsRequired();
            entity.Property(e => e.AssignedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

            entity.HasIndex(e => e.UserId);
            entity.HasIndex(e => e.ABTestGroupId);
            entity.HasIndex(e => new { e.UserId, e.ABTestGroupId }).IsUnique();

            entity.HasOne<ABTestGroup>()
                .WithMany()
                .HasForeignKey(e => e.ABTestGroupId)
                .OnDelete(DeleteBehavior.Cascade);
        });
    }
}
