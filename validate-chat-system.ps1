# Script de validation complète du système de chat NafaPlace
Write-Host "🔍 Validation Complète du Système de Chat NafaPlace" -ForegroundColor Green

# Configuration
$chatApiUrl = "http://localhost:5007"
$webAppUrl = "http://localhost:8080"
$adminUrl = "$webAppUrl/admin/chat-management"
$historyUrl = "$webAppUrl/account/chat-history"

# Compteurs de validation
$totalChecks = 0
$passedChecks = 0

function Test-Feature {
    param(
        [string]$Name,
        [scriptblock]$TestScript
    )
    
    $global:totalChecks++
    Write-Host "`n🔍 Test: $Name" -ForegroundColor Yellow
    
    try {
        $result = & $TestScript
        if ($result) {
            Write-Host "✅ $Name - PASSÉ" -ForegroundColor Green
            $global:passedChecks++
            return $true
        } else {
            Write-Host "❌ $Name - ÉCHEC" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ $Name - ERREUR: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

Write-Host "`n📋 VALIDATION DES FONCTIONNALITÉS" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan

# 1. Service de Chat Réel
Test-Feature "API Chat - Santé" {
    $response = Invoke-WebRequest -Uri "$chatApiUrl/api/config/health" -Method GET -TimeoutSec 5
    return $response.StatusCode -eq 200
}

Test-Feature "API Chat - Configuration" {
    $response = Invoke-WebRequest -Uri "$chatApiUrl/api/config" -Method GET -TimeoutSec 5
    $config = $response.Content | ConvertFrom-Json
    return $config -and $config.PSObject.Properties.Count -gt 0
}

Test-Feature "API Chat - FAQs" {
    $response = Invoke-WebRequest -Uri "$chatApiUrl/api/faq" -Method GET -TimeoutSec 5
    $faqs = $response.Content | ConvertFrom-Json
    return $faqs -and $faqs.Count -gt 0
}

Test-Feature "API Chat - Catégories FAQ" {
    $response = Invoke-WebRequest -Uri "$chatApiUrl/api/faq/categories" -Method GET -TimeoutSec 5
    $categories = $response.Content | ConvertFrom-Json
    return $categories -and $categories.Count -gt 0
}

# 2. SignalR Temps Réel
Test-Feature "SignalR - Négociation" {
    try {
        $response = Invoke-WebRequest -Uri "$chatApiUrl/chathub/negotiate" -Method POST -TimeoutSec 5
        return $response.StatusCode -eq 200
    }
    catch {
        # SignalR peut retourner 404 si mal configuré, mais 200 si OK
        return $false
    }
}

# 3. Authentification
Test-Feature "Chatbot - Réponse Anonyme" {
    $body = @{
        Message = "Test de validation"
        SessionId = [System.Guid]::NewGuid().ToString()
    } | ConvertTo-Json
    
    $response = Invoke-WebRequest -Uri "$chatApiUrl/api/chatbot" -Method POST -Body $body -ContentType "application/json" -TimeoutSec 5
    $result = $response.Content | ConvertFrom-Json
    return $result -and $result.Message -and $result.Message.Length -gt 0
}

# 4. Gestion des Agents
Test-Feature "Agents - Liste Disponible" {
    $response = Invoke-WebRequest -Uri "$chatApiUrl/api/agents/available" -Method GET -TimeoutSec 5
    $agents = $response.Content | ConvertFrom-Json
    return $agents -and $agents.Count -gt 0
}

# 5. Application Web
Test-Feature "Application Web - Page Principale" {
    $response = Invoke-WebRequest -Uri $webAppUrl -Method GET -TimeoutSec 10
    return $response.StatusCode -eq 200 -and $response.Content.Contains("NafaPlace")
}

Test-Feature "Application Web - Widget Chat Présent" {
    $response = Invoke-WebRequest -Uri $webAppUrl -Method GET -TimeoutSec 10
    return $response.Content.Contains("floating-chat-widget") -or $response.Content.Contains("FloatingChatWidget")
}

# 6. Scripts JavaScript
Test-Feature "Scripts - SignalR Disponible" {
    $response = Invoke-WebRequest -Uri "$webAppUrl/js/chat-signalr.js" -Method GET -TimeoutSec 5
    return $response.StatusCode -eq 200 -and $response.Content.Contains("chatSignalR")
}

Test-Feature "Scripts - Chat Scroll" {
    $response = Invoke-WebRequest -Uri "$webAppUrl/js/chat-scroll.js" -Method GET -TimeoutSec 5
    return $response.StatusCode -eq 200
}

# 7. Styles CSS
Test-Feature "Styles - Chat Global CSS" {
    $response = Invoke-WebRequest -Uri "$webAppUrl/css/floating-chat-global.css" -Method GET -TimeoutSec 5
    return $response.StatusCode -eq 200 -and $response.Content.Contains("floating-chat")
}

# 8. Base de Données
Test-Feature "Base de Données - Connexion via API" {
    # Test indirect via l'API qui utilise la DB
    $response = Invoke-WebRequest -Uri "$chatApiUrl/api/faq" -Method GET -TimeoutSec 5
    return $response.StatusCode -eq 200
}

# 9. Services Docker
Test-Feature "Docker - Services Actifs" {
    $services = docker ps --format "table {{.Names}}\t{{.Status}}" | Select-String "nafaplace"
    return $services -and $services.Count -gt 0
}

Test-Feature "Docker - Service Chat API" {
    $chatService = docker ps --filter "name=chat-api" --format "{{.Status}}"
    return $chatService -and $chatService.Contains("Up")
}

# 10. Fonctionnalités Avancées
Test-Feature "Recherche FAQ" {
    $response = Invoke-WebRequest -Uri "$chatApiUrl/api/faq/search?query=commande" -Method GET -TimeoutSec 5
    return $response.StatusCode -eq 200
}

Test-Feature "FAQs Populaires" {
    $response = Invoke-WebRequest -Uri "$chatApiUrl/api/faq/popular" -Method GET -TimeoutSec 5
    return $response.StatusCode -eq 200
}

# Résultats finaux
Write-Host "`n📊 RÉSULTATS DE LA VALIDATION" -ForegroundColor Magenta
Write-Host "==============================" -ForegroundColor Magenta

$successRate = [math]::Round(($passedChecks / $totalChecks) * 100, 1)

Write-Host "Tests réussis: $passedChecks/$totalChecks ($successRate%)" -ForegroundColor $(if($successRate -ge 90){'Green'}elseif($successRate -ge 70){'Yellow'}else{'Red'})

if ($successRate -ge 90) {
    Write-Host "`n🎉 EXCELLENT! Le système de chat est entièrement opérationnel." -ForegroundColor Green
    Write-Host "✨ Toutes les fonctionnalités principales sont validées." -ForegroundColor Green
} elseif ($successRate -ge 70) {
    Write-Host "`n⚠️  BON! Le système fonctionne avec quelques problèmes mineurs." -ForegroundColor Yellow
    Write-Host "🔧 Vérifiez les tests échoués pour optimiser le système." -ForegroundColor Yellow
} else {
    Write-Host "`n❌ ATTENTION! Le système nécessite des corrections importantes." -ForegroundColor Red
    Write-Host "🛠️  Consultez les logs et corrigez les erreurs avant la production." -ForegroundColor Red
}

# Informations d'accès
Write-Host "`n🔗 LIENS D'ACCÈS" -ForegroundColor Cyan
Write-Host "===============" -ForegroundColor Cyan
Write-Host "Application Web: $webAppUrl" -ForegroundColor White
Write-Host "API Chat: $chatApiUrl" -ForegroundColor White
Write-Host "Admin Chat: $adminUrl" -ForegroundColor White
Write-Host "Historique: $historyUrl" -ForegroundColor White

# Commandes utiles
Write-Host "`n🛠️  COMMANDES UTILES" -ForegroundColor Cyan
Write-Host "==================" -ForegroundColor Cyan
Write-Host "Logs Chat API: docker-compose logs -f chat-api" -ForegroundColor White
Write-Host "Redémarrer: docker-compose restart chat-api" -ForegroundColor White
Write-Host "Status: docker-compose ps" -ForegroundColor White
Write-Host "Test complet: .\test-chat-integration.ps1" -ForegroundColor White

# Recommandations finales
Write-Host "`n💡 RECOMMANDATIONS" -ForegroundColor Cyan
Write-Host "==================" -ForegroundColor Cyan

if ($successRate -ge 90) {
    Write-Host "✅ Le système est prêt pour la production" -ForegroundColor Green
    Write-Host "✅ Configurez la surveillance en production" -ForegroundColor Green
    Write-Host "✅ Planifiez les sauvegardes régulières" -ForegroundColor Green
} else {
    Write-Host "🔍 Analysez les tests échoués" -ForegroundColor Yellow
    Write-Host "📋 Consultez CHAT_DEPLOYMENT_GUIDE.md" -ForegroundColor Yellow
    Write-Host "🔧 Vérifiez la configuration des services" -ForegroundColor Yellow
}

Write-Host "`n🏁 Validation terminée." -ForegroundColor Blue
Write-Host "📚 Documentation complète disponible dans CHAT_SYSTEM_README.md" -ForegroundColor Blue
