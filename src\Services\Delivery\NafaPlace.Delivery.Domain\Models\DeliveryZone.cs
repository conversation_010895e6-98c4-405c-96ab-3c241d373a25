using System.ComponentModel.DataAnnotations;
using NafaPlace.Common.Models;

namespace NafaPlace.Delivery.Domain.Models;

public class DeliveryZone : BaseEntity
{
    [Required]
    [MaxLength(100)]
    public required string Name { get; set; }

    [Required]
    [MaxLength(50)]
    public required string Code { get; set; }

    [MaxLength(500)]
    public string? Description { get; set; }

    [Required]
    public ZoneType Type { get; set; }

    [MaxLength(100)]
    public string? ParentZoneCode { get; set; }

    // Coordonnées géographiques
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
    public double? Radius { get; set; } // En kilomètres

    // Polygone de zone (JSON format)
    public string? Boundaries { get; set; }

    public bool IsActive { get; set; } = true;

    [Required]
    [Range(0, double.MaxValue)]
    public decimal BaseDeliveryFee { get; set; }

    [Range(0, double.MaxValue)]
    public decimal? FreeDeliveryThreshold { get; set; }

    [Range(1, int.MaxValue)]
    public int EstimatedDeliveryDays { get; set; } = 1;

    [Range(0, int.MaxValue)]
    public int MaxDeliveryDays { get; set; } = 7;

    public bool SameDayDeliveryAvailable { get; set; } = false;

    [Range(0, double.MaxValue)]
    public decimal? SameDayDeliveryFee { get; set; }

    public bool ExpressDeliveryAvailable { get; set; } = false;

    [Range(0, double.MaxValue)]
    public decimal? ExpressDeliveryFee { get; set; }

    [Required]
    [MaxLength(3)]
    public required string Currency { get; set; } = "GNF";

    // Restrictions
    public decimal? MaxWeight { get; set; } // En kg
    public decimal? MaxVolume { get; set; } // En cm³
    public decimal? MaxOrderValue { get; set; }
    public decimal? MinOrderValue { get; set; }

    // Horaires de livraison
    public TimeSpan? DeliveryStartTime { get; set; }
    public TimeSpan? DeliveryEndTime { get; set; }

    // Jours de livraison (JSON format)
    public string? DeliveryDays { get; set; } // ["Monday", "Tuesday", ...]

    [MaxLength(50)]
    public string? CreatedBy { get; set; }

    [MaxLength(50)]
    public string? UpdatedBy { get; set; }

    // Navigation properties
    public virtual ICollection<CarrierZone> CarrierZones { get; set; } = new List<CarrierZone>();
    public virtual ICollection<DeliveryOrder> DeliveryOrders { get; set; } = new List<DeliveryOrder>();
}

public enum ZoneType
{
    Country = 1,        // Pays
    Region = 2,         // Région
    Prefecture = 3,     // Préfecture
    City = 4,           // Ville
    District = 5,       // Quartier/District
    Custom = 6          // Zone personnalisée
}

public class Carrier : BaseEntity
{
    [Required]
    [MaxLength(100)]
    public required string Name { get; set; }

    [Required]
    [MaxLength(50)]
    public required string Code { get; set; }

    [MaxLength(500)]
    public string? Description { get; set; }

    [MaxLength(200)]
    public string? LogoUrl { get; set; }

    [MaxLength(100)]
    public string? ContactEmail { get; set; }

    [MaxLength(20)]
    public string? ContactPhone { get; set; }

    [MaxLength(500)]
    public string? Address { get; set; }

    [MaxLength(200)]
    public string? Website { get; set; }

    public bool IsActive { get; set; } = true;

    public CarrierType Type { get; set; }

    // Configuration API pour le tracking
    [MaxLength(200)]
    public string? ApiEndpoint { get; set; }

    [MaxLength(100)]
    public string? ApiKey { get; set; }

    [MaxLength(100)]
    public string? ApiSecret { get; set; }

    // Évaluation
    public decimal Rating { get; set; } = 0;
    public int ReviewCount { get; set; } = 0;

    // Statistiques
    public int TotalDeliveries { get; set; } = 0;
    public int SuccessfulDeliveries { get; set; } = 0;
    public decimal SuccessRate => TotalDeliveries > 0 ? (decimal)SuccessfulDeliveries / TotalDeliveries * 100 : 0;

    [MaxLength(50)]
    public string? CreatedBy { get; set; }

    [MaxLength(50)]
    public string? UpdatedBy { get; set; }

    // Navigation properties
    public virtual ICollection<CarrierZone> CarrierZones { get; set; } = new List<CarrierZone>();
    public virtual ICollection<DeliveryOrder> DeliveryOrders { get; set; } = new List<DeliveryOrder>();
}

public enum CarrierType
{
    Internal = 1,       // Livraison interne
    ThirdParty = 2,     // Transporteur tiers
    Marketplace = 3,    // Marketplace (Amazon, etc.)
    Postal = 4,         // Service postal
    Express = 5         // Service express
}

public class CarrierZone : BaseEntity
{
    [Required]
    public int CarrierId { get; set; }

    [Required]
    public int ZoneId { get; set; }

    public bool IsActive { get; set; } = true;

    [Required]
    [Range(0, double.MaxValue)]
    public decimal DeliveryFee { get; set; }

    [Range(0, double.MaxValue)]
    public decimal? FreeDeliveryThreshold { get; set; }

    [Range(1, int.MaxValue)]
    public int EstimatedDeliveryDays { get; set; } = 1;

    [Range(0, int.MaxValue)]
    public int MaxDeliveryDays { get; set; } = 7;

    public bool SameDayDeliveryAvailable { get; set; } = false;

    [Range(0, double.MaxValue)]
    public decimal? SameDayDeliveryFee { get; set; }

    public bool ExpressDeliveryAvailable { get; set; } = false;

    [Range(0, double.MaxValue)]
    public decimal? ExpressDeliveryFee { get; set; }

    // Restrictions spécifiques au transporteur
    public decimal? MaxWeight { get; set; }
    public decimal? MaxVolume { get; set; }
    public decimal? MaxOrderValue { get; set; }
    public decimal? MinOrderValue { get; set; }

    // Priorité (plus bas = plus prioritaire)
    public int Priority { get; set; } = 1;

    [MaxLength(50)]
    public string? CreatedBy { get; set; }

    [MaxLength(50)]
    public string? UpdatedBy { get; set; }

    // Navigation properties
    public virtual Carrier? Carrier { get; set; }
    public virtual DeliveryZone? Zone { get; set; }
}
