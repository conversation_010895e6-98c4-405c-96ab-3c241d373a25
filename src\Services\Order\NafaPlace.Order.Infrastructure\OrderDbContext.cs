using Microsoft.EntityFrameworkCore;
using NafaPlace.Order.Domain;

namespace NafaPlace.Order.Infrastructure
{
    public class OrderDbContext : DbContext
    {
        public OrderDbContext(DbContextOptions<OrderDbContext> options) : base(options)
        {
        }

        public DbSet<Domain.Order> Orders { get; set; }
        public DbSet<OrderItem> OrderItems { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configuration de ShippingAddress comme owned entity
            modelBuilder.Entity<Domain.Order>()
                .OwnsOne(o => o.ShippingAddress, sa =>
                {
                    sa.Property(s => s.FullName).HasColumnName("ShippingAddress_FullName");
                    sa.Property(s => s.Address).HasColumnName("ShippingAddress_Address");
                    sa.Property(s => s.City).HasColumnName("ShippingAddress_City");
                    sa.Property(s => s.Country).HasColumnName("ShippingAddress_Country");
                    sa.Property(s => s.PostalCode).HasColumnName("ShippingAddress_PostalCode");
                    sa.Property(s => s.PhoneNumber).HasColumnName("ShippingAddress_PhoneNumber");
                });
        }
    }
}