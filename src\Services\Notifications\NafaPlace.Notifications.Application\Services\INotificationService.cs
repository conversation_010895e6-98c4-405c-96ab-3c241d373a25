using NafaPlace.Notifications.Application.DTOs;

namespace NafaPlace.Notifications.Application.Services;

public interface INotificationService
{
    Task<NotificationDto> CreateNotificationAsync(CreateNotificationRequest request);
    Task<List<NotificationDto>> CreateBulkNotificationAsync(BulkNotificationRequest request);
    Task<NotificationsPagedResult> GetUserNotificationsAsync(string userId, int page = 1, int pageSize = 20, bool unreadOnly = false);
    Task<NotificationDto?> GetNotificationByIdAsync(int id);
    Task<bool> MarkAsReadAsync(int id, string userId);
    Task<int> MarkAllAsReadAsync(string userId);
    Task<bool> DeleteNotificationAsync(int id, string userId);
    Task<int> DeleteAllReadAsync(string userId);
    Task<NotificationStatsDto> GetUserStatsAsync(string userId);
    Task<List<NotificationDto>> GetRecentNotificationsAsync(string userId, int count = 5);
    
    // Template methods
    Task<NotificationTemplateDto> CreateTemplateAsync(CreateNotificationTemplateRequest request);
    Task<List<NotificationTemplateDto>> GetTemplatesAsync();
    Task<NotificationDto> CreateFromTemplateAsync(int templateId, string userId, Dictionary<string, string> parameters);
    
    // System notifications
    Task NotifyOrderStatusChangeAsync(string userId, string orderId, string status);
    Task NotifyPaymentStatusChangeAsync(string userId, string orderId, string status);
    Task NotifyNewReviewAsync(string sellerId, string productName, int rating);
    Task NotifyLowStockAsync(string sellerId, string productName, int currentStock);
}
