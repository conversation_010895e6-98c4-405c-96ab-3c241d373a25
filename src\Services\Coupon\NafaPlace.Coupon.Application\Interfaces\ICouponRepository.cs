using NafaPlace.Coupon.Application.DTOs;
using NafaPlace.Coupon.Domain.Models;

namespace NafaPlace.Coupon.Application.Interfaces;

public interface ICouponRepository
{
    Task<Domain.Models.Coupon?> GetByIdAsync(int id);
    Task<Domain.Models.Coupon?> GetByCodeAsync(string code);
    Task<IEnumerable<Domain.Models.Coupon>> GetAllAsync();
    Task<IEnumerable<Domain.Models.Coupon>> GetActiveAsync();
    Task<Domain.Models.Coupon> CreateAsync(Domain.Models.Coupon coupon);
    Task<Domain.Models.Coupon> UpdateAsync(Domain.Models.Coupon coupon);
    Task DeleteAsync(int id);
    Task<bool> IsCodeUniqueAsync(string code, int? excludeId = null);
    Task<bool> CanUseCouponAsync(string code, string userId, decimal orderAmount);
    Task<CouponUsage> RecordUsageAsync(int couponId, string userId, decimal discountAmount);
    Task<IEnumerable<CouponUsage>> GetUsageHistoryAsync(int couponId);
    Task<int> GetUsageCountAsync(int couponId);
    Task<int> GetUserUsageCountAsync(int couponId, string userId);

    // Additional methods needed by CouponService
    Task<IEnumerable<Domain.Models.Coupon>> GetCouponsAsync();
    Task<IEnumerable<Domain.Models.Coupon>> GetActiveCouponsAsync();
    Task IncrementUsageCountAsync(int couponId);
    Task<IEnumerable<Domain.Models.Coupon>> GetExpiredCouponsAsync();
    Task<int> CleanupExpiredCouponsAsync();
    Task<CouponStatsDto> GetCouponStatsAsync(int couponId);
    Task<UsageStatsDto> GetUsageStatsAsync(int couponId);
    Task<List<DTOs.CouponUsageStatsDto>> GetUsageStatsAsync(DateTime? startDate, DateTime? endDate);
}
