@page "/coupons"
@using NafaPlace.SellerPortal.Services
@inject CouponService CouponService
@inject NotificationService NotificationService

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-ticket-alt me-2"></i>
                        Coupons Applicables
                    </h3>
                    <div class="card-tools">
                        <button class="btn btn-outline-secondary btn-sm" @onclick="RefreshCoupons">
                            <i class="fas fa-sync-alt me-2"></i>
                            Actualiser
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Information :</strong> Voici les coupons créés par les administrateurs qui peuvent s'appliquer à vos produits.
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h5 class="card-title">Statistiques</h5>
                                    <div class="row">
                                        <div class="col-6">
                                            <div class="text-primary">
                                                <i class="fas fa-ticket-alt fa-2x"></i>
                                                <h4 class="mt-2">@activeCoupons.Count</h4>
                                                <small>Coupons Actifs</small>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="text-success">
                                                <i class="fas fa-chart-line fa-2x"></i>
                                                <h4 class="mt-2">@totalUsages</h4>
                                                <small>Utilisations</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    @if (isLoading)
                    {
                        <div class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Chargement...</span>
                            </div>
                        </div>
                    }
                    else if (activeCoupons.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Code</th>
                                        <th>Nom</th>
                                        <th>Type</th>
                                        <th>Valeur</th>
                                        <th>Période</th>
                                        <th>Utilisations</th>
                                        <th>Statut</th>
                                        <th>Applicabilité</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var coupon in activeCoupons)
                                    {
                                        <tr>
                                            <td>
                                                <strong class="text-primary">@coupon.Code</strong>
                                            </td>
                                            <td>@coupon.Name</td>
                                            <td>
                                                <span class="badge @GetTypeBadgeClass(coupon.Type)">
                                                    @GetTypeText(coupon.Type)
                                                </span>
                                            </td>
                                            <td>
                                                @if (coupon.Type == CouponType.Percentage)
                                                {
                                                    <text>@coupon.Value%</text>
                                                }
                                                else if (coupon.Type == CouponType.FixedAmount)
                                                {
                                                    <text>@coupon.Value.ToString("N0") GNF</text>
                                                }
                                                else
                                                {
                                                    <text>-</text>
                                                }
                                            </td>
                                            <td>
                                                <small>
                                                    Du @coupon.StartDate.ToString("dd/MM/yyyy")<br />
                                                    Au @coupon.EndDate.ToString("dd/MM/yyyy")
                                                </small>
                                            </td>
                                            <td>
                                                @coupon.UsageCount
                                                @if (coupon.UsageLimit.HasValue)
                                                {
                                                    <text> / @coupon.UsageLimit</text>
                                                }
                                            </td>
                                            <td>
                                                <span class="badge @(coupon.IsActive ? "bg-success" : "bg-danger")">
                                                    @(coupon.IsActive ? "Actif" : "Inactif")
                                                </span>
                                            </td>
                                            <td>
                                                @if (coupon.ApplicableToAllProducts)
                                                {
                                                    <span class="badge bg-info">Tous les produits</span>
                                                }
                                                else if (coupon.ApplicableSellerIds?.Contains(_sellerId) == true)
                                                {
                                                    <span class="badge bg-success">Vos produits</span>
                                                }
                                                else if (coupon.ApplicableCategoryIds?.Any() == true)
                                                {
                                                    <span class="badge bg-warning">Catégories spécifiques</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-secondary">Produits spécifiques</span>
                                                }
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        @if (usageStats.Any())
                        {
                            <div class="mt-4">
                                <h5>Statistiques d'utilisation de vos produits</h5>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Code Coupon</th>
                                                <th>Nom</th>
                                                <th>Utilisations</th>
                                                <th>Réduction Totale</th>
                                                <th>Dernière Utilisation</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var stat in usageStats)
                                            {
                                                <tr>
                                                    <td><strong>@stat.CouponCode</strong></td>
                                                    <td>@stat.CouponName</td>
                                                    <td>@stat.TotalUsages</td>
                                                    <td>@stat.TotalDiscountAmount.ToString("N0") GNF</td>
                                                    <td>@stat.LastUsed.ToString("dd/MM/yyyy HH:mm")</td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-ticket-alt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Aucun coupon actif</h5>
                            <p class="text-muted">Il n'y a actuellement aucun coupon actif applicable à vos produits.</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private List<CouponDto> activeCoupons = new();
    private List<CouponUsageStatsDto> usageStats = new();
    private bool isLoading = true;
    private int _sellerId = 1; // TODO: Récupérer l'ID du vendeur connecté
    private int totalUsages = 0;

    protected override async Task OnInitializedAsync()
    {
        await LoadCoupons();
        await LoadUsageStats();
    }

    private async Task LoadCoupons()
    {
        isLoading = true;
        try
        {
            activeCoupons = await CouponService.GetActiveCouponsAsync();
            totalUsages = activeCoupons.Sum(c => c.UsageCount);
        }
        catch (Exception ex)
        {
            NotificationService.Error($"Erreur lors du chargement des coupons: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task LoadUsageStats()
    {
        try
        {
            usageStats = await CouponService.GetCouponUsageStatsAsync(_sellerId);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading usage stats: {ex.Message}");
        }
    }

    private async Task RefreshCoupons()
    {
        await LoadCoupons();
        await LoadUsageStats();
        NotificationService.Success("Données actualisées");
    }

    private string GetTypeBadgeClass(CouponType type)
    {
        return type switch
        {
            CouponType.FixedAmount => "bg-primary",
            CouponType.Percentage => "bg-success",
            CouponType.FreeShipping => "bg-info",
            CouponType.BuyXGetY => "bg-warning",
            _ => "bg-secondary"
        };
    }

    private string GetTypeText(CouponType type)
    {
        return type switch
        {
            CouponType.FixedAmount => "Montant Fixe",
            CouponType.Percentage => "Pourcentage",
            CouponType.FreeShipping => "Livraison Gratuite",
            CouponType.BuyXGetY => "Achetez X Obtenez Y",
            _ => "Inconnu"
        };
    }
}
