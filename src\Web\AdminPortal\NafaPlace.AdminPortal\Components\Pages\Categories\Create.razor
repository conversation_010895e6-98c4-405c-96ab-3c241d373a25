@page "/categories/create"
@using NafaPlace.AdminPortal.Models
@using NafaPlace.AdminPortal.Services
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Components.Routing
@inject CategoryService CategoryService
@inject NavigationManager NavigationManager
@inject ImageService ImageService
@inject IJSRuntime JSRuntime

<PageTitle>Créer une catégorie - NafaPlace Admin</PageTitle>

<h1>Créer une nouvelle catégorie</h1>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <EditForm Model="@categoryRequest" OnValidSubmit="HandleValidSubmit">
                    <DataAnnotationsValidator />
                    <ValidationSummary />

                    <div class="mb-3">
                        <label for="name" class="form-label">Nom</label>
                        <InputText id="name" @bind-Value="categoryRequest.Name" class="form-control" />
                        <ValidationMessage For="@(() => categoryRequest.Name)" />
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <InputTextArea id="description" @bind-Value="categoryRequest.Description" class="form-control" rows="3" />
                        <ValidationMessage For="@(() => categoryRequest.Description)" />
                    </div>

                    <div class="mb-3">
                        <label for="imageUrl" class="form-label">Image de la catégorie</label>

                        @if (!string.IsNullOrEmpty(categoryRequest.ImageUrl) && !categoryRequest.ImageUrl.Contains("default"))
                        {
                            <div class="mb-2">
                                <img src="@categoryRequest.ImageUrl" alt="Aperçu" class="img-thumbnail" style="max-width: 200px; max-height: 200px;" />
                            </div>
                        }

                        <InputFile OnChange="OnImageSelected" class="form-control" accept="image/*" />
                        <small class="form-text text-muted">Sélectionnez une image pour la catégorie (JPG, PNG, GIF - Max 5MB)</small>

                        @if (isUploadingImage)
                        {
                            <div class="mt-2">
                                <div class="progress">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 100%"></div>
                                </div>
                                <small class="text-muted">Upload en cours...</small>
                            </div>
                        }

                        <div class="mt-2">
                            <label for="imageUrlManual" class="form-label">Ou saisissez une URL d'image</label>
                            <InputText id="imageUrlManual" @bind-Value="categoryRequest.ImageUrl" class="form-control" placeholder="https://exemple.com/image.jpg" />
                            <ValidationMessage For="@(() => categoryRequest.ImageUrl)" />
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="iconUrl" class="form-label">URL de l'icône (optionnel)</label>
                        <InputText id="iconUrl" @bind-Value="categoryRequest.IconUrl" class="form-control" />
                        <ValidationMessage For="@(() => categoryRequest.IconUrl)" />
                        <small class="form-text text-muted">URL de l'icône pour la catégorie (optionnel)</small>
                    </div>

                    <div class="mb-3">
                        <label for="parentId" class="form-label">Catégorie parente (optionnel)</label>
                        <InputSelect id="parentId" @bind-Value="parentIdString" class="form-control">
                            <option value="">-- Aucune catégorie parente --</option>
                            @if (parentCategories != null)
                            {
                                @foreach (var category in parentCategories)
                                {
                                    <option value="@category.Id">@category.Name</option>
                                }
                            }
                        </InputSelect>
                        <small class="form-text text-muted">Sélectionnez une catégorie parente si cette catégorie est une sous-catégorie</small>
                    </div>

                    <div class="mb-3 form-check">
                        <InputCheckbox id="isActive" @bind-Value="categoryRequest.IsActive" class="form-check-input" />
                        <label class="form-check-label" for="isActive">Actif</label>
                        <small class="form-text text-muted d-block">Décochez cette case pour désactiver temporairement la catégorie</small>
                    </div>

                    <div class="mt-4">
                        <button type="submit" class="btn btn-primary">Créer</button>
                        <a href="/categories" class="btn btn-secondary ms-2">Annuler</a>
                    </div>
                </EditForm>
            </div>
        </div>
    </div>
</div>

@if (!string.IsNullOrEmpty(errorMessage))
{
    <div class="alert alert-danger mt-3">
        @errorMessage
    </div>
}

@code {
    private CreateCategoryRequest categoryRequest = new()
    {
        IsActive = true,
        ImageUrl = "/images/categories/default.jpg"
    };

    private IEnumerable<CategoryDto>? parentCategories;
    private string parentIdString = "";
    private string errorMessage = "";
    private bool isUploadingImage = false;

    protected override async Task OnInitializedAsync()
    {
        try
        {
            Console.WriteLine("OnInitializedAsync: Chargement des catégories parentes");
            parentCategories = await CategoryService.GetAllCategoriesAsync();
            Console.WriteLine($"OnInitializedAsync: {parentCategories?.Count() ?? 0} catégories chargées");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des catégories parentes: {ex.Message}");
            errorMessage = $"Erreur lors du chargement des catégories parentes: {ex.Message}";
        }
    }

    private async Task HandleValidSubmit()
    {
        try
        {
            Console.WriteLine("HandleValidSubmit: Début de la création de catégorie");
            Console.WriteLine($"Nom: {categoryRequest.Name}");
            Console.WriteLine($"Description: {categoryRequest.Description}");
            Console.WriteLine($"ImageUrl: {categoryRequest.ImageUrl}");
            Console.WriteLine($"ParentIdString: {parentIdString}");

            // Convertir la chaîne en int? pour ParentId
            if (!string.IsNullOrEmpty(parentIdString) && int.TryParse(parentIdString, out int parentId))
            {
                categoryRequest.ParentId = parentId;
                Console.WriteLine($"ParentId converti: {categoryRequest.ParentId}");
            }
            else
            {
                categoryRequest.ParentId = null;
                Console.WriteLine("ParentId est null");
            }

            var result = await CategoryService.CreateCategoryAsync(categoryRequest);
            Console.WriteLine($"Catégorie créée avec succès, ID: {result?.Id}");
            NavigationManager.NavigateTo("/categories");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la création de la catégorie: {ex.Message}");
            errorMessage = $"Erreur lors de la création de la catégorie: {ex.Message}";
            StateHasChanged();
        }
    }

    private async Task OnImageSelected(InputFileChangeEventArgs e)
    {
        var file = e.File;
        if (file != null)
        {
            try
            {
                isUploadingImage = true;
                StateHasChanged();

                // Vérifier la taille du fichier (5MB max)
                if (file.Size > 5 * 1024 * 1024)
                {
                    errorMessage = "La taille du fichier ne doit pas dépasser 5 MB.";
                    return;
                }

                // Vérifier le type de fichier
                var allowedTypes = new[] { "image/jpeg", "image/jpg", "image/png", "image/gif" };
                if (!allowedTypes.Contains(file.ContentType.ToLower()))
                {
                    errorMessage = "Seuls les fichiers JPG, PNG et GIF sont autorisés.";
                    return;
                }

                // Lire le fichier et le convertir en base64
                using var stream = file.OpenReadStream(5 * 1024 * 1024);
                using var ms = new MemoryStream();
                await stream.CopyToAsync(ms);
                var imageBytes = ms.ToArray();
                var base64Image = Convert.ToBase64String(imageBytes);
                var dataUrl = $"data:{file.ContentType};base64,{base64Image}";

                // Uploader l'image
                var imageUrl = await ImageService.UploadCategoryImageAsync(dataUrl);

                if (!string.IsNullOrEmpty(imageUrl))
                {
                    categoryRequest.ImageUrl = imageUrl;
                    errorMessage = "";
                    await JSRuntime.InvokeVoidAsync("console.log", $"Image uploadée avec succès: {imageUrl}");
                }
                else
                {
                    errorMessage = "Erreur lors de l'upload de l'image.";
                }
            }
            catch (Exception ex)
            {
                errorMessage = $"Erreur lors de l'upload de l'image: {ex.Message}";
                await JSRuntime.InvokeVoidAsync("console.error", ex.Message);
            }
            finally
            {
                isUploadingImage = false;
                StateHasChanged();
            }
        }
    }
}
