using NafaPlace.Cart.Domain;
using System.Threading.Tasks;

namespace NafaPlace.Cart.Application
{
    public interface IShoppingCartRepository
    {
        Task<ShoppingCart> GetCartAsync(string userId);
        Task<ShoppingCart> UpdateCartAsync(ShoppingCart cart);
        Task DeleteCartAsync(string userId);
        Task<ShoppingCart> AddItemToCartAsync(string userId, CartItem item);
        Task<ShoppingCart> RemoveItemFromCartAsync(string userId, int productId);
    }
}