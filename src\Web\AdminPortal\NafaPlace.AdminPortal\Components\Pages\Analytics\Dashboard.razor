@page "/analytics/dashboard"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.JSInterop
@attribute [Authorize]
@inject IJSRuntime JSRuntime

<PageTitle>Tableau de Bord Analytics - NafaPlace Admin</PageTitle>

<div class="container-fluid py-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-1 text-dark fw-bold">📊 Analytics & KPIs Avancés</h1>
                    <p class="text-muted mb-0">Tableau de bord complet pour les administrateurs</p>
                </div>
                <div class="btn-group" role="group">
                    <button type="button" class="btn @(selectedPeriod == 7 ? "btn-primary" : "btn-outline-primary") btn-sm" @onclick="() => SetPeriod(7)">7 jours</button>
                    <button type="button" class="btn @(selectedPeriod == 30 ? "btn-primary" : "btn-outline-primary") btn-sm" @onclick="() => SetPeriod(30)">30 jours</button>
                    <button type="button" class="btn @(selectedPeriod == 90 ? "btn-primary" : "btn-outline-primary") btn-sm" @onclick="() => SetPeriod(90)">90 jours</button>
                </div>
            </div>
        </div>
    </div>

    <!-- KPIs Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card border-0 shadow-sm h-100 kpi-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="kpi-icon bg-primary">
                                <i class="bi bi-people text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="text-muted small">Utilisateurs Totaux</div>
                            <div class="h4 mb-0 fw-bold">@FormatNumber(totalUsers)</div>
                            <div class="text-success small">
                                <i class="bi bi-arrow-up"></i> +12% vs période précédente
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card border-0 shadow-sm h-100 kpi-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="kpi-icon bg-success">
                                <i class="bi bi-cart-check text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="text-muted small">Commandes</div>
                            <div class="h4 mb-0 fw-bold">@FormatNumber(totalOrders)</div>
                            <div class="text-success small">
                                <i class="bi bi-arrow-up"></i> +8% vs période précédente
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card border-0 shadow-sm h-100 kpi-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="kpi-icon bg-warning">
                                <i class="bi bi-currency-dollar text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="text-muted small">Chiffre d'Affaires</div>
                            <div class="h4 mb-0 fw-bold">@FormatCurrency(totalRevenue)</div>
                            <div class="text-success small">
                                <i class="bi bi-arrow-up"></i> +15% vs période précédente
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card border-0 shadow-sm h-100 kpi-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="kpi-icon bg-info">
                                <i class="bi bi-shop text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="text-muted small">Vendeurs Actifs</div>
                            <div class="h4 mb-0 fw-bold">@FormatNumber(activeSellers)</div>
                            <div class="text-success small">
                                <i class="bi bi-arrow-up"></i> +5% vs période précédente
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row g-4 mb-4">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-0">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-graph-up text-primary me-2"></i>Évolution des Ventes
                    </h5>
                </div>
                <div class="card-body">
                    <div style="height: 350px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 10px; display: flex; align-items: center; justify-content: center; color: white;">
                        <div class="text-center">
                            <i class="bi bi-graph-up" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                            <h5>Évolution des Ventes</h5>
                            <p class="mb-0">Graphique interactif des ventes mensuelles</p>
                            <div class="mt-3">
                                <span class="badge bg-light text-dark">+25% ce mois</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-0">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-pie-chart text-primary me-2"></i>Top Catégories
                    </h5>
                </div>
                <div class="card-body">
                    <div style="height: 350px; background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); border-radius: 10px; display: flex; align-items: center; justify-content: center; color: white;">
                        <div class="text-center">
                            <i class="bi bi-pie-chart" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                            <h5>Top Catégories</h5>
                            <p class="mb-0">Répartition des ventes par catégorie</p>
                            <div class="mt-3">
                                <div class="row text-center">
                                    <div class="col-6"><small>Électronique: 35%</small></div>
                                    <div class="col-6"><small>Mode: 25%</small></div>
                                    <div class="col-6"><small>Maison: 20%</small></div>
                                    <div class="col-6"><small>Sport: 12%</small></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Charts -->
    <div class="row g-4">
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-0">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-people text-primary me-2"></i>Croissance Utilisateurs
                    </h5>
                </div>
                <div class="card-body">
                    <div style="height: 300px; background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); border-radius: 10px; display: flex; align-items: center; justify-content: center; color: white;">
                        <div class="text-center">
                            <i class="bi bi-people" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                            <h5>Croissance Utilisateurs</h5>
                            <p class="mb-0">Nouveaux utilisateurs par mois</p>
                            <div class="mt-3">
                                <span class="badge bg-light text-dark">+18% ce mois</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-0">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-shop text-primary me-2"></i>Performance Vendeurs
                    </h5>
                </div>
                <div class="card-body">
                    <div style="height: 300px; background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); border-radius: 10px; display: flex; align-items: center; justify-content: center; color: white;">
                        <div class="text-center">
                            <i class="bi bi-shop" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                            <h5>Performance Vendeurs</h5>
                            <p class="mb-0">Analyse des performances moyennes</p>
                            <div class="mt-3">
                                <div class="row text-center">
                                    <div class="col-6"><small>Ventes: 85%</small></div>
                                    <div class="col-6"><small>Avis: 90%</small></div>
                                    <div class="col-6"><small>Livraisons: 78%</small></div>
                                    <div class="col-6"><small>Support: 88%</small></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



@code {
    private int selectedPeriod = 30;
    private int totalUsers = 12543;
    private int totalOrders = 1876;
    private decimal totalRevenue = 2456789;
    private int activeSellers = 234;

    private void SetPeriod(int days)
    {
        selectedPeriod = days;
        // Recharger les données selon la période
        LoadDataForPeriod(days);
    }

    private void LoadDataForPeriod(int days)
    {
        // Simuler le chargement des données selon la période
        switch (days)
        {
            case 7:
                totalUsers = 8432;
                totalOrders = 456;
                totalRevenue = 567890;
                activeSellers = 189;
                break;
            case 30:
                totalUsers = 12543;
                totalOrders = 1876;
                totalRevenue = 2456789;
                activeSellers = 234;
                break;
            case 90:
                totalUsers = 23456;
                totalOrders = 5432;
                totalRevenue = 7890123;
                activeSellers = 345;
                break;
        }
        StateHasChanged();
    }

    private string FormatNumber(int number)
    {
        if (number >= 1000000)
            return $"{number / 1000000.0:F1}M";
        else if (number >= 1000)
            return $"{number / 1000.0:F1}K";
        else
            return number.ToString();
    }

    private string FormatCurrency(decimal amount)
    {
        if (amount >= 1000000000)
            return $"{amount / 1000000000:F1}B GNF";
        else if (amount >= 1000000)
            return $"{amount / 1000000:F1}M GNF";
        else if (amount >= 1000)
            return $"{amount / 1000:F0}K GNF";
        else
            return $"{amount:F0} GNF";
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await JSRuntime.InvokeVoidAsync("initializeCharts");
        }
    }
}

<style>
    .kpi-card {
        transition: all 0.3s ease;
        border-left: 4px solid transparent;
        background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    }

    .kpi-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.1) !important;
        border-left-color: var(--bs-primary);
    }

    .kpi-icon {
        width: 60px;
        height: 60px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
    }

    .card {
        border-radius: 15px;
        overflow: hidden;
    }

    .card-header {
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        padding: 1.25rem 1.5rem;
    }

    .card-body {
        padding: 1.5rem;
    }

    .bg-primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important; }
    .bg-success { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important; }
    .bg-warning { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important; }
    .bg-info { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%) !important; }

    .text-primary { color: #667eea !important; }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 8px;
        font-weight: 500;
    }

    .btn-outline-primary {
        border-color: #667eea;
        color: #667eea;
        border-radius: 8px;
        font-weight: 500;
    }

    .btn-outline-primary:hover {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-color: #667eea;
    }

    .h3 {
        color: #2d3748;
        font-weight: 700;
    }

    .small {
        font-size: 0.875rem;
        font-weight: 500;
    }

    .h4 {
        font-weight: 700;
        color: #2d3748;
    }

    canvas {
        border-radius: 10px;
    }
</style>
