using System.Collections.Generic;

namespace NafaPlace.Web.Models.Cart
{
    public class CartDto
    {
        public string UserId { get; set; } = string.Empty;
        public List<CartItemDto> Items { get; set; } = new();
        public decimal SubTotal { get; set; }
        public decimal ShippingFee { get; set; }
        public decimal Tax { get; set; }
        public decimal Total { get; set; }
        public int ItemCount { get; set; }
        public string Currency { get; set; } = "GNF";
        public DateTime LastUpdated { get; set; }

        // Coupon information
        public string? CouponCode { get; set; }
        public decimal CouponDiscount { get; set; } = 0;
        public string? CouponDescription { get; set; }

        // Propriété de compatibilité
        public decimal TotalPrice => Total;
    }
}
