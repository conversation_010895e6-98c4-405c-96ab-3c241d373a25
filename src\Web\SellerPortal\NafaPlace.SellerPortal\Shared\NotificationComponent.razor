@using NafaPlace.SellerPortal.Services
@inject NotificationService NotificationService
@implements IDisposable

@if (!string.IsNullOrEmpty(_message))
{
    <div class="notification-container @(_isVisible ? "show" : "")">
        <div class="toast show" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header @GetHeaderClass()">
                <strong class="me-auto">@GetTitle()</strong>
                <button type="button" class="btn-close" @onclick="CloseNotification"></button>
            </div>
            <div class="toast-body">
                <div>@_message</div>
                @if (!string.IsNullOrEmpty(_details))
                {
                    <div class="mt-2 small text-muted">@_details</div>
                }
            </div>
        </div>
    </div>
}

<style>
    .notification-container {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        opacity: 0;
        transform: translateY(-20px);
        transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
    }

    .notification-container.show {
        opacity: 1;
        transform: translateY(0);
    }

    .toast {
        min-width: 300px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .toast-header.bg-success, .toast-header.bg-info, 
    .toast-header.bg-warning, .toast-header.bg-danger {
        color: white;
    }
</style>

@code {
    private string _message = "";
    private string _details = "";
    private NotificationType _type = NotificationType.None;
    private bool _isVisible = false;

    protected override void OnInitialized()
    {
        NotificationService.OnNotification += ShowNotification;
    }

    private void ShowNotification(string message, NotificationType type, string details)
    {
        _message = message;
        _type = type;
        _details = details;
        _isVisible = !string.IsNullOrEmpty(message);
        StateHasChanged();
    }

    private void CloseNotification()
    {
        _isVisible = false;
        StateHasChanged();
    }

    private string GetHeaderClass()
    {
        return _type switch
        {
            NotificationType.Success => "bg-success text-white",
            NotificationType.Info => "bg-info text-white",
            NotificationType.Warning => "bg-warning text-white",
            NotificationType.Error => "bg-danger text-white",
            _ => ""
        };
    }

    private string GetTitle()
    {
        return _type switch
        {
            NotificationType.Success => "Succès",
            NotificationType.Info => "Information",
            NotificationType.Warning => "Attention",
            NotificationType.Error => "Erreur",
            _ => "Notification"
        };
    }

    public void Dispose()
    {
        NotificationService.OnNotification -= ShowNotification;
    }
}
