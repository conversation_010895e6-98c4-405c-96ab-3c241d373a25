@page "/login"
@layout AuthLayout
@using NafaPlace.SellerPortal.Models.Auth
@using NafaPlace.SellerPortal.Services
@inject IAuthService AuthService
@inject NavigationManager NavigationManager

<PageTitle>NafaPlace - Connexion Vendeur</PageTitle>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow-lg mt-5">
                <div class="card-header bg-primary text-white text-center py-3">
                    <h4 class="mb-0">Espace Vendeur</h4>
                </div>
                <div class="card-body p-4">
                    <h5 class="card-title text-center mb-4">Connexion</h5>
                    
                    @if (!string.IsNullOrEmpty(errorMessage))
                    {
                        <div class="alert alert-danger" role="alert">
                            @errorMessage
                        </div>
                    }
                    
                    <EditForm Model="@loginRequest" OnValidSubmit="HandleLogin">
                        <DataAnnotationsValidator />
                        <ValidationSummary />
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">Email</label>
                            <InputText id="email" @bind-Value="loginRequest.Email" class="form-control" placeholder="Entrez votre email" />
                            <ValidationMessage For="@(() => loginRequest.Email)" />
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">Mot de passe</label>
                            <InputText id="password" type="password" @bind-Value="loginRequest.Password" class="form-control" placeholder="Entrez votre mot de passe" />
                            <ValidationMessage For="@(() => loginRequest.Password)" />
                        </div>
                        
                        <div class="mb-3 form-check">
                            <InputCheckbox id="rememberMe" @bind-Value="loginRequest.RememberMe" class="form-check-input" />
                            <label class="form-check-label" for="rememberMe">Se souvenir de moi</label>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary" disabled="@isLoading">
                                @if (isLoading)
                                {
                                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                    <span class="ms-1">Connexion en cours...</span>
                                }
                                else
                                {
                                    <span>Se connecter</span>
                                }
                            </button>
                        </div>
                    </EditForm>
                    
                    <div class="mt-3 text-center">
                        <p>Vous n'avez pas de compte ? <a href="/register" class="text-primary">Inscrivez-vous</a></p>
                    </div>
                </div>
                <div class="card-footer text-center py-3">
                    <div class="small">
                        <a href="/">Retour au site principal</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private LoginRequest loginRequest = new LoginRequest();
    private bool isLoading = false;
    private string errorMessage = string.Empty;
    
    protected override async Task OnInitializedAsync()
    {
        // Rediriger vers le tableau de bord si l'utilisateur est déjà connecté
        var currentUser = await AuthService.GetCurrentUserAsync();
        if (currentUser.IsAuthenticated && currentUser.Roles.Contains("Seller"))
        {
            NavigationManager.NavigateTo("/dashboard");
        }
    }
    
    private async Task HandleLogin()
    {
        try
        {
            isLoading = true;
            errorMessage = string.Empty;
            
            var result = await AuthService.LoginAsync(loginRequest.Email, loginRequest.Password);
            
            if (result.Success)
            {
                // Vérifier si l'utilisateur a le rôle "Seller"
                var currentUser = await AuthService.GetCurrentUserAsync();
                if (currentUser.Roles.Contains("Seller"))
                {
                    // Rediriger vers le tableau de bord du vendeur
                    NavigationManager.NavigateTo("/dashboard", true);
                }
                else
                {
                    // L'utilisateur n'a pas le rôle "Seller"
                    await AuthService.LogoutAsync();
                    errorMessage = "Vous n'avez pas les autorisations nécessaires pour accéder à l'espace vendeur.";
                }
            }
            else
            {
                errorMessage = result.Message;
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Erreur lors de la connexion: {ex.Message}";
        }
        finally
        {
            isLoading = false;
        }
    }
}
