@inherits LayoutComponentBase
@inject IAuthService AuthService
@inject NavigationManager NavigationManager
@implements IDisposable
@inject IJSRuntime JSRuntime

<AuthorizeView>
    <Authorized>
        <!-- Sidebar -->
        <nav class="sidebar">
            <NavMenu />
        </nav>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Header -->
            <header class="header d-flex align-items-center px-4">
                <div class="d-flex align-items-center flex-grow-1">
                    <h5 class="mb-0 me-4">NafaPlace Admin</h5>
                    <div class="input-group" style="max-width: 300px;">
                        <span class="input-group-text bg-light border-0">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control border-0 bg-light"
                               placeholder="Rechercher...">
                    </div>
                </div>

                <div class="d-flex align-items-center">
                    <div class="position-relative me-3">
                        <i class="fas fa-bell fs-5 text-muted"></i>
                        <span class="notification-dot"></span>
                    </div>
                    <div class="position-relative me-3">
                        <i class="fas fa-envelope fs-5 text-muted"></i>
                        <span class="notification-dot"></span>
                    </div>
                    <div class="dropdown">
                        <a href="#" class="d-flex align-items-center text-decoration-none dropdown-toggle" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle me-2 fs-4"></i>
                            <span>@context.User.Identity?.Name</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="userDropdown">
                            <li><a class="dropdown-item" href="/profile"><i class="fas fa-id-card me-2"></i>Mon profil</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><button class="dropdown-item" @onclick="Logout"><i class="fas fa-sign-out-alt me-2"></i>Déconnexion</button></li>
                        </ul>
                    </div>
                </div>
            </header>

            <!-- Dashboard Content -->
            <div class="p-4">
                @Body
            </div>
        </div>
    </Authorized>
    <NotAuthorized>
        <div class="d-flex justify-content-center align-items-center vh-100">
            <div class="text-center">
                <h3>Accès non autorisé</h3>
                <p>Veuillez vous connecter pour accéder au portail admin.</p>
                <a href="/login" class="btn btn-primary">Connexion</a>
            </div>
        </div>
    </NotAuthorized>
</AuthorizeView>

@code {
    protected override void OnInitialized()
    {
        // S'abonner aux changements d'état d'authentification
        AuthService.AuthenticationStateChanged += StateHasChanged;
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        await JSRuntime.InvokeVoidAsync("reinitializeDropdowns");
    }

    public void Dispose()
    {
        // Se désabonner des changements d'état d'authentification
        AuthService.AuthenticationStateChanged -= StateHasChanged;
    }

    private async Task Logout()
    {
        await AuthService.LogoutAsync();
        NavigationManager.NavigateTo("/login");
    }
}
