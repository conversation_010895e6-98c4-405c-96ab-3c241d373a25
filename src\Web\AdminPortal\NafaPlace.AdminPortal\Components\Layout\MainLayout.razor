@inherits LayoutComponentBase
@inject IAuthService AuthService
@inject NavigationManager NavigationManager
@implements IDisposable
@inject IJSRuntime JSRuntime

<div class="page">
    <AuthorizeView>
        <Authorized>
            <div class="sidebar">
                <NavMenu />
            </div>
        </Authorized>
    </AuthorizeView>

    <main>
        <div class="top-row px-4 justify-content-end">
            <AuthorizeView>
                <Authorized>
                    <div class="dropdown">
                        <a href="#" class="d-flex align-items-center text-decoration-none dropdown-toggle" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle me-2"></i>
                            <span>@context.User.Identity?.Name</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="userDropdown">
                            <li><a class="dropdown-item" href="/profile"><i class="fas fa-id-card me-2"></i>Mon profil</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><button class="dropdown-item" @onclick="Logout"><i class="fas fa-sign-out-alt me-2"></i>Déconnexion</button></li>
                        </ul>
                    </div>
                </Authorized>
                <NotAuthorized>
                    <a href="/login" class="btn btn-outline-primary me-2">Connexion</a>
                </NotAuthorized>
            </AuthorizeView>
        </div>

        <article class="content px-4">
            @Body
        </article>
    </main>
</div>

@code {
    protected override void OnInitialized()
    {
        // S'abonner aux changements d'état d'authentification
        AuthService.AuthenticationStateChanged += StateHasChanged;
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        await JSRuntime.InvokeVoidAsync("reinitializeDropdowns");
    }

    public void Dispose()
    {
        // Se désabonner des changements d'état d'authentification
        AuthService.AuthenticationStateChanged -= StateHasChanged;
    }

    private async Task Logout()
    {
        await AuthService.LogoutAsync();
        NavigationManager.NavigateTo("/login");
    }
}
