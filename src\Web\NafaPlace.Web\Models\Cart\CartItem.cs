namespace NafaPlace.Web.Models.Cart;

public class CartItem
{
    public int ProductId { get; set; } = 0;
    public string Name { get; set; } = string.Empty;
    public string ImageUrl { get; set; } = string.Empty;
    public decimal UnitPrice { get; set; } = 0;
    public int Quantity { get; set; } = 0;
    public decimal TotalPrice { get; set; } = 0;
    public string Currency { get; set; } = "XOF";
}
