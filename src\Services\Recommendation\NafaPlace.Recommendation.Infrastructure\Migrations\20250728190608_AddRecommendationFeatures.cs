﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace NafaPlace.Recommendation.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddRecommendationFeatures : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "ABTestGroups",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    Algorithm = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Parameters = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "{}"),
                    TrafficPercentage = table.Column<decimal>(type: "numeric(5,2)", nullable: false),
                    StartDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    EndDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    Metrics = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "{}"),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ABTestGroups", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "ProductSimilarities",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    ProductId1 = table.Column<int>(type: "integer", nullable: false),
                    ProductId2 = table.Column<int>(type: "integer", nullable: false),
                    SimilarityScore = table.Column<decimal>(type: "numeric(5,4)", nullable: false),
                    Algorithm = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Metadata = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "{}"),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ProductSimilarities", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "RecommendationModels",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Algorithm = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Version = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    Parameters = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "{}"),
                    ModelData = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "{}"),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    Accuracy = table.Column<decimal>(type: "numeric(5,4)", nullable: true),
                    Precision = table.Column<decimal>(type: "numeric(5,4)", nullable: true),
                    Recall = table.Column<decimal>(type: "numeric(5,4)", nullable: true),
                    F1Score = table.Column<decimal>(type: "numeric(5,4)", nullable: true),
                    LastTrainedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    TrainingData = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "{}"),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RecommendationModels", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "UserInteractions",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    UserId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    ProductId = table.Column<int>(type: "integer", nullable: false),
                    Type = table.Column<int>(type: "integer", nullable: false),
                    Weight = table.Column<double>(type: "double precision", nullable: false, defaultValue: 1.0),
                    SessionId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Context = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "{}"),
                    Timestamp = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserInteractions", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "UserPreferences",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    UserId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    PreferenceType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    PreferenceValue = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Score = table.Column<decimal>(type: "numeric(5,4)", nullable: false),
                    Confidence = table.Column<decimal>(type: "numeric(5,4)", nullable: false),
                    Source = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    Metadata = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "{}"),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserPreferences", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "UserABTestAssignments",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    UserId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    ABTestGroupId = table.Column<int>(type: "integer", nullable: false),
                    AssignedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserABTestAssignments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_UserABTestAssignments_ABTestGroups_ABTestGroupId",
                        column: x => x.ABTestGroupId,
                        principalTable: "ABTestGroups",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "RecommendationResults",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    UserId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    RecommendedProducts = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "[]"),
                    Algorithm = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    ModelId = table.Column<int>(type: "integer", nullable: true),
                    Context = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "{}"),
                    Scores = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "{}"),
                    Metadata = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "{}"),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RecommendationResults", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RecommendationResults_RecommendationModels_ModelId",
                        column: x => x.ModelId,
                        principalTable: "RecommendationModels",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateIndex(
                name: "IX_ABTestGroups_IsActive_StartDate_EndDate",
                table: "ABTestGroups",
                columns: new[] { "IsActive", "StartDate", "EndDate" });

            migrationBuilder.CreateIndex(
                name: "IX_ProductSimilarities_ProductId1_Algorithm",
                table: "ProductSimilarities",
                columns: new[] { "ProductId1", "Algorithm" });

            migrationBuilder.CreateIndex(
                name: "IX_ProductSimilarities_ProductId2_Algorithm",
                table: "ProductSimilarities",
                columns: new[] { "ProductId2", "Algorithm" });

            migrationBuilder.CreateIndex(
                name: "IX_ProductSimilarities_SimilarityScore",
                table: "ProductSimilarities",
                column: "SimilarityScore");

            migrationBuilder.CreateIndex(
                name: "IX_RecommendationModels_Accuracy",
                table: "RecommendationModels",
                column: "Accuracy");

            migrationBuilder.CreateIndex(
                name: "IX_RecommendationModels_Algorithm_IsActive",
                table: "RecommendationModels",
                columns: new[] { "Algorithm", "IsActive" });

            migrationBuilder.CreateIndex(
                name: "IX_RecommendationResults_Algorithm",
                table: "RecommendationResults",
                column: "Algorithm");

            migrationBuilder.CreateIndex(
                name: "IX_RecommendationResults_ModelId",
                table: "RecommendationResults",
                column: "ModelId");

            migrationBuilder.CreateIndex(
                name: "IX_RecommendationResults_UserId_CreatedAt",
                table: "RecommendationResults",
                columns: new[] { "UserId", "CreatedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_UserABTestAssignments_ABTestGroupId",
                table: "UserABTestAssignments",
                column: "ABTestGroupId");

            migrationBuilder.CreateIndex(
                name: "IX_UserABTestAssignments_UserId",
                table: "UserABTestAssignments",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_UserABTestAssignments_UserId_ABTestGroupId",
                table: "UserABTestAssignments",
                columns: new[] { "UserId", "ABTestGroupId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_UserInteractions_ProductId_Type",
                table: "UserInteractions",
                columns: new[] { "ProductId", "Type" });

            migrationBuilder.CreateIndex(
                name: "IX_UserInteractions_SessionId",
                table: "UserInteractions",
                column: "SessionId");

            migrationBuilder.CreateIndex(
                name: "IX_UserInteractions_UserId_Timestamp",
                table: "UserInteractions",
                columns: new[] { "UserId", "Timestamp" });

            migrationBuilder.CreateIndex(
                name: "IX_UserPreferences_Score",
                table: "UserPreferences",
                column: "Score");

            migrationBuilder.CreateIndex(
                name: "IX_UserPreferences_UserId_PreferenceType",
                table: "UserPreferences",
                columns: new[] { "UserId", "PreferenceType" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ProductSimilarities");

            migrationBuilder.DropTable(
                name: "RecommendationResults");

            migrationBuilder.DropTable(
                name: "UserABTestAssignments");

            migrationBuilder.DropTable(
                name: "UserInteractions");

            migrationBuilder.DropTable(
                name: "UserPreferences");

            migrationBuilder.DropTable(
                name: "RecommendationModels");

            migrationBuilder.DropTable(
                name: "ABTestGroups");
        }
    }
}
