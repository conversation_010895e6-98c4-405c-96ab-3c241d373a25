using NafaPlace.Analytics.Application.DTOs;

namespace NafaPlace.Analytics.Application.Services;

public interface IReportService
{
    // Génération de rapports
    Task<ReportResultDto> GenerateReportAsync(ReportRequestDto request);
    Task<ReportResultDto> GenerateSalesReportAsync(AnalyticsFilterDto filters, string format = "pdf");
    Task<ReportResultDto> GenerateProductReportAsync(AnalyticsFilterDto filters, string format = "pdf");
    Task<ReportResultDto> GenerateCustomerReportAsync(AnalyticsFilterDto filters, string format = "pdf");
    Task<ReportResultDto> GenerateInventoryReportAsync(AnalyticsFilterDto filters, string format = "pdf");
    Task<ReportResultDto> GenerateFinancialReportAsync(AnalyticsFilterDto filters, string format = "pdf");
    
    // Rapports personnalisés
    Task<ReportResultDto> GenerateCustomReportAsync(CustomReportDefinitionDto definition, AnalyticsFilterDto filters);
    Task<List<CustomReportTemplateDto>> GetReportTemplatesAsync(int? sellerId = null);
    Task<bool> SaveReportTemplateAsync(CustomReportTemplateDto template);
    Task<bool> DeleteReportTemplateAsync(int templateId);
    
    // Rapports planifiés
    Task<bool> ScheduleReportAsync(ScheduledReportDto scheduledReport);
    Task<List<ScheduledReportDto>> GetScheduledReportsAsync(int? sellerId = null);
    Task<bool> UpdateScheduledReportAsync(ScheduledReportDto scheduledReport);
    Task<bool> DeleteScheduledReportAsync(int scheduledReportId);
    Task ProcessScheduledReportsAsync();
    
    // Exports de données
    Task<byte[]> ExportToExcelAsync(string dataType, AnalyticsFilterDto filters);
    Task<byte[]> ExportToCsvAsync(string dataType, AnalyticsFilterDto filters);
    Task<byte[]> ExportToPdfAsync(string dataType, AnalyticsFilterDto filters);
    Task<string> ExportToJsonAsync(string dataType, AnalyticsFilterDto filters);
    
    // Gestion des fichiers
    Task<List<ReportFileDto>> GetReportFilesAsync(int? sellerId = null, int days = 30);
    Task<byte[]> DownloadReportFileAsync(string reportId);
    Task<bool> DeleteReportFileAsync(string reportId);
    Task<int> CleanupOldReportsAsync(int daysToKeep = 30);
    
    // Partage et distribution
    Task<bool> ShareReportAsync(string reportId, List<string> recipients, string message = "");
    Task<bool> EmailReportAsync(string reportId, List<string> recipients, string subject, string message = "");
    Task<string> GenerateReportShareLinkAsync(string reportId, DateTime? expiresAt = null);
    
    // Historique et audit
    Task<List<ReportHistoryDto>> GetReportHistoryAsync(int? sellerId = null, int limit = 50);
    Task LogReportGenerationAsync(string reportType, int? sellerId, bool success, string? errorMessage = null);
    Task<Dictionary<string, object>> GetReportStatisticsAsync(int? sellerId = null);
}

public class CustomReportDefinitionDto
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<string> DataSources { get; set; } = new();
    public List<ReportSectionDto> Sections { get; set; } = new();
    public List<ReportChartDto> Charts { get; set; } = new();
    public ReportLayoutDto Layout { get; set; } = new();
    public Dictionary<string, object> Parameters { get; set; } = new();
}

public class CustomReportTemplateDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public int? SellerId { get; set; }
    public bool IsPublic { get; set; }
    public CustomReportDefinitionDto Definition { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class ScheduledReportDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string ReportType { get; set; } = string.Empty;
    public AnalyticsFilterDto Filters { get; set; } = new();
    public string Format { get; set; } = "pdf";
    public string Schedule { get; set; } = string.Empty; // Cron expression
    public List<string> Recipients { get; set; } = new();
    public bool IsActive { get; set; } = true;
    public int? SellerId { get; set; }
    public DateTime? LastRun { get; set; }
    public DateTime? NextRun { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class ReportFileDto
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Format { get; set; } = string.Empty;
    public long SizeBytes { get; set; }
    public DateTime GeneratedAt { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public string DownloadUrl { get; set; } = string.Empty;
    public int? SellerId { get; set; }
    public string GeneratedBy { get; set; } = string.Empty;
}

public class ReportHistoryDto
{
    public int Id { get; set; }
    public string ReportType { get; set; } = string.Empty;
    public string ReportName { get; set; } = string.Empty;
    public string Format { get; set; } = string.Empty;
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime GeneratedAt { get; set; }
    public double GenerationTimeMs { get; set; }
    public long? FileSizeBytes { get; set; }
    public int? SellerId { get; set; }
    public string GeneratedBy { get; set; } = string.Empty;
}

public class ReportSectionDto
{
    public string Name { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty; // table, chart, text, kpi
    public Dictionary<string, object> Configuration { get; set; } = new();
    public List<string> DataFields { get; set; } = new();
    public int Order { get; set; }
}

public class ReportChartDto
{
    public string Name { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty; // line, bar, pie, area
    public string DataSource { get; set; } = string.Empty;
    public List<string> XAxis { get; set; } = new();
    public List<string> YAxis { get; set; } = new();
    public Dictionary<string, object> Styling { get; set; } = new();
}

public class ReportLayoutDto
{
    public string PageSize { get; set; } = "A4";
    public string Orientation { get; set; } = "Portrait";
    public ReportHeaderDto Header { get; set; } = new();
    public ReportFooterDto Footer { get; set; } = new();
    public Dictionary<string, object> Styling { get; set; } = new();
}

public class ReportHeaderDto
{
    public string Title { get; set; } = string.Empty;
    public string Subtitle { get; set; } = string.Empty;
    public string LogoUrl { get; set; } = string.Empty;
    public bool ShowDate { get; set; } = true;
    public bool ShowPageNumbers { get; set; } = true;
}

public class ReportFooterDto
{
    public string Text { get; set; } = string.Empty;
    public bool ShowGeneratedBy { get; set; } = true;
    public bool ShowTimestamp { get; set; } = true;
}
