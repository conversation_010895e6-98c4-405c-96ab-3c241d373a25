@page "/payment/cancel"
@inject NavigationManager NavigationManager

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow-lg border-warning">
                <div class="card-header bg-warning text-dark text-center">
                    <h3><i class="fas fa-exclamation-triangle me-2"></i>Paiement Annulé</h3>
                </div>
                <div class="card-body text-center">
                    <div class="mb-4">
                        <i class="fas fa-times-circle text-warning" style="font-size: 4rem;"></i>
                    </div>
                    
                    <h4 class="text-warning mb-3">Votre paiement a été annulé</h4>
                    
                    @if (!string.IsNullOrEmpty(_orderId))
                    {
                        <div class="alert alert-warning">
                            <strong>Commande :</strong> #@_orderId.PadLeft(8, '0')<br>
                            <small>Votre commande est toujours en attente de paiement</small>
                        </div>
                    }
                    
                    <p class="mb-4">
                        Aucun montant n'a été débité de votre compte.
                        Vous pouvez reprendre le processus de paiement à tout moment.
                    </p>
                    
                    <div class="d-grid gap-2">
                        @if (!string.IsNullOrEmpty(_orderId))
                        {
                            <button class="btn btn-primary btn-lg" @onclick="RetryPayment">
                                <i class="fas fa-credit-card me-2"></i>
                                Reprendre le paiement
                            </button>
                        }
                        <button class="btn btn-outline-secondary" @onclick="ContinueShopping">
                            <i class="fas fa-shopping-bag me-2"></i>
                            Continuer mes achats
                        </button>
                        <button class="btn btn-outline-info" @onclick="ViewCart">
                            <i class="fas fa-shopping-cart me-2"></i>
                            Retour au panier
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private string _orderId = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        try
        {
            // Récupérer l'orderId depuis les paramètres de l'URL
            var uri = NavigationManager.ToAbsoluteUri(NavigationManager.Uri);
            var query = System.Web.HttpUtility.ParseQueryString(uri.Query);
            
            var orderIdValue = query["orderId"];
            if (!string.IsNullOrEmpty(orderIdValue))
            {
                _orderId = orderIdValue;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du traitement de l'annulation: {ex.Message}");
        }
    }

    private void RetryPayment()
    {
        if (!string.IsNullOrEmpty(_orderId))
        {
            NavigationManager.NavigateTo($"/payment/stripe/{_orderId}");
        }
        else
        {
            NavigationManager.NavigateTo("/cart");
        }
    }

    private void ContinueShopping()
    {
        NavigationManager.NavigateTo("/");
    }

    private void ViewCart()
    {
        NavigationManager.NavigateTo("/cart");
    }
}
