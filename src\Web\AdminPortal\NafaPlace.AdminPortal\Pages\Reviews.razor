@page "/reviews"
@using NafaPlace.AdminPortal.Models.Reviews
@using NafaPlace.AdminPortal.Services
@inject IReviewService ReviewService
@inject IJSRuntime JSRuntime

<h1 class="visually-hidden">Modération des Avis - NafaPlace Admin</h1>

<div class="container-fluid px-4">
    <h1 class="mt-4">Modération des Avis</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="/dashboard">Dashboard</a></li>
        <li class="breadcrumb-item active">Avis</li>
    </ol>

    <!-- Statistiques des avis -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="text-xs font-weight-bold text-uppercase mb-1">Total Avis</div>
                            <div class="h5 mb-0 font-weight-bold">@_reviewStats.TotalReviews</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-star fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-warning text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="text-xs font-weight-bold text-uppercase mb-1">En Attente</div>
                            <div class="h5 mb-0 font-weight-bold">@_reviewStats.PendingReviews</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-hourglass-half fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="text-xs font-weight-bold text-uppercase mb-1">Approuvés</div>
                            <div class="h5 mb-0 font-weight-bold">@_reviewStats.ApprovedReviews</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-danger text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="text-xs font-weight-bold text-uppercase mb-1">Rejetés</div>
                            <div class="h5 mb-0 font-weight-bold">@_reviewStats.RejectedReviews</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Actions rapides -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-bolt me-1"></i>
                    Actions Rapides
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <button class="btn btn-success w-100" @onclick="() => BulkApproveSelected()" 
                                    disabled="@(!_selectedReviews.Any())">
                                <i class="fas fa-check me-2"></i>Approuver Sélectionnés (@_selectedReviews.Count)
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-danger w-100" @onclick="() => BulkRejectSelected()" 
                                    disabled="@(!_selectedReviews.Any())">
                                <i class="fas fa-times me-2"></i>Rejeter Sélectionnés (@_selectedReviews.Count)
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-warning w-100" @onclick="() => FilterPendingReviews()">
                                <i class="fas fa-filter me-2"></i>Avis en Attente
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-info w-100" @onclick="() => ClearFilters()">
                                <i class="fas fa-refresh me-2"></i>Réinitialiser Filtres
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtres -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-filter me-1"></i>
            Filtres
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-2">
                    <label for="statusFilter" class="form-label">Statut</label>
                    <select class="form-select" id="statusFilter" @bind="_selectedStatus">
                        <option value="">Tous les statuts</option>
                        <option value="approved">Approuvé</option>
                        <option value="pending">En attente</option>
                        <option value="rejected">Rejeté</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="ratingFilter" class="form-label">Note</label>
                    <select class="form-select" id="ratingFilter" @bind="_selectedRating">
                        <option value="0">Toutes les notes</option>
                        <option value="5">5 étoiles</option>
                        <option value="4">4 étoiles</option>
                        <option value="3">3 étoiles</option>
                        <option value="2">2 étoiles</option>
                        <option value="1">1 étoile</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="verifiedFilter" class="form-label">Achat vérifié</label>
                    <select class="form-select" id="verifiedFilter" @bind="_verifiedPurchaseFilter">
                        <option value="">Tous</option>
                        <option value="true">Vérifié</option>
                        <option value="false">Non vérifié</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="dateFilter" class="form-label">Période</label>
                    <select class="form-select" id="dateFilter" @bind="_dateFilter">
                        <option value="">Toutes les dates</option>
                        <option value="today">Aujourd'hui</option>
                        <option value="week">Cette semaine</option>
                        <option value="month">Ce mois</option>
                        <option value="quarter">Ce trimestre</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="searchInput" class="form-label">Recherche</label>
                    <input type="text" class="form-control" id="searchInput" placeholder="Rechercher..." 
                           @bind="_searchTerm" @onkeyup="OnSearchKeyUp">
                </div>
            </div>
        </div>
    </div>

    <!-- Liste des avis -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <i class="fas fa-comments me-1"></i>
                Avis des clients (@_reviews.Count)
            </div>
            <div>
                <input type="checkbox" class="form-check-input me-2" id="selectAll" 
                       @onchange="ToggleSelectAll" checked="@_selectAll">
                <label for="selectAll" class="form-check-label">Tout sélectionner</label>
            </div>
        </div>
        <div class="card-body">
            @if (_loading)
            {
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                </div>
            }
            else if (_reviews.Any())
            {
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead>
                            <tr>
                                <th width="50">
                                    <input type="checkbox" class="form-check-input" @onchange="ToggleSelectAll" checked="@_selectAll">
                                </th>
                                <th>Produit</th>
                                <th>Client</th>
                                <th>Note</th>
                                <th>Commentaire</th>
                                <th>Date</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var review in _reviews)
                            {
                                <tr class="@(GetRowClass(review))">
                                    <td>
                                        <input type="checkbox" class="form-check-input" 
                                               @onchange="(e) => ToggleReviewSelection(review.Id, (bool)e.Value!)" 
                                               checked="@_selectedReviews.Contains(review.Id)">
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if (!string.IsNullOrEmpty(review.ProductImageUrl))
                                            {
                                                <img src="@review.ProductImageUrl" alt="@review.ProductName" 
                                                     class="me-2" style="width: 40px; height: 40px; object-fit: cover;">
                                            }
                                            <div>
                                                <strong>@review.ProductName</strong>
                                                <br><small class="text-muted">ID: @review.ProductId</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>@review.UserName</strong>
                                            <br><small class="text-muted">@review.UserId</small>
                                            @if (review.IsVerifiedPurchase)
                                            {
                                                <br><small class="text-success">
                                                    <i class="fas fa-check-circle me-1"></i>Achat vérifié
                                                </small>
                                            }
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @for (int i = 1; i <= 5; i++)
                                            {
                                                <i class="fas fa-star @(i <= review.Rating ? "text-warning" : "text-muted")"></i>
                                            }
                                            <span class="ms-2">(@review.Rating)</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            @if (!string.IsNullOrEmpty(review.Title))
                                            {
                                                <strong>@review.Title</strong><br>
                                            }
                                            <span>@(review.Comment.Length > 80 ? review.Comment.Substring(0, 80) + "..." : review.Comment)</span>
                                            @if (review.HelpfulCount > 0)
                                            {
                                                <br><small class="text-info">
                                                    <i class="fas fa-thumbs-up me-1"></i>@review.HelpfulCount utile(s)
                                                </small>
                                            }
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            @review.CreatedAt.ToString("dd/MM/yyyy")
                                            <br><small class="text-muted">@review.CreatedAt.ToString("HH:mm")</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge @GetStatusBadgeClass(review.IsApproved)">
                                            @GetStatusText(review.IsApproved)
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            @if (review.IsApproved != true)
                                            {
                                                <button class="btn btn-sm btn-success" 
                                                        @onclick="() => ApproveReview(review.Id)" 
                                                        title="Approuver">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            }
                                            @if (review.IsApproved != false)
                                            {
                                                <button class="btn btn-sm btn-danger" 
                                                        @onclick="() => RejectReview(review.Id)" 
                                                        title="Rejeter">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            }
                                            <button class="btn btn-sm btn-outline-primary" 
                                                    @onclick="() => ViewReviewDetails(review)" 
                                                    title="Voir détails">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger"
                                                    @onclick="() => DeleteReview(review.Id)"
                                                    title="Supprimer"
                                                    data-bs-toggle="tooltip">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if (_totalPages > 1)
                {
                    <nav aria-label="Reviews pagination" class="mt-3">
                        <ul class="pagination justify-content-center">
                            @if (_currentPage > 1)
                            {
                                <li class="page-item">
                                    <button class="page-link" @onclick="() => ChangePage(_currentPage - 1)">
                                        Précédent
                                    </button>
                                </li>
                            }
                            
                            @for (int i = 1; i <= _totalPages; i++)
                            {
                                var pageNumber = i;
                                <li class="page-item @(_currentPage == pageNumber ? "active" : "")">
                                    <button class="page-link" @onclick="() => ChangePage(pageNumber)">
                                        @pageNumber
                                    </button>
                                </li>
                            }
                            
                            @if (_currentPage < _totalPages)
                            {
                                <li class="page-item">
                                    <button class="page-link" @onclick="() => ChangePage(_currentPage + 1)">
                                        Suivant
                                    </button>
                                </li>
                            }
                        </ul>
                    </nav>
                }
            }
            else
            {
                <div class="text-center py-4">
                    <i class="fas fa-star fa-3x text-muted mb-3"></i>
                    <h5>Aucun avis trouvé</h5>
                    <p class="text-muted">Aucun avis ne correspond aux critères de recherche.</p>
                </div>
            }
        </div>
    </div>
</div>

<!-- Modal pour les détails de l'avis -->
@if (_selectedReviewForDetails != null)
{
    <div class="modal fade show" style="display: block; background-color: rgba(0,0,0,0.5);" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-eye me-2"></i>Détails de l'avis
                    </h5>
                    <button type="button" class="btn-close" @onclick="CloseReviewDetails"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <!-- Informations du produit -->
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-header">
                                    <i class="fas fa-box me-2"></i>Produit
                                </div>
                                <div class="card-body">
                                    @if (!string.IsNullOrEmpty(_selectedReviewForDetails.ProductImageUrl))
                                    {
                                        <img src="@_selectedReviewForDetails.ProductImageUrl"
                                             alt="@_selectedReviewForDetails.ProductName"
                                             class="img-fluid mb-2"
                                             style="max-height: 150px; object-fit: cover;" />
                                    }
                                    <h6>@_selectedReviewForDetails.ProductName</h6>
                                    <p class="text-muted mb-1">
                                        <i class="fas fa-store me-1"></i>
                                        Vendeur: @_selectedReviewForDetails.SellerName
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Informations du client -->
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-header">
                                    <i class="fas fa-user me-2"></i>Client
                                </div>
                                <div class="card-body">
                                    <h6>@_selectedReviewForDetails.UserName</h6>
                                    <p class="text-muted mb-1">
                                        <i class="fas fa-envelope me-1"></i>
                                        @_selectedReviewForDetails.UserEmail
                                    </p>
                                    @if (_selectedReviewForDetails.IsVerifiedPurchase)
                                    {
                                        <span class="badge bg-success">
                                            <i class="fas fa-check-circle me-1"></i>Achat vérifié
                                        </span>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Détails de l'avis -->
                    <div class="card">
                        <div class="card-header">
                            <i class="fas fa-comment me-2"></i>Avis
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <strong>Note:</strong>
                                    <div class="mt-1">
                                        @for (int i = 1; i <= 5; i++)
                                        {
                                            <i class="fas fa-star @(i <= _selectedReviewForDetails.Rating ? "text-warning" : "text-muted")"></i>
                                        }
                                        <span class="ms-2">(@_selectedReviewForDetails.Rating/5)</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <strong>Date:</strong>
                                    <div class="mt-1">@_selectedReviewForDetails.CreatedAt.ToString("dd/MM/yyyy HH:mm")</div>
                                </div>
                            </div>

                            @if (!string.IsNullOrEmpty(_selectedReviewForDetails.Title))
                            {
                                <div class="mb-3">
                                    <strong>Titre:</strong>
                                    <div class="mt-1">@_selectedReviewForDetails.Title</div>
                                </div>
                            }

                            <div class="mb-3">
                                <strong>Commentaire:</strong>
                                <div class="mt-1 p-3 bg-light rounded">
                                    @_selectedReviewForDetails.Comment
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <strong>Statut:</strong>
                                    <div class="mt-1">
                                        <span class="badge @GetStatusBadgeClass(_selectedReviewForDetails.IsApproved)">
                                            @GetStatusText(_selectedReviewForDetails.IsApproved)
                                        </span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <strong>Interactions:</strong>
                                    <div class="mt-1">
                                        <span class="badge bg-info me-2">
                                            <i class="fas fa-thumbs-up me-1"></i>@_selectedReviewForDetails.HelpfulCount utiles
                                        </span>
                                        @if (_selectedReviewForDetails.ReportCount > 0)
                                        {
                                            <span class="badge bg-warning">
                                                <i class="fas fa-flag me-1"></i>@_selectedReviewForDetails.ReportCount signalements
                                            </span>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    @if (_selectedReviewForDetails.IsApproved != true)
                    {
                        <button class="btn btn-success" @onclick="() => ApproveReviewFromModal(_selectedReviewForDetails.Id)">
                            <i class="fas fa-check me-2"></i>Approuver
                        </button>
                    }
                    @if (_selectedReviewForDetails.IsApproved != false)
                    {
                        <button class="btn btn-danger" @onclick="() => RejectReviewFromModal(_selectedReviewForDetails.Id)">
                            <i class="fas fa-times me-2"></i>Rejeter
                        </button>
                    }
                    <button class="btn btn-outline-danger" @onclick="() => DeleteReviewFromModal(_selectedReviewForDetails.Id)">
                        <i class="fas fa-trash me-2"></i>Supprimer
                    </button>
                    <button type="button" class="btn btn-secondary" @onclick="CloseReviewDetails">
                        Fermer
                    </button>
                </div>
            </div>
        </div>
    </div>
}

@code {
    private List<AdminReviewDto> _reviews = new();
    private AdminReviewStatsDto _reviewStats = new();
    private bool _loading = true;
    
    // Filters
    private string _selectedStatus = string.Empty;
    private int _selectedRating = 0;
    private string _verifiedPurchaseFilter = string.Empty;
    private string _dateFilter = string.Empty;
    private string _searchTerm = string.Empty;
    
    // Selection
    private HashSet<int> _selectedReviews = new();
    private AdminReviewDto? _selectedReviewForDetails = null;
    private bool _selectAll = false;
    
    // Pagination
    private int _currentPage = 1;
    private int _totalPages = 1;
    private const int _pageSize = 15;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        _loading = true;
        try
        {
            await Task.WhenAll(
                LoadReviewStats(),
                LoadReviews()
            );
        }
        finally
        {
            _loading = false;
        }
    }

    private async Task LoadReviewStats()
    {
        try
        {
            _reviewStats = await ReviewService.GetAdminReviewStatsAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des statistiques: {ex.Message}");
        }
    }

    private async Task LoadReviews()
    {
        try
        {
            var request = new AdminReviewFilterRequest
            {
                Status = !string.IsNullOrEmpty(_selectedStatus) ? _selectedStatus : null,
                Rating = _selectedRating > 0 ? _selectedRating : null,
                IsVerifiedPurchase = !string.IsNullOrEmpty(_verifiedPurchaseFilter) ? bool.Parse(_verifiedPurchaseFilter) : null,
                DateFilter = !string.IsNullOrEmpty(_dateFilter) ? _dateFilter : null,
                SearchTerm = !string.IsNullOrEmpty(_searchTerm) ? _searchTerm : null,
                Page = _currentPage,
                PageSize = _pageSize
            };

            var response = await ReviewService.GetAdminReviewsAsync(request);
            _reviews = response.Reviews;
            _totalPages = response.TotalPages;
            
            // Reset selection when data changes
            _selectedReviews.Clear();
            _selectAll = false;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des avis: {ex.Message}");
        }
    }

    private async Task OnSearchKeyUp(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            _currentPage = 1;
            await LoadReviews();
        }
    }

    private async Task ChangePage(int page)
    {
        _currentPage = page;
        await LoadReviews();
    }

    private void ToggleSelectAll(ChangeEventArgs e)
    {
        _selectAll = (bool)e.Value!;
        if (_selectAll)
        {
            _selectedReviews = _reviews.Select(r => r.Id).ToHashSet();
        }
        else
        {
            _selectedReviews.Clear();
        }
    }

    private void ToggleReviewSelection(int reviewId, bool selected)
    {
        if (selected)
        {
            _selectedReviews.Add(reviewId);
        }
        else
        {
            _selectedReviews.Remove(reviewId);
        }
        
        _selectAll = _selectedReviews.Count == _reviews.Count;
    }

    private async Task ApproveReview(int reviewId)
    {
        var success = await ReviewService.ApproveReviewAsync(reviewId);
        if (success)
        {
            await LoadData();
        }
    }

    private async Task RejectReview(int reviewId)
    {
        var success = await ReviewService.RejectReviewAsync(reviewId);
        if (success)
        {
            await LoadData();
        }
    }

    private async Task DeleteReview(int reviewId)
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", "Êtes-vous sûr de vouloir supprimer cet avis ?"))
        {
            var success = await ReviewService.DeleteReviewAsync(reviewId);
            if (success)
            {
                await LoadData();
            }
        }
    }

    private async Task BulkApproveSelected()
    {
        if (_selectedReviews.Any())
        {
            var success = await ReviewService.BulkApproveReviewsAsync(_selectedReviews.ToList());
            if (success)
            {
                await LoadData();
            }
        }
    }

    private async Task BulkRejectSelected()
    {
        if (_selectedReviews.Any())
        {
            var success = await ReviewService.BulkRejectReviewsAsync(_selectedReviews.ToList());
            if (success)
            {
                await LoadData();
            }
        }
    }

    private async Task FilterPendingReviews()
    {
        _selectedStatus = "pending";
        _currentPage = 1;
        await LoadReviews();
    }

    private async Task ClearFilters()
    {
        _selectedStatus = string.Empty;
        _selectedRating = 0;
        _verifiedPurchaseFilter = string.Empty;
        _dateFilter = string.Empty;
        _searchTerm = string.Empty;
        _currentPage = 1;
        await LoadReviews();
    }

    private void ViewReviewDetails(AdminReviewDto review)
    {
        _selectedReviewForDetails = review;
        StateHasChanged();
    }

    private void CloseReviewDetails()
    {
        _selectedReviewForDetails = null;
        StateHasChanged();
    }

    private async Task ApproveReviewFromModal(int reviewId)
    {
        await ApproveReview(reviewId);
        CloseReviewDetails();
    }

    private async Task RejectReviewFromModal(int reviewId)
    {
        await RejectReview(reviewId);
        CloseReviewDetails();
    }

    private async Task DeleteReviewFromModal(int reviewId)
    {
        await DeleteReview(reviewId);
        CloseReviewDetails();
    }

    private string GetStatusBadgeClass(bool? isApproved)
    {
        return isApproved switch
        {
            true => "bg-success",
            false => "bg-danger",
            null => "bg-warning"
        };
    }

    private string GetStatusText(bool? isApproved)
    {
        return isApproved switch
        {
            true => "Approuvé",
            false => "Rejeté",
            null => "En attente"
        };
    }

    private string GetRowClass(AdminReviewDto review)
    {
        return review.IsApproved switch
        {
            null => "table-warning",
            false => "table-danger",
            _ => ""
        };
    }
}
