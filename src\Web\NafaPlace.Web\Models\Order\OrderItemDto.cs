namespace NafaPlace.Web.Models.Order
{
    public class OrderItemDto
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public decimal UnitPrice { get; set; }
        public int Quantity { get; set; }
        public string ImageUrl { get; set; } = string.Empty;

        // Propriétés supplémentaires pour compatibilité
        public string ProductImageUrl => ImageUrl;
        public string ProductDescription { get; set; } = string.Empty;
    }
}