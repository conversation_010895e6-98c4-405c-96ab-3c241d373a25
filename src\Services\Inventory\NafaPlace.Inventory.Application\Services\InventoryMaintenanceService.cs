using Microsoft.Extensions.Logging;
using NafaPlace.Inventory.Application.Interfaces;
using NafaPlace.Inventory.Domain.Enums;

namespace NafaPlace.Inventory.Application.Services;

public interface IInventoryMaintenanceService
{
    Task CleanupExpiredReservationsAsync();
    Task RecalculateStockLevelsAsync();
    Task ProcessPendingAlertsAsync();
    Task ArchiveOldMovementsAsync(int daysToKeep = 90);
}

public class InventoryMaintenanceService : IInventoryMaintenanceService
{
    private readonly IInventoryRepository _inventoryRepository;
    private readonly IProductStockRepository _productStockRepository;
    private readonly INotificationClient _notificationClient;
    private readonly ILogger<InventoryMaintenanceService> _logger;

    public InventoryMaintenanceService(
        IInventoryRepository inventoryRepository,
        IProductStockRepository productStockRepository,
        INotificationClient notificationClient,
        ILogger<InventoryMaintenanceService> logger)
    {
        _inventoryRepository = inventoryRepository;
        _productStockRepository = productStockRepository;
        _notificationClient = notificationClient;
        _logger = logger;
    }

    public async Task CleanupExpiredReservationsAsync()
    {
        try
        {
            _logger.LogInformation("Starting cleanup of expired reservations");
            
            var cleanedCount = await _inventoryRepository.CleanupExpiredReservationsAsync();
            
            _logger.LogInformation("Cleaned up {Count} expired reservations", cleanedCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during expired reservations cleanup");
        }
    }

    public async Task RecalculateStockLevelsAsync()
    {
        try
        {
            _logger.LogInformation("Starting stock levels recalculation");
            
            await _inventoryRepository.RecalculateStockLevelsAsync();
            
            _logger.LogInformation("Stock levels recalculation completed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during stock levels recalculation");
        }
    }

    public async Task ProcessPendingAlertsAsync()
    {
        try
        {
            _logger.LogInformation("Processing pending stock alerts");
            
            // Récupérer les alertes actives non traitées
            var alerts = await _inventoryRepository.GetActiveAlertsAsync();
            
            foreach (var alert in alerts.Where(a => !a.IsAcknowledged))
            {
                try
                {
                    // Envoyer des notifications pour les alertes non traitées
                    if (alert.Type == AlertType.LowStock)
                    {
                        await _notificationClient.NotifyLowStockAsync(
                            alert.SellerId.ToString(),
                            alert.ProductName,
                            alert.CurrentStock);
                    }
                    else if (alert.Type == AlertType.OutOfStock)
                    {
                        await _notificationClient.NotifyOutOfStockAsync(
                            alert.SellerId.ToString(),
                            alert.ProductName);
                    }
                    
                    _logger.LogDebug("Processed alert {AlertId} for product {ProductName}", 
                        alert.Id, alert.ProductName);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to process alert {AlertId}", alert.Id);
                }
            }
            
            _logger.LogInformation("Processed {Count} pending alerts", alerts.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during pending alerts processing");
        }
    }

    public async Task ArchiveOldMovementsAsync(int daysToKeep = 90)
    {
        try
        {
            _logger.LogInformation("Starting archival of old stock movements (older than {Days} days)", daysToKeep);
            
            // Pour l'instant, on ne fait que logger
            // Dans une implémentation complète, on déplacerait les anciens mouvements vers une table d'archive
            _logger.LogInformation("Old movements archival completed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during old movements archival");
        }
    }
}


