namespace NafaPlace.Web.Models.Auth;

public class AuthResponse
{
    public bool Success { get; set; } = false;
    public string Message { get; set; } = string.Empty;
    public string AccessToken { get; set; } = string.Empty;
    public string RefreshToken { get; set; } = string.Empty;
    public DateTime ExpiresAt { get; set; } = DateTime.MinValue;
    public UserDto User { get; set; } = new UserDto();
    public string? Token { get; set; }
}
