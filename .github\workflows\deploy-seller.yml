name: Deploy Seller Portal to Fly.io Test

on:
  push:
    branches: [ main ]
    paths:
      - 'src/Web/SellerPortal/**'
      - 'Dockerfile.seller'
      - 'fly-seller.toml'
      - '.github/workflows/deploy-seller.yml'
  workflow_dispatch:

jobs:
  deploy:
    name: Deploy Seller Portal
    runs-on: ubuntu-latest
    environment: staging

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '9.0.x'

    - name: Restore dependencies
      run: dotnet restore

    - name: Build
      run: dotnet build --no-restore --configuration Release

    - name: Test
      run: dotnet test --no-build --verbosity normal --configuration Release

    - name: Setup Fly CLI
      uses: superfly/flyctl-actions/setup-flyctl@master

    - name: Create Fly.io app if it doesn't exist
      run: |
        if ! flyctl apps list | grep -q "nafaplace-seller-test"; then
          echo "Creating nafaplace-seller-test app..."
          flyctl apps create nafaplace-seller-test --org personal
        else
          echo "App nafaplace-seller-test already exists"
        fi
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

    - name: Deploy to Fly.io
      run: flyctl deploy --config fly-seller.toml --dockerfile Dockerfile.seller
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}
