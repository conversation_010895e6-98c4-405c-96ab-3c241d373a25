using Microsoft.Extensions.Logging;
using NafaPlace.Notification.Application.DTOs;

namespace NafaPlace.Notification.Application.Services;

public class NotificationIntegrationService : INotificationIntegrationService
{
    private readonly INotificationService _notificationService;
    private readonly IRealTimeNotificationService _realTimeService;
    private readonly IPushNotificationService _pushService;
    private readonly IEmailService _emailService;
    private readonly ILogger<NotificationIntegrationService> _logger;

    public NotificationIntegrationService(
        INotificationService notificationService,
        IRealTimeNotificationService realTimeService,
        IPushNotificationService pushService,
        IEmailService emailService,
        ILogger<NotificationIntegrationService> logger)
    {
        _notificationService = notificationService;
        _realTimeService = realTimeService;
        _pushService = pushService;
        _emailService = emailService;
        _logger = logger;
    }

    // Intégrations avec le service Order
    public async Task HandleOrderCreatedAsync(int orderId, string userId, decimal amount, List<OrderItemDto> items)
    {
        try
        {
            _logger.LogInformation("Traitement de la création de commande {OrderId} pour {UserId}", orderId, userId);

            // Notification in-app
            var inAppNotification = new CreateNotificationDto
            {
                Title = "Commande créée",
                Message = $"Votre commande #{orderId} a été créée avec succès pour un montant de {amount:N0} GNF",
                Type = NotificationType.OrderCreated,
                Priority = NotificationPriority.High,
                RecipientId = userId,
                RecipientType = "User",
                Channels = new List<NotificationChannel> { NotificationChannel.InApp, NotificationChannel.WebSocket },
                Data = new Dictionary<string, object>
                {
                    ["orderId"] = orderId,
                    ["amount"] = amount,
                    ["itemCount"] = items.Count
                },
                ActionUrl = $"/orders/{orderId}"
            };

            await _notificationService.CreateNotificationAsync(inAppNotification);

            // Notification temps réel
            var realTimeNotification = new RealTimeNotificationDto
            {
                Id = Guid.NewGuid().ToString(),
                Title = "Commande créée",
                Message = $"Commande #{orderId} créée avec succès",
                Type = "OrderCreated",
                Priority = "High",
                Data = new Dictionary<string, object>
                {
                    ["orderId"] = orderId,
                    ["amount"] = amount
                },
                ActionUrl = $"/orders/{orderId}",
                RequiresAction = true
            };

            await _realTimeService.SendToUserAsync(userId, realTimeNotification);

            // Notification push
            await _pushService.SendOrderPushAsync(userId, orderId, "créée", amount);

            // Email de confirmation
            await _emailService.SendOrderConfirmationAsync(await GetUserEmailAsync(userId), orderId, amount, items);

            _logger.LogInformation("Notifications de création de commande envoyées pour {OrderId}", orderId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du traitement de la création de commande {OrderId}", orderId);
        }
    }

    public async Task HandleOrderStatusUpdatedAsync(int orderId, string userId, string status, string? trackingNumber = null)
    {
        try
        {
            _logger.LogInformation("Traitement de la mise à jour de statut de commande {OrderId}: {Status}", orderId, status);

            var statusMessages = new Dictionary<string, string>
            {
                ["confirmed"] = "confirmée",
                ["processing"] = "en cours de traitement",
                ["shipped"] = "expédiée",
                ["delivered"] = "livrée",
                ["cancelled"] = "annulée"
            };

            var statusMessage = statusMessages.GetValueOrDefault(status, status);
            var priority = status == "delivered" ? NotificationPriority.High : NotificationPriority.Normal;

            // Notification in-app
            var notification = new CreateNotificationDto
            {
                Title = "Mise à jour de commande",
                Message = $"Votre commande #{orderId} est maintenant {statusMessage}",
                Type = NotificationType.OrderUpdated,
                Priority = priority,
                RecipientId = userId,
                RecipientType = "User",
                Channels = new List<NotificationChannel> { NotificationChannel.InApp, NotificationChannel.WebSocket, NotificationChannel.Push },
                Data = new Dictionary<string, object>
                {
                    ["orderId"] = orderId,
                    ["status"] = status,
                    ["trackingNumber"] = trackingNumber ?? ""
                },
                ActionUrl = $"/orders/{orderId}"
            };

            await _notificationService.CreateNotificationAsync(notification);

            // Notification temps réel
            await _realTimeService.SendOrderUpdateAsync(userId, orderId, status, new Dictionary<string, object>
            {
                ["trackingNumber"] = trackingNumber ?? ""
            });

            // Email de mise à jour
            await _emailService.SendOrderStatusUpdateAsync(await GetUserEmailAsync(userId), orderId, statusMessage, trackingNumber);

            _logger.LogInformation("Notifications de mise à jour de commande envoyées pour {OrderId}", orderId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du traitement de la mise à jour de commande {OrderId}", orderId);
        }
    }

    // Intégrations avec le service Payment
    public async Task HandlePaymentCompletedAsync(int orderId, string userId, decimal amount, string paymentMethod)
    {
        try
        {
            _logger.LogInformation("Traitement du paiement complété pour commande {OrderId}", orderId);

            // Notification in-app
            var notification = new CreateNotificationDto
            {
                Title = "Paiement confirmé",
                Message = $"Votre paiement de {amount:N0} GNF a été confirmé pour la commande #{orderId}",
                Type = NotificationType.PaymentReceived,
                Priority = NotificationPriority.High,
                RecipientId = userId,
                RecipientType = "User",
                Channels = new List<NotificationChannel> { NotificationChannel.InApp, NotificationChannel.WebSocket, NotificationChannel.Push },
                Data = new Dictionary<string, object>
                {
                    ["orderId"] = orderId,
                    ["amount"] = amount,
                    ["paymentMethod"] = paymentMethod
                },
                ActionUrl = $"/orders/{orderId}"
            };

            await _notificationService.CreateNotificationAsync(notification);

            // Notification temps réel
            await _realTimeService.SendPaymentUpdateAsync(userId, orderId, "completed", new Dictionary<string, object>
            {
                ["amount"] = amount,
                ["paymentMethod"] = paymentMethod
            });

            // Notification push
            await _pushService.SendPaymentPushAsync(userId, orderId, "completed", amount);

            // Email de confirmation
            await _emailService.SendPaymentConfirmationAsync(await GetUserEmailAsync(userId), orderId, amount, paymentMethod);

            _logger.LogInformation("Notifications de paiement complété envoyées pour {OrderId}", orderId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du traitement du paiement complété {OrderId}", orderId);
        }
    }

    public async Task HandlePaymentFailedAsync(int orderId, string userId, decimal amount, string reason)
    {
        try
        {
            _logger.LogInformation("Traitement de l'échec de paiement pour commande {OrderId}", orderId);

            // Notification in-app
            var notification = new CreateNotificationDto
            {
                Title = "Problème de paiement",
                Message = $"Le paiement de {amount:N0} GNF pour la commande #{orderId} a échoué. Veuillez réessayer.",
                Type = NotificationType.PaymentFailed,
                Priority = NotificationPriority.High,
                RecipientId = userId,
                RecipientType = "User",
                Channels = new List<NotificationChannel> { NotificationChannel.InApp, NotificationChannel.WebSocket, NotificationChannel.Push },
                Data = new Dictionary<string, object>
                {
                    ["orderId"] = orderId,
                    ["amount"] = amount,
                    ["reason"] = reason
                },
                ActionUrl = $"/orders/{orderId}/payment"
            };

            await _notificationService.CreateNotificationAsync(notification);

            // Notification push
            await _pushService.SendPaymentPushAsync(userId, orderId, "failed", amount);

            _logger.LogInformation("Notifications d'échec de paiement envoyées pour {OrderId}", orderId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du traitement de l'échec de paiement {OrderId}", orderId);
        }
    }

    // Intégrations avec le service Catalog
    public async Task HandleProductApprovedAsync(int productId, string sellerId, string productName)
    {
        try
        {
            _logger.LogInformation("Traitement de l'approbation du produit {ProductId} pour vendeur {SellerId}", productId, sellerId);

            // Notification in-app pour le vendeur
            var notification = new CreateNotificationDto
            {
                Title = "Produit approuvé",
                Message = $"Votre produit '{productName}' a été approuvé et est maintenant visible sur la marketplace",
                Type = NotificationType.ProductApproved,
                Priority = NotificationPriority.Normal,
                RecipientId = sellerId,
                RecipientType = "Seller",
                Channels = new List<NotificationChannel> { NotificationChannel.InApp, NotificationChannel.WebSocket, NotificationChannel.Push },
                Data = new Dictionary<string, object>
                {
                    ["productId"] = productId,
                    ["productName"] = productName
                },
                ActionUrl = $"/seller/products/{productId}"
            };

            await _notificationService.CreateNotificationAsync(notification);

            // Notification temps réel
            var realTimeNotification = new RealTimeNotificationDto
            {
                Id = Guid.NewGuid().ToString(),
                Title = "Produit approuvé",
                Message = $"'{productName}' est maintenant en ligne",
                Type = "ProductApproved",
                Priority = "Normal",
                Data = new Dictionary<string, object>
                {
                    ["productId"] = productId,
                    ["productName"] = productName
                },
                ActionUrl = $"/seller/products/{productId}"
            };

            await _realTimeService.SendToUserAsync(sellerId, realTimeNotification);

            _logger.LogInformation("Notifications d'approbation de produit envoyées pour {ProductId}", productId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du traitement de l'approbation du produit {ProductId}", productId);
        }
    }

    public async Task HandleProductRejectedAsync(int productId, string sellerId, string productName, string reason)
    {
        try
        {
            _logger.LogInformation("Traitement du rejet du produit {ProductId} pour vendeur {SellerId}", productId, sellerId);

            // Notification in-app pour le vendeur
            var notification = new CreateNotificationDto
            {
                Title = "Produit rejeté",
                Message = $"Votre produit '{productName}' a été rejeté. Raison: {reason}",
                Type = NotificationType.ProductRejected,
                Priority = NotificationPriority.High,
                RecipientId = sellerId,
                RecipientType = "Seller",
                Channels = new List<NotificationChannel> { NotificationChannel.InApp, NotificationChannel.WebSocket, NotificationChannel.Push },
                Data = new Dictionary<string, object>
                {
                    ["productId"] = productId,
                    ["productName"] = productName,
                    ["reason"] = reason
                },
                ActionUrl = $"/seller/products/{productId}"
            };

            await _notificationService.CreateNotificationAsync(notification);

            _logger.LogInformation("Notifications de rejet de produit envoyées pour {ProductId}", productId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du traitement du rejet du produit {ProductId}", productId);
        }
    }

    // Intégrations avec le service Inventory
    public async Task HandleStockLowAsync(int productId, string sellerId, string productName, int currentStock, int threshold = 10)
    {
        try
        {
            _logger.LogInformation("Traitement de l'alerte de stock faible pour produit {ProductId}", productId);

            var priority = currentStock == 0 ? NotificationPriority.Critical : NotificationPriority.High;
            var title = currentStock == 0 ? "Rupture de stock" : "Stock faible";
            var message = currentStock == 0 
                ? $"Le produit '{productName}' est en rupture de stock"
                : $"Stock faible pour '{productName}': {currentStock} unités restantes";

            // Notification in-app pour le vendeur
            var notification = new CreateNotificationDto
            {
                Title = title,
                Message = message,
                Type = currentStock == 0 ? NotificationType.ProductOutOfStock : NotificationType.ProductLowStock,
                Priority = priority,
                RecipientId = sellerId,
                RecipientType = "Seller",
                Channels = new List<NotificationChannel> { NotificationChannel.InApp, NotificationChannel.WebSocket, NotificationChannel.Push },
                Data = new Dictionary<string, object>
                {
                    ["productId"] = productId,
                    ["productName"] = productName,
                    ["currentStock"] = currentStock,
                    ["threshold"] = threshold
                },
                ActionUrl = $"/seller/inventory"
            };

            await _notificationService.CreateNotificationAsync(notification);

            // Notification temps réel
            await _realTimeService.SendStockAlertAsync(sellerId, productId, productName, currentStock);

            // Notification push
            await _pushService.SendStockAlertPushAsync(sellerId, productName, currentStock);

            // Email d'alerte
            await _emailService.SendStockAlertAsync(await GetSellerEmailAsync(sellerId), productName, currentStock);

            _logger.LogInformation("Notifications d'alerte de stock envoyées pour {ProductId}", productId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du traitement de l'alerte de stock {ProductId}", productId);
        }
    }

    // Intégrations avec le service Review
    public async Task HandleNewReviewAsync(int productId, string sellerId, string productName, int rating, string? reviewText = null)
    {
        try
        {
            _logger.LogInformation("Traitement du nouvel avis pour produit {ProductId}", productId);

            var ratingText = rating switch
            {
                5 => "excellent (5⭐)",
                4 => "très bon (4⭐)",
                3 => "bon (3⭐)",
                2 => "moyen (2⭐)",
                1 => "faible (1⭐)",
                _ => $"{rating}⭐"
            };

            // Notification in-app pour le vendeur
            var notification = new CreateNotificationDto
            {
                Title = "Nouvel avis client",
                Message = $"Nouvel avis {ratingText} pour '{productName}'",
                Type = NotificationType.NewReview,
                Priority = NotificationPriority.Normal,
                RecipientId = sellerId,
                RecipientType = "Seller",
                Channels = new List<NotificationChannel> { NotificationChannel.InApp, NotificationChannel.WebSocket, NotificationChannel.Push },
                Data = new Dictionary<string, object>
                {
                    ["productId"] = productId,
                    ["productName"] = productName,
                    ["rating"] = rating,
                    ["reviewText"] = reviewText ?? ""
                },
                ActionUrl = $"/seller/reviews"
            };

            await _notificationService.CreateNotificationAsync(notification);

            // Notification push
            await _pushService.SendReviewPushAsync(sellerId, productName, rating, reviewText);

            _logger.LogInformation("Notifications de nouvel avis envoyées pour {ProductId}", productId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du traitement du nouvel avis {ProductId}", productId);
        }
    }

    // Intégrations avec le service User
    public async Task HandleUserRegisteredAsync(string userId, string userName, string email)
    {
        try
        {
            _logger.LogInformation("Traitement de l'inscription utilisateur {UserId}", userId);

            // Email de bienvenue
            var activationLink = $"https://nafaplace.com/activate?token={Guid.NewGuid()}";
            await _emailService.SendWelcomeEmailAsync(email, userName, activationLink);

            // Notification push de bienvenue (si l'utilisateur s'abonne)
            await _pushService.SendWelcomePushAsync(userId, userName);

            // Notification in-app de bienvenue
            var notification = new CreateNotificationDto
            {
                Title = $"Bienvenue {userName} !",
                Message = "Merci de vous être inscrit sur NafaPlace. Découvrez nos produits et commencez vos achats !",
                Type = NotificationType.Welcome,
                Priority = NotificationPriority.Normal,
                RecipientId = userId,
                RecipientType = "User",
                Channels = new List<NotificationChannel> { NotificationChannel.InApp },
                Data = new Dictionary<string, object>
                {
                    ["userName"] = userName,
                    ["registrationDate"] = DateTime.UtcNow
                },
                ActionUrl = "/catalog"
            };

            await _notificationService.CreateNotificationAsync(notification);

            _logger.LogInformation("Notifications de bienvenue envoyées pour {UserId}", userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du traitement de l'inscription {UserId}", userId);
        }
    }

    // Notifications système
    public async Task HandleSystemMaintenanceAsync(string message, DateTime? scheduledAt = null)
    {
        try
        {
            _logger.LogInformation("Traitement de la notification de maintenance système");

            // Notification temps réel à tous les utilisateurs
            await _realTimeService.SendSystemMaintenanceAsync(message, scheduledAt);

            // Notification in-app à tous les utilisateurs connectés
            var notification = new CreateNotificationDto
            {
                Title = "Maintenance système",
                Message = message,
                Type = NotificationType.SystemAlert,
                Priority = NotificationPriority.High,
                RecipientId = "all",
                RecipientType = "System",
                Channels = new List<NotificationChannel> { NotificationChannel.InApp, NotificationChannel.WebSocket },
                Data = new Dictionary<string, object>
                {
                    ["scheduledAt"] = scheduledAt ?? DateTime.UtcNow,
                    ["type"] = "maintenance"
                }
            };

            // Note: Pour une vraie implémentation, il faudrait récupérer tous les utilisateurs actifs
            _logger.LogInformation("Notification de maintenance système envoyée");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du traitement de la maintenance système");
        }
    }

    // Méthodes d'aide privées
    private async Task<string> GetUserEmailAsync(string userId)
    {
        // Simuler la récupération de l'email utilisateur
        await Task.Delay(10);
        return $"user{userId}@nafaplace.com";
    }

    private async Task<string> GetSellerEmailAsync(string sellerId)
    {
        // Simuler la récupération de l'email vendeur
        await Task.Delay(10);
        return $"seller{sellerId}@nafaplace.com";
    }
}

public interface INotificationIntegrationService
{
    // Intégrations Order
    Task HandleOrderCreatedAsync(int orderId, string userId, decimal amount, List<OrderItemDto> items);
    Task HandleOrderStatusUpdatedAsync(int orderId, string userId, string status, string? trackingNumber = null);
    
    // Intégrations Payment
    Task HandlePaymentCompletedAsync(int orderId, string userId, decimal amount, string paymentMethod);
    Task HandlePaymentFailedAsync(int orderId, string userId, decimal amount, string reason);
    
    // Intégrations Catalog
    Task HandleProductApprovedAsync(int productId, string sellerId, string productName);
    Task HandleProductRejectedAsync(int productId, string sellerId, string productName, string reason);
    
    // Intégrations Inventory
    Task HandleStockLowAsync(int productId, string sellerId, string productName, int currentStock, int threshold = 10);
    
    // Intégrations Review
    Task HandleNewReviewAsync(int productId, string sellerId, string productName, int rating, string? reviewText = null);
    
    // Intégrations User
    Task HandleUserRegisteredAsync(string userId, string userName, string email);
    
    // Notifications système
    Task HandleSystemMaintenanceAsync(string message, DateTime? scheduledAt = null);
}
