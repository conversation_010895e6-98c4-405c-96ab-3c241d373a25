@page "/contact"
@inject NavigationManager NavigationManager

<PageTitle>Contact - NafaPlace</PageTitle>

<div class="container-fluid py-4">
    <!-- Header Section Professionnel -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="professional-contact-header">
                <div class="header-content">
                    <div class="contact-icon">
                        <i class="bi bi-envelope"></i>
                    </div>
                    <h1 class="contact-title">Contactez-nous</h1>
                    <p class="contact-subtitle">Nous sommes là pour vous aider. N'hésitez pas à nous contacter pour toute question ou assistance !</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Contact Form -->
        <div class="col-lg-8 mb-4">
            <div class="professional-card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="bi bi-chat-dots me-2"></i>Envoyez-nous un message
                    </h4>
                </div>
                <div class="card-body">
                    <form @onsubmit="SubmitContactForm" @onsubmit:preventDefault="true">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="firstName" class="form-label">Prénom *</label>
                                <input type="text" class="form-control" id="firstName" @bind="contactForm.FirstName" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="lastName" class="form-label">Nom *</label>
                                <input type="text" class="form-control" id="lastName" @bind="contactForm.LastName" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email *</label>
                                <input type="email" class="form-control" id="email" @bind="contactForm.Email" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Téléphone</label>
                                <input type="tel" class="form-control" id="phone" @bind="contactForm.Phone" placeholder="+224 XX XX XX XX">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="subject" class="form-label">Sujet *</label>
                            <select class="form-select" id="subject" @bind="contactForm.Subject" required>
                                <option value="">Sélectionnez un sujet</option>
                                <option value="support">Support technique</option>
                                <option value="vendeur">Devenir vendeur</option>
                                <option value="commande">Question sur une commande</option>
                                <option value="paiement">Problème de paiement</option>
                                <option value="livraison">Question sur la livraison</option>
                                <option value="partenariat">Partenariat</option>
                                <option value="autre">Autre</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="message" class="form-label">Message *</label>
                            <textarea class="form-control" id="message" rows="5" @bind="contactForm.Message" 
                                      placeholder="Décrivez votre demande en détail..." required></textarea>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="newsletter" @bind="contactForm.SubscribeNewsletter">
                            <label class="form-check-label" for="newsletter">
                                Je souhaite recevoir la newsletter NafaPlace
                            </label>
                        </div>

                        <button type="submit" class="btn btn-primary btn-lg" disabled="@isSubmitting">
                            @if (isSubmitting)
                            {
                                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                <span>Envoi en cours...</span>
                            }
                            else
                            {
                                <i class="bi bi-send me-2"></i>
                                <span>Envoyer le message</span>
                            }
                        </button>
                    </form>

                    @if (showSuccessMessage)
                    {
                        <div class="alert alert-success mt-3" role="alert">
                            <i class="bi bi-check-circle me-2"></i>
                            Votre message a été envoyé avec succès ! Nous vous répondrons dans les plus brefs délais.
                        </div>
                    }
                </div>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="col-lg-4">
            <!-- Contact Details -->
            <div class="professional-card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-info-circle me-2"></i>Informations de contact
                    </h5>
                </div>
                <div class="card-body">
                    <div class="contact-item mb-3">
                        <div class="contact-icon">
                            <i class="bi bi-geo-alt-fill text-primary"></i>
                        </div>
                        <div class="contact-details">
                            <h6>Adresse</h6>
                            <p class="mb-0">Quartier Almamya, Conakry<br>République de Guinée</p>
                        </div>
                    </div>

                    <div class="contact-item mb-3">
                        <div class="contact-icon">
                            <i class="bi bi-telephone-fill text-success"></i>
                        </div>
                        <div class="contact-details">
                            <h6>Téléphone</h6>
                            <p class="mb-0">+224 621 123 456<br>+224 664 789 012</p>
                        </div>
                    </div>

                    <div class="contact-item mb-3">
                        <div class="contact-icon">
                            <i class="bi bi-envelope-fill text-info"></i>
                        </div>
                        <div class="contact-details">
                            <h6>Email</h6>
                            <p class="mb-0"><EMAIL><br><EMAIL></p>
                        </div>
                    </div>

                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="bi bi-clock-fill text-warning"></i>
                        </div>
                        <div class="contact-details">
                            <h6>Horaires</h6>
                            <p class="mb-0">Lun - Ven: 8h00 - 18h00<br>Sam: 9h00 - 15h00</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Social Media -->
            <div class="card social-media-card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-share me-2"></i>Suivez-nous
                    </h5>
                </div>
                <div class="card-body">
                    <div class="social-links">
                        <a href="#" class="social-link facebook">
                            <i class="bi bi-facebook"></i>
                            <span>Facebook</span>
                        </a>
                        <a href="#" class="social-link twitter">
                            <i class="bi bi-twitter"></i>
                            <span>Twitter</span>
                        </a>
                        <a href="#" class="social-link instagram">
                            <i class="bi bi-instagram"></i>
                            <span>Instagram</span>
                        </a>
                        <a href="#" class="social-link linkedin">
                            <i class="bi bi-linkedin"></i>
                            <span>LinkedIn</span>
                        </a>
                        <a href="#" class="social-link whatsapp">
                            <i class="bi bi-whatsapp"></i>
                            <span>WhatsApp</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- FAQ Link -->
            <div class="card faq-card">
                <div class="card-body text-center">
                    <i class="bi bi-question-circle display-4 text-muted mb-3"></i>
                    <h5>Questions fréquentes</h5>
                    <p class="text-muted">Consultez notre FAQ pour trouver rapidement des réponses à vos questions.</p>
                    <a href="/faq" class="btn btn-outline-primary">
                        <i class="bi bi-arrow-right me-1"></i>Voir la FAQ
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Map Section (Placeholder) -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card map-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-map me-2"></i>Notre localisation
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="map-placeholder">
                        <div class="map-content">
                            <i class="bi bi-geo-alt-fill display-1 text-primary mb-3"></i>
                            <h4>NafaPlace</h4>
                            <p class="text-muted">Quartier Almamya, Conakry, République de Guinée</p>
                            <button class="btn btn-primary" onclick="window.open('https://maps.google.com/?q=Conakry,Guinea', '_blank')">
                                <i class="bi bi-map me-1"></i>Voir sur Google Maps
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private ContactFormModel contactForm = new();
    private bool isSubmitting = false;
    private bool showSuccessMessage = false;

    private async Task SubmitContactForm()
    {
        if (string.IsNullOrWhiteSpace(contactForm.FirstName) || 
            string.IsNullOrWhiteSpace(contactForm.LastName) ||
            string.IsNullOrWhiteSpace(contactForm.Email) ||
            string.IsNullOrWhiteSpace(contactForm.Subject) ||
            string.IsNullOrWhiteSpace(contactForm.Message))
        {
            return;
        }

        isSubmitting = true;
        StateHasChanged();

        try
        {
            // Simuler l'envoi du formulaire
            await Task.Delay(2000);
            
            // TODO: Implémenter l'envoi réel du formulaire
            Console.WriteLine($"Contact form submitted: {contactForm.FirstName} {contactForm.LastName} - {contactForm.Subject}");
            
            // Réinitialiser le formulaire
            contactForm = new ContactFormModel();
            showSuccessMessage = true;
            
            // Masquer le message de succès après 5 secondes
            _ = Task.Delay(5000).ContinueWith(_ => 
            {
                showSuccessMessage = false;
                InvokeAsync(StateHasChanged);
            });
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error submitting contact form: {ex.Message}");
        }
        finally
        {
            isSubmitting = false;
            StateHasChanged();
        }
    }

    public class ContactFormModel
    {
        public string FirstName { get; set; } = "";
        public string LastName { get; set; } = "";
        public string Email { get; set; } = "";
        public string Phone { get; set; } = "";
        public string Subject { get; set; } = "";
        public string Message { get; set; } = "";
        public bool SubscribeNewsletter { get; set; } = false;
    }
}
