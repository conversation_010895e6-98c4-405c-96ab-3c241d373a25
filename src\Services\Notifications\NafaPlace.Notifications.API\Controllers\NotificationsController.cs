using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NafaPlace.Notifications.Application.DTOs;
using NafaPlace.Notifications.Application.Services;
using System.Security.Claims;

namespace NafaPlace.Notifications.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class NotificationsController : ControllerBase
{
    private readonly INotificationService _notificationService;

    public NotificationsController(INotificationService notificationService)
    {
        _notificationService = notificationService;
    }

    [HttpGet]
    public async Task<ActionResult<NotificationsPagedResult>> GetNotifications(
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20,
        [FromQuery] bool unreadOnly = false)
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
            return Unauthorized();

        var result = await _notificationService.GetUserNotificationsAsync(userId, page, pageSize, unreadOnly);
        return Ok(result);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<NotificationDto>> GetNotification(int id)
    {
        var notification = await _notificationService.GetNotificationByIdAsync(id);
        if (notification == null)
            return NotFound();

        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (notification.UserId != userId)
            return Forbid();

        return Ok(notification);
    }

    [HttpGet("recent")]
    public async Task<ActionResult<List<NotificationDto>>> GetRecentNotifications([FromQuery] int count = 5)
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
            return Unauthorized();

        var notifications = await _notificationService.GetRecentNotificationsAsync(userId, count);
        return Ok(notifications);
    }

    [HttpGet("stats")]
    public async Task<ActionResult<NotificationStatsDto>> GetStats()
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
            return Unauthorized();

        var stats = await _notificationService.GetUserStatsAsync(userId);
        return Ok(stats);
    }

    [HttpPost]
    [Authorize(Roles = "Admin,System")]
    public async Task<ActionResult<NotificationDto>> CreateNotification([FromBody] CreateNotificationRequest request)
    {
        try
        {
            var notification = await _notificationService.CreateNotificationAsync(request);
            return CreatedAtAction(nameof(GetNotification), new { id = notification.Id }, notification);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal server error: {ex.Message}");
        }
    }

    [HttpPost("bulk")]
    [Authorize(Roles = "Admin,System")]
    public async Task<ActionResult<List<NotificationDto>>> CreateBulkNotification([FromBody] BulkNotificationRequest request)
    {
        try
        {
            var notifications = await _notificationService.CreateBulkNotificationAsync(request);
            return Ok(notifications);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal server error: {ex.Message}");
        }
    }

    [HttpPut("{id}/read")]
    public async Task<IActionResult> MarkAsRead(int id)
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
            return Unauthorized();

        var result = await _notificationService.MarkAsReadAsync(id, userId);
        if (!result)
            return NotFound();

        return Ok();
    }

    [HttpPut("mark-all-read")]
    public async Task<ActionResult<int>> MarkAllAsRead()
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
            return Unauthorized();

        var count = await _notificationService.MarkAllAsReadAsync(userId);
        return Ok(new { markedCount = count });
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteNotification(int id)
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
            return Unauthorized();

        var result = await _notificationService.DeleteNotificationAsync(id, userId);
        if (!result)
            return NotFound();

        return NoContent();
    }

    [HttpDelete("read")]
    public async Task<ActionResult<int>> DeleteAllRead()
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
            return Unauthorized();

        var count = await _notificationService.DeleteAllReadAsync(userId);
        return Ok(new { deletedCount = count });
    }

    // Template endpoints
    [HttpGet("templates")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<List<NotificationTemplateDto>>> GetTemplates()
    {
        var templates = await _notificationService.GetTemplatesAsync();
        return Ok(templates);
    }

    [HttpPost("templates")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<NotificationTemplateDto>> CreateTemplate([FromBody] CreateNotificationTemplateRequest request)
    {
        try
        {
            var template = await _notificationService.CreateTemplateAsync(request);
            return Ok(template);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal server error: {ex.Message}");
        }
    }

    [HttpPost("from-template/{templateId}")]
    [Authorize(Roles = "Admin,System")]
    public async Task<ActionResult<NotificationDto>> CreateFromTemplate(
        int templateId,
        [FromBody] Dictionary<string, string> parameters,
        [FromQuery] string userId)
    {
        try
        {
            var notification = await _notificationService.CreateFromTemplateAsync(templateId, userId, parameters);
            return Ok(notification);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal server error: {ex.Message}");
        }
    }
}
