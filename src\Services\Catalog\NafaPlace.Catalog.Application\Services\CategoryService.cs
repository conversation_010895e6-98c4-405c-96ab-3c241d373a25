using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using NafaPlace.Catalog.Application.Common.Interfaces;
using NafaPlace.Catalog.Application.DTOs.Category;
using NafaPlace.Catalog.Domain.Models;

namespace NafaPlace.Catalog.Application.Services
{
    public class CategoryService : ICategoryService
    {
        private readonly ICatalogDbContext _context;
        private readonly IMapper _mapper;
        private readonly ICategoryImageService _categoryImageService;

        public CategoryService(ICatalogDbContext context, IMapper mapper, ICategoryImageService categoryImageService)
        {
            _context = context;
            _mapper = mapper;
            _categoryImageService = categoryImageService;
        }

        public async Task<CategoryDto> CreateCategoryAsync(CreateCategoryRequest request)
        {
            var category = new Category
            {
                
                Name = request.Name,
                Description = request.Description,
                IconUrl = request.IconUrl,
                ImageUrl = request.ImageUrl ?? string.Empty,
                ParentCategoryId = request.ParentId,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _context.Categories.Add(category);
            await _context.SaveChangesAsync();

            // Recharger la catégorie avec sa catégorie parente
            var createdCategory = await _context.Categories
                .Include(c => c.ParentCategory)
                .FirstOrDefaultAsync(c => c.Id == category.Id);

            return _mapper.Map<CategoryDto>(createdCategory);
        }

        public async Task<CategoryDto> UpdateCategoryAsync(int id, UpdateCategoryRequest request)
        {
            var category = await _context.Categories
                .FirstOrDefaultAsync(c => c.Id == id);

            if (category == null)
            {
                throw new KeyNotFoundException($"Category with ID {id} not found.");
            }

            category.Name = request.Name;
            category.Description = request.Description;
            category.IconUrl = request.IconUrl;
            category.ImageUrl = request.ImageUrl ?? category.ImageUrl;
            category.ParentCategoryId = request.ParentId;
            category.IsActive = request.IsActive;
            category.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            // Recharger la catégorie avec sa catégorie parente
            var updatedCategory = await _context.Categories
                .Include(c => c.ParentCategory)
                .FirstOrDefaultAsync(c => c.Id == id);

            return _mapper.Map<CategoryDto>(updatedCategory);
        }

        public async Task<bool> DeleteCategoryAsync(int id)
        {
            var category = await _context.Categories.FindAsync(id);

            if (category == null)
            {
                return false;
            }

            // Vérifier si la catégorie a des sous-catégories
            bool hasChildren = await _context.Categories.AnyAsync(c => c.ParentCategoryId == id);
            if (hasChildren)
            {
                return false;
            }

            // Vérifier si la catégorie a des produits
            var hasProducts = await _context.Products.AnyAsync(p => p.CategoryId == id);
            if (hasProducts)
            {
                return false;
            }

            _context.Categories.Remove(category);
            await _context.SaveChangesAsync();

            return true;
        }

        public async Task<CategoryDto> GetCategoryByIdAsync(int id)
        {
            var category = await _context.Categories
                .Where(c => c.Id == id)
                .Select(c => new Category
                {
                    Id = c.Id,
                    Name = c.Name,
                    Description = c.Description,
                    IconUrl = c.IconUrl,
                    ImageUrl = c.ImageUrl,
                    IsActive = c.IsActive,
                    CreatedAt = c.CreatedAt,
                    UpdatedAt = c.UpdatedAt
                })
                .FirstOrDefaultAsync();

            if (category == null)
                throw new KeyNotFoundException($"Category with ID {id} not found.");

            return _mapper.Map<CategoryDto>(category);
        }

        public async Task<IEnumerable<CategoryDto>> GetAllCategoriesAsync()
        {
            var categories = await _context.Categories
                .Include(c => c.ParentCategory)
                .Where(c => c.IsActive)
                .OrderBy(c => c.Name)
                .ToListAsync();

            var categoryDtos = categories.Select(c => new CategoryDto
            {
                Id = c.Id,
                Name = c.Name,
                Description = c.Description,
                IconUrl = c.IconUrl,
                ImageUrl = c.ImageUrl,
                ParentCategoryId = c.ParentCategoryId,
                ParentCategory = c.ParentCategory != null ? new CategoryDto 
                {
                    Id = c.ParentCategory.Id,
                    Name = c.ParentCategory.Name,
                    Description = c.ParentCategory.Description,
                    IconUrl = c.ParentCategory.IconUrl,
                    ImageUrl = c.ParentCategory.ImageUrl
                } : null,
                CreatedAt = c.CreatedAt,
                UpdatedAt = c.UpdatedAt
            }).ToList();

            return categoryDtos;
        }

        public async Task<IEnumerable<CategoryDto>> GetMainCategoriesAsync()
        {
            var categories = await _context.Categories
                .Select(c => new Category
                {
                    Id = c.Id,
                    Name = c.Name,
                    Description = c.Description,
                    IconUrl = c.IconUrl,
                    ImageUrl = c.ImageUrl,
                    IsActive = c.IsActive,
                    CreatedAt = c.CreatedAt,
                    UpdatedAt = c.UpdatedAt
                })
                .ToListAsync();

            return _mapper.Map<IEnumerable<CategoryDto>>(categories);
        }

        public async Task<IEnumerable<CategoryDto>> GetSubcategoriesAsync(int parentId)
        {
            var subcategories = await _context.Categories
                .Where(c => c.ParentCategoryId == parentId && c.IsActive)
                .Select(c => new Category
                {
                    Id = c.Id,
                    Name = c.Name,
                    Description = c.Description,
                    IconUrl = c.IconUrl,
                    ImageUrl = c.ImageUrl,
                    IsActive = c.IsActive,
                    ParentCategoryId = c.ParentCategoryId,
                    CreatedAt = c.CreatedAt,
                    UpdatedAt = c.UpdatedAt
                })
                .ToListAsync();

            return _mapper.Map<IEnumerable<CategoryDto>>(subcategories);
        }

        public async Task<IEnumerable<CategoryDto>> SearchCategoriesAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return await GetAllCategoriesAsync();

            var normalizedSearchTerm = searchTerm.ToLower();

            var categories = await _context.Categories
                .Where(c => c.Name.ToLower().Contains(normalizedSearchTerm) || 
                           (c.Description != null && c.Description.ToLower().Contains(normalizedSearchTerm)))
                .Select(c => new Category
                {
                    Id = c.Id,
                    Name = c.Name,
                    Description = c.Description,
                    IconUrl = c.IconUrl,
                    ImageUrl = c.ImageUrl,
                    IsActive = c.IsActive,
                    CreatedAt = c.CreatedAt,
                    UpdatedAt = c.UpdatedAt
                })
                .ToListAsync();

            return _mapper.Map<IEnumerable<CategoryDto>>(categories);
        }

        public async Task<string> UploadCategoryImageAsync(string base64Image)
        {
            try
            {
                if (string.IsNullOrEmpty(base64Image))
                {
                    return string.Empty;
                }

                // Utiliser le service d'images de catégories pour l'upload
                var imageUrl = await _categoryImageService.UploadCategoryImageAsync(base64Image);

                return imageUrl;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors de l'upload de l'image de catégorie: {ex.Message}");
                return string.Empty;
            }
        }
    }
}
