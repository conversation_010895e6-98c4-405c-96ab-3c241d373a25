using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NafaPlace.Catalog.Domain.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace NafaPlace.Catalog.Infrastructure.Persistence.Initialization
{
    public class CatalogDbInitializer
    {
        private readonly CatalogDbContext _context;
        private readonly ILogger<CatalogDbInitializer> _logger;

        public CatalogDbInitializer(CatalogDbContext context, ILogger<CatalogDbInitializer> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task InitializeAsync()
        {
            try
            {
                // Vérifier si la base de données existe, sinon la créer
                await _context.Database.MigrateAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Une erreur s'est produite lors de la migration de la base de données.");
                throw;
            }
        }

        public async Task SeedAsync()
        {
            try
            {
                await SeedCategoriesAsync();
                await SeedSellersAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Une erreur s'est produite lors de l'initialisation des données.");
                throw;
            }
        }

        private async Task SeedCategoriesAsync()
        {
            // Vérifier s'il y a déjà des catégories
            if (await _context.Categories.AnyAsync())
            {
                _logger.LogInformation("Les catégories existent déjà, pas de réinitialisation nécessaire.");
                return;
            }

            _logger.LogInformation("Initialisation des catégories...");

            var categories = new List<Category>
            {
                new Category
                {
                    Name = "Électronique",
                    Description = "Produits électroniques et gadgets",
                    ImageUrl = "/images/categories/electronics.jpg",
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new Category
                {
                    Name = "Mode",
                    Description = "Vêtements et accessoires",
                    ImageUrl = "/images/categories/fashion.jpg",
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new Category
                {
                    Name = "Maison",
                    Description = "Articles pour la maison",
                    ImageUrl = "/images/categories/home.jpg",
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new Category
                {
                    Name = "Beauté & Santé",
                    Description = "Produits de beauté et de santé",
                    ImageUrl = "/images/categories/beauty.jpg",
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new Category
                {
                    Name = "Sports & Loisirs",
                    Description = "Équipements sportifs et articles de loisirs",
                    ImageUrl = "/images/categories/sports.jpg",
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                }
            };

            await _context.Categories.AddRangeAsync(categories);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Initialisation des catégories terminée.");
        }

        private async Task SeedSellersAsync()
        {
            // Vérifier si des vendeurs existent déjà
            if (await _context.Sellers.AnyAsync())
            {
                _logger.LogInformation("Les vendeurs existent déjà dans la base de données.");
                return;
            }

            _logger.LogInformation("Initialisation des vendeurs...");

            var sellers = new List<Seller>
            {
                new Seller
                {
                    Name = "Électronique Mali",
                    Email = "<EMAIL>",
                    PhoneNumber = "+22379123456",
                    Address = "123 Rue Bamako, Mali",
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    UserId = "1",
                    ProfilePictureUrl = ""
                },
                new Seller
                {
                    Name = "Mode Africaine",
                    Email = "<EMAIL>",
                    PhoneNumber = "+22378456789",
                    Address = "45 Avenue Dakar, Sénégal",
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    UserId = "2",
                    ProfilePictureUrl = ""
                }
            };

            await _context.Sellers.AddRangeAsync(sellers);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Initialisation des vendeurs terminée.");
        }
    }
}
