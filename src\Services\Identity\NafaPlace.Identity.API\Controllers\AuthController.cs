using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NafaPlace.Identity.Application.Common.Exceptions;
using NafaPlace.Identity.Application.Common.Interfaces;
using NafaPlace.Identity.Application.DTOs;
using NafaPlace.Identity.Application.DTOs.Auth;
using Swashbuckle.AspNetCore.Annotations;
using System.Security.Claims;

namespace NafaPlace.Identity.API.Controllers;

/// <summary>
/// Contrôleur pour gérer l'authentification des utilisateurs
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
[SwaggerTag("Authentification et gestion des tokens")]
public class AuthController : ControllerBase
{
    private readonly IAuthService _authService;

    public AuthController(IAuthService authService)
    {
        _authService = authService;
    }

    /// <summary>
    /// Inscription d'un nouvel utilisateur
    /// </summary>
    /// <param name="request">Informations d'inscription de l'utilisateur</param>
    /// <returns>Token JWT et informations de l'utilisateur</returns>
    /// <response code="200">Inscription réussie</response>
    /// <response code="400">Email ou nom d'utilisateur déjà utilisé</response>
    [HttpPost("register")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(AuthResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [SwaggerOperation(
        Summary = "Inscription d'un nouvel utilisateur",
        Description = "Crée un nouveau compte utilisateur et retourne un token JWT",
        OperationId = "Auth_Register",
        Tags = new[] { "Authentication" }
    )]
    public async Task<IActionResult> Register([FromBody] RegisterRequest request)
    {
        try
        {
            var result = await _authService.RegisterAsync(request);
            return Ok(result);
        }
        catch (ValidationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (AuthenticationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            // Log the exception
            Console.WriteLine($"Erreur lors de l'inscription: {ex.Message}");
            if (ex.InnerException != null)
            {
                Console.WriteLine($"Inner Exception: {ex.InnerException.Message}");
            }
            
            // Return a more specific error message
            return StatusCode(500, $"Une erreur est survenue lors de l'inscription: {ex.Message}");
        }
    }

    /// <summary>
    /// Inscription d'un nouveau vendeur
    /// </summary>
    /// <param name="request">Informations d'inscription du vendeur</param>
    /// <returns>Token JWT et informations du vendeur</returns>
    /// <response code="200">Inscription réussie</response>
    /// <response code="400">Email ou nom d'utilisateur déjà utilisé</response>
    [HttpPost("register-seller")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(AuthResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [SwaggerOperation(
        Summary = "Inscription d'un nouveau vendeur",
        Description = "Crée un nouveau compte vendeur et retourne un token JWT",
        OperationId = "Auth_RegisterSeller",
        Tags = new[] { "Authentication" }
    )]
    public async Task<IActionResult> RegisterSeller([FromBody] RegisterRequest request)
    {
        try
        {
            var result = await _authService.RegisterAsync(request, "Seller");
            return Ok(result);
        }
        catch (ValidationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (AuthenticationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            // Log the exception
            Console.WriteLine($"Erreur lors de l'inscription du vendeur: {ex.Message}");
            if (ex.InnerException != null)
            {
                Console.WriteLine($"Inner Exception: {ex.InnerException.Message}");
            }
            
            // Return a more specific error message
            return StatusCode(500, $"Une erreur est survenue lors de l'inscription du vendeur: {ex.Message}");
        }
    }

    /// <summary>
    /// Connexion d'un utilisateur existant
    /// </summary>
    /// <param name="request">Identifiants de connexion</param>
    /// <returns>Token JWT et informations de l'utilisateur</returns>
    /// <response code="200">Connexion réussie</response>
    /// <response code="400">Identifiants invalides</response>
    [HttpPost("login")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(AuthResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [SwaggerOperation(
        Summary = "Connexion d'un utilisateur existant",
        Description = "Authentifie l'utilisateur et retourne un token JWT",
        OperationId = "Auth_Login",
        Tags = new[] { "Authentication" }
    )]
    public async Task<IActionResult> Login([FromBody] LoginRequest request)
    {
        try
        {
            var result = await _authService.LoginAsync(request);
            return Ok(result);
        }
        catch (AuthenticationException ex)
        {
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Rafraîchissement du token JWT
    /// </summary>
    /// <param name="request">Refresh token actuel</param>
    /// <returns>Nouveau token JWT et refresh token</returns>
    /// <response code="200">Token rafraîchi avec succès</response>
    /// <response code="400">Refresh token invalide ou expiré</response>
    [HttpPost("refresh")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(AuthResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [SwaggerOperation(
        Summary = "Rafraîchissement du token JWT",
        Description = "Génère un nouveau token JWT à partir d'un refresh token valide",
        OperationId = "Auth_RefreshToken",
        Tags = new[] { "Authentication" }
    )]
    public async Task<IActionResult> RefreshToken([FromBody] RefreshTokenRequest request)
    {
        try
        {
            var result = await _authService.RefreshTokenAsync(request);
            return Ok(result);
        }
        catch (AuthenticationException ex)
        {
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Déconnexion de l'utilisateur
    /// </summary>
    /// <returns>Aucun contenu</returns>
    /// <response code="200">Déconnexion réussie</response>
    /// <response code="401">Non authentifié</response>
    [Authorize]
    [HttpPost("logout")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [SwaggerOperation(
        Summary = "Déconnexion de l'utilisateur",
        Description = "Révoque le refresh token de l'utilisateur",
        OperationId = "Auth_Logout",
        Tags = new[] { "Authentication" }
    )]
    public async Task<IActionResult> Logout()
    {
        var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? throw new UnauthorizedAccessException("Utilisateur non authentifié"));
        await _authService.LogoutAsync(userId);
        return Ok();
    }

    /// <summary>
    /// Récupère les informations de l'utilisateur actuellement connecté
    /// </summary>
    /// <returns>Informations de l'utilisateur</returns>
    /// <response code="200">Informations récupérées avec succès</response>
    /// <response code="401">Non authentifié</response>
    [Authorize]
    [HttpGet("current-user")]
    [ProducesResponseType(typeof(UserDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [SwaggerOperation(
        Summary = "Récupère les informations de l'utilisateur connecté",
        Description = "Retourne les informations de l'utilisateur actuellement authentifié",
        OperationId = "Auth_GetCurrentUser",
        Tags = new[] { "Authentication" }
    )]
    public async Task<IActionResult> GetCurrentUser()
    {
        try
        {
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? throw new UnauthorizedAccessException("Utilisateur non authentifié"));
            var user = await _authService.GetUserByIdAsync(userId);
            return Ok(user);
        }
        catch (UnauthorizedAccessException)
        {
            return Unauthorized();
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Une erreur est survenue: {ex.Message}");
        }
    }

    /// <summary>
    /// Récupère les informations de l'utilisateur connecté
    /// </summary>
    /// <returns>Informations de l'utilisateur</returns>
    /// <response code="200">Informations récupérées avec succès</response>
    /// <response code="401">Non authentifié</response>
    [Authorize]
    [HttpGet("currentuser")]
    [ProducesResponseType(typeof(UserDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [SwaggerOperation(
        Summary = "Récupère les informations de l'utilisateur connecté",
        Description = "Retourne les informations de l'utilisateur authentifié",
        OperationId = "Auth_CurrentUser",
        Tags = new[] { "Authentication" }
    )]
    public async Task<IActionResult> CurrentUser()
    {
        try
        {
            // Vérifier si l'utilisateur est authentifié
            if (User.Identity == null || !User.Identity.IsAuthenticated)
            {
                Console.WriteLine("CurrentUser: L'utilisateur n'est pas authentifié");
                return Unauthorized("L'utilisateur n'est pas authentifié");
            }

            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
            if (userIdClaim == null || string.IsNullOrEmpty(userIdClaim.Value))
            {
                Console.WriteLine("CurrentUser: Identifiant utilisateur non trouvé dans le token");
                return Unauthorized("Identifiant utilisateur non trouvé dans le token");
            }

            // Afficher toutes les claims pour le débogage
            Console.WriteLine("Claims dans le token:");
            foreach (var claim in User.Claims)
            {
                Console.WriteLine($"  {claim.Type}: {claim.Value}");
            }

            try
            {
                var userId = int.Parse(userIdClaim.Value);
                var user = await _authService.GetUserByIdAsync(userId);
                if (user == null)
                {
                    Console.WriteLine($"CurrentUser: Utilisateur non trouvé pour l'ID: {userId}");
                    return NotFound("Utilisateur non trouvé");
                }

                Console.WriteLine($"CurrentUser: Utilisateur trouvé et retourné avec succès: {user.Username}");
                return Ok(user);
            }
            catch (FormatException)
            {
                Console.WriteLine($"CurrentUser: Format d'identifiant utilisateur invalide: {userIdClaim.Value}");
                return BadRequest("Format d'identifiant utilisateur invalide");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur dans CurrentUser: {ex.Message}");
            Console.WriteLine($"StackTrace: {ex.StackTrace}");
            return StatusCode(500, "Une erreur s'est produite lors de la récupération des informations utilisateur");
        }
    }
}
