# Build stage
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copier les fichiers csproj et restaurer les dépendances
COPY ["src/Services/Identity/NafaPlace.Identity.Domain/NafaPlace.Identity.Domain.csproj", "Services/Identity/NafaPlace.Identity.Domain/"]
COPY ["src/Services/Identity/NafaPlace.Identity.Application/NafaPlace.Identity.Application.csproj", "Services/Identity/NafaPlace.Identity.Application/"]
COPY ["src/Services/Identity/NafaPlace.Identity.Infrastructure/NafaPlace.Identity.Infrastructure.csproj", "Services/Identity/NafaPlace.Identity.Infrastructure/"]
COPY ["src/Services/Identity/NafaPlace.Identity.API/NafaPlace.Identity.API.csproj", "Services/Identity/NafaPlace.Identity.API/"]
RUN dotnet restore "Services/Identity/NafaPlace.Identity.API/NafaPlace.Identity.API.csproj"

# Copier le reste des fichiers et construire
COPY ["src/Services/Identity/", "Services/Identity/"]
WORKDIR "/src/Services/Identity/NafaPlace.Identity.API"
RUN dotnet build "NafaPlace.Identity.API.csproj" -c Release -o /app/build

# Publish stage
FROM build AS publish
RUN dotnet publish "NafaPlace.Identity.API.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Final stage
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS final
WORKDIR /app
COPY --from=publish /app/publish .

# Configuration pour le marché africain
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false
ENV TZ=Africa/Dakar
ENV LANG=fr_FR.UTF-8
ENV LANGUAGE=fr_FR.UTF-8
ENV LC_ALL=fr_FR.UTF-8

EXPOSE 80
ENTRYPOINT ["dotnet", "NafaPlace.Identity.API.dll"]
