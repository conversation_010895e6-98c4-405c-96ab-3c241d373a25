using NafaPlace.Inventory.Domain.Models;
using NafaPlace.Inventory.Domain.Enums;

namespace NafaPlace.Inventory.Application.DTOs;

public class StockReservationDto
{
    public int Id { get; set; }
    public int ProductId { get; set; }
    public string UserId { get; set; } = string.Empty;
    public string SessionId { get; set; } = string.Empty;
    public int Quantity { get; set; }
    public ReservationStatus Status { get; set; }
    public DateTime ReservedAt { get; set; }
    public DateTime ExpiresAt { get; set; }
    public string? OrderId { get; set; }
    public string? Reason { get; set; }
    public DateTime? ReleasedAt { get; set; }
    public bool IsExpired { get; set; }
    public bool IsActive { get; set; }
    public TimeSpan TimeRemaining { get; set; }
}

public class StockAlertDto
{
    public int Id { get; set; }
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public AlertType Type { get; set; }
    public AlertSeverity Severity { get; set; }
    public string Message { get; set; } = string.Empty;
    public int CurrentStock { get; set; }
    public int? ThresholdValue { get; set; }
    public bool IsActive { get; set; }
    public bool IsAcknowledged { get; set; }
    public string? AcknowledgedBy { get; set; }
    public DateTime? AcknowledgedAt { get; set; }
    public int SellerId { get; set; }
    public string? SellerName { get; set; }
    public DateTime CreatedAt { get; set; }
}

public class StockMovementDto
{
    public int Id { get; set; }
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public MovementType Type { get; set; }
    public int Quantity { get; set; }
    public int PreviousStock { get; set; }
    public int NewStock { get; set; }
    public string Reason { get; set; } = string.Empty;
    public string? Reference { get; set; }
    public string UserId { get; set; } = string.Empty;
    public string? UserName { get; set; }
    public int SellerId { get; set; }
    public string? Notes { get; set; }
    public DateTime CreatedAt { get; set; }
}

public class CreateReservationRequest
{
    public int ProductId { get; set; }
    public string UserId { get; set; } = string.Empty;
    public string SessionId { get; set; } = string.Empty;
    public int Quantity { get; set; }
    public int ExpirationMinutes { get; set; } = 15; // 15 minutes par défaut
    public string? Reason { get; set; }
}

public class StockAdjustmentRequest
{
    public int ProductId { get; set; }
    public int NewQuantity { get; set; }
    public string Reason { get; set; } = string.Empty;
    public string? Notes { get; set; }
}

public class StockAlertConfigDto
{
    public int ProductId { get; set; }
    public int LowStockThreshold { get; set; } = 10;
    public int CriticalStockThreshold { get; set; } = 5;
    public bool EnableLowStockAlerts { get; set; } = true;
    public bool EnableOutOfStockAlerts { get; set; } = true;
    public bool EnableEmailNotifications { get; set; } = true;
    public bool EnableSmsNotifications { get; set; } = false;
    public List<string> NotificationEmails { get; set; } = new();
    public List<string> NotificationPhones { get; set; } = new();
}

public class InventoryDashboardDto
{
    public int TotalProducts { get; set; }
    public int LowStockCount { get; set; }
    public int OutOfStockProducts { get; set; }
    public int ActiveReservations { get; set; }
    public int PendingAlerts { get; set; }
    public decimal TotalInventoryValue { get; set; }
    public string Currency { get; set; } = "GNF";
    public List<StockAlertDto> RecentAlerts { get; set; } = new();
    public List<StockMovementDto> RecentMovements { get; set; } = new();
    public List<TopProductDto> TopSellingProducts { get; set; } = new();
    public List<TopProductDto> LowStockProducts { get; set; } = new();
}

public class TopProductDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public int CurrentStock { get; set; }
    public int SoldQuantity { get; set; }
    public decimal Revenue { get; set; }
    public string Currency { get; set; } = "GNF";
}

public class StockValidationResult
{
    public bool IsValid { get; set; }
    public string? ErrorMessage { get; set; }
    public int AvailableStock { get; set; }
    public int ReservedStock { get; set; }
    public int RequestedQuantity { get; set; }
    public bool RequiresReservation { get; set; }
}

public class BulkStockUpdateRequest
{
    public List<StockUpdateItem> Items { get; set; } = new();
    public string Reason { get; set; } = string.Empty;
    public string? Notes { get; set; }
}

public class StockUpdateItem
{
    public int ProductId { get; set; }
    public int NewQuantity { get; set; }
    public string? ProductSpecificReason { get; set; }
}

public class ProductInfo
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public decimal Price { get; set; }
    public int SellerId { get; set; }
    public string SellerName { get; set; } = string.Empty;
    public int CurrentStock { get; set; }
    public string Currency { get; set; } = "GNF";
}

public class UpdateStockRequest
{
    public int NewQuantity { get; set; }
    public string Reason { get; set; } = string.Empty;
    public string? Notes { get; set; }
}

public class CartItemForReservation
{
    public int ProductId { get; set; }
    public int Quantity { get; set; }
    public string ProductName { get; set; } = string.Empty;
}
