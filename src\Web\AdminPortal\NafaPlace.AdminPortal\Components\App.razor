@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Authorization
@using NafaPlace.AdminPortal.Components

<CascadingAuthenticationState>
    <Router AppAssembly="@typeof(App).Assembly">
        <Found Context="routeData">
            <AuthorizeRouteView RouteData="@routeData" DefaultLayout="@typeof(Layout.MainLayout)">
                <NotAuthorized>
                    @if (context.User.Identity?.IsAuthenticated != true)
                    {
                        <RedirectToLogin />
                    }
                    else
                    {
                        <div class="container">
                            <div class="row justify-content-center">
                                <div class="col-md-8 mt-5 text-center">
                                    <h1 class="display-4">Accès Refusé</h1>
                                    <p class="lead">Vous n'avez pas les autorisations nécessaires pour accéder à cette page.</p>
                                    <a href="" class="btn btn-primary">Retour à l'accueil</a>
                                </div>
                            </div>
                        </div>
                    }
                </NotAuthorized>
            </AuthorizeRouteView>
            <FocusOnNavigate RouteData="@routeData" Selector="h1" />
        </Found>
        <NotFound>
            <PageTitle>Not Found</PageTitle>
            <LayoutView Layout="@typeof(Layout.MainLayout)">
                <div class="container">
                    <div class="row justify-content-center">
                        <div class="col-md-8 mt-5 text-center">
                            <h1 class="display-4">404 - Page Not Found</h1>
                            <p class="lead">La page que vous recherchez n'existe pas.</p>
                            <a href="" class="btn btn-primary">Retour à l'accueil</a>
                        </div>
                    </div>
                </div>
            </LayoutView>
        </NotFound>
    </Router>
</CascadingAuthenticationState>

@code {
    private class RedirectToLogin : ComponentBase
    {
        [Inject]
        private NavigationManager NavigationManager { get; set; } = default!;

        protected override void OnInitialized()
        {
            NavigationManager.NavigateTo("/login", true);
        }
    }
}
