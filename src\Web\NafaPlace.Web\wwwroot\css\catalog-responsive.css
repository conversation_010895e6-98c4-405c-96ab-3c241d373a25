/* Catalog Responsive Styles */

/* Mobile-first approach */
.catalog-container {
    padding: 0.5rem;
}

/* Filters mobile styles */
@media (max-width: 991.98px) {
    .filters-mobile-btn {
        position: sticky;
        top: 0;
        z-index: 10;
        background: white;
        padding: 0.5rem 0;
        margin-bottom: 1rem;
        border-bottom: 1px solid #dee2e6;
    }
    
    #filtersCollapse {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        margin-bottom: 1rem;
    }
    
    #filtersCollapse .card {
        border: none;
        box-shadow: none;
    }
}

/* Product cards mobile optimization */
@media (max-width: 768px) {
    .product-card-mobile {
        margin-bottom: 1rem;
    }
    
    .product-card-mobile .card-img-top {
        height: 150px;
        object-fit: cover;
    }
    
    .product-card-mobile .card-body {
        padding: 0.75rem;
    }
    
    .product-card-mobile .card-title {
        font-size: 0.9rem;
        line-height: 1.3;
        margin-bottom: 0.5rem;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
    
    .product-card-mobile .card-text {
        font-size: 0.8rem;
        margin-bottom: 0.5rem;
    }
    
    .product-card-mobile .btn {
        font-size: 0.8rem;
        padding: 0.4rem 0.6rem;
    }
    
    .product-price-mobile {
        font-size: 0.9rem;
        font-weight: 600;
    }
    
    .ratings-mobile {
        font-size: 0.8rem;
    }
    
    .ratings-mobile .star-rating {
        font-size: 0.7rem;
    }
}

@media (max-width: 576px) {
    .catalog-container {
        padding: 0.25rem;
    }
    
    .product-card-mobile .card-img-top {
        height: 120px;
    }
    
    .product-card-mobile .card-body {
        padding: 0.5rem;
    }
    
    .product-card-mobile .card-title {
        font-size: 0.85rem;
        -webkit-line-clamp: 2;
    }
    
    .product-card-mobile .card-text {
        font-size: 0.75rem;
    }
    
    .product-card-mobile .btn {
        font-size: 0.75rem;
        padding: 0.3rem 0.5rem;
    }
    
    .product-price-mobile {
        font-size: 0.85rem;
    }
    
    /* Stack buttons vertically on very small screens */
    .product-actions-mobile {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }
    
    .product-actions-mobile .btn {
        width: 100%;
    }
}

/* Pagination mobile styles */
@media (max-width: 768px) {
    .pagination-mobile {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .pagination-mobile .page-item {
        margin: 0.1rem;
    }
    
    .pagination-mobile .page-link {
        padding: 0.375rem 0.5rem;
        font-size: 0.875rem;
    }
    
    /* Hide some pagination items on mobile */
    .pagination-mobile .page-item:not(.active):not(.disabled):nth-child(n+6):nth-last-child(n+6) {
        display: none;
    }
}

/* Search and sort mobile styles */
@media (max-width: 768px) {
    .catalog-header-mobile {
        flex-direction: column;
        align-items: stretch !important;
        gap: 0.5rem;
    }
    
    .catalog-header-mobile h2 {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }
    
    .sort-select-mobile {
        width: 100%;
    }
    
    .sort-select-mobile select {
        font-size: 0.9rem;
    }
}

/* Filter collapse animation */
.collapse {
    transition: height 0.35s ease;
}

.collapsing {
    height: 0;
    overflow: hidden;
    transition: height 0.35s ease;
}

/* Loading states */
.loading-overlay {
    position: relative;
}

.loading-overlay::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

/* Touch-friendly buttons */
@media (max-width: 768px) {
    .btn-touch {
        min-height: 44px;
        min-width: 44px;
    }
    
    .form-check-input {
        width: 1.25em;
        height: 1.25em;
    }
    
    .form-check-label {
        padding-left: 0.5rem;
        line-height: 1.25em;
    }
}

/* Sticky elements */
@media (max-width: 991.98px) {
    .sticky-mobile {
        position: sticky;
        top: 0;
        z-index: 100;
        background: white;
        padding: 0.5rem 0;
        border-bottom: 1px solid #dee2e6;
        margin-bottom: 1rem;
    }
}

/* Improved spacing */
@media (max-width: 768px) {
    .container-mobile {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }
    
    .row-mobile {
        margin-left: -0.375rem;
        margin-right: -0.375rem;
    }
    
    .row-mobile > * {
        padding-left: 0.375rem;
        padding-right: 0.375rem;
    }
}

/* Badge improvements */
@media (max-width: 576px) {
    .badge-mobile {
        font-size: 0.7rem;
        padding: 0.25rem 0.4rem;
    }
}

/* Card hover effects disabled on touch devices */
@media (hover: none) {
    .card:hover {
        transform: none;
        box-shadow: none;
    }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    .collapse,
    .collapsing {
        transition: none;
    }
}
