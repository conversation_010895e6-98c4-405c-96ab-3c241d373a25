@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Routing
@inject NavigationManager NavigationManager

<div class="p-4">
    <h4 class="text-white mb-0">
        <i class="fas fa-cog"></i>
        NafaPlace Admin
    </h4>
</div>

<ul class="nav flex-column px-2">
        <!-- Accueil -->
        <li class="nav-item">
            <a class="nav-link @(GetActive(""))" href="">
                <i class="fas fa-home me-2"></i>
                Accueil
            </a>
        </li>

        <!-- Tableau de bord -->
        <li class="nav-item">
            <a class="nav-link @(GetActive("dashboard"))" href="dashboard">
                <i class="fas fa-tachometer-alt me-2"></i>
                Tableau de bord
            </a>
        </li>

        <!-- Section Gestion des utilisateurs -->
        <div class="nav-item px-3 mt-3">
            <div class="nav-section-header" @onclick="ToggleUsersSection">
                <span class="fas fa-users me-2" aria-hidden="true"></span>
                <span>Gestion des utilisateurs</span>
                <span class="fas @(showUsersSection ? "fa-chevron-down" : "fa-chevron-right") ms-auto" aria-hidden="true"></span>
            </div>
        </div>
        <div class="nav-subsection @(showUsersSection ? "show" : "")">
            <div class="nav-item px-4">
                <a class="nav-link @(GetActive("users"))" href="users">
                    <span class="fas fa-user" aria-hidden="true"></span> Utilisateurs
                </a>
            </div>
            <div class="nav-item px-4">
                <a class="nav-link @(GetActive("roles"))" href="roles">
                    <span class="fas fa-user-tag" aria-hidden="true"></span> Rôles
                </a>
            </div>
            <div class="nav-item px-4">
                <a class="nav-link @(GetActive("sellers"))" href="sellers">
                    <span class="fas fa-store" aria-hidden="true"></span> Vendeurs
                </a>
            </div>
        </div>

        <!-- Section Catalogue -->
        <div class="nav-item px-3 mt-2">
            <div class="nav-section-header" @onclick="ToggleCatalogSection">
                <span class="fas fa-box me-2" aria-hidden="true"></span>
                <span>Catalogue</span>
                <span class="fas @(showCatalogSection ? "fa-chevron-down" : "fa-chevron-right") ms-auto" aria-hidden="true"></span>
            </div>
        </div>
        <div class="nav-subsection @(showCatalogSection ? "show" : "")">
            <div class="nav-item px-4">
                <a class="nav-link @(GetActive("products"))" href="products">
                    <span class="fas fa-box" aria-hidden="true"></span> Produits
                </a>
            </div>
            <div class="nav-item px-4">
                <a class="nav-link @(GetActive("categories"))" href="categories">
                    <span class="fas fa-tags" aria-hidden="true"></span> Catégories
                </a>
            </div>
            <div class="nav-item px-4">
                <a class="nav-link @(GetActive("inventory"))" href="inventory">
                    <span class="fas fa-warehouse" aria-hidden="true"></span> Inventaire
                </a>
            </div>
        </div>

        <!-- Section Commandes & Marketing -->
        <div class="nav-item px-3 mt-2">
            <div class="nav-section-header" @onclick="ToggleOrdersSection">
                <span class="fas fa-shopping-cart me-2" aria-hidden="true"></span>
                <span>Commandes & Marketing</span>
                <span class="fas @(showOrdersSection ? "fa-chevron-down" : "fa-chevron-right") ms-auto" aria-hidden="true"></span>
            </div>
        </div>
        <div class="nav-subsection @(showOrdersSection ? "show" : "")">
            <div class="nav-item px-4">
                <a class="nav-link @(GetActive("orders"))" href="orders">
                    <span class="fas fa-shopping-cart" aria-hidden="true"></span> Commandes
                </a>
            </div>
            <div class="nav-item px-4">
                <a class="nav-link @(GetActive("reviews"))" href="reviews">
                    <span class="fas fa-star" aria-hidden="true"></span> Avis
                </a>
            </div>
            <div class="nav-item px-4">
                <a class="nav-link @(GetActive("coupons"))" href="coupons">
                    <span class="fas fa-ticket-alt" aria-hidden="true"></span> Coupons
                </a>
            </div>
        </div>

        <!-- Section Livraison -->
        <div class="nav-item px-3 mt-2">
            <div class="nav-section-header" @onclick="ToggleDeliverySection">
                <span class="fas fa-truck me-2" aria-hidden="true"></span>
                <span>Livraison</span>
                <span class="fas @(showDeliverySection ? "fa-chevron-down" : "fa-chevron-right") ms-auto" aria-hidden="true"></span>
            </div>
        </div>
        <div class="nav-subsection @(showDeliverySection ? "show" : "")">
            <div class="nav-item px-4">
                <a class="nav-link @(GetActive("delivery/zones"))" href="delivery/zones">
                    <span class="fas fa-map-marked-alt" aria-hidden="true"></span> Zones de Livraison
                </a>
            </div>
            <div class="nav-item px-4">
                <a class="nav-link @(GetActive("delivery/carriers"))" href="delivery/carriers">
                    <span class="fas fa-truck" aria-hidden="true"></span> Transporteurs
                </a>
            </div>
            <div class="nav-item px-4">
                <a class="nav-link @(GetActive("delivery/tracking"))" href="delivery/tracking">
                    <span class="fas fa-route" aria-hidden="true"></span> Suivi des Livraisons
                </a>
            </div>
            <div class="nav-item px-4">
                <a class="nav-link @(GetActive("delivery/calculator"))" href="delivery/calculator">
                    <span class="fas fa-calculator" aria-hidden="true"></span> Calculateur de Frais
                </a>
            </div>
        </div>

        <!-- Section Analytics -->
        <div class="nav-item px-3 mt-3">
            <div class="nav-section-header" @onclick="ToggleAnalyticsSection">
                <span class="fas fa-chart-bar me-2" aria-hidden="true"></span>
                <span>Analytics & Rapports</span>
                <span class="fas @(showAnalyticsSection ? "fa-chevron-down" : "fa-chevron-right") ms-auto" aria-hidden="true"></span>
            </div>
        </div>
        <div class="nav-subsection @(showAnalyticsSection ? "show" : "")">
            <div class="nav-item px-4">
                <a class="nav-link @(GetActive("statistics"))" href="statistics">
                    <span class="fas fa-chart-line" aria-hidden="true"></span> Statistiques
                </a>
            </div>
            <div class="nav-item px-4">
                <a class="nav-link @(GetActive("analytics/dashboard"))" href="analytics/dashboard">
                    <span class="fas fa-chart-bar" aria-hidden="true"></span> Tableau de Bord Avancé
                </a>
            </div>
        </div>
</ul>

<div class="mt-auto p-4">
    <div class="d-flex align-items-center text-white">
        <img src="https://via.placeholder.com/40x40/667eea/ffffff?text=AD"
             class="rounded-circle me-3" alt="Avatar">
        <div>
            <div class="fw-bold">Admin</div>
            <small class="opacity-75"><EMAIL></small>
        </div>
    </div>
</div>

@code {
    private bool collapseNavMenu = true;
    private bool showUsersSection = false;
    private bool showCatalogSection = false;
    private bool showOrdersSection = false;
    private bool showDeliverySection = false;
    private bool showAnalyticsSection = false;

    private string? NavMenuCssClass => collapseNavMenu ? "collapse" : null;

    protected override void OnInitialized()
    {
        // Auto-expand sections based on current route
        var uri = new Uri(NavigationManager.Uri);
        var path = uri.AbsolutePath;

        if (path.Contains("/users") || path.Contains("/roles") || path.Contains("/sellers"))
            showUsersSection = true;
        else if (path.Contains("/products") || path.Contains("/categories") || path.Contains("/inventory"))
            showCatalogSection = true;
        else if (path.Contains("/orders") || path.Contains("/reviews") || path.Contains("/coupons"))
            showOrdersSection = true;
        else if (path.Contains("/delivery"))
            showDeliverySection = true;
    }

    private void ToggleNavMenu()
    {
        collapseNavMenu = !collapseNavMenu;
    }

    private void ToggleUsersSection()
    {
        showUsersSection = !showUsersSection;
    }

    private void ToggleCatalogSection()
    {
        showCatalogSection = !showCatalogSection;
    }

    private void ToggleOrdersSection()
    {
        showOrdersSection = !showOrdersSection;
    }

    private void ToggleDeliverySection()
    {
        showDeliverySection = !showDeliverySection;
    }

    private void ToggleAnalyticsSection()
    {
        showAnalyticsSection = !showAnalyticsSection;
    }

    private string GetActive(string href)
    {
        var uri = new Uri(NavigationManager.Uri);
        var path = uri.AbsolutePath;

        if (string.IsNullOrEmpty(href) && path == "/")
            return "active";

        if (!string.IsNullOrEmpty(href) && path.StartsWith($"/{href}"))
            return "active";

        return "";
    }
}
