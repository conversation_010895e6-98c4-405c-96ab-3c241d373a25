using NafaPlace.Wishlist.Application.DTOs;
using NafaPlace.Wishlist.Application.DTOs.Requests;

namespace NafaPlace.Wishlist.Application.Services;

public interface IWishlistService
{
    // Wishlist operations
    Task<WishlistDto> GetUserWishlistAsync(string userId);
    Task<WishlistDto> CreateWishlistAsync(string userId, CreateWishlistRequest request);
    Task<WishlistDto> UpdateWishlistAsync(string userId, int wishlistId, UpdateWishlistRequest request);
    Task<bool> DeleteWishlistAsync(string userId, int wishlistId);
    Task<List<WishlistSummaryDto>> GetUserWishlistsAsync(string userId);

    // Wishlist items operations
    Task<WishlistItemDto> AddToWishlistAsync(string userId, AddToWishlistRequest request);
    Task<bool> RemoveFromWishlistAsync(string userId, int productId);
    Task<bool> IsProductInWishlistAsync(string userId, int productId);
    Task<WishlistItemDto?> GetWishlistItemAsync(string userId, int productId);
    Task<List<WishlistItemDto>> GetWishlistItemsAsync(string userId, int page = 1, int pageSize = 20);
    Task<bool> ClearWishlistAsync(string userId);

    // Utility operations
    Task<int> GetWishlistItemCountAsync(string userId);
    Task<bool> MoveToCartAsync(string userId, int productId);
    Task<List<WishlistItemDto>> GetRecentlyAddedItemsAsync(string userId, int count = 5);
    Task<bool> UpdateProductAvailabilityAsync(int productId, bool isAvailable);
}
