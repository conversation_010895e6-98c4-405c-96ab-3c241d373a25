@using NafaPlace.Web.Models.Cart
@using NafaPlace.Web.Services
@inject ICartService CartService
@inject IJSRuntime JSRuntime

<div class="card">
    <div class="card-header">
        <h6 class="mb-0">
            <i class="bi bi-tag me-2"></i>Code Promo
        </h6>
    </div>
    <div class="card-body">
        @if (string.IsNullOrEmpty(AppliedCouponCode))
        {
            <div class="input-group">
                <input type="text" class="form-control" placeholder="Entrez votre code promo" 
                       @bind="couponCode" @onkeypress="OnKeyPress" disabled="@_isLoading">
                <button class="btn btn-primary" type="button" @onclick="ApplyCoupon" disabled="@(_isLoading || string.IsNullOrWhiteSpace(couponCode))">
                    @if (_isLoading)
                    {
                        <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                    }
                    else
                    {
                        <i class="bi bi-check-circle me-1"></i>
                    }
                    Appliquer
                </button>
            </div>
            
            @if (!string.IsNullOrEmpty(_errorMessage))
            {
                <div class="alert alert-danger mt-2 mb-0">
                    <small><i class="bi bi-exclamation-triangle me-1"></i>@_errorMessage</small>
                </div>
            }
        }
        else
        {
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <span class="badge bg-success me-2">@AppliedCouponCode</span>
                    @if (CouponDiscount > 0)
                    {
                        <small class="text-success">-@CouponDiscount.ToString("N0") GNF</small>
                    }
                </div>
                <button class="btn btn-sm btn-outline-danger" @onclick="RemoveCoupon" disabled="@_isLoading">
                    @if (_isLoading)
                    {
                        <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                    }
                    else
                    {
                        <i class="bi bi-x"></i>
                    }
                </button>
            </div>
            
            @if (!string.IsNullOrEmpty(CouponDescription))
            {
                <small class="text-muted d-block mt-1">@CouponDescription</small>
            }
        }

        <!-- Suggestions de coupons disponibles -->
        @if (_availableCoupons?.Any() == true && string.IsNullOrEmpty(AppliedCouponCode))
        {
            <div class="mt-3">
                <small class="text-muted d-block mb-2">Coupons disponibles :</small>
                @foreach (var suggestion in _availableCoupons.Take(3))
                {
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <div>
                            <span class="badge bg-light text-dark">@suggestion.Code</span>
                            <small class="text-muted ms-1">@suggestion.Description</small>
                        </div>
                        <button class="btn btn-sm btn-outline-primary" @onclick="() => ApplySuggestedCoupon(suggestion.Code)">
                            Utiliser
                        </button>
                    </div>
                }
            </div>
        }
    </div>
</div>

@code {
    [Parameter] public string? AppliedCouponCode { get; set; }
    [Parameter] public decimal CouponDiscount { get; set; }
    [Parameter] public string? CouponDescription { get; set; }
    [Parameter] public string UserId { get; set; } = string.Empty;
    [Parameter] public EventCallback OnCouponChanged { get; set; }

    private string couponCode = string.Empty;
    private bool _isLoading = false;
    private string? _errorMessage;
    private List<CouponSuggestion>? _availableCoupons;

    protected override async Task OnInitializedAsync()
    {
        await LoadAvailableCoupons();
    }

    private async Task LoadAvailableCoupons()
    {
        try
        {
            // Simuler des coupons disponibles
            // Dans une vraie implémentation, on appellerait l'API Coupon
            _availableCoupons = new List<CouponSuggestion>
            {
                new() { Code = "WELCOME10", Description = "10% de réduction (min. 50,000 GNF)" },
                new() { Code = "FREESHIP", Description = "Livraison gratuite (min. 100,000 GNF)" },
                new() { Code = "SAVE50K", Description = "50,000 GNF de réduction (min. 200,000 GNF)" }
            };
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des coupons disponibles: {ex.Message}");
        }
    }

    private async Task OnKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            await ApplyCoupon();
        }
    }

    private async Task ApplyCoupon()
    {
        if (string.IsNullOrWhiteSpace(couponCode) || string.IsNullOrEmpty(UserId))
            return;

        _isLoading = true;
        _errorMessage = null;
        StateHasChanged();

        try
        {
            await CartService.ApplyCouponAsync(UserId, couponCode.Trim());
            await JSRuntime.InvokeVoidAsync("showToast", "Code promo appliqué avec succès", "success");
            
            // Notifier le parent du changement
            if (OnCouponChanged.HasDelegate)
            {
                await OnCouponChanged.InvokeAsync();
            }
            
            couponCode = string.Empty;
        }
        catch (Exception ex)
        {
            _errorMessage = ex.Message;
            await JSRuntime.InvokeVoidAsync("showToast", $"Erreur: {ex.Message}", "error");
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    private async Task ApplySuggestedCoupon(string code)
    {
        couponCode = code;
        await ApplyCoupon();
    }

    private async Task RemoveCoupon()
    {
        if (string.IsNullOrEmpty(UserId))
            return;

        _isLoading = true;
        StateHasChanged();

        try
        {
            await CartService.RemoveCouponAsync(UserId);
            await JSRuntime.InvokeVoidAsync("showToast", "Code promo retiré", "info");
            
            // Notifier le parent du changement
            if (OnCouponChanged.HasDelegate)
            {
                await OnCouponChanged.InvokeAsync();
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("showToast", $"Erreur: {ex.Message}", "error");
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    public class CouponSuggestion
    {
        public string Code { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
    }
}
