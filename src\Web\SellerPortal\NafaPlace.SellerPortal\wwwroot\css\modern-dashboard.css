/* Modern Dashboard Styles */
:root {
    --sidebar-width: 250px;
    --header-height: 60px;
}

body {
    background-color: #f8f9fa;
}

.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    width: var(--sidebar-width);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    z-index: 1000;
    transition: all 0.3s ease;
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 12px 20px;
    border-radius: 8px;
    margin: 4px 12px;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateX(5px);
}

.main-content {
    margin-left: var(--sidebar-width);
    min-height: 100vh;
}

.header {
    background: white;
    height: var(--header-height);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.stat-card {
    background: white;
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: none;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.chart-container {
    background: white;
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    height: 300px;
    position: relative;
}

.progress-modern {
    height: 8px;
    border-radius: 50px;
    background-color: #e9ecef;
    overflow: hidden;
}

.progress-modern .progress-bar {
    border-radius: 50px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.table-modern {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.table-modern thead {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.table-modern thead th {
    color: white;
    border: none;
    padding: 16px;
    font-weight: 600;
}

.table-modern tbody td {
    padding: 16px;
    border: none;
    border-bottom: 1px solid #f0f0f0;
}

.table-modern tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
}

.badge-modern {
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 500;
    font-size: 0.8rem;
}

.icon-wrapper {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    border-radius: 12px;
    margin-bottom: 16px;
}

.icon-sales {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.icon-orders {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    color: white;
}

.icon-customers {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    color: white;
}

.icon-revenue {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    color: #333;
}

.notification-dot {
    width: 8px;
    height: 8px;
    background: #dc3545;
    border-radius: 50%;
    position: absolute;
    top: -2px;
    right: -2px;
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .main-content {
        margin-left: 0;
    }
}
