using NafaPlace.Catalog.Domain.Enums;

namespace NafaPlace.Catalog.Application.DTOs.Product;

public class ProductDto
{
    public int Id { get; set; }
    public required string Name { get; set; }
    public required string Description { get; set; }
    public decimal Price { get; set; }
    public int CategoryId { get; set; }
    public int StockQuantity { get; set; }

    // Propriété de compatibilité pour les anciens clients qui utilisent "Stock"
    public int Stock => StockQuantity;

    public required string Currency { get; set; }
    public string? Brand { get; set; }
    public string? Model { get; set; }
    public decimal Weight { get; set; }
    public string? Dimensions { get; set; }
    public bool IsActive { get; set; }
    public bool IsFeatured { get; set; }
    public int SellerId { get; set; }

    // Statut d'approbation
    public ProductApprovalStatus ApprovalStatus { get; set; } = ProductApprovalStatus.Pending;
    public string? RejectionReason { get; set; }
    public DateTime? ApprovedAt { get; set; }
    public string? ApprovedBy { get; set; }

    public string? MainImageUrl { get; set; }
    public List<ProductImageDto> Images { get; set; } = new();
    public List<ProductVariantDto> Variants { get; set; } = new();
    public List<ProductAttributeDto> Attributes { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class ProductVariantDto
{
    public int Id { get; set; }
    public required string Name { get; set; }
    public required string Sku { get; set; }
    public decimal Price { get; set; }
    public int StockQuantity { get; set; }
    public List<ProductVariantAttributeDto> Attributes { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class ProductAttributeDto
{
    public int Id { get; set; }
    public required string Name { get; set; }
    public required string Value { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class ProductVariantAttributeDto
{
    public int Id { get; set; }
    public required string Name { get; set; }
    public required string Value { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}
