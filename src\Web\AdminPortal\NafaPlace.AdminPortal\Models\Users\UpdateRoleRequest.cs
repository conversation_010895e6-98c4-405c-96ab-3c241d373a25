using System.ComponentModel.DataAnnotations;

namespace NafaPlace.AdminPortal.Models.Users;

public class UpdateRoleRequest
{
    [Required(ErrorMessage = "Le nom du rôle est requis")]
    [StringLength(50, ErrorMessage = "Le nom du rôle ne peut pas dépasser 50 caractères")]
    public string Name { get; set; } = string.Empty;

    [StringLength(200, ErrorMessage = "La description ne peut pas dépasser 200 caractères")]
    public string Description { get; set; } = string.Empty;
}
