using Microsoft.EntityFrameworkCore;
using NafaPlace.Identity.Infrastructure.Data;
using NafaPlace.Identity.Domain.Models;

namespace NafaPlace.Identity.API.Services;

public interface IUserRoleFixService
{
    Task<string> FixUserRolesAsync();
    Task<List<UserRoleInfo>> GetUsersWithRolesAsync();
}

public class UserRoleInfo
{
    public int UserId { get; set; }
    public string Username { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string? RoleName { get; set; }
    public DateTime? RoleAssignedAt { get; set; }
}

public class UserRoleFixService : IUserRoleFixService
{
    private readonly IdentityDbContext _context;
    private readonly ILogger<UserRoleFixService> _logger;

    public UserRoleFixService(IdentityDbContext context, ILogger<UserRoleFixService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<string> FixUserRolesAsync()
    {
        try
        {
            var results = new List<string>();

            // 1. Vérifier que tous les rôles nécessaires existent
            var adminRole = await _context.Roles.FirstOrDefaultAsync(r => r.Name == "Admin");
            var userRole = await _context.Roles.FirstOrDefaultAsync(r => r.Name == "User");
            var sellerRole = await _context.Roles.FirstOrDefaultAsync(r => r.Name == "Seller");

            if (adminRole == null || userRole == null || sellerRole == null)
            {
                return "Erreur: Tous les rôles requis (Admin, User, Seller) ne sont pas présents dans la base de données.";
            }

            // 2. Trouver tous les utilisateurs sans rôles
            var usersWithoutRoles = await _context.Users
                .Where(u => !u.UserRoles.Any())
                .ToListAsync();

            if (!usersWithoutRoles.Any())
            {
                results.Add("Aucun utilisateur sans rôle trouvé.");
            }
            else
            {
                foreach (var user in usersWithoutRoles)
                {
                    // Déterminer le rôle approprié basé sur le nom d'utilisateur ou l'email
                    Role roleToAssign;
                    
                    if (user.Username.ToLower().Contains("admin") || user.Email.ToLower().Contains("admin"))
                    {
                        roleToAssign = adminRole;
                    }
                    else if (user.Username.ToLower() == "user" || user.Email.ToLower().Contains("user@"))
                    {
                        roleToAssign = userRole;
                    }
                    else
                    {
                        // Par défaut, assigner le rôle Seller pour les comptes du portail vendeur
                        roleToAssign = sellerRole;
                    }

                    // Créer l'association utilisateur-rôle
                    var userRoleAssignment = new UserRole
                    {
                        UserId = user.Id,
                        RoleId = roleToAssign.Id,
                        CreatedAt = DateTime.UtcNow
                    };

                    _context.UserRoles.Add(userRoleAssignment);
                    results.Add($"Rôle '{roleToAssign.Name}' assigné à l'utilisateur '{user.Username}' ({user.Email})");
                }

                await _context.SaveChangesAsync();
            }

            // 3. Statistiques finales
            var totalUsers = await _context.Users.CountAsync();
            var usersWithRoles = await _context.UserRoles.CountAsync();
            
            results.Add($"Résumé: {usersWithRoles} utilisateurs ont maintenant des rôles sur {totalUsers} utilisateurs au total.");

            _logger.LogInformation("Correction des rôles utilisateurs terminée avec succès");
            return string.Join("\n", results);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la correction des rôles utilisateurs");
            return $"Erreur: {ex.Message}";
        }
    }

    public async Task<List<UserRoleInfo>> GetUsersWithRolesAsync()
    {
        try
        {
            var usersWithRoles = await _context.Users
                .Select(u => new UserRoleInfo
                {
                    UserId = u.Id,
                    Username = u.Username,
                    Email = u.Email,
                    RoleName = u.UserRoles.FirstOrDefault() != null ? u.UserRoles.FirstOrDefault()!.Role.Name : null,
                    RoleAssignedAt = u.UserRoles.FirstOrDefault() != null ? u.UserRoles.FirstOrDefault()!.CreatedAt : null
                })
                .OrderBy(u => u.UserId)
                .ToListAsync();

            return usersWithRoles;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des utilisateurs avec rôles");
            return new List<UserRoleInfo>();
        }
    }
}
