using NafaPlace.Notifications.Domain.Models;

namespace NafaPlace.Notifications.Application.DTOs;

public class NotificationDto
{
    public int Id { get; set; }
    public string UserId { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string Recipient { get; set; } = string.Empty;
    public NotificationChannel Channel { get; set; }
    public NotificationType Type { get; set; }
    public string? Data { get; set; }
    public bool IsRead { get; set; }
    public NotificationStatus Status { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? ReadAt { get; set; }
    public DateTime? SentAt { get; set; }
    public DateTime? DeliveredAt { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public NotificationPriority Priority { get; set; }
    public string? ActionUrl { get; set; }
    public string? ImageUrl { get; set; }
    public string? ErrorMessage { get; set; }
    public int RetryCount { get; set; }
    public string? Reference { get; set; }
    public string? TemplateCode { get; set; }

    // Computed properties
    public string Subject => Title;
    public string Content => Message;
}

public class CreateNotificationRequest
{
    public string UserId { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public NotificationType Type { get; set; }
    public string? Data { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public NotificationPriority Priority { get; set; } = NotificationPriority.Normal;
    public string? ActionUrl { get; set; }
    public string? ImageUrl { get; set; }
}

public class BulkNotificationRequest
{
    public List<string> UserIds { get; set; } = new();
    public List<string> Recipients { get; set; } = new();
    public NotificationChannel Channel { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public NotificationType Type { get; set; }
    public string? Data { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public DateTime? ScheduledAt { get; set; }
    public NotificationPriority Priority { get; set; } = NotificationPriority.Normal;
    public string? ActionUrl { get; set; }
    public string? ImageUrl { get; set; }
    public string? Reference { get; set; }
    public string? TemplateCode { get; set; }
    public Dictionary<string, object>? Variables { get; set; }
}

public class NotificationTemplateDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public NotificationChannel Channel { get; set; }
    public string Subject { get; set; } = string.Empty;
    public string Body { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string TitleTemplate { get; set; } = string.Empty;
    public string MessageTemplate { get; set; } = string.Empty;
    public NotificationType Type { get; set; }
    public NotificationPriority Priority { get; set; }
    public string Language { get; set; } = "fr";
    public string? Variables { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class CreateNotificationTemplateRequest
{
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public string Subject { get; set; } = string.Empty;
    public string Body { get; set; } = string.Empty;
    public string Language { get; set; } = "fr";
    public string TitleTemplate { get; set; } = string.Empty;
    public string MessageTemplate { get; set; } = string.Empty;
    public NotificationType Type { get; set; }
    public NotificationChannel Channel { get; set; }
    public NotificationPriority Priority { get; set; } = NotificationPriority.Normal;
    public string? Description { get; set; }
    public string? Variables { get; set; }
    public bool IsActive { get; set; } = true;
}

public class NotificationsPagedResult
{
    public List<NotificationDto> Notifications { get; set; } = new();
    public int TotalCount { get; set; }
    public int UnreadCount { get; set; }
    public int PageNumber { get; set; }
    public int Page { get; set; }
    public int PageSize { get; set; }
    public int TotalPages { get; set; }
}



// New DTOs for enhanced notification system
public class SendNotificationRequest
{
    public string UserId { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public string Recipient { get; set; } = string.Empty;
    public NotificationChannel Channel { get; set; }
    public NotificationType Type { get; set; }
    public string Subject { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public NotificationPriority Priority { get; set; } = NotificationPriority.Normal;
    public DateTime? ScheduledAt { get; set; }
    public string? Reference { get; set; }
    public string? TemplateCode { get; set; }
    public Dictionary<string, object>? Variables { get; set; }
}

public class SendTemplatedNotificationRequest
{
    public string UserId { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public string Recipient { get; set; } = string.Empty;
    public NotificationChannel Channel { get; set; }
    public string TemplateCode { get; set; } = string.Empty;
    public Dictionary<string, object> Variables { get; set; } = new();
    public NotificationPriority Priority { get; set; } = NotificationPriority.Normal;
    public DateTime? ScheduledAt { get; set; }
    public string? Reference { get; set; }
    public string? Language { get; set; } = "fr";
}

public class NotificationPreferenceDto
{
    public int Id { get; set; }
    public string UserId { get; set; } = string.Empty;
    public NotificationType Type { get; set; }
    public NotificationChannel Channel { get; set; }
    public bool IsEnabled { get; set; }
    public TimeSpan? QuietHoursStart { get; set; }
    public TimeSpan? QuietHoursEnd { get; set; }
    public string? TimeZone { get; set; }
    public string? Language { get; set; }
    public NotificationFrequency Frequency { get; set; }
    public DateTime? LastSent { get; set; }
}

public class UpdatePreferenceRequest
{
    public NotificationType Type { get; set; }
    public NotificationChannel Channel { get; set; }
    public bool IsEnabled { get; set; }
    public TimeSpan? QuietHoursStart { get; set; }
    public TimeSpan? QuietHoursEnd { get; set; }
    public NotificationFrequency Frequency { get; set; }
}
