@using Microsoft.AspNetCore.Components.Routing
@using NafaPlace.SellerPortal.Shared
@using NafaPlace.SellerPortal.Layout
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Authorization

<CascadingAuthenticationState>
    <Router AppAssembly="@typeof(App).Assembly">
        <Found Context="routeData">
            <AuthorizeRouteView RouteData="@routeData" DefaultLayout="@typeof(MainLayout)">
                <NotAuthorized>
                    @if (context.User.Identity?.IsAuthenticated != true)
                    {
                        <RedirectToLogin />
                    }
                    else
                    {
                        <p class="alert alert-danger">Vous n'êtes pas autorisé à accéder à cette ressource.</p>
                    }
                </NotAuthorized>
                <Authorizing>
                    <div class="d-flex justify-content-center mt-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Chargement...</span>
                        </div>
                    </div>
                </Authorizing>
            </AuthorizeRouteView>
            <FocusOnNavigate RouteData="@routeData" Selector="h1" />
        </Found>
        <NotFound>
            <PageTitle>Page non trouvée</PageTitle>
            <LayoutView Layout="@typeof(MainLayout)">
                <p role="alert" class="alert alert-danger">Désolé, cette page n'existe pas.</p>
            </LayoutView>
        </NotFound>
    </Router>
</CascadingAuthenticationState>

<NotificationComponent />

@code {
    private class RedirectToLogin : ComponentBase
    {
        [Inject]
        private NavigationManager NavigationManager { get; set; } = default!;

        protected override void OnInitialized()
        {
            NavigationManager.NavigateTo("/login");
        }
    }
}
