// Fonctions JavaScript pour la page Inventory

// Fonction pour télécharger un fichier
window.downloadFile = (fileName, base64Content, contentType) => {
    try {
        // Décoder le contenu base64
        const byteCharacters = atob(base64Content);
        const byteNumbers = new Array(byteCharacters.length);
        
        for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        
        const byteArray = new Uint8Array(byteNumbers);
        const blob = new Blob([byteArray], { type: contentType });
        
        // Créer un lien de téléchargement
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = fileName;
        
        // Déclencher le téléchargement
        document.body.appendChild(link);
        link.click();
        
        // Nettoyer
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
        
    } catch (error) {
        alert('Erreur lors du téléchargement du fichier: ' + error.message);
    }
};

// Fonction pour formater les nombres avec séparateurs de milliers
window.formatNumber = (number) => {
    return new Intl.NumberFormat('fr-FR').format(number);
};

// Fonction pour formater les devises
window.formatCurrency = (amount, currency = 'GNF') => {
    return new Intl.NumberFormat('fr-FR', {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount);
};

// Fonction pour copier du texte dans le presse-papiers
window.copyToClipboard = async (text) => {
    try {
        await navigator.clipboard.writeText(text);
        return true;
    } catch (error) {
        // Erreur lors de la copie
        return false;
    }
};

// Fonction pour afficher une notification toast
window.showToast = (message, type = 'info') => {
    // Créer l'élément toast
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');
    
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    
    // Ajouter le toast au conteneur
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '9999';
        document.body.appendChild(toastContainer);
    }
    
    toastContainer.appendChild(toast);
    
    // Initialiser et afficher le toast
    const bsToast = new bootstrap.Toast(toast, {
        autohide: true,
        delay: 5000
    });
    
    bsToast.show();
    
    // Supprimer le toast après fermeture
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
};

// Fonction pour valider les données d'export
window.validateExportData = (data) => {
    if (!data || typeof data !== 'object') {
        return { isValid: false, error: 'Données d\'export invalides' };
    }
    
    let hasData = false;
    for (const key in data) {
        if (data[key] && Array.isArray(data[key]) && data[key].length > 0) {
            hasData = true;
            break;
        }
    }
    
    if (!hasData) {
        return { isValid: false, error: 'Aucune donnée à exporter' };
    }
    
    return { isValid: true };
};

// Fonction pour générer un nom de fichier unique
window.generateFileName = (prefix, extension) => {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
    return `${prefix}_${timestamp}.${extension}`;
};

// Fonction pour détecter le type de navigateur
window.getBrowserInfo = () => {
    const userAgent = navigator.userAgent;
    let browserName = 'Unknown';
    
    if (userAgent.indexOf('Chrome') > -1) {
        browserName = 'Chrome';
    } else if (userAgent.indexOf('Firefox') > -1) {
        browserName = 'Firefox';
    } else if (userAgent.indexOf('Safari') > -1) {
        browserName = 'Safari';
    } else if (userAgent.indexOf('Edge') > -1) {
        browserName = 'Edge';
    }
    
    return {
        name: browserName,
        userAgent: userAgent,
        supportsDownload: 'download' in document.createElement('a')
    };
};

// Fonction pour vérifier la compatibilité des fonctionnalités
window.checkFeatureSupport = () => {
    return {
        clipboard: !!navigator.clipboard,
        download: 'download' in document.createElement('a'),
        fileReader: !!window.FileReader,
        blob: !!window.Blob,
        url: !!window.URL
    };
};

// Initialisation au chargement de la page
document.addEventListener('DOMContentLoaded', function() {
    // Vérifier la compatibilité des fonctionnalités
    const support = window.checkFeatureSupport();

    // Afficher un avertissement si certaines fonctionnalités ne sont pas supportées
    if (!support.download) {
        // Le téléchargement de fichiers pourrait ne pas fonctionner correctement dans ce navigateur
    }
});

// Export des fonctions pour utilisation globale
window.InventoryUtils = {
    downloadFile: window.downloadFile,
    formatNumber: window.formatNumber,
    formatCurrency: window.formatCurrency,
    copyToClipboard: window.copyToClipboard,
    showToast: window.showToast,
    validateExportData: window.validateExportData,
    generateFileName: window.generateFileName,
    getBrowserInfo: window.getBrowserInfo,
    checkFeatureSupport: window.checkFeatureSupport
};
