using System.ComponentModel.DataAnnotations;
using NafaPlace.Coupon.Domain.Models;

namespace NafaPlace.Coupon.Application.DTOs;

public class CouponDto
{
    public int Id { get; set; }
    public string Code { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public CouponType Type { get; set; }
    public decimal Value { get; set; }
    public decimal? MinimumOrderAmount { get; set; }
    public decimal? MaximumDiscountAmount { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public int? UsageLimit { get; set; }
    public int? UsageLimitPerUser { get; set; }
    public int UsageCount { get; set; }
    public bool IsActive { get; set; }
    public string Currency { get; set; } = "GNF";
    public bool ApplicableToAllProducts { get; set; }
    public List<int>? ApplicableProductIds { get; set; }
    public List<int>? ApplicableCategoryIds { get; set; }
    public List<int>? ApplicableSellerIds { get; set; }
    public List<int>? ExcludedProductIds { get; set; }
    public List<int>? ExcludedCategoryIds { get; set; }
    public List<int>? ExcludedSellerIds { get; set; }
    public string? CreatedBy { get; set; }
    public DateTime CreatedAt { get; set; }
    public bool IsExpired { get; set; }
    public bool IsValid { get; set; }
}

public class CouponValidationResult
{
    public bool IsValid { get; set; }
    public string? ErrorMessage { get; set; }
    public decimal DiscountAmount { get; set; }
    public CouponDto? Coupon { get; set; }
}

public class CouponApplicationResult
{
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public decimal DiscountAmount { get; set; }
    public decimal NewSubTotal { get; set; }
    public decimal NewTotal { get; set; }
    public CouponDto? AppliedCoupon { get; set; }
}

public class CreateCouponRequest
{
    [Required(ErrorMessage = "Le code du coupon est obligatoire")]
    [StringLength(50, ErrorMessage = "Le code du coupon ne doit pas dépasser 50 caractères")]
    public string Code { get; set; } = string.Empty;

    [Required(ErrorMessage = "Le nom du coupon est obligatoire")]
    [StringLength(200, ErrorMessage = "Le nom du coupon ne doit pas dépasser 200 caractères")]
    public string Name { get; set; } = string.Empty;

    [StringLength(500, ErrorMessage = "La description ne doit pas dépasser 500 caractères")]
    public string? Description { get; set; }

    [Required(ErrorMessage = "Le type de coupon est obligatoire")]
    public CouponType Type { get; set; }

    [Required(ErrorMessage = "La valeur du coupon est obligatoire")]
    [Range(0.01, double.MaxValue, ErrorMessage = "La valeur doit être supérieure à 0")]
    public decimal Value { get; set; }

    [Range(0, double.MaxValue, ErrorMessage = "Le montant minimum doit être positif")]
    public decimal? MinimumOrderAmount { get; set; }

    [Range(0, double.MaxValue, ErrorMessage = "Le montant maximum doit être positif")]
    public decimal? MaximumDiscountAmount { get; set; }

    [Required(ErrorMessage = "La date de début est obligatoire")]
    public DateTime StartDate { get; set; }

    [Required(ErrorMessage = "La date de fin est obligatoire")]
    public DateTime EndDate { get; set; }

    [Range(1, int.MaxValue, ErrorMessage = "La limite d'utilisation doit être supérieure à 0")]
    public int? UsageLimit { get; set; }

    [Range(1, int.MaxValue, ErrorMessage = "La limite par utilisateur doit être supérieure à 0")]
    public int? UsageLimitPerUser { get; set; }

    public bool IsActive { get; set; } = true;

    [Required(ErrorMessage = "La devise est obligatoire")]
    [StringLength(3, MinimumLength = 3, ErrorMessage = "La devise doit faire exactement 3 caractères")]
    public string Currency { get; set; } = "GNF";

    public bool ApplicableToAllProducts { get; set; } = true;
    public List<int>? ApplicableProductIds { get; set; }
    public List<int>? ApplicableCategoryIds { get; set; }
    public List<int>? ApplicableSellerIds { get; set; }
    public List<int>? ExcludedProductIds { get; set; }
    public List<int>? ExcludedCategoryIds { get; set; }
    public List<int>? ExcludedSellerIds { get; set; }
}

public class UpdateCouponRequest
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public decimal? Value { get; set; }
    public decimal? MinimumOrderAmount { get; set; }
    public decimal? MaximumDiscountAmount { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public int? UsageLimit { get; set; }
    public int? UsageLimitPerUser { get; set; }
    public bool? IsActive { get; set; }
    public bool? ApplicableToAllProducts { get; set; }
    public List<int>? ApplicableProductIds { get; set; }
    public List<int>? ApplicableCategoryIds { get; set; }
    public List<int>? ApplicableSellerIds { get; set; }
    public List<int>? ExcludedProductIds { get; set; }
    public List<int>? ExcludedCategoryIds { get; set; }
    public List<int>? ExcludedSellerIds { get; set; }
}

public class CartItemForCoupon
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public decimal UnitPrice { get; set; }
    public int Quantity { get; set; }
    public int? CategoryId { get; set; }
    public int? SellerId { get; set; }
    public decimal LineTotal => UnitPrice * Quantity;
}

public class CartForCouponValidation
{
    public string UserId { get; set; } = string.Empty;
    public List<CartItemForCoupon> Items { get; set; } = new();
    public decimal SubTotal { get; set; }
    public string Currency { get; set; } = "GNF";
}
