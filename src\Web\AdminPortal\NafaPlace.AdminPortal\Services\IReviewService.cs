using NafaPlace.AdminPortal.Models.Reviews;

namespace NafaPlace.AdminPortal.Services;

public interface IReviewService
{
    Task<AdminReviewsPagedResponse> GetAdminReviewsAsync(AdminReviewFilterRequest request);
    Task<AdminReviewStatsDto> GetAdminReviewStatsAsync();
    Task<AdminReviewDto?> GetReviewByIdAsync(int reviewId);
    Task<bool> ApproveReviewAsync(int reviewId, string? reason = null);
    Task<bool> RejectReviewAsync(int reviewId, string? reason = null);
    Task<bool> DeleteReviewAsync(int reviewId);
    Task<bool> BulkApproveReviewsAsync(List<int> reviewIds, string? reason = null);
    Task<bool> BulkRejectReviewsAsync(List<int> reviewIds, string? reason = null);
    Task<bool> BulkDeleteReviewsAsync(List<int> reviewIds);
    Task<List<ReviewReportDto>> GetReviewReportsAsync(int reviewId);
    Task<bool> ResolveReviewReportAsync(int reportId, string resolution);
    Task<ReviewAnalyticsDto> GetReviewAnalyticsAsync(string period = "month");
}
