// Dashboard Charts Initialization
window.initializeDashboardCharts = function() {
    // Vérifier si Chart.js est disponible
    if (typeof Chart === 'undefined') {
        console.error('Chart.js n\'est pas chargé');
        return;
    }

    // Configuration par défaut pour tous les graphiques
    Chart.defaults.font.family = "'Inter', sans-serif";
    Chart.defaults.color = '#6c757d';
    Chart.defaults.plugins.legend.display = true;

    // Graphique des ventes (ligne)
    const salesCtx = document.getElementById('salesChart');
    if (salesCtx) {
        new Chart(salesCtx, {
            type: 'line',
            data: {
                labels: ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'],
                datasets: [{
                    label: 'Ventes (GNF)',
                    data: [120000, 190000, 300000, 500000, 200000, 300000, 450000],
                    borderColor: '#E73C30',
                    backgroundColor: 'rgba(231, 60, 48, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#E73C30',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: '#E73C30',
                        borderWidth: 1,
                        callbacks: {
                            label: function(context) {
                                return 'Ventes: ' + context.parsed.y.toLocaleString() + ' GNF';
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        },
                        ticks: {
                            callback: function(value) {
                                return (value / 1000) + 'K';
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            }
        });
    }

    // Graphique des catégories (donut)
    const categoriesCtx = document.getElementById('categoriesChart');
    if (categoriesCtx) {
        new Chart(categoriesCtx, {
            type: 'doughnut',
            data: {
                labels: ['Électronique', 'Vêtements', 'Maison & Jardin', 'Sport & Loisirs', 'Livres'],
                datasets: [{
                    data: [35, 25, 20, 15, 5],
                    backgroundColor: [
                        '#E73C30',
                        '#F96302', 
                        '#28a745',
                        '#17a2b8',
                        '#6f42c1'
                    ],
                    borderWidth: 0,
                    hoverBorderWidth: 3,
                    hoverBorderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '60%',
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            font: {
                                size: 12
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: '#E73C30',
                        borderWidth: 1,
                        callbacks: {
                            label: function(context) {
                                return context.label + ': ' + context.parsed + '%';
                            }
                        }
                    }
                }
            }
        });
    }

    console.log('Graphiques du tableau de bord initialisés avec succès');
};

// Fonction pour mettre à jour les graphiques avec de nouvelles données
window.updateDashboardCharts = function(salesData, categoriesData) {
    if (typeof Chart === 'undefined') {
        console.error('Chart.js n\'est pas chargé');
        return;
    }

    // Mettre à jour le graphique des ventes
    const salesChart = Chart.getChart('salesChart');
    if (salesChart && salesData) {
        salesChart.data.labels = salesData.labels;
        salesChart.data.datasets[0].data = salesData.data;
        salesChart.update();
    }

    // Mettre à jour le graphique des catégories
    const categoriesChart = Chart.getChart('categoriesChart');
    if (categoriesChart && categoriesData) {
        categoriesChart.data.labels = categoriesData.labels;
        categoriesChart.data.datasets[0].data = categoriesData.data;
        categoriesChart.update();
    }
};

// Initialiser automatiquement quand le DOM est prêt
document.addEventListener('DOMContentLoaded', function() {
    // Attendre un peu pour s'assurer que Chart.js est chargé
    setTimeout(function() {
        if (typeof Chart !== 'undefined') {
            console.log('Chart.js détecté, prêt pour l\'initialisation');
        } else {
            console.warn('Chart.js n\'est pas encore chargé');
        }
    }, 100);
});
