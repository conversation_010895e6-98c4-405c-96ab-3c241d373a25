# Build stage
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copier les fichiers csproj et restaurer les dépendances
COPY ["src/Services/Reviews/NafaPlace.Reviews.API/NafaPlace.Reviews.API.csproj", "Services/Reviews/NafaPlace.Reviews.API/"]
COPY ["src/Services/Reviews/NafaPlace.Reviews.Application/NafaPlace.Reviews.Application.csproj", "Services/Reviews/NafaPlace.Reviews.Application/"]
COPY ["src/Services/Reviews/NafaPlace.Reviews.Domain/NafaPlace.Reviews.Domain.csproj", "Services/Reviews/NafaPlace.Reviews.Domain/"]
COPY ["src/Services/Reviews/NafaPlace.Reviews.Infrastructure/NafaPlace.Reviews.Infrastructure.csproj", "Services/Reviews/NafaPlace.Reviews.Infrastructure/"]
RUN dotnet restore "Services/Reviews/NafaPlace.Reviews.API/NafaPlace.Reviews.API.csproj"

# Copier le reste des fichiers et construire
COPY ["src/Services/Reviews/", "Services/Reviews/"]
WORKDIR "/src/Services/Reviews/NafaPlace.Reviews.API"
RUN dotnet build "NafaPlace.Reviews.API.csproj" -c Release -o /app/build

# Publish stage
FROM build AS publish
RUN dotnet publish "NafaPlace.Reviews.API.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Final stage
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS final
WORKDIR /app
COPY --from=publish /app/publish .

# Configuration pour le marché africain
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false
ENV TZ=Africa/Conakry
ENV LANG=fr_GN.UTF-8
ENV LANGUAGE=fr_GN.UTF-8
ENV LC_ALL=fr_GN.UTF-8

EXPOSE 80
ENTRYPOINT ["dotnet", "NafaPlace.Reviews.API.dll"]
