using System.Net.Http.Json;
using System.Text.Json;

namespace NafaPlace.SellerPortal.Services;

public class CouponService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<CouponService> _logger;
    private readonly string _baseUrl;

    public CouponService(HttpClient httpClient, IConfiguration configuration, ILogger<CouponService> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
        _baseUrl = configuration["ApiSettings:BaseUrl"] ?? "http://localhost:5000";
    }

    public async Task<List<CouponDto>> GetCouponsForSellerAsync(int sellerId)
    {
        try
        {
            var response = await _httpClient.GetAsync($"{_baseUrl}/api/coupons/seller/{sellerId}");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<List<CouponDto>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                }) ?? new List<CouponDto>();
            }
            
            _logger.LogError("Failed to get coupons for seller {SellerId}. Status: {StatusCode}", sellerId, response.StatusCode);
            return new List<CouponDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting coupons for seller {SellerId}", sellerId);
            return new List<CouponDto>();
        }
    }

    public async Task<List<CouponDto>> GetActiveCouponsAsync()
    {
        try
        {
            var response = await _httpClient.GetAsync($"{_baseUrl}/api/coupons?isActive=true");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<List<CouponDto>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                }) ?? new List<CouponDto>();
            }
            
            return new List<CouponDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active coupons");
            return new List<CouponDto>();
        }
    }

    public async Task<List<CouponUsageStatsDto>> GetCouponUsageStatsAsync(int sellerId, DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            var query = $"?sellerId={sellerId}";
            if (startDate.HasValue)
                query += $"&startDate={startDate.Value:yyyy-MM-dd}";
            if (endDate.HasValue)
                query += $"&endDate={endDate.Value:yyyy-MM-dd}";

            var response = await _httpClient.GetAsync($"{_baseUrl}/api/coupons/usage-stats{query}");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<List<CouponUsageStatsDto>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                }) ?? new List<CouponUsageStatsDto>();
            }
            
            return new List<CouponUsageStatsDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting coupon usage stats for seller {SellerId}", sellerId);
            return new List<CouponUsageStatsDto>();
        }
    }
}

// DTOs pour le portail vendeur
public class CouponDto
{
    public int Id { get; set; }
    public string Code { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public CouponType Type { get; set; }
    public decimal Value { get; set; }
    public decimal? MinimumOrderAmount { get; set; }
    public decimal? MaximumDiscountAmount { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public int? UsageLimit { get; set; }
    public int? UsageLimitPerUser { get; set; }
    public int UsageCount { get; set; }
    public bool IsActive { get; set; }
    public string Currency { get; set; } = "GNF";
    public bool ApplicableToAllProducts { get; set; }
    public List<int>? ApplicableProductIds { get; set; }
    public List<int>? ApplicableCategoryIds { get; set; }
    public List<int>? ApplicableSellerIds { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class CouponUsageStatsDto
{
    public int CouponId { get; set; }
    public string CouponCode { get; set; } = string.Empty;
    public string CouponName { get; set; } = string.Empty;
    public int TotalUsages { get; set; }
    public decimal TotalDiscountAmount { get; set; }
    public DateTime LastUsed { get; set; }
}

public enum CouponType
{
    FixedAmount = 1,
    Percentage = 2,
    FreeShipping = 3,
    BuyXGetY = 4
}
