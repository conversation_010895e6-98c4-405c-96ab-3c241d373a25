using AutoMapper;
using NafaPlace.Catalog.Application.DTOs.Category;
using NafaPlace.Catalog.Application.DTOs.Product;
using NafaPlace.Catalog.Domain.Models;

namespace NafaPlace.Catalog.Application.Common.Mappings
{
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            CreateMap<Product, ProductDto>();
            CreateMap<ProductImage, ProductImageDto>()
                .ForMember(dest => dest.Url, opt => opt.MapFrom(src => src.ImageUrl))
                .ForMember(dest => dest.ThumbnailUrl, opt => opt.MapFrom(src => src.ThumbnailUrl));
            CreateMap<ProductVariant, ProductVariantDto>()
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name));
            CreateMap<ProductAttribute, ProductAttributeDto>();
            
            // Mapping pour les catégories avec gestion des relations parent/enfant
            CreateMap<Category, CategoryDto>()
                .ForMember(dest => dest.ParentCategory, opt => opt.MapFrom(src => 
                    src.ParentCategory != null ? new CategoryDto 
                    {
                        Id = src.ParentCategory.Id,
                        Name = src.ParentCategory.Name,
                        Description = src.ParentCategory.Description,
                        IconUrl = src.ParentCategory.IconUrl,
                        ImageUrl = src.ParentCategory.ImageUrl,
                        IsActive = src.ParentCategory.IsActive
                    } : null))
                .ForMember(dest => dest.SubCategories, opt => opt.Ignore())
                .ForMember(dest => dest.Products, opt => opt.Ignore());
        }
    }
}
