using Microsoft.Extensions.Caching.Distributed;
using NafaPlace.Cart.Application;
using NafaPlace.Cart.Domain;
using System.Text.Json;
using System.Threading.Tasks;

namespace NafaPlace.Cart.Infrastructure
{
    public class ShoppingCartRepository : IShoppingCartRepository
    {
        private readonly IDistributedCache _redisCache;

        public ShoppingCartRepository(IDistributedCache redisCache)
        {
            _redisCache = redisCache;
        }

        public async Task<ShoppingCart> GetCartAsync(string userId)
        {
            var cart = await _redisCache.GetStringAsync(userId);

            if (string.IsNullOrEmpty(cart))
            {
                return new ShoppingCart(userId);
            }

            return JsonSerializer.Deserialize<ShoppingCart>(cart) ?? new ShoppingCart(userId);
        }

        public async Task<ShoppingCart> UpdateCartAsync(ShoppingCart cart)
        {
            await _redisCache.SetStringAsync(cart.UserId, JsonSerializer.Serialize(cart));
            return await GetCartAsync(cart.UserId);
        }

        public async Task DeleteCartAsync(string userId)
        {
            await _redisCache.RemoveAsync(userId);
        }

        public async Task<ShoppingCart> AddItemToCartAsync(string userId, CartItem item)
        {
            var cart = await GetCartAsync(userId);
            var existingItem = cart.Items.Find(i => i.ProductId == item.ProductId);

            if (existingItem != null)
            {
                existingItem.Quantity += item.Quantity;
            }
            else
            {
                cart.Items.Add(item);
            }

            return await UpdateCartAsync(cart);
        }

        public async Task<ShoppingCart> RemoveItemFromCartAsync(string userId, int productId)
        {
            var cart = await GetCartAsync(userId);
            cart.Items.RemoveAll(i => i.ProductId == productId);
            return await UpdateCartAsync(cart);
        }
    }
}