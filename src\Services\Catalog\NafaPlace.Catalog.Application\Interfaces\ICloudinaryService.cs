using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

namespace NafaPlace.Catalog.Application.Interfaces
{
    public interface ICloudinaryService
    {
        /// <summary>
        /// Upload une image depuis un stream vers Cloudinary
        /// </summary>
        /// <param name="imageStream">Stream de l'image</param>
        /// <param name="fileName">Nom du fichier</param>
        /// <param name="folder">Dossier de destination (optionnel)</param>
        /// <returns>URL publique de l'image uploadée</returns>
        Task<string> UploadImageAsync(Stream imageStream, string fileName, string folder = null);

        /// <summary>
        /// Upload une image depuis une chaîne base64 vers Cloudinary
        /// </summary>
        /// <param name="base64Image">Image en base64</param>
        /// <param name="fileName">Nom du fichier</param>
        /// <param name="folder">Dossier de destination (optionnel)</param>
        /// <returns>URL publique de l'image uploadée</returns>
        Task<string> UploadImageFromBase64Async(string base64Image, string fileName, string folder = null);

        /// <summary>
        /// Upload une image avec transformation (redimensionnement)
        /// </summary>
        /// <param name="imageStream">Stream de l'image</param>
        /// <param name="fileName">Nom du fichier</param>
        /// <param name="width">Largeur souhaitée</param>
        /// <param name="height">Hauteur souhaitée</param>
        /// <param name="folder">Dossier de destination (optionnel)</param>
        /// <returns>URL publique de l'image uploadée</returns>
        Task<string> UploadImageWithTransformationAsync(Stream imageStream, string fileName, int width, int height, string folder = null);

        /// <summary>
        /// Génère une URL de thumbnail pour une image existante
        /// </summary>
        /// <param name="publicId">ID public de l'image sur Cloudinary</param>
        /// <param name="width">Largeur du thumbnail</param>
        /// <param name="height">Hauteur du thumbnail</param>
        /// <returns>URL du thumbnail</returns>
        string GenerateThumbnailUrl(string publicId, int width = 300, int height = 300);

        /// <summary>
        /// Supprime une image de Cloudinary
        /// </summary>
        /// <param name="publicId">ID public de l'image</param>
        /// <returns>True si la suppression a réussi</returns>
        Task<bool> DeleteImageAsync(string publicId);

        /// <summary>
        /// Extrait l'ID public d'une URL Cloudinary
        /// </summary>
        /// <param name="imageUrl">URL de l'image Cloudinary</param>
        /// <returns>ID public de l'image</returns>
        string ExtractPublicIdFromUrl(string imageUrl);
    }
}
