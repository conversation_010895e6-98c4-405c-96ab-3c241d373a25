:root {
    --primary: #E73C30;
    --secondary: #F96302;
    --dark: #003366;  /* Bleu foncé */
    --darker: #002244; /* Bleu plus foncé */
    --accent: #4D90FE; /* Bleu clair accent */
    --light: #f8f9fa;
    --white: #ffffff;
    --gray: #6c757d;
    --border: rgba(255, 255, 255, 0.15);
}

/* Header - Blue Style */
.header-blue {
    background-color: var(--dark);
    color: var(--white);
}

/* Top Bar */
.top-bar {
    background-color: var(--darker);
    padding: 8px 0;
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
    border-bottom: 1px solid var(--border);
}

.top-contact span {
    margin-right: 20px;
}

.top-contact i {
    margin-right: 5px;
    color: var(--accent);
}

.top-links a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    margin-left: 20px;
    transition: color 0.3s;
}

.top-links a:hover {
    color: var(--white);
}

/* Main Header */
.main-header {
    display: flex;
    align-items: center;
    padding: 15px 0;
}

/* Logo */
.logo {
    font-size: 1.8rem;
    font-weight: 700;
    text-decoration: none;
    color: var(--white);
}

.logo-nafa {
    color: var(--primary);
}

.logo-place {
    color: var(--secondary);
}

/* Search Form */
.search-wrapper {
    flex-grow: 1;
    margin: 0 20px;
    display: flex;
}

.search-form {
    display: flex;
    align-items: center;
    width: 100%;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 30px;
    overflow: hidden;
    transition: all 0.3s;
}

.search-form:focus-within {
    background-color: rgba(255, 255, 255, 0.15);
}

.search-category {
    background-color: rgba(0, 0, 0, 0.2);
    border: none;
    color: var(--white);
    font-size: 0.9rem;
    padding: 12px 20px;
    border-radius: 30px 0 0 30px;
    cursor: pointer;
    min-width: 140px;
    outline: none;
}

.search-category option {
    background-color: var(--dark);
    color: var(--white);
}

.search-input {
    flex-grow: 1;
    border: none;
    background-color: transparent;
    padding: 12px 20px;
    color: var(--white);
    outline: none;
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.search-btn {
    background-color: var(--secondary);
    border: none;
    color: var(--white);
    padding: 12px 25px;
    font-weight: 500;
    cursor: pointer;
    border-radius: 0 30px 30px 0;
    transition: background-color 0.3s;
}

.search-btn:hover {
    background-color: var(--primary);
}

/* User Actions */
.user-actions {
    display: flex;
    align-items: center;
    position: relative;
    z-index: 9998;
}

.action-item {
    margin-left: 15px;
    text-decoration: none;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.action-icon {
    font-size: 1.4rem;
    color: var(--white);
    transition: color 0.3s;
}

.action-label {
    font-size: 0.7rem;
    color: rgba(255, 255, 255, 0.7);
    margin-top: 3px;
    transition: color 0.3s;
}

.action-badge {
    position: absolute;
    top: -5px;
    right: -8px;
    background-color: var(--primary);
    color: var(--white);
    font-size: 0.65rem;
    font-weight: 600;
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 9px;
    padding: 0 5px;
}

.action-item:hover .action-icon,
.action-item:hover .action-label {
    color: var(--accent);
}

/* Categories Button */
.categories-button {
    position: relative;
    background-color: var(--primary);
    color: var(--white);
    font-weight: 600;
    padding: 15px 25px;
    display: flex;
    align-items: center;
    border: none;
    cursor: pointer;
    z-index: 1052;
}

.categories-button i {
    margin-right: 10px;
}

/* Products Button */
.products-button {
    position: relative;
    background-color: var(--secondary);
    color: var(--white);
    font-weight: 600;
    padding: 15px 25px;
    display: flex;
    align-items: center;
    border: none;
    cursor: pointer;
    z-index: 1052;
    margin-left: 10px;
}

.products-button i {
    margin-right: 10px;
}

/* Dropdown Menus */
.dropdown-menu-custom {
    position: absolute;
    top: 100%;
    left: 0;
    width: 270px;
    background-color: var(--white);
    border-radius: 0 0 5px 5px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    display: none;
    z-index: 1000;
}

/* Bootstrap Dropdown Fixes */
.navbar-blue .dropdown {
    position: relative;
    z-index: 9999;
}

.navbar-blue .dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 9999 !important;
    min-width: 250px;
    background-color: var(--white);
    border: none;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    margin-top: 0;
    padding: 0.5rem 0;
    pointer-events: auto;
    display: none;
}

.navbar-blue .dropdown-menu.show {
    display: block !important;
    pointer-events: auto !important;
    z-index: 9999 !important;
}

/* User Actions Dropdown Fixes */
.user-actions .dropdown {
    position: relative;
    z-index: 99999;
}

.user-actions .dropdown-menu {
    position: fixed !important;
    top: 70px !important;
    right: 10px !important;
    left: auto !important;
    z-index: 99999 !important;
    min-width: 220px;
    max-width: 280px;
    background-color: var(--white);
    border: none;
    border-radius: 8px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.25);
    margin-top: 0;
    padding: 0.5rem 0;
    pointer-events: auto;
    display: none;
}

.user-actions .dropdown-menu.show {
    display: block !important;
    pointer-events: auto !important;
    z-index: 99999 !important;
    position: fixed !important;
}

.user-actions .dropdown-item {
    padding: 0.75rem 1.25rem;
    color: var(--dark);
    transition: all 0.3s ease;
    border: none;
    background: none;
    display: block;
    width: 100%;
    text-decoration: none;
    cursor: pointer;
    pointer-events: auto;
}

.user-actions .dropdown-item:hover,
.user-actions .dropdown-item:focus {
    background-color: rgba(231, 60, 48, 0.05);
    color: var(--primary);
    padding-left: 1.5rem;
}

.user-actions .dropdown-item i {
    color: var(--secondary);
    margin-right: 0.75rem;
    width: 16px;
    text-align: center;
}

.user-actions .dropdown-divider {
    margin: 0.5rem 0;
    border-color: rgba(0, 0, 0, 0.1);
}

/* Force all dropdowns to be on top */
.dropdown-menu {
    z-index: 99999 !important;
}

.dropdown-menu.show {
    z-index: 99999 !important;
}

/* Mobile Menu Styles */
.mobile-dropdown {
    width: 100%;
}

.mobile-dropdown-toggle {
    width: 100%;
    display: flex !important;
    align-items: center;
    justify-content: space-between;
    border: none;
    background: none;
    text-align: left;
    position: relative;
}

.mobile-dropdown-toggle:focus {
    outline: none;
    box-shadow: none;
}

.mobile-dropdown-toggle:hover {
    background-color: rgba(255, 255, 255, 0.05);
    color: var(--accent);
}

.mobile-dropdown-toggle .bi-chevron-down {
    transition: transform 0.3s ease;
    font-size: 0.8rem;
    color: var(--accent);
}

.mobile-dropdown-toggle[aria-expanded="true"] .bi-chevron-down {
    transform: rotate(180deg);
}

.mobile-submenu {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.03));
    padding: 0.75rem 0;
    margin: 0.5rem 0 0.5rem 1rem;
    border-left: 3px solid var(--accent);
    border-radius: 0 12px 12px 0;
    backdrop-filter: blur(10px);
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.mobile-submenu-link {
    display: block;
    padding: 0.875rem 1.25rem;
    color: rgba(255, 255, 255, 0.95);
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 0.9rem;
    font-weight: 500;
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
    position: relative;
    overflow: hidden;
}

.mobile-submenu-link::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 0;
    background: linear-gradient(90deg, var(--accent), var(--primary));
    transition: width 0.3s ease;
    z-index: -1;
}

.mobile-submenu-link:last-child {
    border-bottom: none;
    border-radius: 0 0 12px 0;
}

.mobile-submenu-link:hover {
    color: white;
    padding-left: 1.75rem;
    transform: translateX(8px);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.mobile-submenu-link:hover::before {
    width: 100%;
}

.mobile-submenu-link i {
    margin-right: 0.875rem;
    width: 20px;
    color: var(--accent);
    transition: all 0.3s ease;
}

.mobile-submenu-link:hover i {
    color: white;
    transform: scale(1.1);
}

.mobile-submenu-link.text-primary i {
    color: var(--primary);
}

/* Responsive fixes */
@media (max-width: 991.98px) {
    /* Garder la navbar visible en mobile mais l'adapter */
    .navbar-blue {
        display: block !important;
        padding: 0.5rem 0;
        overflow-x: auto;
        white-space: nowrap;
        background-color: var(--dark) !important;
    }

    .navbar-container {
        display: flex;
        flex-wrap: nowrap;
        gap: 0.5rem;
        min-width: max-content;
    }

    /* Adapter les boutons de catégories et produits */
    .categories-button,
    .products-button {
        font-size: 0.85rem;
        padding: 0.5rem 0.75rem;
        white-space: nowrap;
    }

    /* Adapter la navigation principale */
    .main-nav {
        display: flex;
        flex-wrap: nowrap;
        gap: 0.25rem;
        margin-left: 0.5rem;
    }

    .nav-link {
        font-size: 0.85rem;
        padding: 0.5rem 0.75rem !important;
        white-space: nowrap;
    }

    /* Adapter les dropdowns pour mobile */
    .dropdown-menu {
        position: fixed !important;
        z-index: 99999 !important;
        max-height: 70vh;
        overflow-y: auto;
        min-width: 250px;
        left: 10px !important;
        right: 10px !important;
        width: auto !important;
        margin-top: 0.5rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    }

    .services-dropdown {
        min-width: 280px;
    }

    /* Améliorer l'apparence des éléments dropdown en mobile */
    .dropdown-item {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }

    .dropdown-item i {
        width: 20px;
        text-align: center;
    }

    .main-header {
        padding: 0.5rem 0;
    }

    .menu-btn {
        display: block !important;
        background: none;
        border: none;
        color: var(--white);
        font-size: 1.5rem;
        padding: 0.5rem;
    }

    .user-actions .action-item {
        margin-left: 0.5rem;
    }

    .action-label {
        display: none;
    }

    .search-wrapper {
        flex: 1;
        margin: 0 1rem;
    }
}

@media (max-width: 576px) {
    .top-bar {
        display: none;
    }

    .main-header {
        padding: 0.25rem 0;
    }

    .logo img {
        height: 50px;
    }

    .user-actions .action-item {
        margin-left: 0.25rem;
    }

    .search-wrapper {
        margin: 0 0.5rem;
    }

    /* Sur très petits écrans, utiliser le menu hamburger */
    .navbar-blue {
        display: none !important;
    }

    .menu-btn {
        display: block !important;
    }
}

.navbar-blue .dropdown-item {
    padding: 0.75rem 1.25rem;
    color: var(--dark);
    transition: all 0.3s ease;
    border: none;
    background: none;
}

.navbar-blue .dropdown-item:hover,
.navbar-blue .dropdown-item:focus {
    background-color: rgba(231, 60, 48, 0.05);
    color: var(--primary);
    padding-left: 1.5rem;
}

.navbar-blue .dropdown-item i {
    color: var(--secondary);
    margin-right: 0.75rem;
    width: 16px;
    text-align: center;
}

.navbar-blue .dropdown-divider {
    margin: 0.5rem 0;
    border-color: rgba(0, 0, 0, 0.1);
}

/* Ensure main content is below dropdowns */
.page {
    position: relative;
    z-index: 1;
}

.header-blue {
    position: relative;
    z-index: 9997;
}

main {
    position: relative;
    z-index: 1;
}

/* Ensure hero sections and content sections are below dropdowns */
.hero-section,
.hero,
.bestsellers-section,
.promotions-section,
.featured-section,
.content-section {
    position: relative;
    z-index: 1;
}

.categories-button:hover + .categories-dropdown,
.categories-dropdown:hover {
    display: block;
}

.products-button:hover + .products-dropdown,
.products-dropdown:hover {
    display: block;
}

.menu-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.menu-item {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.menu-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: var(--dark);
    text-decoration: none;
    transition: all 0.3s;
}

.menu-link:hover {
    background-color: rgba(0, 0, 0, 0.03);
    color: var(--primary);
    padding-left: 25px;
}

.menu-icon {
    margin-right: 15px;
    color: var(--secondary);
    font-size: 1.1rem;
}

/* Navigation Bar */
.navbar-blue {
    background-color: var(--dark);
    padding: 0;
    border-top: 1px solid var(--border);
    position: relative;
    z-index: 9998;
}

.navbar-container {
    display: flex;
    align-items: center;
    position: relative;
}

/* Main Navigation */
.main-nav {
    display: flex;
    margin-left: 20px;
    list-style: none;
    padding: 0;
}

.nav-item {
    position: relative;
}

.nav-link {
    color: var(--white);
    padding: 15px 20px !important;
    font-weight: 500;
    transition: color 0.3s;
    text-decoration: none;
    display: block;
}

.nav-link:hover,
.nav-link.active {
    color: var(--accent);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 20px;
    right: 20px;
    height: 3px;
    background-color: var(--accent);
}

/* Services Dropdown */
.services-dropdown {
    min-width: 320px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    border-radius: 12px;
    padding: 0.5rem 0;
    margin-top: 0.5rem;
}

.services-dropdown .dropdown-item {
    padding: 0.75rem 1.25rem;
    display: flex;
    align-items: center;
    border: none;
    transition: all 0.3s ease;
}

.services-dropdown .dropdown-item:hover {
    background-color: rgba(77, 144, 254, 0.1);
    transform: translateX(5px);
}

.services-dropdown .dropdown-item i {
    font-size: 1.25rem;
    width: 30px;
    flex-shrink: 0;
}

.services-dropdown .dropdown-item div {
    flex-grow: 1;
}

.services-dropdown .dropdown-item strong {
    font-weight: 600;
    color: var(--dark);
}

.services-dropdown .dropdown-item small {
    font-size: 0.75rem;
    line-height: 1.2;
}

.services-dropdown .dropdown-divider {
    margin: 0.5rem 1rem;
    border-color: rgba(0, 0, 0, 0.1);
}

/* Hotline */
.hotline {
    margin-left: auto;
    display: flex;
    align-items: center;
    padding: 15px 0;
}

.hotline-icon {
    font-size: 1.3rem;
    color: var(--primary);
    margin-right: 10px;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

.hotline-content {
    display: flex;
    flex-direction: column;
}

.hotline-label {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.6);
}

.hotline-number {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--white);
}

/* Mobile Menu Button */
.menu-btn {
    display: none;
    background: transparent;
    border: none;
    color: var(--white);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    margin-right: 15px;
}

/* Mobile Navigation */
.offcanvas {
    background-color: var(--dark);
    color: var(--white);
}

.offcanvas-header {
    background-color: var(--primary);
    padding: 1rem 1.5rem;
}

.offcanvas-title {
    font-weight: 600;
}

.mobile-search {
    padding: 15px;
    position: relative;
}

.mobile-search-input {
    width: 100%;
    background-color: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: 25px;
    padding: 12px 20px 12px 45px;
    color: var(--white);
}

.mobile-search-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.mobile-search-icon {
    position: absolute;
    left: 30px;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.5);
}

.mobile-nav {
    list-style: none;
    padding: 0;
    margin: 0;
}

.mobile-nav-item {
    border-bottom: 1px solid var(--border);
}

.mobile-nav-link {
    display: flex;
    align-items: center;
    padding: 1.125rem 1.5rem;
    color: rgba(255, 255, 255, 0.95);
    text-decoration: none;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    width: 100%;
    border: none;
    background: linear-gradient(135deg, transparent, rgba(255, 255, 255, 0.02));
    text-align: left;
    position: relative;
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.mobile-nav-link::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 4px;
    background: var(--accent);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.mobile-nav-link:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    color: white;
    padding-left: 2rem;
    transform: translateX(8px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.mobile-nav-link:hover::before {
    transform: scaleY(1);
}

.mobile-nav-icon {
    margin-right: 1rem;
    color: var(--accent);
    font-size: 1.1rem;
    width: 22px;
    text-align: center;
    transition: all 0.3s ease;
}

.mobile-nav-link:hover .mobile-nav-icon {
    color: white;
    transform: scale(1.1);
}

.mobile-account-btn {
    display: block;
    margin: 15px;
    padding: 12px;
    background-color: var(--secondary);
    color: var(--white);
    text-align: center;
    font-weight: 600;
    border-radius: 25px;
    text-decoration: none;
    transition: background-color 0.3s;
}

.mobile-account-btn:hover {
    background-color: var(--primary);
    color: var(--white);
}

/* Promotional Banner - Compact Orange Style */
.promo-banner {
    background: linear-gradient(135deg, #FF6B35 0%, #F59E0B 100%) !important;
    color: white !important;
    padding: 20px 0 !important;
    margin: 30px 0 !important;
    position: relative;
    overflow: hidden;
    border-radius: 0 !important;
    min-height: auto !important;
}

.promo-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.promo-banner h3 {
    font-weight: 700 !important;
    margin-bottom: 8px !important;
    font-size: 1.3rem !important;
    position: relative;
    z-index: 2;
    color: white !important;
}

.promo-banner p {
    margin-bottom: 12px !important;
    opacity: 0.95;
    font-size: 0.95rem !important;
    position: relative;
    z-index: 2;
    color: white !important;
}

.promo-banner .btn-outline-light {
    border: 2px solid rgba(255, 255, 255, 0.8) !important;
    color: white !important;
    padding: 8px 16px !important;
    font-weight: 600;
    border-radius: 20px !important;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(10px);
    position: relative;
    z-index: 2;
    font-size: 0.85rem !important;
}

.promo-banner .btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    border-color: white !important;
    color: white !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.3);
}

.promo-banner .bi-truck {
    color: rgba(255, 255, 255, 0.2) !important;
    font-size: 2.5rem !important;
}

/* Responsive pour la bannière promotionnelle */
@media (max-width: 768px) {
    .promo-banner {
        padding: 15px 0 !important;
        margin: 20px 0 !important;
    }

    .promo-banner h3 {
        font-size: 1.1rem !important;
        margin-bottom: 6px !important;
    }

    .promo-banner p {
        font-size: 0.85rem !important;
        margin-bottom: 10px !important;
    }

    .promo-banner .btn-outline-light {
        padding: 6px 12px !important;
        font-size: 0.8rem !important;
    }

    .promo-banner .bi-truck {
        font-size: 2rem !important;
    }
}

/* Responsive */
@media (max-width: 991.98px) {
    .top-bar {
        display: none;
    }

    .main-header {
        padding: 15px 0;
    }

    .search-wrapper {
        display: none;
    }

    .menu-btn {
        display: block;
    }

    .user-actions {
        margin-left: auto;
    }

    .action-item {
        margin-left: 15px;
    }

    .action-label {
        display: none;
    }

    .navbar-blue {
        display: none;
    }
}

/* Améliorations pour mobile */
@media (max-width: 768px) {
    .main-header {
        padding: 10px 0;
    }

    .logo img {
        height: 60px;
    }

    .action-item {
        margin-left: 10px;
    }

    .action-icon {
        font-size: 1.2rem;
    }

    .action-badge {
        font-size: 0.7rem;
        min-width: 16px;
        height: 16px;
        line-height: 16px;
    }
}

/* Menu mobile amélioré */
.offcanvas-body {
    padding: 0;
}

.mobile-search {
    padding: 1rem;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.mobile-search-input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid #ced4da;
    border-radius: 25px;
    font-size: 0.95rem;
}

.mobile-search-icon {
    position: absolute;
    left: 1.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    z-index: 10;
}

.mobile-nav {
    list-style: none;
    padding: 0;
    margin: 0;
}

.mobile-nav-item {
    border-bottom: 1px solid #f1f3f4;
}

.mobile-nav-link {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    color: #333;
    text-decoration: none;
    font-size: 0.95rem;
    transition: all 0.2s ease;
}

.mobile-nav-link:hover {
    background-color: #f8f9fa;
    color: var(--primary);
}

.mobile-nav-icon {
    margin-right: 0.75rem;
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

/* Amélioration des boutons d'action mobile */
@media (max-width: 576px) {
    .logo img {
        height: 50px;
    }

    .action-item {
        margin-left: 8px;
    }

    .action-icon {
        font-size: 1.1rem;
    }

    .mobile-nav-link {
        padding: 0.875rem 1.25rem;
        font-size: 0.9rem;
    }

    .mobile-search-input {
        padding: 0.625rem 0.875rem 0.625rem 2.25rem;
        font-size: 0.9rem;
    }

    .mobile-search-icon {
        left: 1.5rem;
    }
}
