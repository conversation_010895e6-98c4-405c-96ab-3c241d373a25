using NafaPlace.Web.Models.Catalog;

namespace NafaPlace.Web.Services;

public interface IProductService
{
    Task<ProductSearchResponse> SearchProductsAsync(ProductSearchRequest request);
    Task<ProductDto?> GetProductByIdAsync(int id);
    Task<IEnumerable<ProductDto>> GetRelatedProductsAsync(int productId, int count = 4);
    Task<IEnumerable<ProductDto>> GetFeaturedProductsAsync(int count = 8);
    Task<IEnumerable<ProductDto>> GetNewProductsAsync(int count = 8);
    Task<IEnumerable<ProductDto>> GetSellerProductsAsync(int sellerId, int page, int pageSize);
    Task<IEnumerable<ProductDto>> GetProductsByCategoryAsync(int categoryId, int page, int pageSize);
    Task<IEnumerable<ProductDto>> GetAllProductsAsync();
    string GetImageUrl(ProductImageDto image, bool thumbnail = false);
}
