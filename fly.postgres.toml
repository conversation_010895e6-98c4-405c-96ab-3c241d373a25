app = "nafaplace-test-db"
primary_region = "cdg"

[env]
  POSTGRES_USER = "nafaplace_test"
  POSTGRES_DB = "nafaplace_test"

[[services]]
  http_checks = []
  internal_port = 5432
  processes = ["app"]
  protocol = "tcp"
  script_checks = []

  [[services.ports]]
    port = 5432

  [[services.tcp_checks]]
    grace_period = "1s"
    interval = "15s"
    restart_limit = 0
    timeout = "2s"

[experimental]
  auto_rollback = true

[[vm]]
  cpu_kind = "shared"
  cpus = 1
  memory_mb = 512
