# Script simple pour vérifier les versions .NET
Write-Host "Verification des versions .NET dans NafaPlace" -ForegroundColor Green

# Trouver tous les fichiers .csproj
$csprojFiles = Get-ChildItem -Path . -Recurse -Filter "*.csproj" | Where-Object { $_.FullName -notlike "*\bin\*" -and $_.FullName -notlike "*\obj\*" }

Write-Host "`nFichiers .csproj trouves:" -ForegroundColor Cyan

$net9Count = 0
$net8Count = 0
$otherCount = 0

foreach ($file in $csprojFiles) {
    $content = Get-Content $file.FullName -Raw
    $relativePath = $file.FullName.Replace((Get-Location).Path, "").TrimStart('\')
    
    if ($content -match '<TargetFramework>(.*?)</TargetFramework>') {
        $version = $matches[1]
        
        if ($version -eq "net9.0") {
            Write-Host "  OK  $relativePath - $version" -ForegroundColor Green
            $net9Count++
        } elseif ($version -eq "net8.0") {
            Write-Host "  OLD $relativePath - $version" -ForegroundColor Yellow
            $net8Count++
        } else {
            Write-Host "  ??? $relativePath - $version" -ForegroundColor Red
            $otherCount++
        }
    } else {
        Write-Host "  ERR $relativePath - Version non trouvee" -ForegroundColor Red
        $otherCount++
    }
}

Write-Host "`nResume:" -ForegroundColor Cyan
Write-Host "  .NET 9.0: $net9Count projets" -ForegroundColor Green
Write-Host "  .NET 8.0: $net8Count projets" -ForegroundColor Yellow
Write-Host "  Autres:   $otherCount projets" -ForegroundColor Red

$total = $net9Count + $net8Count + $otherCount
$successRate = [math]::Round(($net9Count / $total) * 100, 1)

Write-Host "`nTaux de conformite: $successRate%" -ForegroundColor $(if($successRate -eq 100){'Green'}elseif($successRate -ge 90){'Yellow'}else{'Red'})

if ($net8Count -gt 0) {
    Write-Host "`nProjets a mettre a jour vers .NET 9.0:" -ForegroundColor Yellow
    foreach ($file in $csprojFiles) {
        $content = Get-Content $file.FullName -Raw
        if ($content -match '<TargetFramework>net8\.0</TargetFramework>') {
            $relativePath = $file.FullName.Replace((Get-Location).Path, "").TrimStart('\')
            Write-Host "  - $relativePath" -ForegroundColor White
        }
    }
}

Write-Host "`nVerification terminee." -ForegroundColor Blue
