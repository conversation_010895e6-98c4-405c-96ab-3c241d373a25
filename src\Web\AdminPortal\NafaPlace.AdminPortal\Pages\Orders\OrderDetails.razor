@page "/orders/{OrderNumber}"
@using NafaPlace.AdminPortal.Models.Orders
@using NafaPlace.AdminPortal.Services
@inject IOrderService OrderService
@inject IJSRuntime JSRuntime
@inject NavigationManager NavigationManager

<PageTitle>Détails de la Commande</PageTitle>

<div class="container-fluid">
    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Chargement...</span>
            </div>
            <p class="mt-2">Chargement des détails de la commande...</p>
        </div>
    }
    else if (order == null)
    {
        <div class="alert alert-danger" role="alert">
            <h4 class="alert-heading">Commande introuvable</h4>
            <p>La commande demandée n'existe pas ou a été supprimée.</p>
            <hr>
            <button class="btn btn-primary" @onclick="@(() => NavigationManager.NavigateTo("/orders"))">
                <i class="fas fa-arrow-left me-2"></i>Retour aux commandes
            </button>
        </div>
    }
    else
    {
        <!-- En-tête de la commande -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="card-title mb-0">
                                <i class="fas fa-shopping-cart me-2"></i>Commande @order.OrderNumber
                            </h3>
                            <small class="text-muted">ID: @order.Id</small>
                        </div>
                        <div>
                            <button class="btn btn-outline-secondary me-2" @onclick="@(() => NavigationManager.NavigateTo("/orders"))">
                                <i class="fas fa-arrow-left me-2"></i>Retour
                            </button>
                            <button class="btn btn-primary" @onclick="ShowUpdateStatusModal">
                                <i class="fas fa-edit me-2"></i>Modifier le statut
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <h6 class="text-muted">Date de commande</h6>
                                <p>@order.OrderDate.ToString("dd/MM/yyyy HH:mm")</p>
                            </div>
                            <div class="col-md-3">
                                <h6 class="text-muted">Statut</h6>
                                <span class="@OrderStatus.GetBadgeClass(order.Status)">
                                    @OrderStatus.GetDisplayName(order.Status)
                                </span>
                            </div>
                            <div class="col-md-3">
                                <h6 class="text-muted">Paiement</h6>
                                <span class="@PaymentStatus.GetBadgeClass(order.PaymentStatus)">
                                    @PaymentStatus.GetDisplayName(order.PaymentStatus)
                                </span>
                                <br />
                                <small class="text-muted">@order.PaymentMethod</small>
                            </div>
                            <div class="col-md-3">
                                <h6 class="text-muted">Montant total</h6>
                                <h4 class="text-primary">@order.TotalAmount.ToString("N0") @order.Currency</h4>
                            </div>
                        </div>
                        
                        @if (!string.IsNullOrEmpty(order.TrackingNumber))
                        {
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <h6 class="text-muted">Numéro de suivi</h6>
                                    <p><code>@order.TrackingNumber</code></p>
                                </div>
                            </div>
                        }
                        
                        @if (!string.IsNullOrEmpty(order.Notes))
                        {
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h6 class="text-muted">Notes</h6>
                                    <p>@order.Notes</p>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Informations client -->
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-user me-2"></i>Informations client
                        </h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Nom :</strong> @order.UserName</p>
                        <p><strong>Email :</strong> @order.UserEmail</p>
                        <p><strong>ID Client :</strong> @order.UserId</p>
                    </div>
                </div>
            </div>

            <!-- Adresse de livraison -->
            <div class="col-md-6">
                @if (order.ShippingAddress != null)
                {
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-shipping-fast me-2"></i>Adresse de livraison
                            </h5>
                        </div>
                        <div class="card-body">
                            <p><strong>@order.ShippingAddress.FullName</strong></p>
                            <p>@order.ShippingAddress.AddressLine1</p>
                            @if (!string.IsNullOrEmpty(order.ShippingAddress.AddressLine2))
                            {
                                <p>@order.ShippingAddress.AddressLine2</p>
                            }
                            <p>@order.ShippingAddress.City, @order.ShippingAddress.State @order.ShippingAddress.PostalCode</p>
                            <p>@order.ShippingAddress.Country</p>
                            <p><strong>Téléphone :</strong> @order.ShippingAddress.PhoneNumber</p>
                        </div>
                    </div>
                }
            </div>
        </div>

        <!-- Articles de la commande -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-box me-2"></i>Articles commandés (@order.Items.Count)
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Produit</th>
                                        <th>SKU</th>
                                        <th>Variante</th>
                                        <th>Prix unitaire</th>
                                        <th>Quantité</th>
                                        <th>Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var item in order.Items)
                                    {
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    @if (!string.IsNullOrEmpty(item.ProductImage))
                                                    {
                                                        <img src="@item.ProductImage" alt="@item.ProductName" class="me-3" style="width: 50px; height: 50px; object-fit: cover;" />
                                                    }
                                                    <div>
                                                        <strong>@item.ProductName</strong>
                                                        <br />
                                                        <small class="text-muted">ID: @item.ProductId</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>@item.ProductSku</td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(item.VariantName))
                                                {
                                                    @item.VariantName
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                            <td>@item.UnitPrice.ToString("N0") @order.Currency</td>
                                            <td>@item.Quantity</td>
                                            <td><strong>@item.TotalPrice.ToString("N0") @order.Currency</strong></td>
                                        </tr>
                                    }
                                </tbody>
                                <tfoot>
                                    <tr class="table-dark">
                                        <th colspan="5" class="text-end">Total de la commande :</th>
                                        <th><strong>@order.TotalAmount.ToString("N0") @order.Currency</strong></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@code {
    [Parameter] public string OrderNumber { get; set; } = string.Empty;

    private OrderDto? order;
    private bool isLoading = true;

    protected override async Task OnInitializedAsync()
    {
        await LoadOrder();
    }

    private async Task LoadOrder()
    {
        isLoading = true;

        try
        {
            order = await OrderService.GetOrderByIdAsync(OrderNumber);
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Erreur lors du chargement de la commande: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void ShowUpdateStatusModal()
    {
        // TODO: Implémenter la modal de mise à jour du statut
        JSRuntime.InvokeVoidAsync("alert", "Modal de mise à jour du statut à implémenter");
    }
}
