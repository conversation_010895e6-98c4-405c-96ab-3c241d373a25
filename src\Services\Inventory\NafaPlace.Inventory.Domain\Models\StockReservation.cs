using System.ComponentModel.DataAnnotations;
using NafaPlace.Inventory.Domain.Common;
using NafaPlace.Inventory.Domain.Enums;

namespace NafaPlace.Inventory.Domain.Models;

public class StockReservation : BaseEntity
{
    [Required]
    public int ProductId { get; set; }

    [Required]
    [MaxLength(50)]
    public required string UserId { get; set; }

    [Required]
    [MaxLength(50)]
    public required string SessionId { get; set; }

    [Required]
    [Range(1, int.MaxValue)]
    public int Quantity { get; set; }

    [Required]
    public ReservationStatus Status { get; set; } = ReservationStatus.Active;

    [Required]
    public DateTime ReservedAt { get; set; } = DateTime.UtcNow;

    [Required]
    public DateTime ExpiresAt { get; set; }

    [MaxLength(50)]
    public string? OrderId { get; set; }

    [MaxLength(200)]
    public string? Reason { get; set; }

    public DateTime? ReleasedAt { get; set; }

    // Computed properties
    public bool IsExpired => DateTime.UtcNow > ExpiresAt;
    public bool IsActive => Status == ReservationStatus.Active && !IsExpired;
    public TimeSpan TimeRemaining => ExpiresAt > DateTime.UtcNow ? ExpiresAt - DateTime.UtcNow : TimeSpan.Zero;
}


