using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using NafaPlace.Identity.Domain.Models;
using NafaPlace.Identity.Domain.Services;

namespace NafaPlace.Identity.Application.Services;

public class TokenService : ITokenService
{
    private readonly string _secretKey;
    private readonly string _issuer;
    private readonly string _audience;
    private readonly int _tokenLifetimeMinutes;

    public TokenService(IConfiguration configuration)
    {
        _secretKey = configuration["JwtSettings:Secret"] ?? throw new ArgumentNullException(nameof(configuration), "JWT secret key is not configured");
        _issuer = configuration["JwtSettings:Issuer"] ?? throw new ArgumentNullException(nameof(configuration), "JWT issuer is not configured");
        _audience = configuration["JwtSettings:Audience"] ?? throw new ArgumentNullException(nameof(configuration), "JWT audience is not configured");
        
        var tokenLifetime = configuration["JwtSettings:TokenLifetimeMinutes"];
        if (!int.TryParse(tokenLifetime, out _tokenLifetimeMinutes))
        {
            _tokenLifetimeMinutes = 60; // Valeur par défaut
        }
    }

    public string GenerateAccessToken(User user)
    {
        var claims = new[]
        {
            new Claim(JwtRegisteredClaimNames.Sub, user.Id.ToString()),
            new Claim(JwtRegisteredClaimNames.Email, user.Email),
            new Claim(JwtRegisteredClaimNames.UniqueName, user.Username),
            new Claim(JwtRegisteredClaimNames.Jti, Random.Shared.Next(1, int.MaxValue).ToString()),
        };

        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_secretKey));
        var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

        var token = new JwtSecurityToken(
            issuer: _issuer,
            audience: _audience,
            claims: claims,
            expires: DateTime.UtcNow.AddMinutes(_tokenLifetimeMinutes),
            signingCredentials: creds);

        return new JwtSecurityTokenHandler().WriteToken(token);
    }

    public string GenerateRefreshToken()
    {
        var randomNumber = new byte[64];
        using var rng = RandomNumberGenerator.Create();
        rng.GetBytes(randomNumber);
        return Convert.ToBase64String(randomNumber);
    }

    public int? ValidateRefreshToken(string refreshToken)
    {
        try
        {
            // Dans une implémentation réelle, nous vérifierions si le refresh token
            // existe dans la base de données et n'est pas expiré ou révoqué
            return 1; // Simulé pour les tests
        }
        catch
        {
            return null;
        }
    }

    public bool ValidateToken(string? token)
    {
        if (string.IsNullOrEmpty(token))
            return false;

        var tokenHandler = new JwtSecurityTokenHandler();
        var key = Encoding.UTF8.GetBytes(_secretKey);

        try
        {
            tokenHandler.ValidateToken(token, new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(key),
                ValidateIssuer = true,
                ValidateAudience = true,
                ValidIssuer = _issuer,
                ValidAudience = _audience,
                ClockSkew = TimeSpan.Zero
            }, out _);

            return true;
        }
        catch
        {
            return false;
        }
    }
}
