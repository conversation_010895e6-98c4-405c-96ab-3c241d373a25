@page "/track-delivery"
@page "/track/{trackingNumber}"
@using NafaPlace.Web.Services
@inject IDeliveryTrackingService DeliveryTrackingService
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@inject ILogger<TrackDelivery> Logger

<PageTitle>Suivi de Livraison - NafaPlace</PageTitle>

<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="text-center mb-5">
                <h1 class="display-5 fw-bold text-primary">
                    <i class="fas fa-shipping-fast me-3"></i>
                    Suivi de Livraison
                </h1>
                <p class="lead text-muted">Suivez votre commande en temps réel</p>
            </div>

            <!-- Formulaire de recherche -->
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <div class="row align-items-end">
                        <div class="col-md-8">
                            <label for="trackingInput" class="form-label fw-semibold">Numéro de suivi</label>
                            <input type="text" 
                                   id="trackingInput" 
                                   class="form-control form-control-lg" 
                                   @bind="searchTrackingNumber" 
                                   @onkeypress="OnSearchKeyPress"
                                   placeholder="Entrez votre numéro de suivi (ex: NP20241220001)">
                        </div>
                        <div class="col-md-4">
                            <button class="btn btn-primary btn-lg w-100" 
                                    @onclick="SearchTracking" 
                                    disabled="@isSearching">
                                @if (isSearching)
                                {
                                    <span class="spinner-border spinner-border-sm me-2"></span>
                                }
                                <i class="fas fa-search me-2"></i>Suivre
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Résultats du suivi -->
            @if (isSearching)
            {
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
                        <span class="visually-hidden">Recherche en cours...</span>
                    </div>
                    <p class="mt-3 text-muted">Recherche des informations de livraison...</p>
                </div>
            }
            else if (trackingEvents.Any())
            {
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-route me-2"></i>
                            Suivi pour: <code class="text-white">@currentTrackingNumber</code>
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Statut actuel -->
                        <div class="alert @GetCurrentStatusAlertClass() d-flex align-items-center mb-4">
                            <i class="fas @GetCurrentStatusIcon() fa-2x me-3"></i>
                            <div>
                                <h6 class="alert-heading mb-1">@GetCurrentStatusText()</h6>
                                <p class="mb-0">@GetCurrentStatusDescription()</p>
                            </div>
                        </div>

                        <!-- Timeline des événements -->
                        <div class="timeline">
                            @foreach (var trackingEvent in trackingEvents.OrderByDescending(t => t.EventDate))
                            {
                                <div class="timeline-item @(trackingEvent == trackingEvents.OrderByDescending(t => t.EventDate).First() ? "current" : "")">
                                    <div class="timeline-marker @GetStatusMarkerClass(trackingEvent.Status)">
                                        <i class="fas @GetStatusIcon(trackingEvent.Status)"></i>
                                    </div>
                                    <div class="timeline-content">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h6 class="mb-1 fw-semibold">@trackingEvent.Description</h6>
                                                <p class="text-muted mb-1">
                                                    <i class="fas fa-clock me-1"></i>
                                                    @trackingEvent.EventDate.ToString("dd/MM/yyyy à HH:mm")
                                                </p>
                                                @if (!string.IsNullOrEmpty(trackingEvent.Location))
                                                {
                                                    <p class="text-muted mb-1">
                                                        <i class="fas fa-map-marker-alt me-1"></i>
                                                        @trackingEvent.Location
                                                    </p>
                                                }
                                                @if (!string.IsNullOrEmpty(trackingEvent.Notes))
                                                {
                                                    <p class="text-muted mb-0">
                                                        <i class="fas fa-sticky-note me-1"></i>
                                                        @trackingEvent.Notes
                                                    </p>
                                                }
                                            </div>
                                            <span class="badge @GetStatusBadgeClass(trackingEvent.Status)">
                                                @GetStatusText(trackingEvent.Status)
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            }
            else if (hasSearched)
            {
                <div class="card shadow-sm">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Aucun résultat trouvé</h5>
                        <p class="text-muted mb-4">
                            Aucune information de livraison trouvée pour le numéro de suivi 
                            <code>@searchTrackingNumber</code>
                        </p>
                        <div class="alert alert-info">
                            <h6 class="alert-heading">Conseils de recherche :</h6>
                            <ul class="mb-0 text-start">
                                <li>Vérifiez que le numéro de suivi est correct</li>
                                <li>Le numéro de suivi commence généralement par "NP"</li>
                                <li>Attendez quelques minutes après la création de votre commande</li>
                                <li>Contactez notre service client si le problème persiste</li>
                            </ul>
                        </div>
                    </div>
                </div>
            }
            else
            {
                <div class="card shadow-sm">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-shipping-fast fa-3x text-primary mb-3"></i>
                        <h5 class="text-muted">Prêt pour le suivi</h5>
                        <p class="text-muted">
                            Entrez votre numéro de suivi dans le champ ci-dessus pour suivre votre livraison.
                        </p>
                        <div class="alert alert-light">
                            <h6 class="alert-heading">Où trouver votre numéro de suivi ?</h6>
                            <ul class="mb-0 text-start">
                                <li>Dans l'email de confirmation de commande</li>
                                <li>Dans votre espace client, section "Mes commandes"</li>
                                <li>Sur le reçu de votre commande</li>
                            </ul>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

<style>
    .timeline {
        position: relative;
        padding-left: 30px;
    }

    .timeline::before {
        content: '';
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #dee2e6;
    }

    .timeline-item {
        position: relative;
        margin-bottom: 30px;
    }

    .timeline-item.current .timeline-content {
        background: linear-gradient(135deg, #e3f2fd 0%, #f8f9fa 100%);
        border-left: 4px solid #2196f3;
    }

    .timeline-marker {
        position: absolute;
        left: -22px;
        top: 0;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 12px;
        z-index: 1;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .timeline-marker.status-pending { background-color: #6c757d; }
    .timeline-marker.status-confirmed { background-color: #0d6efd; }
    .timeline-marker.status-pickedup { background-color: #fd7e14; }
    .timeline-marker.status-intransit { background-color: #6f42c1; }
    .timeline-marker.status-outfordelivery { background-color: #0dcaf0; }
    .timeline-marker.status-delivered { background-color: #198754; }
    .timeline-marker.status-failed { background-color: #dc3545; }
    .timeline-marker.status-returned { background-color: #ffc107; color: #000; }
    .timeline-marker.status-cancelled { background-color: #6c757d; }

    .timeline-content {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 12px;
        border-left: 3px solid #dee2e6;
        transition: all 0.3s ease;
    }

    .timeline-content:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }
</style>

@code {
    [Parameter] public string? TrackingNumber { get; set; }
    
    private List<DeliveryTrackingDto> trackingEvents = new();
    private string searchTrackingNumber = string.Empty;
    private string currentTrackingNumber = string.Empty;
    private bool isSearching = false;
    private bool hasSearched = false;

    protected override async Task OnInitializedAsync()
    {
        if (!string.IsNullOrEmpty(TrackingNumber))
        {
            searchTrackingNumber = TrackingNumber;
            await SearchTracking();
        }
    }

    private async Task OnSearchKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            await SearchTracking();
        }
    }

    private async Task SearchTracking()
    {
        if (string.IsNullOrWhiteSpace(searchTrackingNumber))
        {
            await JSRuntime.InvokeVoidAsync("alert", "Veuillez entrer un numéro de suivi");
            return;
        }

        try
        {
            isSearching = true;
            hasSearched = false;
            trackingEvents.Clear();

            currentTrackingNumber = searchTrackingNumber.Trim();
            trackingEvents = await DeliveryTrackingService.GetDeliveryTrackingAsync(currentTrackingNumber);
            
            hasSearched = true;

            // Update URL without triggering navigation
            var uri = Navigation.GetUriWithQueryParameters(new Dictionary<string, object?>
            {
                ["trackingNumber"] = currentTrackingNumber
            });
            Navigation.NavigateTo($"/track/{currentTrackingNumber}", false, true);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error searching tracking {TrackingNumber}", searchTrackingNumber);
            await JSRuntime.InvokeVoidAsync("alert", "Erreur lors de la recherche. Veuillez réessayer.");
        }
        finally
        {
            isSearching = false;
        }
    }

    private string GetCurrentStatusAlertClass()
    {
        if (!trackingEvents.Any()) return "alert-secondary";
        
        var latestEvent = trackingEvents.OrderByDescending(t => t.EventDate).First();
        return latestEvent.Status.ToLower() switch
        {
            "delivered" => "alert-success",
            "failed" or "cancelled" or "lost" or "damaged" => "alert-danger",
            "outfordelivery" => "alert-info",
            "intransit" or "pickedup" => "alert-warning",
            _ => "alert-primary"
        };
    }

    private string GetCurrentStatusIcon()
    {
        if (!trackingEvents.Any()) return "fa-question";
        
        var latestEvent = trackingEvents.OrderByDescending(t => t.EventDate).First();
        return GetStatusIcon(latestEvent.Status);
    }

    private string GetCurrentStatusText()
    {
        if (!trackingEvents.Any()) return "Statut inconnu";
        
        var latestEvent = trackingEvents.OrderByDescending(t => t.EventDate).First();
        return GetStatusText(latestEvent.Status);
    }

    private string GetCurrentStatusDescription()
    {
        if (!trackingEvents.Any()) return "";
        
        var latestEvent = trackingEvents.OrderByDescending(t => t.EventDate).First();
        return latestEvent.Description;
    }

    private string GetStatusText(string status)
    {
        return status.ToLower() switch
        {
            "pending" => "En attente",
            "confirmed" => "Confirmée",
            "pickedup" => "Récupérée",
            "intransit" => "En transit",
            "outfordelivery" => "En cours de livraison",
            "delivered" => "Livrée",
            "failed" => "Échec de livraison",
            "returned" => "Retournée",
            "cancelled" => "Annulée",
            "lost" => "Perdue",
            "damaged" => "Endommagée",
            _ => "Inconnu"
        };
    }

    private string GetStatusBadgeClass(string status)
    {
        return status.ToLower() switch
        {
            "pending" => "bg-secondary",
            "confirmed" => "bg-primary",
            "pickedup" => "bg-warning text-dark",
            "intransit" => "bg-info",
            "outfordelivery" => "bg-primary",
            "delivered" => "bg-success",
            "failed" => "bg-danger",
            "returned" => "bg-warning text-dark",
            "cancelled" => "bg-secondary",
            "lost" => "bg-danger",
            "damaged" => "bg-danger",
            _ => "bg-light text-dark"
        };
    }

    private string GetStatusMarkerClass(string status)
    {
        return status.ToLower() switch
        {
            "pending" => "status-pending",
            "confirmed" => "status-confirmed",
            "pickedup" => "status-pickedup",
            "intransit" => "status-intransit",
            "outfordelivery" => "status-outfordelivery",
            "delivered" => "status-delivered",
            "failed" => "status-failed",
            "returned" => "status-returned",
            "cancelled" => "status-cancelled",
            _ => "status-pending"
        };
    }

    private string GetStatusIcon(string status)
    {
        return status.ToLower() switch
        {
            "pending" => "fa-clock",
            "confirmed" => "fa-check",
            "pickedup" => "fa-hand-paper",
            "intransit" => "fa-truck",
            "outfordelivery" => "fa-shipping-fast",
            "delivered" => "fa-check-circle",
            "failed" => "fa-times",
            "returned" => "fa-undo",
            "cancelled" => "fa-ban",
            "lost" => "fa-question",
            "damaged" => "fa-exclamation-triangle",
            _ => "fa-circle"
        };
    }
}
