using Microsoft.EntityFrameworkCore;
using NafaPlace.Wishlist.Domain.Models;
using NafaPlace.Wishlist.Infrastructure.Data;

namespace NafaPlace.Wishlist.Infrastructure.Repositories;

public class WishlistRepository : IWishlistRepository
{
    private readonly WishlistDbContext _context;

    public WishlistRepository(WishlistDbContext context)
    {
        _context = context;
    }

    public async Task<Domain.Models.Wishlist?> GetWishlistByIdAsync(int id)
    {
        return await _context.Wishlists
            .Include(w => w.Items)
            .FirstOrDefaultAsync(w => w.Id == id);
    }

    public async Task<Domain.Models.Wishlist?> GetUserWishlistAsync(string userId)
    {
        return await _context.Wishlists
            .Include(w => w.Items.OrderByDescending(i => i.AddedAt))
            .FirstOrDefaultAsync(w => w.UserId == userId);
    }

    public async Task<List<Domain.Models.Wishlist>> GetUserWishlistsAsync(string userId)
    {
        return await _context.Wishlists
            .Include(w => w.Items)
            .Where(w => w.UserId == userId)
            .OrderByDescending(w => w.LastUpdated)
            .ToListAsync();
    }

    public async Task<Domain.Models.Wishlist> CreateWishlistAsync(Domain.Models.Wishlist wishlist)
    {
        _context.Wishlists.Add(wishlist);
        await _context.SaveChangesAsync();
        return wishlist;
    }

    public async Task<Domain.Models.Wishlist> UpdateWishlistAsync(Domain.Models.Wishlist wishlist)
    {
        _context.Wishlists.Update(wishlist);
        await _context.SaveChangesAsync();
        return wishlist;
    }

    public async Task<bool> DeleteWishlistAsync(int id)
    {
        var wishlist = await _context.Wishlists.FindAsync(id);
        if (wishlist == null) return false;

        _context.Wishlists.Remove(wishlist);
        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<WishlistItem?> GetWishlistItemAsync(string userId, int productId)
    {
        return await _context.WishlistItems
            .FirstOrDefaultAsync(i => i.UserId == userId && i.ProductId == productId);
    }

    public async Task<List<WishlistItem>> GetWishlistItemsAsync(string userId, int page = 1, int pageSize = 20)
    {
        return await _context.WishlistItems
            .Where(i => i.UserId == userId)
            .OrderByDescending(i => i.AddedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();
    }

    public async Task<WishlistItem> AddItemToWishlistAsync(int wishlistId, WishlistItem item)
    {
        // Set the wishlist foreign key
        var wishlist = await _context.Wishlists.FindAsync(wishlistId);
        if (wishlist != null)
        {
            item.Wishlist = wishlist;
        }

        _context.WishlistItems.Add(item);
        await _context.SaveChangesAsync();
        return item;
    }

    public async Task<bool> RemoveItemFromWishlistAsync(string userId, int productId)
    {
        var item = await _context.WishlistItems
            .FirstOrDefaultAsync(i => i.UserId == userId && i.ProductId == productId);
        
        if (item == null) return false;

        _context.WishlistItems.Remove(item);
        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> ClearWishlistAsync(string userId)
    {
        var items = await _context.WishlistItems
            .Where(i => i.UserId == userId)
            .ToListAsync();

        if (!items.Any()) return true;

        _context.WishlistItems.RemoveRange(items);
        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<int> GetWishlistItemCountAsync(string userId)
    {
        return await _context.WishlistItems
            .CountAsync(i => i.UserId == userId);
    }

    public async Task<List<WishlistItem>> GetRecentlyAddedItemsAsync(string userId, int count = 5)
    {
        return await _context.WishlistItems
            .Where(i => i.UserId == userId)
            .OrderByDescending(i => i.AddedAt)
            .Take(count)
            .ToListAsync();
    }

    public async Task<bool> UpdateProductAvailabilityAsync(int productId, bool isAvailable)
    {
        var items = await _context.WishlistItems
            .Where(i => i.ProductId == productId)
            .ToListAsync();

        if (!items.Any()) return true;

        foreach (var item in items)
        {
            item.IsAvailable = isAvailable;
        }

        await _context.SaveChangesAsync();
        return true;
    }
}
