using NafaPlace.Cart.Application.DTOs;
using NafaPlace.Cart.Domain;

namespace NafaPlace.Cart.Application.Services;

public class CartService : ICartService
{
    private readonly IShoppingCartRepository _repository;
    private readonly IProductService _productService;

    public CartService(IShoppingCartRepository repository, IProductService productService)
    {
        _repository = repository;
        _productService = productService;
    }

    public async Task<ShoppingCartDto> GetCartAsync(string userId)
    {
        var cart = await _repository.GetCartAsync(userId);
        return MapToDto(cart);
    }

    public async Task<ShoppingCartDto> AddItemToCartAsync(string userId, AddToCartRequest request)
    {
        Console.WriteLine($"🛒 DEBUG CartService: AddItemToCartAsync - UserId: {userId}, ProductId: {request.ProductId}, Quantity: {request.Quantity}");

        // Validate product exists and get details
        var product = await _productService.GetProductAsync(request.ProductId);
        if (product == null)
        {
            Console.WriteLine($"❌ DEBUG CartService: Produit {request.ProductId} non trouvé");
            throw new ArgumentException("Product not found");
        }

        Console.WriteLine($"📦 DEBUG CartService: Produit trouvé - Name: {product.Name}, Price: {product.Price}, Stock: {product.Stock}, IsAvailable: {product.IsAvailable}");

        if (!product.IsAvailable)
        {
            Console.WriteLine($"❌ DEBUG CartService: Produit {request.ProductId} non disponible (IsAvailable = false)");
            throw new InvalidOperationException("Product is not available");
        }

        if (product.Stock < request.Quantity)
        {
            Console.WriteLine($"❌ DEBUG CartService: Stock insuffisant - Stock: {product.Stock}, Demandé: {request.Quantity}");
            throw new InvalidOperationException("Insufficient stock");
        }

        Console.WriteLine($"✅ DEBUG CartService: Validation OK, ajout au panier...");

        var cartItem = new CartItem
        {
            ProductId = request.ProductId,
            ProductName = product.Name,
            UnitPrice = product.Price,
            Quantity = request.Quantity,
            ImageUrl = product.ImageUrl ?? string.Empty,
            CategoryId = product.CategoryId,
            SellerId = product.SellerId
        };

        var cart = await _repository.AddItemToCartAsync(userId, cartItem);
        return MapToDto(cart);
    }

    public async Task<ShoppingCartDto> UpdateItemQuantityAsync(string userId, UpdateCartItemRequest request)
    {
        if (request.Quantity <= 0)
        {
            return await RemoveItemFromCartAsync(userId, request.ProductId);
        }

        // Validate product availability
        var product = await _productService.GetProductAsync(request.ProductId);
        if (product == null || !product.IsAvailable || product.Stock < request.Quantity)
        {
            throw new InvalidOperationException("Product is not available or insufficient stock");
        }

        var cart = await _repository.GetCartAsync(userId);
        var existingItem = cart.Items.FirstOrDefault(i => i.ProductId == request.ProductId);
        
        if (existingItem != null)
        {
            existingItem.Quantity = request.Quantity;
            cart = await _repository.UpdateCartAsync(cart);
        }

        return MapToDto(cart);
    }

    public async Task<ShoppingCartDto> RemoveItemFromCartAsync(string userId, int productId)
    {
        var cart = await _repository.RemoveItemFromCartAsync(userId, productId);
        return MapToDto(cart);
    }

    public async Task<bool> ClearCartAsync(string userId)
    {
        await _repository.DeleteCartAsync(userId);
        return true;
    }

    public async Task<CartSummaryDto> GetCartSummaryAsync(string userId)
    {
        var cart = await _repository.GetCartAsync(userId);
        var dto = MapToDto(cart);
        
        return new CartSummaryDto
        {
            ItemCount = dto.ItemCount,
            SubTotal = dto.SubTotal,
            Total = dto.Total,
            Currency = dto.Currency
        };
    }

    public async Task<bool> ValidateCartAsync(string userId)
    {
        var cart = await _repository.GetCartAsync(userId);
        bool isValid = true;

        foreach (var item in cart.Items.ToList())
        {
            var product = await _productService.GetProductAsync(item.ProductId);
            if (product == null || !product.IsAvailable || product.Stock < item.Quantity)
            {
                // Remove invalid items
                await _repository.RemoveItemFromCartAsync(userId, item.ProductId);
                isValid = false;
            }
            else if (product.Price != item.UnitPrice)
            {
                // Update price if changed
                item.UnitPrice = product.Price;
                await _repository.UpdateCartAsync(cart);
            }
        }

        return isValid;
    }

    public async Task<ShoppingCartDto> ApplyCouponAsync(string userId, string couponCode)
    {
        var cart = await _repository.GetCartAsync(userId);

        // Valider et appliquer le coupon via un service externe ou une API
        // Pour l'instant, on simule une validation simple
        var discountAmount = await ValidateAndCalculateCouponDiscount(couponCode, cart);

        if (discountAmount > 0)
        {
            // Stocker le coupon appliqué dans le panier (on peut étendre le modèle ShoppingCart)
            // Pour l'instant, on retourne le panier avec la réduction calculée
            var dto = MapToDto(cart);
            dto.CouponCode = couponCode;
            dto.CouponDiscount = discountAmount;
            dto.Total = Math.Max(0, dto.SubTotal - discountAmount + dto.ShippingFee + dto.Tax);
            return dto;
        }

        throw new InvalidOperationException("Code coupon invalide ou expiré");
    }

    public async Task<ShoppingCartDto> RemoveCouponAsync(string userId)
    {
        var cart = await _repository.GetCartAsync(userId);
        var dto = MapToDto(cart);

        // Supprimer le coupon (réinitialiser les valeurs)
        dto.CouponCode = null;
        dto.CouponDiscount = 0;
        dto.Total = dto.SubTotal + dto.ShippingFee + dto.Tax;

        return dto;
    }

    private async Task<decimal> ValidateAndCalculateCouponDiscount(string couponCode, ShoppingCart cart)
    {
        try
        {
            // Appeler le service Coupon pour validation et calcul réels
            using var httpClient = new HttpClient();
            var couponApiUrl = "http://coupon-api/api/coupon"; // URL du service Coupon

            // Préparer les données du panier pour la validation
            var cartForValidation = new
            {
                SubTotal = cart.Items.Sum(i => i.UnitPrice * i.Quantity),
                Items = cart.Items.Select(i => new
                {
                    ProductId = i.ProductId,
                    Quantity = i.Quantity,
                    UnitPrice = i.UnitPrice,
                    CategoryId = 1, // TODO: Récupérer la vraie catégorie
                    SellerId = 1    // TODO: Récupérer le vrai vendeur
                }).ToList(),
                UserId = "temp-user" // TODO: Récupérer le vrai userId
            };

            // Appeler l'API de validation des coupons
            var requestContent = new StringContent(
                System.Text.Json.JsonSerializer.Serialize(new { couponCode, cart = cartForValidation }),
                System.Text.Encoding.UTF8,
                "application/json"
            );

            var response = await httpClient.PostAsync($"{couponApiUrl}/validate", requestContent);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                var validationResult = System.Text.Json.JsonSerializer.Deserialize<CouponValidationResult>(responseContent);

                return validationResult?.DiscountAmount ?? 0;
            }

            // Si l'API n'est pas disponible, utiliser la simulation comme fallback
            return GetFallbackDiscount(couponCode, cart);
        }
        catch (Exception)
        {
            // En cas d'erreur, utiliser la simulation comme fallback
            return GetFallbackDiscount(couponCode, cart);
        }
    }

    private decimal GetFallbackDiscount(string couponCode, ShoppingCart cart)
    {
        // Simulation de validation de coupon (fallback)
        var upperCode = couponCode.ToUpper();
        var subTotal = cart.Items.Sum(i => i.UnitPrice * i.Quantity);

        return upperCode switch
        {
            "WELCOME10" when subTotal >= 50000 => Math.Min(subTotal * 0.10m, 100000), // 10% max 100k GNF
            "FREESHIP" when subTotal >= 100000 => 25000, // Livraison gratuite
            "SAVE50K" when subTotal >= 200000 => 50000, // 50k GNF de réduction
            "NEWCLIENT15" when subTotal >= 75000 => Math.Min(subTotal * 0.15m, 150000), // 15% max 150k GNF
            _ => 0
        };
    }

    // Classe pour la désérialisation de la réponse de validation
    private class CouponValidationResult
    {
        public bool Success { get; set; }
        public decimal DiscountAmount { get; set; }
        public string? Message { get; set; }
    }

    public async Task<decimal> CalculateShippingAsync(string userId, string shippingAddress)
    {
        // TODO: Implement shipping calculation logic
        return 25000; // Default shipping fee in GNF (environ 2.5 USD)
    }

    private static ShoppingCartDto MapToDto(ShoppingCart cart)
    {
        var items = cart.Items.Select(i => new CartItemDto
        {
            ProductId = i.ProductId,
            ProductName = i.ProductName,
            UnitPrice = i.UnitPrice,
            Quantity = i.Quantity,
            ImageUrl = i.ImageUrl,
            CategoryId = i.CategoryId,
            SellerId = i.SellerId
        }).ToList();

        var subTotal = items.Sum(i => i.LineTotal);
        var shippingFee = subTotal > 500000 ? 0 : 25000; // Free shipping over 500,000 GNF (environ 50 USD)
        var tax = subTotal * 0.18m; // 18% VAT
        var total = subTotal + shippingFee + tax;

        return new ShoppingCartDto
        {
            UserId = cart.UserId,
            Items = items,
            SubTotal = subTotal,
            ShippingFee = shippingFee,
            Tax = tax,
            Total = total,
            ItemCount = items.Sum(i => i.Quantity),
            Currency = "GNF",
            LastUpdated = DateTime.UtcNow
        };
    }
}

// Interface for product service
public interface IProductService
{
    Task<ProductInfo?> GetProductAsync(int productId);
}

public class ProductInfo
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public decimal Price { get; set; }
    public int Stock { get; set; }
    public bool IsAvailable { get; set; }
    public string? ImageUrl { get; set; }
    public int CategoryId { get; set; }
    public int SellerId { get; set; }
}

// Implementation of ProductService that calls Catalog API
public class ProductService : IProductService
{
    private readonly HttpClient _httpClient;

    public ProductService(HttpClient httpClient)
    {
        _httpClient = httpClient;
    }

    public async Task<ProductInfo?> GetProductAsync(int productId)
    {
        try
        {
            var response = await _httpClient.GetAsync($"/api/v1/products/{productId}");
            if (!response.IsSuccessStatusCode)
            {
                return null;
            }

            var content = await response.Content.ReadAsStringAsync();
            var productDto = System.Text.Json.JsonSerializer.Deserialize<ProductDto>(content, new System.Text.Json.JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            if (productDto == null) return null;

            return new ProductInfo
            {
                Id = productDto.Id,
                Name = productDto.Name,
                Price = productDto.Price,
                Stock = productDto.StockQuantity,  // ← Corrigé pour utiliser StockQuantity
                IsAvailable = productDto.IsActive && productDto.StockQuantity > 0,  // ← Corrigé
                ImageUrl = productDto.Images?.FirstOrDefault()?.Url,
                CategoryId = productDto.CategoryId,
                SellerId = productDto.SellerId
            };
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération du produit {productId}: {ex.Message}");
            return null;
        }
    }
}

// DTO for deserializing product from Catalog API
public class ProductDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public decimal Price { get; set; }
    public int StockQuantity { get; set; }  // ← Corrigé pour correspondre à l'API Catalog
    public bool IsActive { get; set; }
    public int CategoryId { get; set; }
    public int SellerId { get; set; }
    public List<ProductImageDto>? Images { get; set; }
}

public class ProductImageDto
{
    public string Url { get; set; } = string.Empty;
}
