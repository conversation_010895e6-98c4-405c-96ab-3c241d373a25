namespace NafaPlace.Web.Models.Catalog;

public class ProductSearchRequest
{
    public string? SearchTerm { get; set; }
    public int? CategoryId { get; set; }
    public List<int>? CategoryIds { get; set; }
    public decimal? MinPrice { get; set; }
    public decimal? MaxPrice { get; set; }
    public string? Brand { get; set; }
    public List<string>? Brands { get; set; }
    public bool InStockOnly { get; set; }
    public int? SellerId { get; set; }
    public List<int>? SellerIds { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string? SortBy { get; set; } = "CreatedAt";
    public bool SortDescending { get; set; } = true;

    // Nouveaux filtres avancés
    public decimal? MinRating { get; set; }
    public decimal? MaxRating { get; set; }
    public DateTime? CreatedAfter { get; set; }
    public DateTime? CreatedBefore { get; set; }
    public List<string>? Tags { get; set; }
    public Dictionary<string, List<string>>? Attributes { get; set; }
    public string? Color { get; set; }
    public string? Size { get; set; }
    public string? Material { get; set; }
    public bool? HasDiscount { get; set; }
    public decimal? MinDiscountPercentage { get; set; }
    public bool? IsNewArrival { get; set; }
    public bool? HasFreeShipping { get; set; }

    // Options de recherche avancée
    public bool UseFullTextSearch { get; set; } = true;
    public bool IncludeSimilarProducts { get; set; } = false;
    public string SearchMode { get; set; } = "relevance"; // relevance, exact, fuzzy
    public int? MaxSuggestions { get; set; }
}
