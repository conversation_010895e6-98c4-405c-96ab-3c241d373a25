@page "/auth/logout"
@using NafaPlace.Web.Services
@inject IAuthService AuthService
@inject NavigationManager NavigationManager

<div class="container mt-5">
    <div class="text-center">
        <h3>Déconnexion en cours...</h3>
        <div class="spinner-border text-primary mt-3" role="status">
            <span class="visually-hidden">Chargement...</span>
        </div>
    </div>
</div>

@code {
    protected override async Task OnInitializedAsync()
    {
        await AuthService.Logout();
        NavigationManager.NavigateTo("/", true);
    }
}
