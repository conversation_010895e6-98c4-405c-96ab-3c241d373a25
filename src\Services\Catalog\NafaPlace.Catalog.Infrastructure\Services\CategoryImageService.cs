using System;
using System.IO;
using System.Net.Http;
using System.Threading.Tasks;
using Azure.Storage.Blobs;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using NafaPlace.Catalog.Application.Common.Interfaces;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Processing;
using SixLabors.ImageSharp.Formats.Jpeg;

namespace NafaPlace.Catalog.Infrastructure.Services
{
    public class CategoryImageService : ICategoryImageService
    {
        private readonly BlobServiceClient _blobServiceClient;
        private readonly string _containerName;
        private readonly HttpClient _httpClient;
        private readonly ILogger<CategoryImageService> _logger;

        public CategoryImageService(
            BlobServiceClient blobServiceClient,
            IConfiguration configuration,
            ILogger<CategoryImageService> logger)
        {
            _blobServiceClient = blobServiceClient;
            _containerName = configuration["Storage:CategoryImagesContainer"] ?? "category-images";
            _httpClient = new HttpClient();
            _logger = logger;
        }

        public async Task<string> UploadCategoryImageAsync(string base64Image)
        {
            try
            {
                if (string.IsNullOrEmpty(base64Image))
                {
                    return string.Empty;
                }

                // Supprimer le préfixe data:image si présent
                string cleanBase64 = base64Image;
                if (base64Image.Contains(","))
                {
                    cleanBase64 = base64Image.Split(',')[1];
                }

                var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);
                await containerClient.CreateIfNotExistsAsync();

                var imageName = $"category_{Guid.NewGuid()}.jpg";
                var blobClient = containerClient.GetBlobClient(imageName);

                using (var imageStream = new MemoryStream(Convert.FromBase64String(cleanBase64)))
                {
                    using (var optimizedStream = await OptimizeImageAsync(imageStream, 800, 600))
                    {
                        await blobClient.UploadAsync(optimizedStream, true);
                    }
                }

                return ConvertAzuriteUrlToPublic(blobClient.Uri.ToString());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de l'upload de l'image de catégorie");
                return string.Empty;
            }
        }

        public async Task<string> GenerateThumbnailAsync(string imageUrl)
        {
            try
            {
                using (var imageStream = await DownloadImageAsync(imageUrl))
                {
                    using (var thumbnailStream = await GenerateThumbnailStreamAsync(imageStream))
                    {
                        var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);
                        await containerClient.CreateIfNotExistsAsync();
                        
                        var thumbnailBlobName = $"thumbnails/category_{Guid.NewGuid()}.jpg";
                        var thumbnailBlobClient = containerClient.GetBlobClient(thumbnailBlobName);

                        await thumbnailBlobClient.UploadAsync(thumbnailStream, true);
                        return ConvertAzuriteUrlToPublic(thumbnailBlobClient.Uri.ToString());
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la génération du thumbnail pour la catégorie");
                return imageUrl; // Retourner l'URL originale en cas d'erreur
            }
        }

        public Task<bool> ValidateImageAsync(string image)
        {
            try
            {
                if (string.IsNullOrEmpty(image))
                    return Task.FromResult(false);

                // Vérifier si c'est une URL ou du base64
                if (image.StartsWith("http"))
                {
                    return Task.FromResult(Uri.IsWellFormedUriString(image, UriKind.Absolute));
                }

                // Vérifier le format base64
                if (image.Contains(","))
                {
                    var base64Data = image.Split(',')[1];
                    try
                    {
                        Convert.FromBase64String(base64Data);
                        return Task.FromResult(true);
                    }
                    catch
                    {
                        return Task.FromResult(false);
                    }
                }

                return Task.FromResult(false);
            }
            catch
            {
                return Task.FromResult(false);
            }
        }

        private async Task<Stream> OptimizeImageAsync(Stream imageStream, int maxWidth, int maxHeight)
        {
            var outputStream = new MemoryStream();
            
            using (var image = await Image.LoadAsync(imageStream))
            {
                // Redimensionner l'image si nécessaire
                if (image.Width > maxWidth || image.Height > maxHeight)
                {
                    image.Mutate(x => x.Resize(new ResizeOptions
                    {
                        Size = new Size(maxWidth, maxHeight),
                        Mode = ResizeMode.Max
                    }));
                }

                // Sauvegarder avec une qualité optimisée
                await image.SaveAsJpegAsync(outputStream, new JpegEncoder
                {
                    Quality = 85
                });
            }

            outputStream.Position = 0;
            return outputStream;
        }

        private async Task<Stream> GenerateThumbnailStreamAsync(Stream imageStream)
        {
            var outputStream = new MemoryStream();
            
            using (var image = await Image.LoadAsync(imageStream))
            {
                // Créer un thumbnail de 200x200
                image.Mutate(x => x.Resize(new ResizeOptions
                {
                    Size = new Size(200, 200),
                    Mode = ResizeMode.Crop
                }));

                await image.SaveAsJpegAsync(outputStream, new JpegEncoder
                {
                    Quality = 80
                });
            }

            outputStream.Position = 0;
            return outputStream;
        }

        private async Task<Stream> DownloadImageAsync(string imageUrl)
        {
            var response = await _httpClient.GetAsync(imageUrl);
            response.EnsureSuccessStatusCode();
            return await response.Content.ReadAsStreamAsync();
        }

        private string ConvertAzuriteUrlToPublic(string azuriteUrl)
        {
            // Convertir les URLs Azurite en URLs d'API proxy pour les catégories
            if (string.IsNullOrEmpty(azuriteUrl))
                return azuriteUrl;

            // Extraire le chemin du blob depuis l'URL Azurite
            // Format: http://azurite:10000/devstoreaccount1/category-images/path/to/image.jpg
            // Résultat: /api/v1/images/categories/path/to/image.jpg

            try
            {
                var uri = new Uri(azuriteUrl);
                var pathSegments = uri.AbsolutePath.Split('/', StringSplitOptions.RemoveEmptyEntries);

                // Ignorer 'devstoreaccount1' et 'category-images', prendre le reste
                if (pathSegments.Length >= 3)
                {
                    var imagePath = string.Join("/", pathSegments.Skip(2));
                    return $"http://localhost:5243/api/v1/images/categories/{imagePath}";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la conversion de l'URL Azurite: {AzuriteUrl}", azuriteUrl);
            }

            // Fallback: retourner l'URL originale
            return azuriteUrl;
        }
    }
}
