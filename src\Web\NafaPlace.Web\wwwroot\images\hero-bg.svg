<svg xmlns="http://www.w3.org/2000/svg" width="1200" height="600" viewBox="0 0 1200 600">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1d3557;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#457b9d;stop-opacity:1" />
    </linearGradient>
    <pattern id="pattern1" width="60" height="60" patternUnits="userSpaceOnUse">
      <path d="M0 0 L20 0 L20 20 L0 20 Z" fill="#ffffff" fill-opacity="0.05" />
    </pattern>
  </defs>
  <rect width="100%" height="100%" fill="url(#grad1)" />
  <rect width="100%" height="100%" fill="url(#pattern1)" />
  <g opacity="0.2">
    <circle cx="200" cy="300" r="100" fill="#e63946" />
    <circle cx="1000" cy="200" r="150" fill="#fca311" />
    <circle cx="600" cy="500" r="120" fill="#a8dadc" />
  </g>
</svg>
