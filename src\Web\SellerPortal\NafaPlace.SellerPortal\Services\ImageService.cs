using System;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Components.Forms;
using System.IO;
using System.Text.Json;
using System.Text;
using System.Net.Http.Headers;

namespace NafaPlace.SellerPortal.Services
{
    public class ImageService
    {
        private readonly HttpClient _httpClient;
        private readonly IAuthService _authService;
        private readonly string _apiBaseUrl = "http://localhost:5243"; // URL de l'API Catalog

        public ImageService(HttpClient httpClient, IAuthService authService)
        {
            _httpClient = httpClient;
            _authService = authService;
            _httpClient.BaseAddress = new Uri(_apiBaseUrl);
        }

        public async Task<string> UploadProductImageAsync(IBrowserFile file, int productId, int sellerId, bool isMain = false)
        {
            try
            {
                // Vérification de la taille et du type de fichier
                if (file.Size > 5 * 1024 * 1024) // 5 MB max
                {
                    throw new Exception("La taille du fichier ne doit pas dépasser 5 MB.");
                }

                var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif" };
                var extension = Path.GetExtension(file.Name).ToLowerInvariant();
                if (!Array.Exists(allowedExtensions, e => e == extension))
                {
                    throw new Exception("Le format du fichier n'est pas pris en charge. Utilisez JPG, PNG ou GIF.");
                }

                // Lire le fichier en mémoire
                using var stream = file.OpenReadStream(5 * 1024 * 1024); // 5 MB max
                using var ms = new MemoryStream();
                await stream.CopyToAsync(ms);
                ms.Position = 0;

                // Convertir l'image en base64
                var imageBytes = ms.ToArray();
                var base64Image = Convert.ToBase64String(imageBytes);
                
                // Créer un objet JSON pour envoyer l'image à l'API
                var requestObject = new
                {
                    Image = base64Image,
                    IsMain = isMain
                };
                
                var jsonContent = new StringContent(
                    JsonSerializer.Serialize(requestObject),
                    Encoding.UTF8,
                    "application/json");
                
                // Envoyer la requête à l'API
                var response = await _httpClient.PostAsync($"/api/v1/products/{productId}/images", jsonContent);
                
                if (response.IsSuccessStatusCode)
                {
                    var jsonString = await response.Content.ReadAsStringAsync();
                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    };
                    
                    var result = JsonSerializer.Deserialize<ImageUploadResult>(jsonString, options);
                    Console.WriteLine($"Image téléchargée avec succès. URL: {result?.Url}, ImageData: {(result?.ImageData != null ? "présent" : "absent")}");
                    
                    // Si l'API ne renvoie pas ImageData, utilisons l'image base64 que nous avons envoyée
                    if (string.IsNullOrEmpty(result?.ImageData) && !string.IsNullOrEmpty(base64Image))
                    {
                        // Préfixer avec le type MIME pour en faire une URL data valide
                        string mimeType = GetMimeTypeFromExtension(extension);
                        if (result != null)
                        {
                            result.ImageData = $"data:{mimeType};base64,{base64Image}";
                        }
                        Console.WriteLine("ImageData ajouté localement car absent de la réponse API");
                    }
                    
                    return result?.Url ?? string.Empty;
                }
                
                var error = await response.Content.ReadAsStringAsync();
                throw new Exception($"Erreur lors de l'envoi de l'image à l'API: {error}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors du téléchargement de l'image : {ex.Message}");
                throw;
            }
        }

        public async Task<bool> DeleteProductImageAsync(string imageUrl, int productId, int imageId)
        {
            try
            {
                var response = await _httpClient.DeleteAsync($"/api/v1/products/{productId}/images/{imageId}");
                
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors de la suppression de l'image : {ex.Message}");
                return false;
            }
        }

        public async Task<bool> SetMainImageAsync(int productId, int imageId)
        {
            try
            {
                var response = await _httpClient.PutAsync($"/api/v1/products/{productId}/images/{imageId}/main", null);
                
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors de la définition de l'image principale : {ex.Message}");
                return false;
            }
        }
        
        public async Task<string> UploadProfileImageAsync(IBrowserFile file)
        {
            try
            {
                if (file.Size > 5 * 1024 * 1024) // 5 MB max
                {
                    throw new Exception("La taille du fichier ne doit pas dépasser 5 MB.");
                }

                using var stream = file.OpenReadStream(5 * 1024 * 1024);
                using var ms = new MemoryStream();
                await stream.CopyToAsync(ms);
                var imageBytes = ms.ToArray();
                var base64Image = Convert.ToBase64String(imageBytes);

                var currentUser = await _authService.GetCurrentUserAsync();
                if (currentUser == null)
                {
                    throw new Exception("Utilisateur non authentifié.");
                }

                var requestObject = new { Image = base64Image, SellerId = currentUser.Id };
                var jsonContent = new StringContent(JsonSerializer.Serialize(requestObject), Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync("/api/sellers/profile-picture", jsonContent);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ImageUploadResult>();
                    return result?.Url ?? string.Empty;
                }

                var error = await response.Content.ReadAsStringAsync();
                throw new Exception($"Erreur API: {error}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors du téléversement de l'image de profil : {ex.Message}");
                throw;
            }
        }
        
        // Classe pour désérialiser la réponse de l'API
        private class ImageUploadResult
        {
            public int Id { get; set; }
            public string Url { get; set; } = string.Empty;
            public bool IsMain { get; set; }
            public int ProductId { get; set; }
            public string? ImageData { get; set; }
            
            // Propriété pour compatibilité
            public string ImageUrl => Url;
        }

        private string GetMimeTypeFromExtension(string extension)
        {
            switch (extension)
            {
                case ".jpg":
                case ".jpeg":
                    return "image/jpeg";
                case ".png":
                    return "image/png";
                case ".gif":
                    return "image/gif";
                default:
                    throw new Exception($"Type MIME inconnu pour l'extension {extension}");
            }
        }
    }
}
