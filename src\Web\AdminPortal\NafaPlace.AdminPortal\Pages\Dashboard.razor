@page "/dashboard"
@using NafaPlace.AdminPortal.Models.Orders
@using NafaPlace.AdminPortal.Models.Common
@using NafaPlace.AdminPortal.Models.Products
@using NafaPlace.AdminPortal.Services
@inject IOrderService OrderService
@inject IUserService UserService
@inject ProductService ProductService
@inject IJSRuntime JSRuntime
@attribute [Authorize]

<PageTitle>Dashboard - NafaPlace Admin</PageTitle>

<!-- Styles modernes intégrés dans modern-dashboard.css -->

<div class="container-fluid px-4">
    <!-- Stats Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="stat-card">
                <div class="icon-wrapper icon-customers">
                    <i class="fas fa-users fs-4"></i>
                </div>
                <div class="stat-number">@(userCount?.ToString("N0") ?? "12,5K")</div>
                <div class="text-muted">Utilisateurs</div>
                <div class="d-flex align-items-center mt-2">
                    <i class="fas fa-arrow-up text-success me-1"></i>
                    <small class="text-success">+12.5%</small>
                    <small class="text-muted ms-2">vs mois dernier</small>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="stat-card">
                <div class="icon-wrapper icon-orders">
                    <i class="fas fa-shopping-cart fs-4"></i>
                </div>
                <div class="stat-number">@(orderStats?.TotalOrders?.ToString("N0") ?? "1,2K")</div>
                <div class="text-muted">Commandes</div>
                <div class="d-flex align-items-center mt-2">
                    <i class="fas fa-arrow-up text-success me-1"></i>
                    <small class="text-success">+8.3%</small>
                    <small class="text-muted ms-2">vs semaine dernière</small>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="stat-card">
                <div class="icon-wrapper icon-revenue">
                    <i class="fas fa-coins fs-4"></i>
                </div>
                <div class="stat-number">@(orderStats?.TotalRevenue?.ToString("N0") ?? "15,7M") GNF</div>
                <div class="text-muted">Chiffre d'affaires</div>
                <div class="d-flex align-items-center mt-2">
                    <i class="fas fa-arrow-up text-success me-1"></i>
                    <small class="text-success">+16.7%</small>
                    <small class="text-muted ms-2">vs mois dernier</small>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="stat-card">
                <div class="icon-wrapper icon-sales">
                    <i class="fas fa-percentage fs-4"></i>
                </div>
                <div class="stat-number">4,2%</div>
                <div class="text-muted">Taux de conversion</div>
                <div class="d-flex align-items-center mt-2">
                    <i class="fas fa-arrow-up text-success me-1"></i>
                    <small class="text-success">+2.1%</small>
                    <small class="text-muted ms-2">vs mois dernier</small>
                </div>
            </div>
        </div>
    </div>
    <!-- Charts Row -->
    <div class="row g-4 mb-4">
        <div class="col-xl-8">
            <div class="chart-container">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">Évolution des ventes</h6>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary active">7J</button>
                        <button class="btn btn-outline-primary">30J</button>
                        <button class="btn btn-outline-primary">3M</button>
                    </div>
                </div>
                <div class="d-flex align-items-end justify-content-between" style="height: 200px;">
                    <div class="d-flex flex-column align-items-center">
                        <div style="width: 30px; height: 120px; background: linear-gradient(to top, #667eea, #764ba2); border-radius: 4px; margin-bottom: 8px;"></div>
                        <small class="text-muted">Lun</small>
                    </div>
                    <div class="d-flex flex-column align-items-center">
                        <div style="width: 30px; height: 80px; background: linear-gradient(to top, #667eea, #764ba2); border-radius: 4px; margin-bottom: 8px;"></div>
                        <small class="text-muted">Mar</small>
                    </div>
                    <div class="d-flex flex-column align-items-center">
                        <div style="width: 30px; height: 160px; background: linear-gradient(to top, #667eea, #764ba2); border-radius: 4px; margin-bottom: 8px;"></div>
                        <small class="text-muted">Mer</small>
                    </div>
                    <div class="d-flex flex-column align-items-center">
                        <div style="width: 30px; height: 100px; background: linear-gradient(to top, #667eea, #764ba2); border-radius: 4px; margin-bottom: 8px;"></div>
                        <small class="text-muted">Jeu</small>
                    </div>
                    <div class="d-flex flex-column align-items-center">
                        <div style="width: 30px; height: 140px; background: linear-gradient(to top, #667eea, #764ba2); border-radius: 4px; margin-bottom: 8px;"></div>
                        <small class="text-muted">Ven</small>
                    </div>
                    <div class="d-flex flex-column align-items-center">
                        <div style="width: 30px; height: 180px; background: linear-gradient(to top, #667eea, #764ba2); border-radius: 4px; margin-bottom: 8px;"></div>
                        <small class="text-muted">Sam</small>
                    </div>
                    <div class="d-flex flex-column align-items-center">
                        <div style="width: 30px; height: 90px; background: linear-gradient(to top, #667eea, #764ba2); border-radius: 4px; margin-bottom: 8px;"></div>
                        <small class="text-muted">Dim</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4">
            <div class="stat-card h-100">
                <h6 class="mb-3">Top Catégories</h6>

                <div class="mb-3">
                    <div class="d-flex justify-content-between mb-1">
                        <span class="fw-medium">Électronique</span>
                        <span class="text-muted">45%</span>
                    </div>
                    <div class="progress progress-modern">
                        <div class="progress-bar" style="width: 45%"></div>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="d-flex justify-content-between mb-1">
                        <span class="fw-medium">Vêtements</span>
                        <span class="text-muted">32%</span>
                    </div>
                    <div class="progress progress-modern">
                        <div class="progress-bar" style="width: 32%"></div>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="d-flex justify-content-between mb-1">
                        <span class="fw-medium">Maison</span>
                        <span class="text-muted">18%</span>
                    </div>
                    <div class="progress progress-modern">
                        <div class="progress-bar" style="width: 18%"></div>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="d-flex justify-content-between mb-1">
                        <span class="fw-medium">Sport</span>
                        <span class="text-muted">5%</span>
                    </div>
                    <div class="progress progress-modern">
                        <div class="progress-bar" style="width: 5%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Mois</th>
                                    <th>Ventes</th>
                                    <th>Progression</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>Jan</strong></td>
                                    <td>12K GNF</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-danger" style="width: 40%"></div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Fév</strong></td>
                                    <td>19K GNF</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-warning" style="width: 63%"></div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Mar</strong></td>
                                    <td>15K GNF</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-info" style="width: 50%"></div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Avr</strong></td>
                                    <td>25K GNF</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-success" style="width: 83%"></div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Mai</strong></td>
                                    <td>22K GNF</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-primary" style="width: 73%"></div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Jun</strong></td>
                                    <td>30K GNF</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-dark" style="width: 100%"></div>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-4">
            <div class="card">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie text-danger"></i>
                        Top Catégories
                    </h5>
                </div>
                <div class="card-body">
                    <div class="category-list">
                        <div class="category-item mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <span><i class="fas fa-tshirt text-danger"></i> Mode</span>
                                <strong>35%</strong>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-danger" style="width: 35%"></div>
                            </div>
                        </div>
                        <div class="category-item mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <span><i class="fas fa-laptop text-warning"></i> Électronique</span>
                                <strong>25%</strong>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-warning" style="width: 25%"></div>
                            </div>
                        </div>
                        <div class="category-item mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <span><i class="fas fa-home text-success"></i> Maison</span>
                                <strong>20%</strong>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-success" style="width: 20%"></div>
                            </div>
                        </div>
                        <div class="category-item mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <span><i class="fas fa-football-ball text-info"></i> Sport</span>
                                <strong>15%</strong>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-info" style="width: 15%"></div>
                            </div>
                        </div>
                        <div class="category-item mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <span><i class="fas fa-book text-secondary"></i> Livres</span>
                                <strong>5%</strong>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-secondary" style="width: 5%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Orders Table -->
    <div class="table-modern">
        <table class="table mb-0">
            <thead>
                <tr>
                    <th>Commande</th>
                    <th>Client</th>
                    <th>Produit</th>
                    <th>Montant</th>
                    <th>Statut</th>
                    <th>Date</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="fw-bold">#ORD-2024-001</td>
                    <td>
                        <div class="d-flex align-items-center">
                            <img src="https://via.placeholder.com/32x32/667eea/ffffff?text=JD"
                                 class="rounded-circle me-2" alt="Client">
                            <div>
                                <div class="fw-medium">Jean Dupont</div>
                                <small class="text-muted"><EMAIL></small>
                            </div>
                        </div>
                    </td>
                    <td>iPhone 15 Pro</td>
                    <td class="fw-bold">1,299,000 GNF</td>
                    <td>
                        <span class="badge badge-modern bg-success">Livré</span>
                    </td>
                    <td class="text-muted">29 Jul 2025</td>
                </tr>
                <tr>
                    <td class="fw-bold">#ORD-2024-002</td>
                    <td>
                        <div class="d-flex align-items-center">
                            <img src="https://via.placeholder.com/32x32/764ba2/ffffff?text=ML"
                                 class="rounded-circle me-2" alt="Client">
                            <div>
                                <div class="fw-medium">Marie Leroy</div>
                                <small class="text-muted"><EMAIL></small>
                            </div>
                        </div>
                    </td>
                    <td>MacBook Air M3</td>
                    <td class="fw-bold">1,599,000 GNF</td>
                    <td>
                        <span class="badge badge-modern bg-warning">En transit</span>
                    </td>
                    <td class="text-muted">28 Jul 2025</td>
                </tr>
                <tr>
                    <td class="fw-bold">#ORD-2024-003</td>
                    <td>
                        <div class="d-flex align-items-center">
                            <img src="https://via.placeholder.com/32x32/43e97b/ffffff?text=PM"
                                 class="rounded-circle me-2" alt="Client">
                            <div>
                                <div class="fw-medium">Pierre Martin</div>
                                <small class="text-muted"><EMAIL></small>
                            </div>
                        </div>
                    </td>
                    <td>AirPods Pro</td>
                    <td class="fw-bold">279,000 GNF</td>
                    <td>
                        <span class="badge badge-modern bg-primary">Traitement</span>
                    </td>
                    <td class="text-muted">27 Jul 2025</td>
                </tr>
            </tbody>
        </table>
    </div>

@code {
    private OrderStatistics? orderStats;
    private int? userCount;
    private bool isLoading = true;
    private List<ProductDto> _topProducts = new List<ProductDto>();

    protected override async Task OnInitializedAsync()
    {
        await LoadDashboardData();
    }

    // Plus besoin d'initialiser les graphiques Chart.js

    private async Task LoadDashboardData()
    {
        isLoading = true;

        try
        {
            // Charger les statistiques des commandes
            var orderStatsTask = OrderService.GetOrderStatisticsAsync();

            // Charger le nombre d'utilisateurs
            var usersTask = UserService.GetUsersAsync(1, 1);

            await Task.WhenAll(orderStatsTask, usersTask);

            orderStats = await orderStatsTask;
            var usersResult = await usersTask;
            userCount = usersResult.TotalCount;

            // Charger les top produits pour l'affichage
            var products = await ProductService.GetProductsAsync();
            _topProducts = products?.Take(5).ToList() ?? new List<ProductDto>();
        }
        catch (Exception ex)
        {
            // Log error silently - could be enhanced with proper logging service
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    // Plus besoin de flag pour les graphiques

    // Plus besoin d'initialiser Chart.js

    private string GetProductImageSrc(ProductDto? product)
    {
        try
        {
            if (product?.Images != null && product.Images.Any())
            {
                var mainImage = product.Images.FirstOrDefault(i => i.IsMain);
                if (mainImage != null)
                {
                    return ProductService.GetImageUrl(mainImage, true);
                }

                var firstImage = product.Images.FirstOrDefault();
                if (firstImage != null)
                {
                    return ProductService.GetImageUrl(firstImage, true);
                }
            }

            return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNk+A8AAQUBAScY42YAAAAASUVORK5CYII=";
        }
        catch (Exception)
        {
            return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNk+A8AAQUBAScY42YAAAAASUVORK5CYII=";
        }
    }
}