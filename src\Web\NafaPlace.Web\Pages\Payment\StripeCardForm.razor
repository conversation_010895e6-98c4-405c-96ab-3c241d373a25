@page "/payment/stripe-form/{OrderId:int}"
@using NafaPlace.Web.Models.Order
@using NafaPlace.Web.Services
@inject NavigationManager NavigationManager
@inject IOrderService OrderService
@inject IJSRuntime JSRuntime

<PageTitle>Paiement par Carte - NafaPlace</PageTitle>

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-6">
            @if (_loading)
            {
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                    <p class="mt-2">Chargement des détails de la commande...</p>
                </div>
            }
            else if (_order == null)
            {
                <div class="alert alert-danger">
                    <h4>Commande introuvable</h4>
                    <p>La commande demandée n'existe pas ou n'est plus disponible.</p>
                    <a href="/" class="btn btn-primary">Retour à l'accueil</a>
                </div>
            }
            else
            {
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fab fa-stripe me-2"></i>
                            Paiement par Carte Bancaire
                        </h4>
                    </div>
                    <div class="card-body">
                        <!-- Résumé de la commande -->
                        <div class="mb-4">
                            <h5>Résumé de la commande</h5>
                            <div class="bg-light p-3 rounded">
                                <div class="d-flex justify-content-between">
                                    <span>Commande #@_order.Id.ToString().PadLeft(8, '0')</span>
                                    <span class="fw-bold">@_order.TotalAmount.ToString("N0") GNF</span>
                                </div>
                            </div>
                        </div>

                        <!-- Formulaire de carte -->
                        <div class="mb-4">
                            <h5>Informations de paiement</h5>
                            
                            <div class="mb-3">
                                <label class="form-label">Numéro de carte</label>
                                <input type="text" class="form-control" @bind="_cardNumber" 
                                       placeholder="1234 5678 9012 3456" maxlength="19"
                                       @oninput="FormatCardNumber">
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <label class="form-label">Date d'expiration</label>
                                    <input type="text" class="form-control" @bind="_expiryDate" 
                                           placeholder="MM/YY" maxlength="5"
                                           @oninput="FormatExpiryDate">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">CVV</label>
                                    <input type="text" class="form-control" @bind="_cvv" 
                                           placeholder="123" maxlength="4">
                                </div>
                            </div>
                            
                            <div class="mt-3">
                                <label class="form-label">Nom sur la carte</label>
                                <input type="text" class="form-control" @bind="_cardholderName" 
                                       placeholder="Nom complet">
                            </div>
                        </div>

                        <!-- Sécurité -->
                        <div class="alert alert-info">
                            <i class="fas fa-shield-alt me-2"></i>
                            <small>Vos informations de paiement sont sécurisées et cryptées par Stripe.</small>
                        </div>

                        <!-- Boutons d'action -->
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary btn-lg" 
                                    @onclick="ProcessStripePayment"
                                    disabled="@(_processing || !IsFormValid())">
                                @if (_processing)
                                {
                                    <span class="spinner-border spinner-border-sm me-2"></span>
                                    @("Traitement en cours")
                                }
                                else
                                {
                                    <i class="fas fa-credit-card me-2"></i>
                                    @("Payer maintenant")
                                }
                            </button>
                            
                            <button class="btn btn-outline-secondary" 
                                    @onclick="GoBack"
                                    disabled="@_processing">
                                <i class="fas fa-arrow-left me-2"></i>
                                Retour
                            </button>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

@code {
    [Parameter] public int OrderId { get; set; }
    
    private OrderDto? _order;
    private bool _loading = true;
    private bool _processing = false;
    
    private string _cardNumber = "";
    private string _expiryDate = "";
    private string _cvv = "";
    private string _cardholderName = "";

    protected override async Task OnInitializedAsync()
    {
        try
        {
            _order = await OrderService.GetOrderByIdAsync(OrderId);
            Console.WriteLine($"Commande chargée pour paiement Stripe: {_order?.Id}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement de la commande: {ex.Message}");
        }
        finally
        {
            _loading = false;
        }
    }

    private async Task ProcessStripePayment()
    {
        if (_processing || !IsFormValid()) return;

        try
        {
            _processing = true;

            Console.WriteLine($"Traitement du paiement Stripe pour la commande {OrderId}");

            // Simulation du traitement Stripe
            await Task.Delay(3000);

            // Générer un ID de transaction simulé
            string transactionId = $"stripe_sim_{DateTime.Now.Ticks}";

            // Mettre à jour le statut de paiement
            var success = await OrderService.UpdatePaymentStatusAsync(OrderId, "Completed", transactionId);

            if (success)
            {
                Console.WriteLine($"Paiement Stripe traité avec succès pour la commande {OrderId}");
                NavigationManager.NavigateTo($"/payment/success?orderId={OrderId}");
            }
            else
            {
                Console.WriteLine("Erreur lors du traitement du paiement Stripe");
                // Rediriger vers une page d'erreur ou afficher un message
                NavigationManager.NavigateTo($"/payment/cancel?orderId={OrderId}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du traitement du paiement Stripe: {ex.Message}");
            NavigationManager.NavigateTo($"/payment/cancel?orderId={OrderId}");
        }
        finally
        {
            _processing = false;
        }
    }

    private bool IsFormValid()
    {
        return !string.IsNullOrWhiteSpace(_cardNumber) &&
               !string.IsNullOrWhiteSpace(_expiryDate) &&
               !string.IsNullOrWhiteSpace(_cvv) &&
               !string.IsNullOrWhiteSpace(_cardholderName) &&
               _cardNumber.Replace(" ", "").Length >= 13 &&
               _expiryDate.Length == 5 &&
               _cvv.Length >= 3;
    }

    private void FormatCardNumber(ChangeEventArgs e)
    {
        var value = e.Value?.ToString()?.Replace(" ", "") ?? "";
        if (value.Length <= 16)
        {
            // Ajouter des espaces tous les 4 chiffres
            var formatted = "";
            for (int i = 0; i < value.Length; i++)
            {
                if (i > 0 && i % 4 == 0)
                    formatted += " ";
                formatted += value[i];
            }
            _cardNumber = formatted;
        }
    }

    private void FormatExpiryDate(ChangeEventArgs e)
    {
        var value = e.Value?.ToString()?.Replace("/", "") ?? "";
        if (value.Length <= 4)
        {
            if (value.Length > 2)
            {
                _expiryDate = value.Substring(0, 2) + "/" + value.Substring(2);
            }
            else if (value.Length == 2)
            {
                _expiryDate = value + "/";
            }
            else
            {
                _expiryDate = value;
            }
        }
    }

    private void GoBack()
    {
        NavigationManager.NavigateTo($"/payment/simple/{OrderId}");
    }
}
