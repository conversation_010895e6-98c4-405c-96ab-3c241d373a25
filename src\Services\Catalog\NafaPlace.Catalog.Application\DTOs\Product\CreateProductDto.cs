using System;
using System.Collections.Generic;

namespace NafaPlace.Catalog.Application.DTOs.Product
{
    public class CreateProductDto
    {
        public required string Name { get; set; }
        public required string Description { get; set; }
        public decimal Price { get; set; }
        public int CategoryId { get; set; }
        public int StockQuantity { get; set; }
        public required string Currency { get; set; }
        public string? Brand { get; set; }
        public string? Model { get; set; }
        public decimal Weight { get; set; }
        public string? Dimensions { get; set; }
        public int SellerId { get; set; }
        public List<CreateProductImageRequest>? Images { get; set; }
        public List<CreateProductVariantRequest>? Variants { get; set; }
        public List<CreateProductAttributeRequest>? Attributes { get; set; }
    }
}
