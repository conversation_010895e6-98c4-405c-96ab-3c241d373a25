namespace NafaPlace.SellerPortal.Models.Statistics;

public class SalesStatistics
{
    public decimal TotalSales { get; set; }
    public double SalesGrowth { get; set; }
    public int TotalOrders { get; set; }
    public double OrdersGrowth { get; set; }
    public decimal AverageOrderValue { get; set; }
    public double AovGrowth { get; set; }
    public double ConversionRate { get; set; }
    public double ConversionGrowth { get; set; }
}

public class TopProduct
{
    public int ProductId { get; set; }
    public string Name { get; set; } = "";
    public string ImageUrl { get; set; } = "";
    public int UnitsSold { get; set; }
    public decimal Revenue { get; set; }
    public string CategoryName { get; set; } = "";
}

public class RegionSales
{
    public string Name { get; set; } = "";
    public int OrderCount { get; set; }
    public decimal Revenue { get; set; }
    public double Percentage { get; set; }
}

public class SalesReport
{
    public DateTime Date { get; set; }
    public int OrderCount { get; set; }
    public int ProductsSold { get; set; }
    public decimal Revenue { get; set; }
    public decimal AverageOrderValue { get; set; }
}

public class SalesChartData
{
    public DateTime Date { get; set; }
    public decimal Sales { get; set; }
    public int Orders { get; set; }
}

public class CategorySales
{
    public int CategoryId { get; set; }
    public string CategoryName { get; set; } = "";
    public decimal Revenue { get; set; }
    public int OrderCount { get; set; }
    public double Percentage { get; set; }
    public string Color { get; set; } = "";
}

public class StatisticsRequest
{
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public int? SellerId { get; set; }
    public string Period { get; set; } = "30"; // 7, 30, 90, 365 days
}

public class DashboardStatistics
{
    public SalesStatistics SalesStats { get; set; } = new();
    public List<TopProduct> TopProducts { get; set; } = new();
    public List<RegionSales> SalesByRegion { get; set; } = new();
    public List<CategorySales> SalesByCategory { get; set; } = new();
    public List<SalesChartData> SalesChartData { get; set; } = new();
    public List<SalesReport> RecentSales { get; set; } = new();
}

public class ExportRequest
{
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public string Format { get; set; } = "excel"; // excel, csv, pdf
    public int? SellerId { get; set; }
    public string ReportType { get; set; } = "sales"; // sales, orders, products
}
