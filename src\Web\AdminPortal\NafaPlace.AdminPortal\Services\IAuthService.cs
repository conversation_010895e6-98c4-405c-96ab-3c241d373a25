using NafaPlace.AdminPortal.Models.Auth;
using NafaPlace.AdminPortal.Pages.Users;

namespace NafaPlace.AdminPortal.Services;

public interface IAuthService
{
    event Action? AuthenticationStateChanged;
    Task<AuthResponse> LoginAsync(string email, string password);
    Task LogoutAsync();
    Task<UserDto> GetCurrentUserAsync();
    
    // Méthodes pour la gestion des rôles
    Task<List<RoleDto>> GetRolesAsync();
    Task<RoleDto> CreateRoleAsync(RoleDto role);
    Task<RoleDto> UpdateRoleAsync(RoleDto role);
    Task<bool> DeleteRoleAsync(int roleId);
    Task<bool> AssignRoleToUserAsync(int userId, string roleName);
    
    // Méthodes pour la gestion des utilisateurs
    Task<UserListResponse> GetUsersAsync(string searchTerm = "", int page = 1, int pageSize = 10);
    Task<UserDto> GetUserByIdAsync(int userId);
    Task<bool> RegisterUserAsync(object userModel);
    Task<bool> UpdateUserAsync(object userModel);
    Task<bool> DeleteUserAsync(int userId);
    Task<bool> UpdateUserRolesAsync(int userId, List<string> roles);
}

public class UserListResponse
{
    public List<UserDto> Users { get; set; } = new List<UserDto>();
    public int TotalCount { get; set; }
}
