@page "/categories"
@using NafaPlace.AdminPortal.Models
@using NafaPlace.AdminPortal.Services
@using Microsoft.AspNetCore.Components.Forms
@inject CategoryService CategoryService
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime

<PageTitle>Gestion des catégories - NafaPlace Admin</PageTitle>

<h1>Gestion des catégories</h1>

<div class="mb-3">
    <a href="/categories/create" class="btn btn-primary">
        <span class="oi oi-plus" aria-hidden="true"></span> Nouvelle catégorie
    </a>
</div>

@if (categories == null)
{
    <p><em>Chargement...</em></p>
}
else if (!categories.Any())
{
    <div class="alert alert-info">
        Aucune catégorie n'a été trouvée. Créez votre première catégorie en cliquant sur le bouton "Nouvelle catégorie".
    </div>
}
else
{
    <div class="table-responsive">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Image</th>
                    <th>Nom</th>
                    <th>Description</th>
                    <th>Catégorie parente</th>
                    <th>Statut</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var category in categories)
                {
                    <tr>
                        <td>@category.Id</td>
                        <td>
                            <img src="@CategoryService.GetImageUrl(category, true)" alt="@category.Name" class="img-thumbnail" style="width: 50px; height: 50px; object-fit: cover;" />
                        </td>
                        <td>@category.Name</td>
                        <td>@(category.Description?.Length > 50 ? category.Description.Substring(0, 47) + "..." : category.Description)</td>
                        <td>@(category.ParentCategoryName ?? "-")</td>
                        <td>
                            @if (category.IsActive)
                            {
                                <span class="badge bg-success">Actif</span>
                            }
                            else
                            {
                                <span class="badge bg-danger">Inactif</span>
                            }
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="/categories/edit/@category.Id" class="btn btn-sm btn-primary">
                                    <span class="oi oi-pencil" aria-hidden="true"></span> Modifier
                                </a>
                                <button class="btn btn-sm btn-danger" @onclick="() => DeleteCategory(category.Id)">
                                    <span class="oi oi-trash" aria-hidden="true"></span> Supprimer
                                </button>
                            </div>
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    </div>
}

@code {
    private IEnumerable<CategoryDto>? categories;

    protected override async Task OnInitializedAsync()
    {
        try
        {
            categories = await CategoryService.GetAllCategoriesAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des catégories: {ex.Message}");
            // Gérer l'erreur ici (par exemple, afficher un message à l'utilisateur)
        }
    }

    private async Task DeleteCategory(int id)
    {
        bool confirmed = await JSRuntime.InvokeAsync<bool>("confirm", "Êtes-vous sûr de vouloir supprimer cette catégorie ?");
        if (confirmed)
        {
            try
            {
                await CategoryService.DeleteCategoryAsync(id);
                // Recharger les catégories après la suppression
                categories = await CategoryService.GetAllCategoriesAsync();
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("alert", $"Erreur lors de la suppression: {ex.Message}");
            }
        }
    }
}
