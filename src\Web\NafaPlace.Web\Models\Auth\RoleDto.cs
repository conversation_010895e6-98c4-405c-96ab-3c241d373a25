using System.ComponentModel.DataAnnotations;

namespace NafaPlace.Web.Models.Auth;

public class RoleDto
{
    public int Id { get; set; }
    
    [Required(ErrorMessage = "Le nom du rôle est obligatoire")]
    public string Name { get; set; } = string.Empty;
    
    public string Description { get; set; } = string.Empty;
    
    public DateTime CreatedAt { get; set; }
    
    public DateTime? UpdatedAt { get; set; }
}
