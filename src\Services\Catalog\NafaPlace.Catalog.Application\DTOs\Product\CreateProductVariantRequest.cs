using System;
using System.Collections.Generic;

namespace NafaPlace.Catalog.Application.DTOs.Product
{
    public class CreateProductVariantRequest
    {
        public required string Name { get; set; }
        public required string Sku { get; set; }
        public decimal Price { get; set; }
        public int StockQuantity { get; set; }
        public required string Color { get; set; }
        public required string Size { get; set; }
        public List<CreateProductVariantAttributeRequest> Attributes { get; set; } = new();
    }

    public class CreateProductVariantAttributeRequest
    {
        public string? Name { get; set; }
        public string? Value { get; set; }
    }
}
