# Build stage
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copier les fichiers csproj et restaurer les dépendances
COPY ["src/Services/Catalog/NafaPlace.Catalog.API/NafaPlace.Catalog.API/NafaPlace.Catalog.API.csproj", "Services/Catalog/NafaPlace.Catalog.API/NafaPlace.Catalog.API/"]
COPY ["src/Services/Catalog/NafaPlace.Catalog.Application/NafaPlace.Catalog.Application.csproj", "Services/Catalog/NafaPlace.Catalog.Application/"]
COPY ["src/Services/Catalog/NafaPlace.Catalog.Domain/NafaPlace.Catalog.Domain.csproj", "Services/Catalog/NafaPlace.Catalog.Domain/"]
COPY ["src/Services/Catalog/NafaPlace.Catalog.Infrastructure/NafaPlace.Catalog.Infrastructure.csproj", "Services/Catalog/NafaPlace.Catalog.Infrastructure/"]
RUN dotnet restore "Services/Catalog/NafaPlace.Catalog.API/NafaPlace.Catalog.API/NafaPlace.Catalog.API.csproj"

# Copier le reste des fichiers et construire
COPY ["src/Services/Catalog/", "Services/Catalog/"]
WORKDIR "/src/Services/Catalog/NafaPlace.Catalog.API/NafaPlace.Catalog.API"
RUN dotnet build "NafaPlace.Catalog.API.csproj" -c Release -o /app/build

# Publish stage
FROM build AS publish
RUN dotnet publish "NafaPlace.Catalog.API.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Final stage
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS final
WORKDIR /app
COPY --from=publish /app/publish .

# Installer les dépendances pour le traitement d'images
RUN apt-get update && apt-get install -y \
    libc6-dev \
    libgdiplus \
    libx11-dev \
    && rm -rf /var/lib/apt/lists/*

# Configuration pour le marché africain
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false
ENV TZ=Africa/Dakar
ENV LANG=fr_FR.UTF-8
ENV LANGUAGE=fr_FR.UTF-8
ENV LC_ALL=fr_FR.UTF-8

# Créer le dossier pour les uploads
RUN mkdir -p /app/wwwroot/uploads && chmod 755 /app/wwwroot/uploads

EXPOSE 80
ENTRYPOINT ["dotnet", "NafaPlace.Catalog.API.dll"]
