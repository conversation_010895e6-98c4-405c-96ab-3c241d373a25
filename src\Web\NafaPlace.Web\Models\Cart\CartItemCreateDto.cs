namespace NafaPlace.Web.Models.Cart
{
    public class CartItemCreateDto
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public decimal Price { get; set; }
        public int Quantity { get; set; }
        public string ImageUrl { get; set; } = string.Empty;
        public int? VariantId { get; set; }
        public string? VariantName { get; set; }
    }
}
