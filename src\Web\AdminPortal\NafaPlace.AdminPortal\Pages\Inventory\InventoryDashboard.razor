@page "/inventory"
@page "/inventory/dashboard"
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.JSInterop
@using NafaPlace.AdminPortal.Services
@using NafaPlace.AdminPortal.Models.Inventory
@attribute [Authorize]
@inject InventoryService InventoryService
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime
@inject NotificationService NotificationService

<h1 class="visually-hidden">Tableau de Bord Inventaire - NafaPlace Admin</h1>

<div class="container-fluid px-4">
    <h1 class="mt-4">Gestion des Stocks</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="/dashboard">Dashboard</a></li>
        <li class="breadcrumb-item active">Inventaire</li>
    </ol>

    @if (_isLoading)
    {
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Chargement...</span>
            </div>
        </div>
    }
    else
    {
        <!-- Statistiques générales -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6">
                <div class="card bg-primary text-white mb-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Total Produits</h6>
                                <h3 class="mb-0">@_dashboard?.TotalProducts</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-box-seam fs-2"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between">
                        <a class="small text-white stretched-link" href="/products">Voir les détails</a>
                        <div class="small text-white"><i class="bi bi-arrow-right"></i></div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6">
                <div class="card bg-warning text-white mb-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Stock Faible</h6>
                                <h3 class="mb-0">@_dashboard?.LowStockCount</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-exclamation-triangle fs-2"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between">
                        <button class="btn btn-link text-white p-0 small" @onclick="() => ShowLowStockProducts()">
                            Voir les détails
                        </button>
                        <div class="small text-white"><i class="bi bi-exclamation-triangle"></i></div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6">
                <div class="card bg-danger text-white mb-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Rupture de Stock</h6>
                                <h3 class="mb-0">@_dashboard?.OutOfStockProducts</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-x-circle fs-2"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between">
                        <button class="btn btn-link text-white p-0 small" @onclick="() => ShowOutOfStockProducts()">
                            Voir les détails
                        </button>
                        <div class="small text-white"><i class="bi bi-x-circle"></i></div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6">
                <div class="card bg-success text-white mb-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Valeur Totale</h6>
                                <h3 class="mb-0">@(_dashboard?.TotalInventoryValue.ToString("N0")) GNF</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-currency-exchange fs-2"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Alertes récentes -->
        @if (_dashboard?.RecentAlerts?.Any() == true)
        {
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <i class="bi bi-bell me-1"></i>
                        Alertes Récentes
                    </div>
                    <a href="/inventory/alerts" class="btn btn-sm btn-outline-primary">Voir toutes</a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Produit</th>
                                    <th>Type</th>
                                    <th>Stock Actuel</th>
                                    <th>Seuil</th>
                                    <th>Vendeur</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var alert in _dashboard.RecentAlerts.Take(5))
                                {
                                    <tr class="@GetAlertRowClass(alert.Severity)">
                                        <td>@alert.ProductName</td>
                                        <td>
                                            <span class="badge @GetAlertBadgeClass(alert.Type)">
                                                @GetAlertTypeText(alert.Type)
                                            </span>
                                        </td>
                                        <td>@alert.CurrentStock</td>
                                        <td>@alert.ThresholdValue</td>
                                        <td>@alert.SellerName</td>
                                        <td>@alert.CreatedAt.ToString("dd/MM/yyyy HH:mm")</td>
                                        <td>
                                            @if (!alert.IsAcknowledged)
                                            {
                                                <button class="btn btn-sm btn-outline-success" @onclick="() => AcknowledgeAlert(alert.Id)">
                                                    <i class="bi bi-check"></i>
                                                </button>
                                            }
                                            else
                                            {
                                                <span class="text-success"><i class="bi bi-check-circle"></i></span>
                                            }
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        }

        <!-- Produits les plus vendus et stock faible -->
        <div class="row">
            <div class="col-xl-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="bi bi-graph-up me-1"></i>
                        Produits les Plus Vendus
                    </div>
                    <div class="card-body">
                        @if (_dashboard?.TopSellingProducts?.Any() == true)
                        {
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Produit</th>
                                            <th>Quantité Vendue</th>
                                            <th>Stock Actuel</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var product in _dashboard.TopSellingProducts.Take(5))
                                        {
                                            <tr>
                                                <td>@product.ProductName</td>
                                                <td>@product.SoldQuantity</td>
                                                <td>@product.CurrentStock</td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        }
                        else
                        {
                            <p class="text-muted">Aucune donnée de vente disponible</p>
                        }
                    </div>
                </div>
            </div>
            <div class="col-xl-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="bi bi-exclamation-triangle me-1"></i>
                        Produits en Stock Faible
                    </div>
                    <div class="card-body">
                        @if (_dashboard?.LowStockProducts?.Any() == true)
                        {
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Produit</th>
                                            <th>Stock Actuel</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var product in _dashboard.LowStockProducts.Take(5))
                                        {
                                            <tr class="table-warning">
                                                <td>@product.ProductName</td>
                                                <td>@product.CurrentStock</td>
                                                <td>
                                                    <button class="btn btn-sm btn-primary" @onclick="() => NavigateToProduct(product.ProductId)">
                                                        <i class="bi bi-pencil"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        }
                        else
                        {
                            <p class="text-success">Tous les produits ont un stock suffisant</p>
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions rapides -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <i class="bi bi-lightning me-1"></i>
                        Actions Rapides
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <button class="btn btn-outline-primary w-100 mb-2" @onclick="NavigateToAlerts">
                                    <i class="bi bi-bell me-1"></i>
                                    Gérer les Alertes
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-success w-100 mb-2" @onclick="NavigateToMovements">
                                    <i class="bi bi-arrow-left-right me-1"></i>
                                    Mouvements de Stock
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-info w-100 mb-2" @onclick="NavigateToReservations">
                                    <i class="bi bi-bookmark me-1"></i>
                                    Réservations
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-warning w-100 mb-2" @onclick="RecalculateStock">
                                    <i class="bi bi-arrow-clockwise me-1"></i>
                                    Recalculer les Stocks
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@code {
    private InventoryDashboardDto? _dashboard;
    private bool _isLoading = true;

    protected override async Task OnInitializedAsync()
    {
        await LoadDashboard();
    }

    private async Task LoadDashboard()
    {
        _isLoading = true;
        try
        {
            _dashboard = await InventoryService.GetInventoryDashboardAsync();
        }
        catch (Exception ex)
        {
            NotificationService.Error($"Erreur lors du chargement du tableau de bord: {ex.Message}");
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    private async Task AcknowledgeAlert(int alertId)
    {
        try
        {
            await InventoryService.AcknowledgeAlertAsync(alertId);
            await LoadDashboard();
            NotificationService.Success("Alerte acquittée avec succès");
        }
        catch (Exception ex)
        {
            NotificationService.Error($"Erreur lors de l'acquittement: {ex.Message}");
        }
    }

    private async Task RecalculateStock()
    {
        try
        {
            await InventoryService.RecalculateStockLevelsAsync();
            await LoadDashboard();
            NotificationService.Success("Recalcul des stocks effectué avec succès");
        }
        catch (Exception ex)
        {
            NotificationService.Error($"Erreur lors du recalcul: {ex.Message}");
        }
    }

    private void NavigateToProduct(int productId)
    {
        NavigationManager.NavigateTo($"/products/edit/{productId}");
    }

    private void NavigateToAlerts()
    {
        NavigationManager.NavigateTo("/inventory/alerts");
    }

    private void NavigateToMovements()
    {
        NavigationManager.NavigateTo("/inventory/movements");
    }

    private void NavigateToReservations()
    {
        NavigationManager.NavigateTo("/inventory/reservations");
    }

    private string GetAlertRowClass(AlertSeverity severity)
    {
        return severity switch
        {
            AlertSeverity.Critical => "table-danger",
            AlertSeverity.Warning => "table-warning",
            AlertSeverity.Emergency => "table-danger",
            _ => ""
        };
    }

    private string GetAlertBadgeClass(AlertType type)
    {
        return type switch
        {
            AlertType.OutOfStock => "bg-danger",
            AlertType.LowStock => "bg-warning",
            AlertType.OverStock => "bg-info",
            _ => "bg-secondary"
        };
    }

    private string GetAlertTypeText(AlertType type)
    {
        return type switch
        {
            AlertType.OutOfStock => "Rupture",
            AlertType.LowStock => "Stock Faible",
            AlertType.OverStock => "Surstock",
            AlertType.StockMovement => "Mouvement",
            AlertType.ReservationExpiry => "Réservation",
            _ => type.ToString()
        };
    }

    private void ShowLowStockProducts()
    {
        // Afficher une notification ou filtrer les produits en stock faible
        if (_dashboard?.LowStockCount > 0)
        {
            NotificationService.Info($"Il y a {_dashboard.LowStockCount} produit(s) en stock faible. Consultez la section 'Produits en Stock Faible' ci-dessous.");
        }
        else
        {
            NotificationService.Success("Aucun produit en stock faible actuellement.");
        }
    }

    private void ShowOutOfStockProducts()
    {
        // Afficher une notification ou filtrer les produits en rupture de stock
        if (_dashboard?.OutOfStockProducts > 0)
        {
            NotificationService.Warning($"Il y a {_dashboard.OutOfStockProducts} produit(s) en rupture de stock. Action requise!");
        }
        else
        {
            NotificationService.Success("Aucun produit en rupture de stock actuellement.");
        }
    }
}
