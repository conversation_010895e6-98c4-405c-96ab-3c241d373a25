using System.ComponentModel.DataAnnotations;

namespace NafaPlace.AdminPortal.Models.Products
{
    public class CategoryDto
    {
        public int Id { get; set; }
        
        [Required(ErrorMessage = "Le nom de la catégorie est obligatoire")]
        [StringLength(50, ErrorMessage = "Le nom de la catégorie ne doit pas dépasser 50 caractères")]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(200, ErrorMessage = "La description de la catégorie ne doit pas dépasser 200 caractères")]
        public string Description { get; set; } = string.Empty;
        
        public string? ImageUrl { get; set; }
        
        public bool IsActive { get; set; } = true;
    }
}
