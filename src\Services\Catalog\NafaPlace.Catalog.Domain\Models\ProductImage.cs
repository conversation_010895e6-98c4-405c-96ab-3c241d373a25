using System;
using System.ComponentModel.DataAnnotations;
using NafaPlace.Catalog.Domain.Common;

namespace NafaPlace.Catalog.Domain.Models;

public class ProductImage : BaseEntity
{
    [Required]
    public string? ImageUrl { get; set; }

    public string? ThumbnailUrl { get; set; }

    public bool IsMain { get; set; }

    [Required]
    public int ProductId { get; set; }
    public Product? Product { get; set; }
}
