namespace NafaPlace.Common.Models;

/// <summary>
/// Résultat paginé générique
/// </summary>
/// <typeparam name="T">Type des éléments</typeparam>
public class PagedResultDto<T>
{
    /// <summary>
    /// Liste des éléments de la page courante
    /// </summary>
    public List<T> Items { get; set; } = new();
    
    /// <summary>
    /// Nombre total d'éléments
    /// </summary>
    public int TotalCount { get; set; }
    
    /// <summary>
    /// Numéro de la page courante (commence à 1)
    /// </summary>
    public int PageNumber { get; set; }
    
    /// <summary>
    /// Taille de la page
    /// </summary>
    public int PageSize { get; set; }
    
    /// <summary>
    /// Nombre total de pages
    /// </summary>
    public int TotalPages { get; set; }
    
    /// <summary>
    /// Indique s'il y a une page précédente
    /// </summary>
    public bool HasPreviousPage => PageNumber > 1;
    
    /// <summary>
    /// Indique s'il y a une page suivante
    /// </summary>
    public bool HasNextPage => PageNumber < TotalPages;
    
    /// <summary>
    /// Indique si c'est la première page
    /// </summary>
    public bool IsFirstPage => PageNumber == 1;
    
    /// <summary>
    /// Indique si c'est la dernière page
    /// </summary>
    public bool IsLastPage => PageNumber == TotalPages;
    
    /// <summary>
    /// Index du premier élément de la page (base 1)
    /// </summary>
    public int FirstItemIndex => (PageNumber - 1) * PageSize + 1;
    
    /// <summary>
    /// Index du dernier élément de la page (base 1)
    /// </summary>
    public int LastItemIndex => Math.Min(FirstItemIndex + PageSize - 1, TotalCount);
    
    /// <summary>
    /// Constructeur par défaut
    /// </summary>
    public PagedResultDto()
    {
    }
    
    /// <summary>
    /// Constructeur avec paramètres
    /// </summary>
    /// <param name="items">Liste des éléments</param>
    /// <param name="totalCount">Nombre total d'éléments</param>
    /// <param name="pageNumber">Numéro de page</param>
    /// <param name="pageSize">Taille de page</param>
    public PagedResultDto(List<T> items, int totalCount, int pageNumber, int pageSize)
    {
        Items = items ?? new List<T>();
        TotalCount = totalCount;
        PageNumber = pageNumber;
        PageSize = pageSize;
        TotalPages = (int)Math.Ceiling((double)totalCount / pageSize);
    }
    
    /// <summary>
    /// Crée un résultat paginé vide
    /// </summary>
    /// <param name="pageNumber">Numéro de page</param>
    /// <param name="pageSize">Taille de page</param>
    /// <returns>Résultat paginé vide</returns>
    public static PagedResultDto<T> Empty(int pageNumber = 1, int pageSize = 20)
    {
        return new PagedResultDto<T>(new List<T>(), 0, pageNumber, pageSize);
    }
    
    /// <summary>
    /// Transforme un résultat paginé d'un type vers un autre
    /// </summary>
    /// <typeparam name="TResult">Type de destination</typeparam>
    /// <param name="mapper">Fonction de transformation</param>
    /// <returns>Résultat paginé transformé</returns>
    public PagedResultDto<TResult> Map<TResult>(Func<T, TResult> mapper)
    {
        var mappedItems = Items.Select(mapper).ToList();
        return new PagedResultDto<TResult>(mappedItems, TotalCount, PageNumber, PageSize);
    }
}

/// <summary>
/// Extensions pour faciliter la création de résultats paginés
/// </summary>
public static class PagedResultExtensions
{
    /// <summary>
    /// Convertit une liste en résultat paginé
    /// </summary>
    /// <typeparam name="T">Type des éléments</typeparam>
    /// <param name="source">Liste source</param>
    /// <param name="pageNumber">Numéro de page</param>
    /// <param name="pageSize">Taille de page</param>
    /// <returns>Résultat paginé</returns>
    public static PagedResultDto<T> ToPagedResult<T>(this IEnumerable<T> source, int pageNumber, int pageSize)
    {
        var items = source.ToList();
        var totalCount = items.Count;
        var pagedItems = items.Skip((pageNumber - 1) * pageSize).Take(pageSize).ToList();
        
        return new PagedResultDto<T>(pagedItems, totalCount, pageNumber, pageSize);
    }
    
    /// <summary>
    /// Convertit une requête IQueryable en résultat paginé
    /// </summary>
    /// <typeparam name="T">Type des éléments</typeparam>
    /// <param name="source">Requête source</param>
    /// <param name="pageNumber">Numéro de page</param>
    /// <param name="pageSize">Taille de page</param>
    /// <returns>Résultat paginé</returns>
    public static async Task<PagedResultDto<T>> ToPagedResultAsync<T>(this IQueryable<T> source, int pageNumber, int pageSize)
    {
        var totalCount = source.Count();
        var items = source.Skip((pageNumber - 1) * pageSize).Take(pageSize).ToList();
        
        return new PagedResultDto<T>(items, totalCount, pageNumber, pageSize);
    }
}
