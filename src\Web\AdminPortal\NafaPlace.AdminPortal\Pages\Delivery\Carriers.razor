@page "/delivery/carriers"
@using NafaPlace.AdminPortal.Models.Delivery
@using NafaPlace.AdminPortal.Services
@inject IDeliveryService DeliveryService
@inject IJSRuntime JSRuntime
@inject ILogger<Carriers> Logger

<PageTitle>Gestion des Transporteurs</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-truck text-primary me-2"></i>
                    Transporteurs
                </h2>
                <button class="btn btn-primary" @onclick="ShowCreateModal">
                    <i class="fas fa-plus me-2"></i>Nouveau Transporteur
                </button>
            </div>

            @if (isLoading)
            {
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                </div>
            }
            else
            {
                <div class="card">
                    <div class="card-header">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h5 class="card-title mb-0">Liste des Transporteurs (@carriers.Count)</h5>
                            </div>
                            <div class="col-md-6">
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="Rechercher un transporteur..." @bind="searchTerm" @oninput="FilterCarriers">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        @if (filteredCarriers.Any())
                        {
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Transporteur</th>
                                            <th>Code</th>
                                            <th>Type</th>
                                            <th>Contact</th>
                                            <th>Performance</th>
                                            <th>Statut</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var carrier in filteredCarriers)
                                        {
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        @if (!string.IsNullOrEmpty(carrier.LogoUrl))
                                                        {
                                                            <img src="@carrier.LogoUrl" alt="@carrier.Name" class="me-3" style="width: 40px; height: 40px; object-fit: cover; border-radius: 4px;">
                                                        }
                                                        else
                                                        {
                                                            <div class="bg-secondary text-white d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px; border-radius: 4px;">
                                                                <i class="fas fa-truck"></i>
                                                            </div>
                                                        }
                                                        <div>
                                                            <strong>@carrier.Name</strong>
                                                            @if (!string.IsNullOrEmpty(carrier.Description))
                                                            {
                                                                <br><small class="text-muted">@carrier.Description</small>
                                                            }
                                                        </div>
                                                    </div>
                                                </td>
                                                <td><code>@carrier.Code</code></td>
                                                <td>
                                                    <span class="badge @GetCarrierTypeBadgeClass(carrier.Type)">@GetCarrierTypeText(carrier.Type)</span>
                                                </td>
                                                <td>
                                                    @if (!string.IsNullOrEmpty(carrier.ContactEmail))
                                                    {
                                                        <div><i class="fas fa-envelope me-1"></i>@carrier.ContactEmail</div>
                                                    }
                                                    @if (!string.IsNullOrEmpty(carrier.ContactPhone))
                                                    {
                                                        <div><i class="fas fa-phone me-1"></i>@carrier.ContactPhone</div>
                                                    }
                                                </td>
                                                <td>
                                                    <div>
                                                        <div class="d-flex align-items-center mb-1">
                                                            <span class="me-2">Note:</span>
                                                            <div class="text-warning">
                                                                @for (int i = 1; i <= 5; i++)
                                                                {
                                                                    <i class="fas fa-star@(i <= carrier.Rating ? "" : "-o")"></i>
                                                                }
                                                            </div>
                                                            <small class="text-muted ms-1">(@carrier.ReviewCount)</small>
                                                        </div>
                                                        <small class="text-muted">
                                                            @carrier.TotalDeliveries livraisons • @carrier.SuccessRate.ToString("F1")% réussite
                                                        </small>
                                                    </div>
                                                </td>
                                                <td>
                                                    @if (carrier.IsActive)
                                                    {
                                                        <span class="badge bg-success">Actif</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="badge bg-danger">Inactif</span>
                                                    }
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <button class="btn btn-outline-primary" @onclick="() => EditCarrier(carrier)" title="Modifier">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button class="btn btn-outline-info" @onclick="() => ViewCarrierZones(carrier)" title="Zones">
                                                            <i class="fas fa-map-marked-alt"></i>
                                                        </button>
                                                        <button class="btn btn-outline-danger" @onclick="() => DeleteCarrier(carrier)" title="Supprimer">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        }
                        else
                        {
                            <div class="text-center py-5">
                                <i class="fas fa-truck fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">Aucun transporteur trouvé</h5>
                                <p class="text-muted">
                                    @if (string.IsNullOrEmpty(searchTerm))
                                    {
                                        <span>Créez votre premier transporteur pour commencer.</span>
                                    }
                                    else
                                    {
                                        <span>Aucun transporteur ne correspond à votre recherche.</span>
                                    }
                                </p>
                            </div>
                        }
                    </div>
                </div>
            }
        </div>
    </div>
</div>

<!-- Modal Création/Édition Transporteur -->
@if (showModal)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        @(isEditMode ? "Modifier le Transporteur" : "Nouveau Transporteur")
                    </h5>
                    <button type="button" class="btn-close" @onclick="CloseModal"></button>
                </div>
                <EditForm Model="currentCarrier" OnValidSubmit="SaveCarrier">
                    <DataAnnotationsValidator />
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Nom du transporteur *</label>
                                    <InputText class="form-control" @bind-Value="currentCarrier.Name" placeholder="Ex: Express Delivery" />
                                    <ValidationMessage For="@(() => currentCarrier.Name)" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Code du transporteur *</label>
                                    <InputText class="form-control" @bind-Value="currentCarrier.Code" placeholder="Ex: EXP-DEL" />
                                    <ValidationMessage For="@(() => currentCarrier.Code)" />
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <InputTextArea class="form-control" @bind-Value="currentCarrier.Description" rows="3" placeholder="Description du transporteur..." />
                            <ValidationMessage For="@(() => currentCarrier.Description)" />
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Type de transporteur</label>
                                    <InputSelect class="form-select" @bind-Value="currentCarrier.Type">
                                        <option value="@CarrierType.Internal">Interne</option>
                                        <option value="@CarrierType.ThirdParty">Tiers</option>
                                        <option value="@CarrierType.Partner">Partenaire</option>
                                    </InputSelect>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">URL du logo</label>
                                    <InputText class="form-control" @bind-Value="currentCarrier.LogoUrl" placeholder="https://..." />
                                    <ValidationMessage For="@(() => currentCarrier.LogoUrl)" />
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Email de contact</label>
                                    <InputText class="form-control" @bind-Value="currentCarrier.ContactEmail" placeholder="<EMAIL>" />
                                    <ValidationMessage For="@(() => currentCarrier.ContactEmail)" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Téléphone de contact</label>
                                    <InputText class="form-control" @bind-Value="currentCarrier.ContactPhone" placeholder="+224 XXX XXX XXX" />
                                    <ValidationMessage For="@(() => currentCarrier.ContactPhone)" />
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Adresse</label>
                                    <InputTextArea class="form-control" @bind-Value="currentCarrier.Address" rows="2" placeholder="Adresse du transporteur..." />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Site web</label>
                                    <InputText class="form-control" @bind-Value="currentCarrier.Website" placeholder="https://www.transporteur.com" />
                                    <ValidationMessage For="@(() => currentCarrier.Website)" />
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <InputCheckbox class="form-check-input" @bind-Value="currentCarrier.IsActive" />
                                    <label class="form-check-label">Transporteur actif</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" @onclick="CloseModal">Annuler</button>
                        <button type="submit" class="btn btn-primary" disabled="@isSaving">
                            @if (isSaving)
                            {
                                <span class="spinner-border spinner-border-sm me-2"></span>
                            }
                            @(isEditMode ? "Modifier" : "Créer")
                        </button>
                    </div>
                </EditForm>
            </div>
        </div>
    </div>
}

@code {
    private List<CarrierDto> carriers = new();
    private List<CarrierDto> filteredCarriers = new();
    private CarrierDto currentCarrier = new();
    private bool isLoading = true;
    private bool showModal = false;
    private bool isEditMode = false;
    private bool isSaving = false;
    private string searchTerm = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        await LoadCarriers();
    }

    private async Task LoadCarriers()
    {
        try
        {
            isLoading = true;
            carriers = await DeliveryService.GetCarriersAsync();
            filteredCarriers = carriers;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading carriers");
            await JSRuntime.InvokeVoidAsync("alert", "Erreur lors du chargement des transporteurs");
        }
        finally
        {
            isLoading = false;
        }
    }

    private void FilterCarriers(ChangeEventArgs e)
    {
        searchTerm = e.Value?.ToString() ?? string.Empty;

        if (string.IsNullOrWhiteSpace(searchTerm))
        {
            filteredCarriers = carriers;
        }
        else
        {
            filteredCarriers = carriers.Where(c =>
                c.Name.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                c.Code.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                (c.Description?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ?? false) ||
                (c.ContactEmail?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ?? false)
            ).ToList();
        }
    }

    private void ShowCreateModal()
    {
        currentCarrier = new CarrierDto
        {
            Type = CarrierType.ThirdParty,
            IsActive = true
        };
        isEditMode = false;
        showModal = true;
    }

    private void EditCarrier(CarrierDto carrier)
    {
        currentCarrier = new CarrierDto
        {
            Id = carrier.Id,
            Name = carrier.Name,
            Code = carrier.Code,
            Description = carrier.Description,
            LogoUrl = carrier.LogoUrl,
            ContactEmail = carrier.ContactEmail,
            ContactPhone = carrier.ContactPhone,
            Address = carrier.Address,
            Website = carrier.Website,
            Type = carrier.Type,
            IsActive = carrier.IsActive
        };
        isEditMode = true;
        showModal = true;
    }

    private async Task SaveCarrier()
    {
        try
        {
            isSaving = true;

            if (isEditMode)
            {
                await DeliveryService.UpdateCarrierAsync(currentCarrier.Id, currentCarrier);
                await JSRuntime.InvokeVoidAsync("alert", "Transporteur modifié avec succès");
            }
            else
            {
                await DeliveryService.CreateCarrierAsync(currentCarrier);
                await JSRuntime.InvokeVoidAsync("alert", "Transporteur créé avec succès");
            }

            CloseModal();
            await LoadCarriers();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error saving carrier");
            await JSRuntime.InvokeVoidAsync("alert", "Erreur lors de la sauvegarde du transporteur");
        }
        finally
        {
            isSaving = false;
        }
    }

    private async Task DeleteCarrier(CarrierDto carrier)
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", $"Êtes-vous sûr de vouloir supprimer le transporteur '{carrier.Name}' ?");
        if (!confirmed) return;

        try
        {
            var success = await DeliveryService.DeleteCarrierAsync(carrier.Id);
            if (success)
            {
                await JSRuntime.InvokeVoidAsync("alert", "Transporteur supprimé avec succès");
                await LoadCarriers();
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", "Erreur lors de la suppression du transporteur");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error deleting carrier {CarrierId}", carrier.Id);
            await JSRuntime.InvokeVoidAsync("alert", "Erreur lors de la suppression du transporteur");
        }
    }

    private void ViewCarrierZones(CarrierDto carrier)
    {
        // TODO: Navigate to carrier zones management
        JSRuntime.InvokeVoidAsync("alert", $"Gestion des zones pour: {carrier.Name}");
    }

    private void CloseModal()
    {
        showModal = false;
        currentCarrier = new();
        isEditMode = false;
        isSaving = false;
    }

    private string GetCarrierTypeText(CarrierType type)
    {
        return type switch
        {
            CarrierType.Internal => "Interne",
            CarrierType.ThirdParty => "Tiers",
            CarrierType.Partner => "Partenaire",
            _ => "Inconnu"
        };
    }

    private string GetCarrierTypeBadgeClass(CarrierType type)
    {
        return type switch
        {
            CarrierType.Internal => "bg-primary",
            CarrierType.ThirdParty => "bg-secondary",
            CarrierType.Partner => "bg-success",
            _ => "bg-light"
        };
    }
}
