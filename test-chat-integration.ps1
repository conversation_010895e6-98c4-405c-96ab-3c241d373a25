# Script de test pour l'intégration du chat NafaPlace
Write-Host "🚀 Test d'intégration du système de chat NafaPlace" -ForegroundColor Green

# Configuration
$chatApiUrl = "http://localhost:5007"
$webAppUrl = "http://localhost:8080"

# Fonction pour tester une URL
function Test-Endpoint {
    param(
        [string]$Url,
        [string]$Description
    )
    
    try {
        Write-Host "🔍 Test: $Description" -ForegroundColor Yellow
        $response = Invoke-WebRequest -Uri $Url -Method GET -TimeoutSec 10
        
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ $Description - OK" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ $Description - Échec (Status: $($response.StatusCode))" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ $Description - Erreur: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Fonction pour tester l'API Chat
function Test-ChatAPI {
    Write-Host "`n📡 Test de l'API Chat" -ForegroundColor Cyan

    $endpoints = @(
        @{ Url = "$chatApiUrl/api/faq"; Description = "FAQ Endpoint" },
        @{ Url = "$chatApiUrl/api/faq/categories"; Description = "FAQ Categories" },
        @{ Url = "$chatApiUrl/api/chatbot"; Description = "Chatbot Endpoint (POST)" },
        @{ Url = "$chatApiUrl/api/agents/available"; Description = "Agents Disponibles" },
        @{ Url = "$chatApiUrl/api/config"; Description = "Configuration Chat" },
        @{ Url = "$chatApiUrl/api/config/health"; Description = "Health Check" }
    )
    
    $successCount = 0
    foreach ($endpoint in $endpoints) {
        if ($endpoint.Description -like "*POST*") {
            # Test POST pour le chatbot
            try {
                $body = @{
                    Message = "Bonjour"
                    SessionId = [System.Guid]::NewGuid().ToString()
                } | ConvertTo-Json
                
                $response = Invoke-WebRequest -Uri $endpoint.Url -Method POST -Body $body -ContentType "application/json" -TimeoutSec 10
                if ($response.StatusCode -eq 200) {
                    Write-Host "✅ $($endpoint.Description) - OK" -ForegroundColor Green
                    $successCount++
                } else {
                    Write-Host "❌ $($endpoint.Description) - Échec" -ForegroundColor Red
                }
            }
            catch {
                Write-Host "❌ $($endpoint.Description) - Erreur: $($_.Exception.Message)" -ForegroundColor Red
            }
        } else {
            if (Test-Endpoint -Url $endpoint.Url -Description $endpoint.Description) {
                $successCount++
            }
        }
    }
    
    Write-Host "`n📊 Résultats API Chat: $successCount/$($endpoints.Count) endpoints fonctionnels" -ForegroundColor Cyan
    return $successCount -eq $endpoints.Count
}

# Fonction pour tester l'application web
function Test-WebApp {
    Write-Host "`n🌐 Test de l'application web" -ForegroundColor Cyan
    
    $webEndpoints = @(
        @{ Url = "$webAppUrl"; Description = "Page d'accueil" },
        @{ Url = "$webAppUrl/chat/support"; Description = "Page de support chat" }
    )
    
    $successCount = 0
    foreach ($endpoint in $webEndpoints) {
        if (Test-Endpoint -Url $endpoint.Url -Description $endpoint.Description) {
            $successCount++
        }
    }
    
    Write-Host "`n📊 Résultats Web App: $successCount/$($webEndpoints.Count) pages accessibles" -ForegroundColor Cyan
    return $successCount -eq $webEndpoints.Count
}

# Fonction pour tester SignalR
function Test-SignalR {
    Write-Host "`n🔌 Test de SignalR" -ForegroundColor Cyan
    
    try {
        $signalRUrl = "$chatApiUrl/chathub"
        Write-Host "🔍 Test: SignalR Hub" -ForegroundColor Yellow
        
        # Test de négociation SignalR
        $negotiateResponse = Invoke-WebRequest -Uri "$signalRUrl/negotiate" -Method POST -TimeoutSec 10
        
        if ($negotiateResponse.StatusCode -eq 200) {
            Write-Host "✅ SignalR Hub - Négociation OK" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ SignalR Hub - Échec de négociation" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ SignalR Hub - Erreur: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Fonction pour vérifier les services Docker
function Test-DockerServices {
    Write-Host "`n🐳 Vérification des services Docker" -ForegroundColor Cyan
    
    try {
        $services = docker ps --format "table {{.Names}}\t{{.Status}}" | Select-String "nafaplace"
        
        if ($services) {
            Write-Host "✅ Services Docker actifs:" -ForegroundColor Green
            $services | ForEach-Object { Write-Host "  $_" -ForegroundColor White }
            return $true
        } else {
            Write-Host "❌ Aucun service NafaPlace détecté" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ Erreur Docker: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Exécution des tests
Write-Host "🔧 Démarrage des tests d'intégration..." -ForegroundColor Blue

$dockerOk = Test-DockerServices
$chatApiOk = Test-ChatAPI
$webAppOk = Test-WebApp
$signalROk = Test-SignalR

# Résumé final
Write-Host "`n📋 RÉSUMÉ DES TESTS" -ForegroundColor Magenta
Write-Host "==================" -ForegroundColor Magenta
Write-Host "Docker Services: $(if($dockerOk){'✅ OK'}else{'❌ ÉCHEC'})" -ForegroundColor $(if($dockerOk){'Green'}else{'Red'})
Write-Host "API Chat: $(if($chatApiOk){'✅ OK'}else{'❌ ÉCHEC'})" -ForegroundColor $(if($chatApiOk){'Green'}else{'Red'})
Write-Host "Application Web: $(if($webAppOk){'✅ OK'}else{'❌ ÉCHEC'})" -ForegroundColor $(if($webAppOk){'Green'}else{'Red'})
Write-Host "SignalR: $(if($signalROk){'✅ OK'}else{'❌ ÉCHEC'})" -ForegroundColor $(if($signalROk){'Green'}else{'Red'})

# Test des fonctionnalités spécifiques
function Test-ChatFeatures {
    Write-Host "`n🎯 Test des fonctionnalités du chat" -ForegroundColor Cyan

    $features = @()

    # Test du chatbot
    try {
        $body = @{
            Message = "Bonjour, comment ça va ?"
            SessionId = [System.Guid]::NewGuid().ToString()
        } | ConvertTo-Json

        $response = Invoke-WebRequest -Uri "$chatApiUrl/api/chatbot" -Method POST -Body $body -ContentType "application/json" -TimeoutSec 10
        if ($response.StatusCode -eq 200) {
            $features += "✅ Chatbot fonctionnel"
        } else {
            $features += "❌ Chatbot non fonctionnel"
        }
    }
    catch {
        $features += "❌ Chatbot erreur: $($_.Exception.Message)"
    }

    # Test des FAQs
    try {
        $response = Invoke-WebRequest -Uri "$chatApiUrl/api/faq" -Method GET -TimeoutSec 10
        if ($response.StatusCode -eq 200) {
            $faqData = $response.Content | ConvertFrom-Json
            if ($faqData.Count -gt 0) {
                $features += "✅ FAQs disponibles ($($faqData.Count) items)"
            } else {
                $features += "⚠️  FAQs vides"
            }
        } else {
            $features += "❌ FAQs non accessibles"
        }
    }
    catch {
        $features += "❌ FAQs erreur: $($_.Exception.Message)"
    }

    # Test des agents
    try {
        $response = Invoke-WebRequest -Uri "$chatApiUrl/api/agents/available" -Method GET -TimeoutSec 10
        if ($response.StatusCode -eq 200) {
            $agentData = $response.Content | ConvertFrom-Json
            if ($agentData.Count -gt 0) {
                $features += "✅ Agents disponibles ($($agentData.Count) agents)"
            } else {
                $features += "⚠️  Aucun agent disponible"
            }
        } else {
            $features += "❌ Agents non accessibles"
        }
    }
    catch {
        $features += "❌ Agents erreur: $($_.Exception.Message)"
    }

    Write-Host "`n📊 Résultats des fonctionnalités:" -ForegroundColor Cyan
    foreach ($feature in $features) {
        Write-Host "  $feature" -ForegroundColor White
    }

    return $features.Where({$_ -like "✅*"}).Count -eq 3
}

$allTestsPassed = $dockerOk -and $chatApiOk -and $webAppOk -and $signalROk
$featuresOk = Test-ChatFeatures

if ($allTestsPassed -and $featuresOk) {
    Write-Host "`n🎉 TOUS LES TESTS SONT PASSÉS! Le système de chat est opérationnel." -ForegroundColor Green
    Write-Host "🔗 Accédez au chat via: $webAppUrl" -ForegroundColor Cyan
    Write-Host "🔧 Interface admin: $webAppUrl/admin/chat-management" -ForegroundColor Cyan
    Write-Host "📚 Historique: $webAppUrl/account/chat-history" -ForegroundColor Cyan
} else {
    Write-Host "`n⚠️  CERTAINS TESTS ONT ÉCHOUÉ. Vérifiez la configuration." -ForegroundColor Yellow
    Write-Host "💡 Suggestions:" -ForegroundColor Cyan
    Write-Host "  1. Vérifiez que tous les services Docker sont démarrés" -ForegroundColor White
    Write-Host "  2. Vérifiez les logs des services: docker-compose logs chat-api" -ForegroundColor White
    Write-Host "  3. Vérifiez la configuration de la base de données" -ForegroundColor White
    Write-Host "  4. Consultez le guide: CHAT_DEPLOYMENT_GUIDE.md" -ForegroundColor White
}

Write-Host "`n🏁 Tests terminés." -ForegroundColor Blue
