@page "/categories/edit/{Id:int}"
@using NafaPlace.AdminPortal.Models
@using NafaPlace.AdminPortal.Services
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Web
@inject CategoryService CategoryService
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime
@inject ImageService ImageService

<PageTitle>Modifier une catégorie - NafaPlace Admin</PageTitle>

<h1>Modifier la catégorie</h1>

@if (isLoading)
{
    <p><em>Chargement...</em></p>
}
else if (category == null)
{
    <div class="alert alert-danger">
        La catégorie demandée n'a pas été trouvée.
    </div>
    <a href="/categories" class="btn btn-primary">Retour à la liste</a>
}
else
{
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <EditForm Model="@updateRequest" OnValidSubmit="HandleValidSubmit">
                        <DataAnnotationsValidator />
                        <ValidationSummary />

                        <div class="mb-3">
                            <label for="name" class="form-label">Nom</label>
                            <InputText id="name" @bind-Value="updateRequest.Name" class="form-control" />
                            <ValidationMessage For="@(() => updateRequest.Name)" />
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <InputTextArea id="description" @bind-Value="updateRequest.Description" class="form-control" rows="3" />
                            <ValidationMessage For="@(() => updateRequest.Description)" />
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Image de la catégorie</label>
                            <div class="mb-2">
                                <InputFile OnChange="OnImageSelected" class="form-control" accept="image/*" />
                                <small class="form-text text-muted">Sélectionnez une image (JPG, PNG, GIF - max 5MB)</small>
                            </div>
                            @if (isUploading)
                            {
                                <div class="progress mb-2">
                                    <div class="progress-bar" role="progressbar" style="width: @(uploadProgress)%">
                                        @uploadProgress%
                                    </div>
                                </div>
                            }
                            @if (!string.IsNullOrEmpty(previewImageUrl))
                            {
                                <div class="mb-2">
                                    <img src="@previewImageUrl" alt="Aperçu" class="img-thumbnail" style="max-width: 200px; max-height: 200px;" />
                                </div>
                            }
                        </div>

                        <div class="mb-3">
                            <label for="imageUrl" class="form-label">Ou saisissez une URL d'image</label>
                            <InputText id="imageUrl" @bind-Value="updateRequest.ImageUrl" class="form-control" @oninput="OnImageUrlChanged" />
                            <ValidationMessage For="@(() => updateRequest.ImageUrl)" />
                            <small class="form-text text-muted">URL de l'image représentant la catégorie</small>
                        </div>

                        <div class="mb-3">
                            <label for="iconUrl" class="form-label">URL de l'icône (optionnel)</label>
                            <InputText id="iconUrl" @bind-Value="updateRequest.IconUrl" class="form-control" />
                            <ValidationMessage For="@(() => updateRequest.IconUrl)" />
                            <small class="form-text text-muted">URL de l'icône pour la catégorie (optionnel)</small>
                        </div>

                        <div class="mb-3">
                            <label for="parentId" class="form-label">Catégorie parente (optionnel)</label>
                            <InputSelect id="parentId" @bind-Value="parentIdString" class="form-control">
                                <option value="">-- Aucune --</option>
                                @if (parentCategories != null)
                                {
                                    @foreach (var parentCategory in parentCategories.Where(c => c.Id != category.Id))
                                    {
                                        <option value="@parentCategory.Id">@parentCategory.Name</option>
                                    }
                                }
                            </InputSelect>
                            <small class="form-text text-muted">Sélectionnez une catégorie parente si cette catégorie est une sous-catégorie</small>
                        </div>

                        <div class="mb-3 form-check">
                            <InputCheckbox id="isActive" @bind-Value="updateRequest.IsActive" class="form-check-input" />
                            <label class="form-check-label" for="isActive">Actif</label>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="/categories" class="btn btn-secondary">Annuler</a>
                            <button type="submit" class="btn btn-primary">Enregistrer</button>
                        </div>
                    </EditForm>
                </div>
            </div>
        </div>
    </div>
}

@code {
    [Parameter]
    public int Id { get; set; }

    private CategoryDto? category;
    private UpdateCategoryRequest updateRequest = new();
    private IEnumerable<CategoryDto>? parentCategories;
    private string parentIdString = "";
    private bool isLoading = true;
    private bool isUploading = false;
    private int uploadProgress = 0;
    private string previewImageUrl = "";

    protected override async Task OnInitializedAsync()
    {
        try
        {
            // Charger la catégorie à éditer
            category = await CategoryService.GetCategoryByIdAsync(Id);
            
            if (category != null)
            {
                // Initialiser le formulaire avec les données de la catégorie
                updateRequest.Name = category.Name;
                updateRequest.Description = category.Description;
                updateRequest.ImageUrl = category.ImageUrl ?? string.Empty;
                updateRequest.IconUrl = category.IconUrl ?? string.Empty;
                updateRequest.IsActive = category.IsActive;
                
                // Convertir ParentId en string pour InputSelect
                parentIdString = category.ParentCategoryId?.ToString() ?? "";
                
                // Charger les catégories parentes potentielles
                parentCategories = await CategoryService.GetAllCategoriesAsync();
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Erreur lors du chargement de la catégorie: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            // Initialiser l'aperçu de l'image si elle existe
            if (!string.IsNullOrEmpty(updateRequest.ImageUrl))
            {
                previewImageUrl = CategoryService.GetImageUrl(category);
            }
        }
    }

    private async Task OnImageSelected(InputFileChangeEventArgs e)
    {
        var file = e.File;
        if (file != null)
        {
            // Vérifier la taille du fichier (5MB max)
            if (file.Size > 5 * 1024 * 1024)
            {
                await JSRuntime.InvokeVoidAsync("alert", "Le fichier est trop volumineux. Taille maximale: 5MB");
                return;
            }

            // Vérifier le type de fichier
            if (!file.ContentType.StartsWith("image/"))
            {
                await JSRuntime.InvokeVoidAsync("alert", "Veuillez sélectionner un fichier image valide.");
                return;
            }

            try
            {
                isUploading = true;
                uploadProgress = 0;
                StateHasChanged();

                // Simuler le progrès d'upload
                for (int i = 0; i <= 90; i += 10)
                {
                    uploadProgress = i;
                    StateHasChanged();
                    await Task.Delay(50);
                }

                // Upload du fichier
                var result = await ImageService.UploadImageAsync(file, "categories");

                if (result.Success && !string.IsNullOrEmpty(result.ImageUrl))
                {
                    updateRequest.ImageUrl = result.ImageUrl;
                    previewImageUrl = result.ImageUrl;
                    uploadProgress = 100;
                    StateHasChanged();
                    await Task.Delay(500);
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("alert", $"Erreur lors de l'upload: {result.ErrorMessage}");
                }
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("alert", $"Erreur lors de l'upload: {ex.Message}");
            }
            finally
            {
                isUploading = false;
                uploadProgress = 0;
                StateHasChanged();
            }
        }
    }

    private void OnImageUrlChanged(ChangeEventArgs e)
    {
        var newUrl = e.Value?.ToString() ?? "";
        updateRequest.ImageUrl = newUrl;

        // Mettre à jour l'aperçu si l'URL n'est pas vide
        if (!string.IsNullOrEmpty(newUrl))
        {
            previewImageUrl = newUrl;
        }
        else
        {
            previewImageUrl = "";
        }

        StateHasChanged();
    }

    private async Task HandleValidSubmit()
    {
        try
        {
            // Convertir la chaîne en int? pour ParentId
            if (!string.IsNullOrEmpty(parentIdString) && int.TryParse(parentIdString, out int parentId))
            {
                updateRequest.ParentId = parentId;
            }
            else
            {
                updateRequest.ParentId = null;
            }

            await CategoryService.UpdateCategoryAsync(Id, updateRequest);
            NavigationManager.NavigateTo("/categories");
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Erreur lors de la mise à jour de la catégorie: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("alert", $"Erreur lors de la mise à jour: {ex.Message}");
        }
    }
}
