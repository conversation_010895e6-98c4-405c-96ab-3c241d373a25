@page "/analytics/dashboard"
@using NafaPlace.Web.Services
@using NafaPlace.Web.Models.Catalog
@using Microsoft.AspNetCore.Components.Authorization
@using System.Security.Claims
@inject IProductService ProductService
@inject IJSRuntime JSRuntime
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject NavigationManager NavigationManager

<PageTitle>Tableau de Bord - NafaPlace</PageTitle>

@if (!isAuthorized)
{
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card border-0 shadow-lg">
                    <div class="card-body text-center p-5">
                        <div class="mb-4">
                            <i class="bi bi-shield-exclamation text-warning" style="font-size: 4rem;"></i>
                        </div>
                        <h3 class="mb-3">Accès Restreint</h3>
                        <p class="text-muted mb-4">
                            Cette page est réservée aux administrateurs et vendeurs.
                            Veuillez utiliser les portails appropriés pour accéder aux tableaux de bord.
                        </p>
                        <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                            <a href="http://localhost:8081" class="btn btn-primary me-md-2" target="_blank">
                                <i class="bi bi-gear me-2"></i>Portail Admin
                            </a>
                            <a href="http://localhost:8082" class="btn btn-success me-md-2" target="_blank">
                                <i class="bi bi-shop me-2"></i>Portail Vendeur
                            </a>
                            <a href="/" class="btn btn-outline-secondary">
                                <i class="bi bi-house me-2"></i>Retour à l'Accueil
                            </a>
                        </div>
                        <hr class="my-4">
                        <small class="text-muted">
                            <i class="bi bi-info-circle me-1"></i>
                            Les tableaux de bord sont disponibles sur des portails séparés pour une meilleure sécurité.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
}
else
{

<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/">Accueil</a></li>
                    <li class="breadcrumb-item active">Tableau de Bord</li>
                </ol>
            </nav>

            <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center mb-4">
                <div class="mb-3 mb-md-0">
                    <h1 class="h3 mb-1">Tableau de Bord</h1>
                    <p class="text-muted mb-0">Vue d'ensemble des performances</p>
                </div>
                <div class="btn-group btn-group-sm" role="group">
                    <button type="button" class="btn @(selectedPeriod == 7 ? "btn-primary" : "btn-outline-secondary")" @onclick="() => SetPeriod(7)">7 jours</button>
                    <button type="button" class="btn @(selectedPeriod == 30 ? "btn-primary" : "btn-outline-secondary")" @onclick="() => SetPeriod(30)">30 jours</button>
                    <button type="button" class="btn @(selectedPeriod == 90 ? "btn-primary" : "btn-outline-secondary")" @onclick="() => SetPeriod(90)">90 jours</button>
                </div>
            </div>
        </div>
    </div>

    <!-- KPIs Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-primary bg-gradient rounded-circle p-3">
                                <i class="bi bi-people text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Visiteurs Uniques</h6>
                            <h3 class="mb-0">@FormatNumber(visitorsCount)</h3>
                            <small class="text-success">
                                <i class="bi bi-arrow-up"></i> +12.5% vs période précédente
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-gradient rounded-circle p-3">
                                <i class="bi bi-cart-check text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Commandes</h6>
                            <h3 class="mb-0">@FormatNumber(ordersCount)</h3>
                            <small class="text-success">
                                <i class="bi bi-arrow-up"></i> +8.3% vs période précédente
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-warning bg-gradient rounded-circle p-3">
                                <i class="bi bi-currency-exchange text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Chiffre d'Affaires</h6>
                            <h3 class="mb-0">@FormatCurrency(revenue)</h3>
                            <small class="text-success">
                                <i class="bi bi-arrow-up"></i> +15.7% vs période précédente
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-info bg-gradient rounded-circle p-3">
                                <i class="bi bi-percent text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Taux de Conversion</h6>
                            <h3 class="mb-0">@conversionRate.ToString("F1")%</h3>
                            <small class="text-success">
                                <i class="bi bi-arrow-up"></i> +2.1% vs période précédente
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row g-3 g-md-4 mb-4">
        <div class="col-lg-8 col-md-12">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-transparent border-0 pb-0">
                    <h5 class="card-title mb-0 text-primary">
                        <i class="bi bi-graph-up me-2"></i>Évolution des Ventes
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="position: relative; height: 300px;">
                        <canvas id="salesChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-12">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-transparent border-0 pb-0">
                    <h5 class="card-title mb-0 text-primary">
                        <i class="bi bi-pie-chart me-2"></i>Top Catégories
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="position: relative; height: 300px;">
                        <canvas id="categoriesChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Products Performance -->
    <div class="row g-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent border-0 pb-0">
                    <h5 class="card-title mb-0">Performance des Produits</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Produit</th>
                                    <th>Vues</th>
                                    <th>Commandes</th>
                                    <th>Revenus</th>
                                    <th>Taux de Conversion</th>
                                    <th>Tendance</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if (topProducts != null)
                                {
                                    @foreach (var product in topProducts)
                                    {
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <img src="@product.MainImageUrl" alt="@product.Name" class="rounded me-2" style="width: 40px; height: 40px; object-fit: cover;">
                                                    <div>
                                                        <div class="fw-medium">@product.Name</div>
                                                        <small class="text-muted">ID: @product.Id</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>@Random.Shared.Next(100, 1000)</td>
                                            <td>@Random.Shared.Next(10, 50)</td>
                                            <td>@(Random.Shared.Next(50000, 500000).ToString("N0")) GNF</td>
                                            <td>@(Random.Shared.NextDouble() * 10).ToString("F1")%</td>
                                            <td>
                                                <span class="badge bg-success">
                                                    <i class="bi bi-arrow-up"></i> +@Random.Shared.Next(1, 20)%
                                                </span>
                                            </td>
                                        </tr>
                                    }
                                }
                                else
                                {
                                    <tr>
                                        <td colspan="6" class="text-center">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Chargement...</span>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private bool isAuthorized = false;
    private int selectedPeriod = 7;
    private int visitorsCount = 12543;
    private int ordersCount = 1247;
    private decimal revenue = 15678900;
    private double conversionRate = 4.2;
    private List<ProductDto>? topProducts;

    protected override async Task OnInitializedAsync()
    {
        await CheckAuthorization();
        if (isAuthorized)
        {
            await LoadData();
        }
    }

    private async Task CheckAuthorization()
    {
        try
        {
            var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            var user = authState.User;

            if (user.Identity?.IsAuthenticated == true)
            {
                // Vérifier si l'utilisateur a un rôle admin ou vendeur
                var roles = user.FindAll(ClaimTypes.Role).Select(c => c.Value).ToList();
                isAuthorized = roles.Contains("Admin") || roles.Contains("Seller");
            }

            // Rediriger vers la page d'accès refusé si non autorisé
            if (!isAuthorized)
            {
                NavigationManager.NavigateTo("/access-denied");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la vérification d'autorisation: {ex.Message}");
            NavigationManager.NavigateTo("/access-denied");
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await InitializeCharts();
        }
    }

    private async Task LoadData()
    {
        try
        {
            // Charger les produits les plus performants
            var allProducts = await ProductService.GetAllProductsAsync();
            topProducts = allProducts?.Take(5).ToList();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des données: {ex.Message}");
        }
    }

    private async Task SetPeriod(int days)
    {
        selectedPeriod = days;
        
        // Simuler des données différentes selon la période
        visitorsCount = days switch
        {
            7 => 12543,
            30 => 45678,
            90 => 123456,
            _ => 12543
        };
        
        ordersCount = days switch
        {
            7 => 1247,
            30 => 4567,
            90 => 12345,
            _ => 1247
        };
        
        revenue = days switch
        {
            7 => 15678900,
            30 => 56789000,
            90 => 156789000,
            _ => 15678900
        };
        
        StateHasChanged();
        await InitializeCharts();
    }

    private async Task InitializeCharts()
    {
        try
        {
            // Données pour le graphique des ventes
            var salesData = new
            {
                labels = new[] { "Lun", "Mar", "Mer", "Jeu", "Ven", "Sam", "Dim" },
                datasets = new[]
                {
                    new
                    {
                        label = "Ventes",
                        data = new[] { 12, 19, 3, 5, 2, 3, 9 },
                        borderColor = "rgb(75, 192, 192)",
                        backgroundColor = "rgba(75, 192, 192, 0.2)",
                        tension = 0.1
                    }
                }
            };

            // Données pour le graphique des catégories
            var categoriesData = new
            {
                labels = new[] { "Électronique", "Vêtements", "Maison", "Sport", "Livres" },
                datasets = new[]
                {
                    new
                    {
                        data = new[] { 30, 25, 20, 15, 10 },
                        backgroundColor = new[]
                        {
                            "#FF6384",
                            "#36A2EB", 
                            "#FFCE56",
                            "#4BC0C0",
                            "#9966FF"
                        }
                    }
                }
            };

            await JSRuntime.InvokeVoidAsync("initializeCharts", salesData, categoriesData);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de l'initialisation des graphiques: {ex.Message}");
        }
    }

    private string FormatNumber(int number)
    {
        if (number >= 1000000)
            return $"{number / 1000000.0:F1}M";
        else if (number >= 1000)
            return $"{number / 1000.0:F1}K";
        else
            return number.ToString();
    }

    private string FormatCurrency(decimal amount)
    {
        if (amount >= 1000000000)
            return $"{amount / 1000000000:F1}B GNF";
        else if (amount >= 1000000)
            return $"{amount / 1000000:F1}M GNF";
        else if (amount >= 1000)
            return $"{amount / 1000:F0}K GNF";
        else
            return $"{amount:F0} GNF";
    }
}

<style>
    .kpi-card {
        transition: all 0.3s ease;
        border-left: 4px solid transparent;
    }

    .kpi-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
        border-left-color: var(--bs-primary);
    }

    .kpi-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
    }

    /* Responsive adjustments */
    @@media (max-width: 768px) {
        .kpi-card .card-body {
            padding: 1rem !important;
        }

        .kpi-icon {
            width: 40px;
            height: 40px;
            font-size: 1rem;
        }

        .kpi-card h4 {
            font-size: 1.1rem;
        }

        .kpi-card h6 {
            font-size: 0.8rem;
        }
    }

    @@media (max-width: 576px) {
        .btn-group .btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
        }

        .kpi-card .d-flex {
            flex-direction: column;
            text-align: center;
        }

        .kpi-card .flex-grow-1 {
            margin-left: 0 !important;
            margin-top: 0.5rem;
        }
    }

    /* NafaPlace color scheme */
    .bg-primary { background: linear-gradient(135deg, #E73C30, #F96302) !important; }
    .bg-success { background: linear-gradient(135deg, #28a745, #20c997) !important; }
    .bg-warning { background: linear-gradient(135deg, #ffc107, #fd7e14) !important; }
    .bg-info { background: linear-gradient(135deg, #17a2b8, #6f42c1) !important; }

    .text-primary { color: #E73C30 !important; }
    .text-success { color: #28a745 !important; }
    .text-warning { color: #ffc107 !important; }
    .text-info { color: #17a2b8 !important; }

    /* Chart containers responsive */
    .card canvas {
        max-height: 300px;
    }

    @@media (max-width: 768px) {
        .card canvas {
            max-height: 250px;
        }
    }
</style>

}
