using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using NafaPlace.Localization.Infrastructure.Data;

namespace NafaPlace.Localization.Infrastructure
{
    public class DesignTimeDbContextFactory : IDesignTimeDbContextFactory<LocalizationDbContext>
    {
        public LocalizationDbContext CreateDbContext(string[] args)
        {
            var connectionString = "Host=localhost;Port=5432;Database=NafaPlace.Localization;Username=postgres;Password=*****************";
            var optionsBuilder = new DbContextOptionsBuilder<LocalizationDbContext>();
            optionsBuilder.UseNpgsql(connectionString);

            return new LocalizationDbContext(optionsBuilder.Options);
        }
    }
}
