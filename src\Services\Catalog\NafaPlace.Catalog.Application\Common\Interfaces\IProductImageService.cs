using System.Threading.Tasks;
using NafaPlace.Catalog.Application.DTOs.Product;

namespace NafaPlace.Catalog.Application.Common.Interfaces
{
    public interface IProductImageService
    {
        Task<string> UploadImageAsync(string base64Image);
        Task<ProductImageDto> AddProductImageAsync(int productId, CreateProductImageRequest request);
        Task<IEnumerable<ProductImageDto>> AddBulkProductImagesAsync(int productId, IEnumerable<CreateProductImageRequest> request);
        Task DeleteProductImageAsync(int imageId);
        Task SetMainImageAsync(int productId, int imageId);
        Task<bool> ValidateImageAsync(string image);
        Task<ProductImageDto> GetProductImageAsync(int imageId);
    }
}
