namespace NafaPlace.Analytics.Application.DTOs;

public class DashboardAnalyticsDto
{
    public SalesAnalyticsDto Sales { get; set; } = new();
    public ProductAnalyticsDto Products { get; set; } = new();
    public CustomerAnalyticsDto Customers { get; set; } = new();
    public InventoryAnalyticsDto Inventory { get; set; } = new();
    public TrafficAnalyticsDto Traffic { get; set; } = new();
    public PerformanceMetricsDto Performance { get; set; } = new();
    public List<RecentActivityDto> RecentActivities { get; set; } = new();
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
}

public class SalesAnalyticsDto
{
    public decimal TotalRevenue { get; set; }
    public decimal RevenueGrowth { get; set; }
    public int TotalOrders { get; set; }
    public double OrdersGrowth { get; set; }
    public decimal AverageOrderValue { get; set; }
    public double AovGrowth { get; set; }
    public double ConversionRate { get; set; }
    public double ConversionGrowth { get; set; }
    public List<TimeSeriesDataDto> RevenueChart { get; set; } = new();
    public List<TimeSeriesDataDto> OrdersChart { get; set; } = new();
    public List<TopSellerDto> TopSellers { get; set; } = new();
    public List<CategorySalesDto> SalesByCategory { get; set; } = new();
}

public class ProductAnalyticsDto
{
    public int TotalProducts { get; set; }
    public int ActiveProducts { get; set; }
    public int NewProductsThisMonth { get; set; }
    public double ProductGrowth { get; set; }
    public List<TopProductDto> TopSellingProducts { get; set; } = new();
    public List<TopProductDto> TrendingProducts { get; set; } = new();
    public List<ProductPerformanceDto> ProductPerformance { get; set; } = new();
    public Dictionary<string, int> ProductsByCategory { get; set; } = new();
    public Dictionary<string, int> ProductsByStatus { get; set; } = new();
}

public class CustomerAnalyticsDto
{
    public int TotalCustomers { get; set; }
    public int NewCustomersThisMonth { get; set; }
    public double CustomerGrowth { get; set; }
    public int ActiveCustomers { get; set; }
    public double CustomerRetentionRate { get; set; }
    public decimal CustomerLifetimeValue { get; set; }
    public List<CustomerSegmentDto> CustomerSegments { get; set; } = new();
    public List<TimeSeriesDataDto> CustomerAcquisition { get; set; } = new();
    public Dictionary<string, int> CustomersByRegion { get; set; } = new();
}

public class InventoryAnalyticsDto
{
    public int TotalStockValue { get; set; }
    public int LowStockItems { get; set; }
    public int OutOfStockItems { get; set; }
    public double InventoryTurnover { get; set; }
    public int PendingAlerts { get; set; }
    public List<StockAlertSummaryDto> CriticalAlerts { get; set; } = new();
    public List<TopProductDto> SlowMovingProducts { get; set; } = new();
    public Dictionary<string, decimal> InventoryByCategory { get; set; } = new();
}

public class TrafficAnalyticsDto
{
    public int TotalVisitors { get; set; }
    public int UniqueVisitors { get; set; }
    public int PageViews { get; set; }
    public double BounceRate { get; set; }
    public double AverageSessionDuration { get; set; }
    public List<TimeSeriesDataDto> VisitorChart { get; set; } = new();
    public List<TopPageDto> TopPages { get; set; } = new();
    public List<TrafficSourceDto> TrafficSources { get; set; } = new();
    public Dictionary<string, int> VisitorsByDevice { get; set; } = new();
}

public class PerformanceMetricsDto
{
    public double SystemUptime { get; set; }
    public double AverageResponseTime { get; set; }
    public int ErrorRate { get; set; }
    public double DatabasePerformance { get; set; }
    public List<ServiceHealthDto> ServiceHealth { get; set; } = new();
    public List<PerformanceAlertDto> PerformanceAlerts { get; set; } = new();
}

public class TimeSeriesDataDto
{
    public DateTime Date { get; set; }
    public decimal Value { get; set; }
    public string Label { get; set; } = string.Empty;
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class TopSellerDto
{
    public int SellerId { get; set; }
    public string SellerName { get; set; } = string.Empty;
    public decimal Revenue { get; set; }
    public int OrderCount { get; set; }
    public double GrowthRate { get; set; }
    public string Avatar { get; set; } = string.Empty;
}

public class TopProductDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public decimal Revenue { get; set; }
    public int UnitsSold { get; set; }
    public double GrowthRate { get; set; }
    public string ImageUrl { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
}

public class CategorySalesDto
{
    public int CategoryId { get; set; }
    public string CategoryName { get; set; } = string.Empty;
    public decimal Revenue { get; set; }
    public int OrderCount { get; set; }
    public double Percentage { get; set; }
    public double GrowthRate { get; set; }
}

public class ProductPerformanceDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public int Views { get; set; }
    public int CartAdds { get; set; }
    public int Purchases { get; set; }
    public double ConversionRate { get; set; }
    public decimal Revenue { get; set; }
}

public class CustomerSegmentDto
{
    public string SegmentName { get; set; } = string.Empty;
    public int CustomerCount { get; set; }
    public decimal AverageOrderValue { get; set; }
    public double Percentage { get; set; }
    public string Description { get; set; } = string.Empty;
}

public class StockAlertSummaryDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public string AlertType { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public int CurrentStock { get; set; }
    public DateTime CreatedAt { get; set; }
}

public class TopPageDto
{
    public string PageUrl { get; set; } = string.Empty;
    public string PageTitle { get; set; } = string.Empty;
    public int Views { get; set; }
    public int UniqueViews { get; set; }
    public double AverageTimeOnPage { get; set; }
    public double BounceRate { get; set; }
}

public class TrafficSourceDto
{
    public string Source { get; set; } = string.Empty;
    public int Visitors { get; set; }
    public double Percentage { get; set; }
    public double ConversionRate { get; set; }
}

public class ServiceHealthDto
{
    public string ServiceName { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public double ResponseTime { get; set; }
    public double Uptime { get; set; }
    public DateTime LastCheck { get; set; }
}

public class PerformanceAlertDto
{
    public string AlertType { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
}

public class RecentActivityDto
{
    public string Type { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class AnalyticsFilterDto
{
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public int? SellerId { get; set; }
    public int? CategoryId { get; set; }
    public string? Region { get; set; }
    public List<string>? Metrics { get; set; }
    public string TimeGranularity { get; set; } = "day"; // hour, day, week, month
    public bool IncludeComparisons { get; set; } = true;
}

public class KPIDto
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public decimal CurrentValue { get; set; }
    public decimal PreviousValue { get; set; }
    public double ChangePercentage { get; set; }
    public string Trend { get; set; } = string.Empty; // up, down, stable
    public string Format { get; set; } = "number"; // number, currency, percentage
    public string Icon { get; set; } = string.Empty;
    public string Color { get; set; } = string.Empty;
    public DateTime LastUpdated { get; set; }
}

public class ReportRequestDto
{
    public string ReportType { get; set; } = string.Empty; // sales, products, customers, inventory
    public AnalyticsFilterDto Filters { get; set; } = new();
    public string Format { get; set; } = "pdf"; // pdf, excel, csv
    public List<string> Sections { get; set; } = new();
    public bool IncludeCharts { get; set; } = true;
    public string Language { get; set; } = "fr";
}

public class ReportResultDto
{
    public string ReportId { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
    public string DownloadUrl { get; set; } = string.Empty;
    public long FileSizeBytes { get; set; }
    public DateTime GeneratedAt { get; set; }
    public DateTime ExpiresAt { get; set; }
}
