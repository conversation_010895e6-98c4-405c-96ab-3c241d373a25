@page "/categories"
@using NafaPlace.AdminPortal.Models
@using NafaPlace.AdminPortal.Services
@inject CategoryService CategoryService
@inject NavigationManager NavigationManager

<h3>Gestion des catégories</h3>

@if (categories == null)
{
    <p><em>Chargement...</em></p>
}
else
{
    <table class="table">
        <thead>
            <tr>
                <th>Nom</th>
                <th>Description</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var category in categories)
            {
                <tr>
                    <td>@category.Name</td>
                    <td>@category.Description</td>
                    <td>
                        <button class="btn btn-primary" @onclick="() => EditCategory(category.Id)">Modifier</button>
                        <button class="btn btn-danger" @onclick="() => DeleteCategory(category.Id)">Supprimer</button>
                    </td>
                </tr>
            }
        </tbody>
    </table>
}

<button class="btn btn-success" @onclick="() => AddCategory()">Ajouter une catégorie</button>

@code {
    private List<CategoryDto> categories;

    protected override async Task OnInitializedAsync()
    {
        categories = await CategoryService.GetCategoriesAsync();
    }

    void AddCategory()
    {
        NavigationManager.NavigateTo("/categories/add");
    }

    void EditCategory(int id)
    {
        NavigationManager.NavigateTo($"/categories/edit/{id}");
    }

    async Task DeleteCategory(int id)
    {
        await CategoryService.DeleteCategoryAsync(id);
        categories = await CategoryService.GetCategoriesAsync();
    }
}