@page "/categories/edit/{id:int}"
@using NafaPlace.AdminPortal.Models
@using NafaPlace.AdminPortal.Services
@inject CategoryService CategoryService
@inject NavigationManager NavigationManager

<h3>Modifier une catégorie</h3>

@if (category == null)
{
    <p><em>Chargement...</em></p>
}
else
{
    <EditForm Model="@category" OnValidSubmit="@HandleValidSubmit">
        <DataAnnotationsValidator />
        <ValidationSummary />

        <div class="form-group">
            <label for="name">Nom</label>
            <InputText id="name" class="form-control" @bind-Value="category.Name" />
        </div>

        <div class="form-group">
            <label for="description">Description</label>
            <InputText id="description" class="form-control" @bind-Value="category.Description" />
        </div>

        <button type="submit" class="btn btn-primary">Mettre à jour</button>
    </EditForm>
}

@code {
    [Parameter]
    public int id { get; set; }

    private UpdateCategoryRequest category;

    protected override async Task OnInitializedAsync()
    {
        var categoryDto = await CategoryService.GetCategoryByIdAsync(id);
        category = new UpdateCategoryRequest
        {
            Name = categoryDto.Name,
            Description = categoryDto.Description
        };
    }

    private async Task HandleValidSubmit()
    {
        await CategoryService.UpdateCategoryAsync(id, category);
        NavigationManager.NavigateTo("/categories");
    }
}