using Microsoft.AspNetCore.Mvc;
using NafaPlace.Chat.Application.Services;
using NafaPlace.Chat.Application.DTOs;

namespace NafaPlace.Chat.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class ChatbotController : ControllerBase
{
    private readonly IChatbotService _chatbotService;
    private readonly IFAQService _faqService;
    private readonly ILogger<ChatbotController> _logger;

    public ChatbotController(
        IChatbotService chatbotService, 
        IFAQService faqService,
        ILogger<ChatbotController> logger)
    {
        _chatbotService = chatbotService;
        _faqService = faqService;
        _logger = logger;
    }

    [HttpPost]
    public async Task<IActionResult> ProcessMessage([FromBody] ChatbotMessageRequest request)
    {
        try
        {
            var userId = GetUserId();
            
            var response = await _faqService.ProcessChatbotMessageAsync(
                request.Message, 
                request.SessionId, 
                userId,
                request.Context);

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du traitement du message chatbot");
            return StatusCode(500, new ChatbotResponseDto 
            { 
                Message = "Désolé, je rencontre des difficultés techniques. Veuillez réessayer plus tard.",
                Intent = "error",
                Confidence = 0.0
            });
        }
    }

    [HttpGet("suggestions")]
    public async Task<IActionResult> GetSuggestions([FromQuery] string? category = null)
    {
        try
        {
            var suggestions = await _chatbotService.GetQuickSuggestionsAsync(category);
            return Ok(suggestions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des suggestions");
            return Ok(new List<string>
            {
                "Suivi de commande",
                "Retour produit",
                "Problème de paiement",
                "Livraison",
                "Garantie"
            });
        }
    }

    [HttpPost("feedback")]
    public async Task<IActionResult> SubmitFeedback([FromBody] ChatbotFeedbackRequest request)
    {
        try
        {
            var success = await _chatbotService.SubmitFeedbackAsync(
                request.SessionId, 
                request.MessageId, 
                request.IsHelpful, 
                request.Comment);

            if (success)
            {
                return Ok(new { message = "Feedback enregistré avec succès" });
            }

            return BadRequest("Impossible d'enregistrer le feedback");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'enregistrement du feedback");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    [HttpGet("analytics")]
    public async Task<IActionResult> GetAnalytics([FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var analytics = await _chatbotService.GetAnalyticsAsync(
                startDate ?? DateTime.UtcNow.AddDays(-30),
                endDate ?? DateTime.UtcNow);

            return Ok(analytics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des analytics");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    [HttpPost("train")]
    public async Task<IActionResult> TrainModel([FromBody] TrainModelRequest request)
    {
        try
        {
            var success = await _chatbotService.TrainModelAsync(request.TrainingData);
            
            if (success)
            {
                return Ok(new { message = "Entraînement du modèle démarré" });
            }

            return BadRequest("Impossible de démarrer l'entraînement");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'entraînement du modèle");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    private string? GetUserId()
    {
        return User?.Identity?.IsAuthenticated == true 
            ? User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value 
            : null;
    }
}

// DTOs pour les requêtes
public class ChatbotMessageRequest
{
    public string Message { get; set; } = string.Empty;
    public string SessionId { get; set; } = string.Empty;
    public Dictionary<string, object>? Context { get; set; }
}

public class ChatbotFeedbackRequest
{
    public string SessionId { get; set; } = string.Empty;
    public int MessageId { get; set; }
    public bool IsHelpful { get; set; }
    public string? Comment { get; set; }
}

public class TrainModelRequest
{
    public List<TrainingDataItem> TrainingData { get; set; } = new();
}

public class TrainingDataItem
{
    public string Input { get; set; } = string.Empty;
    public string Intent { get; set; } = string.Empty;
    public string Response { get; set; } = string.Empty;
    public List<string> Entities { get; set; } = new();
}
