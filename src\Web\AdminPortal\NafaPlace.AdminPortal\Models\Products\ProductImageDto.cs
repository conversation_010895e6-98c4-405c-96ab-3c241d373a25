using System.ComponentModel.DataAnnotations;

namespace NafaPlace.AdminPortal.Models.Products
{
    public class ProductImageDto
    {
        public int Id { get; set; }
        
        public int ProductId { get; set; }
        
        [Required(ErrorMessage = "L'URL de l'image est obligatoire")]
        public string ImageUrl { get; set; } = string.Empty;

        public string ThumbnailUrl { get; set; } = string.Empty;
        
        public bool IsMain { get; set; } = false;
        
        public int DisplayOrder { get; set; } = 0;
        
        public string? FileName { get; set; }
    }
}
