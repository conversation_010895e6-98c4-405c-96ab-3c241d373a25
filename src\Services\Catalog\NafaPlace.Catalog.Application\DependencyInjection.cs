using System.Reflection;
using Microsoft.Extensions.DependencyInjection;
using NafaPlace.Catalog.Application.Common.Interfaces;
using NafaPlace.Catalog.Application.Services;

namespace NafaPlace.Catalog.Application
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddApplication(this IServiceCollection services)
        {
            services.AddAutoMapper(Assembly.GetExecutingAssembly());

            services.AddScoped<IProductService, ProductService>();
            services.AddScoped<ICategoryService, CategoryService>();
            services.AddScoped<ISellerService, SellerService>();
            // IProductImageService est enregistré dans Infrastructure

            return services;
        }
    }
}
