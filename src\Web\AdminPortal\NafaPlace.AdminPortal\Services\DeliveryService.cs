using System.Text;
using System.Text.Json;
using NafaPlace.AdminPortal.Models.Delivery;

namespace NafaPlace.AdminPortal.Services;

public interface IDeliveryService
{
    Task<List<DeliveryZoneDto>> GetDeliveryZonesAsync();
    Task<DeliveryZoneDto?> GetDeliveryZoneAsync(int id);
    Task<DeliveryZoneDto> CreateDeliveryZoneAsync(DeliveryZoneDto zone);
    Task<DeliveryZoneDto> UpdateDeliveryZoneAsync(int id, DeliveryZoneDto zone);
    Task<bool> DeleteDeliveryZoneAsync(int id);
    
    Task<List<CarrierDto>> GetCarriersAsync();
    Task<CarrierDto?> GetCarrierAsync(int id);
    Task<CarrierDto> CreateCarrierAsync(CarrierDto carrier);
    Task<CarrierDto> UpdateCarrierAsync(int id, CarrierDto carrier);
    Task<bool> DeleteCarrierAsync(int id);
    
    Task<List<CarrierZoneDto>> GetCarrierZonesAsync(int? carrierId = null, int? zoneId = null);
    Task<CarrierZoneDto> CreateCarrierZoneAsync(CarrierZoneDto carrierZone);
    Task<CarrierZoneDto> UpdateCarrierZoneAsync(int id, CarrierZoneDto carrierZone);
    Task<bool> DeleteCarrierZoneAsync(int id);
    
    Task<List<DeliveryOrderDto>> GetDeliveryOrdersAsync();
    Task<DeliveryOrderDto?> GetDeliveryOrderAsync(int id);
    Task<List<DeliveryTrackingDto>> GetDeliveryTrackingAsync(string trackingNumber);
    Task<bool> UpdateDeliveryStatusAsync(int orderId, DeliveryStatus status, string description, string? location = null);
}

public class DeliveryService : IDeliveryService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<DeliveryService> _logger;
    private readonly JsonSerializerOptions _jsonOptions;

    public DeliveryService(HttpClient httpClient, ILogger<DeliveryService> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            PropertyNameCaseInsensitive = true
        };
    }

    // Delivery Zones
    public async Task<List<DeliveryZoneDto>> GetDeliveryZonesAsync()
    {
        try
        {
            var response = await _httpClient.GetAsync("api/deliveryzone");
            response.EnsureSuccessStatusCode();
            
            var json = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<List<DeliveryZoneDto>>(json, _jsonOptions) ?? new List<DeliveryZoneDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting delivery zones");
            return new List<DeliveryZoneDto>();
        }
    }

    public async Task<DeliveryZoneDto?> GetDeliveryZoneAsync(int id)
    {
        try
        {
            var response = await _httpClient.GetAsync($"api/deliveryzone/{id}");
            if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                return null;
                
            response.EnsureSuccessStatusCode();
            
            var json = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<DeliveryZoneDto>(json, _jsonOptions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting delivery zone {Id}", id);
            return null;
        }
    }

    public async Task<DeliveryZoneDto> CreateDeliveryZoneAsync(DeliveryZoneDto zone)
    {
        try
        {
            var json = JsonSerializer.Serialize(zone, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PostAsync("api/deliveryzone", content);
            response.EnsureSuccessStatusCode();
            
            var responseJson = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<DeliveryZoneDto>(responseJson, _jsonOptions)!;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating delivery zone");
            throw;
        }
    }

    public async Task<DeliveryZoneDto> UpdateDeliveryZoneAsync(int id, DeliveryZoneDto zone)
    {
        try
        {
            var json = JsonSerializer.Serialize(zone, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PutAsync($"api/deliveryzone/{id}", content);
            response.EnsureSuccessStatusCode();
            
            var responseJson = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<DeliveryZoneDto>(responseJson, _jsonOptions)!;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating delivery zone {Id}", id);
            throw;
        }
    }

    public async Task<bool> DeleteDeliveryZoneAsync(int id)
    {
        try
        {
            var response = await _httpClient.DeleteAsync($"api/deliveryzone/{id}");
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting delivery zone {Id}", id);
            return false;
        }
    }

    // Carriers
    public async Task<List<CarrierDto>> GetCarriersAsync()
    {
        try
        {
            var response = await _httpClient.GetAsync("api/carrier");
            response.EnsureSuccessStatusCode();
            
            var json = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<List<CarrierDto>>(json, _jsonOptions) ?? new List<CarrierDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting carriers");
            return new List<CarrierDto>();
        }
    }

    public async Task<CarrierDto?> GetCarrierAsync(int id)
    {
        try
        {
            var response = await _httpClient.GetAsync($"api/carrier/{id}");
            if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                return null;
                
            response.EnsureSuccessStatusCode();
            
            var json = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<CarrierDto>(json, _jsonOptions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting carrier {Id}", id);
            return null;
        }
    }

    public async Task<CarrierDto> CreateCarrierAsync(CarrierDto carrier)
    {
        try
        {
            var json = JsonSerializer.Serialize(carrier, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PostAsync("api/carrier", content);
            response.EnsureSuccessStatusCode();
            
            var responseJson = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<CarrierDto>(responseJson, _jsonOptions)!;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating carrier");
            throw;
        }
    }

    public async Task<CarrierDto> UpdateCarrierAsync(int id, CarrierDto carrier)
    {
        try
        {
            var json = JsonSerializer.Serialize(carrier, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PutAsync($"api/carrier/{id}", content);
            response.EnsureSuccessStatusCode();
            
            var responseJson = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<CarrierDto>(responseJson, _jsonOptions)!;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating carrier {Id}", id);
            throw;
        }
    }

    public async Task<bool> DeleteCarrierAsync(int id)
    {
        try
        {
            var response = await _httpClient.DeleteAsync($"api/carrier/{id}");
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting carrier {Id}", id);
            return false;
        }
    }

    // Carrier Zones
    public async Task<List<CarrierZoneDto>> GetCarrierZonesAsync(int? carrierId = null, int? zoneId = null)
    {
        try
        {
            var url = "api/carrier/zones";
            var queryParams = new List<string>();
            
            if (carrierId.HasValue)
                queryParams.Add($"carrierId={carrierId.Value}");
            if (zoneId.HasValue)
                queryParams.Add($"zoneId={zoneId.Value}");
                
            if (queryParams.Any())
                url += "?" + string.Join("&", queryParams);
            
            var response = await _httpClient.GetAsync(url);
            response.EnsureSuccessStatusCode();
            
            var json = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<List<CarrierZoneDto>>(json, _jsonOptions) ?? new List<CarrierZoneDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting carrier zones");
            return new List<CarrierZoneDto>();
        }
    }

    public async Task<CarrierZoneDto> CreateCarrierZoneAsync(CarrierZoneDto carrierZone)
    {
        try
        {
            var json = JsonSerializer.Serialize(carrierZone, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PostAsync("api/carrier/zones", content);
            response.EnsureSuccessStatusCode();
            
            var responseJson = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<CarrierZoneDto>(responseJson, _jsonOptions)!;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating carrier zone");
            throw;
        }
    }

    public async Task<CarrierZoneDto> UpdateCarrierZoneAsync(int id, CarrierZoneDto carrierZone)
    {
        try
        {
            var json = JsonSerializer.Serialize(carrierZone, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PutAsync($"api/carrier/zones/{id}", content);
            response.EnsureSuccessStatusCode();
            
            var responseJson = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<CarrierZoneDto>(responseJson, _jsonOptions)!;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating carrier zone {Id}", id);
            throw;
        }
    }

    public async Task<bool> DeleteCarrierZoneAsync(int id)
    {
        try
        {
            var response = await _httpClient.DeleteAsync($"api/carrier/zones/{id}");
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting carrier zone {Id}", id);
            return false;
        }
    }

    // Delivery Orders and Tracking
    public async Task<List<DeliveryOrderDto>> GetDeliveryOrdersAsync()
    {
        try
        {
            var response = await _httpClient.GetAsync("api/delivery/orders");
            response.EnsureSuccessStatusCode();
            
            var json = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<List<DeliveryOrderDto>>(json, _jsonOptions) ?? new List<DeliveryOrderDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting delivery orders");
            return new List<DeliveryOrderDto>();
        }
    }

    public async Task<DeliveryOrderDto?> GetDeliveryOrderAsync(int id)
    {
        try
        {
            var response = await _httpClient.GetAsync($"api/delivery/orders/{id}");
            if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                return null;
                
            response.EnsureSuccessStatusCode();
            
            var json = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<DeliveryOrderDto>(json, _jsonOptions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting delivery order {Id}", id);
            return null;
        }
    }

    public async Task<List<DeliveryTrackingDto>> GetDeliveryTrackingAsync(string trackingNumber)
    {
        try
        {
            var response = await _httpClient.GetAsync($"api/delivery/orders/tracking/{trackingNumber}");
            response.EnsureSuccessStatusCode();
            
            var json = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<List<DeliveryTrackingDto>>(json, _jsonOptions) ?? new List<DeliveryTrackingDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting delivery tracking for {TrackingNumber}", trackingNumber);
            return new List<DeliveryTrackingDto>();
        }
    }

    public async Task<bool> UpdateDeliveryStatusAsync(int orderId, DeliveryStatus status, string description, string? location = null)
    {
        try
        {
            var request = new
            {
                Status = status,
                Description = description,
                Location = location,
                EventDate = DateTime.UtcNow
            };
            
            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PutAsync($"api/delivery/orders/{orderId}/status", content);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating delivery status for order {OrderId}", orderId);
            return false;
        }
    }
}
