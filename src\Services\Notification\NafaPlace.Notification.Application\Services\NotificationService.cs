using Microsoft.Extensions.Logging;
using NafaPlace.Notification.Application.DTOs;
using NafaPlace.Notification.Application.Interfaces;

namespace NafaPlace.Notification.Application.Services;

public class NotificationService : INotificationService
{
    private readonly INotificationRepository _notificationRepository;
    private readonly IEmailService _emailService;
    private readonly ISmsService _smsService;
    private readonly IPushNotificationService _pushService;
    private readonly IRealTimeNotificationService _realTimeService;
    private readonly ITemplateService _templateService;
    private readonly ILogger<NotificationService> _logger;

    public NotificationService(
        INotificationRepository notificationRepository,
        IEmailService emailService,
        ISmsService smsService,
        IPushNotificationService pushService,
        IRealTimeNotificationService realTimeService,
        ITemplateService templateService,
        ILogger<NotificationService> logger)
    {
        _notificationRepository = notificationRepository;
        _emailService = emailService;
        _smsService = smsService;
        _pushService = pushService;
        _realTimeService = realTimeService;
        _templateService = templateService;
        _logger = logger;
    }

    public async Task<int> CreateNotificationAsync(CreateNotificationDto notification)
    {
        try
        {
            _logger.LogInformation("Création d'une notification pour {RecipientId}", notification.RecipientId);

            // Vérifier les préférences utilisateur
            if (!await CanSendNotificationAsync(notification.RecipientId, notification.Type, NotificationChannel.InApp))
            {
                _logger.LogInformation("Notification bloquée par les préférences utilisateur");
                return 0;
            }

            // Traiter le template si spécifié
            if (!string.IsNullOrEmpty(notification.TemplateId))
            {
                var renderedContent = await RenderTemplateAsync(int.Parse(notification.TemplateId), notification.TemplateData);
                notification.Message = renderedContent;
            }

            var notificationId = await _notificationRepository.CreateAsync(notification);
            
            // Envoyer immédiatement si pas de planification
            if (!notification.ScheduledAt.HasValue)
            {
                await SendNotificationAsync(notificationId);
            }

            return notificationId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la création de la notification");
            throw;
        }
    }

    public async Task<List<int>> CreateBulkNotificationAsync(BulkNotificationDto bulkNotification)
    {
        try
        {
            _logger.LogInformation("Création de {Count} notifications en lot", bulkNotification.RecipientIds.Count);

            var notificationIds = new List<int>();

            foreach (var recipientId in bulkNotification.RecipientIds)
            {
                var notification = new CreateNotificationDto
                {
                    Title = bulkNotification.Title,
                    Message = bulkNotification.Message,
                    Type = bulkNotification.Type,
                    Priority = bulkNotification.Priority,
                    RecipientId = recipientId,
                    RecipientType = bulkNotification.RecipientType,
                    Channels = bulkNotification.Channels,
                    Data = bulkNotification.Data,
                    ActionUrl = bulkNotification.ActionUrl,
                    ImageUrl = bulkNotification.ImageUrl,
                    ScheduledAt = bulkNotification.ScheduledAt,
                    TemplateId = bulkNotification.TemplateId,
                    TemplateData = bulkNotification.TemplateData
                };

                var notificationId = await CreateNotificationAsync(notification);
                if (notificationId > 0)
                {
                    notificationIds.Add(notificationId);
                }
            }

            return notificationIds;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la création des notifications en lot");
            throw;
        }
    }

    public async Task<NotificationDto?> GetNotificationAsync(int id)
    {
        try
        {
            return await _notificationRepository.GetByIdAsync(id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de la notification {Id}", id);
            return null;
        }
    }

    public async Task<List<NotificationDto>> GetNotificationsAsync(NotificationFilterDto filter)
    {
        try
        {
            return await _notificationRepository.GetFilteredAsync(filter);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des notifications");
            return new List<NotificationDto>();
        }
    }

    public async Task<int> GetUnreadCountAsync(string userId)
    {
        try
        {
            return await _notificationRepository.GetUnreadCountAsync(userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du comptage des notifications non lues pour {UserId}", userId);
            return 0;
        }
    }

    public async Task<bool> MarkAsReadAsync(int notificationId, string userId)
    {
        try
        {
            var success = await _notificationRepository.MarkAsReadAsync(notificationId, userId);
            
            if (success)
            {
                // Notifier en temps réel la mise à jour du compteur
                await _realTimeService.SendNotificationCountUpdateAsync(userId, await GetUnreadCountAsync(userId));
            }

            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du marquage comme lu de la notification {Id}", notificationId);
            return false;
        }
    }

    public async Task<bool> MarkAllAsReadAsync(string userId)
    {
        try
        {
            var success = await _notificationRepository.MarkAllAsReadAsync(userId);
            
            if (success)
            {
                await _realTimeService.SendNotificationCountUpdateAsync(userId, 0);
            }

            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du marquage de toutes les notifications comme lues pour {UserId}", userId);
            return false;
        }
    }

    public async Task<bool> DeleteNotificationAsync(int notificationId, string userId)
    {
        try
        {
            return await _notificationRepository.DeleteAsync(notificationId, userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression de la notification {Id}", notificationId);
            return false;
        }
    }

    public async Task<bool> SendNotificationAsync(int notificationId)
    {
        try
        {
            var notification = await GetNotificationAsync(notificationId);
            if (notification == null)
            {
                _logger.LogWarning("Notification {Id} non trouvée", notificationId);
                return false;
            }

            _logger.LogInformation("Envoi de la notification {Id} via {Channels}", 
                notificationId, string.Join(", ", notification.Channels));

            var success = true;

            foreach (var channel in notification.Channels)
            {
                try
                {
                    var channelSuccess = await SendViaChannelAsync(notification, channel);
                    success = success && channelSuccess;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Erreur lors de l'envoi via le canal {Channel}", channel);
                    success = false;
                }
            }

            // Mettre à jour le statut
            await _notificationRepository.UpdateStatusAsync(notificationId, 
                success ? NotificationStatus.Sent : NotificationStatus.Failed);

            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de la notification {Id}", notificationId);
            return false;
        }
    }

    public async Task<bool> SendImmediateNotificationAsync(CreateNotificationDto notification)
    {
        try
        {
            var notificationId = await CreateNotificationAsync(notification);
            return notificationId > 0 && await SendNotificationAsync(notificationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi immédiat de la notification");
            return false;
        }
    }

    public async Task<int> SendBulkNotificationsAsync(List<CreateNotificationDto> notifications)
    {
        var successCount = 0;

        foreach (var notification in notifications)
        {
            try
            {
                if (await SendImmediateNotificationAsync(notification))
                {
                    successCount++;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de l'envoi d'une notification en lot");
            }
        }

        _logger.LogInformation("Envoi en lot terminé: {Success}/{Total} notifications envoyées", 
            successCount, notifications.Count);

        return successCount;
    }

    public async Task ProcessPendingNotificationsAsync()
    {
        try
        {
            _logger.LogInformation("Traitement des notifications en attente");

            var pendingNotifications = await _notificationRepository.GetPendingAsync();
            
            foreach (var notification in pendingNotifications)
            {
                await SendNotificationAsync(notification.Id);
            }

            _logger.LogInformation("Traitement terminé: {Count} notifications traitées", pendingNotifications.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du traitement des notifications en attente");
        }
    }

    public async Task ProcessScheduledNotificationsAsync()
    {
        try
        {
            _logger.LogInformation("Traitement des notifications planifiées");

            var scheduledNotifications = await GetScheduledNotificationsAsync(DateTime.UtcNow);
            
            foreach (var notification in scheduledNotifications)
            {
                await SendNotificationAsync(notification.Id);
            }

            _logger.LogInformation("Traitement terminé: {Count} notifications planifiées traitées", scheduledNotifications.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du traitement des notifications planifiées");
        }
    }

    public async Task SendRealTimeNotificationAsync(string userId, RealTimeNotificationDto notification)
    {
        try
        {
            await _realTimeService.SendToUserAsync(userId, notification);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de notification temps réel à {UserId}", userId);
        }
    }

    public async Task SendRealTimeNotificationToGroupAsync(string groupName, RealTimeNotificationDto notification)
    {
        try
        {
            await _realTimeService.SendToGroupAsync(groupName, notification);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de notification temps réel au groupe {GroupName}", groupName);
        }
    }

    public async Task SendRealTimeNotificationToAllAsync(RealTimeNotificationDto notification)
    {
        try
        {
            await _realTimeService.SendToAllAsync(notification);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de notification temps réel à tous");
        }
    }

    public async Task JoinGroupAsync(string userId, string groupName)
    {
        try
        {
            await _realTimeService.JoinGroupAsync(userId, groupName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'ajout de {UserId} au groupe {GroupName}", userId, groupName);
        }
    }

    public async Task LeaveGroupAsync(string userId, string groupName)
    {
        try
        {
            await _realTimeService.LeaveGroupAsync(userId, groupName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression de {UserId} du groupe {GroupName}", userId, groupName);
        }
    }

    // Méthodes privées d'aide
    private async Task<bool> SendViaChannelAsync(NotificationDto notification, NotificationChannel channel)
    {
        return channel switch
        {
            NotificationChannel.InApp => await SendInAppNotificationAsync(notification),
            NotificationChannel.Email => await SendEmailViaNotificationAsync(notification),
            NotificationChannel.SMS => await SendSmsViaNotificationAsync(notification),
            NotificationChannel.Push => await SendPushViaNotificationAsync(notification),
            NotificationChannel.WebSocket => await SendWebSocketNotificationAsync(notification),
            _ => false
        };
    }

    private async Task<bool> SendInAppNotificationAsync(NotificationDto notification)
    {
        // Les notifications in-app sont déjà stockées en base
        return true;
    }

    private async Task<bool> SendEmailViaNotificationAsync(NotificationDto notification)
    {
        var email = new EmailNotificationDto
        {
            To = await GetUserEmailAsync(notification.RecipientId),
            Subject = notification.Title,
            HtmlBody = notification.Message,
            Priority = notification.Priority
        };

        return await _emailService.SendAsync(email);
    }

    private async Task<bool> SendSmsViaNotificationAsync(NotificationDto notification)
    {
        var sms = new SmsNotificationDto
        {
            To = await GetUserPhoneAsync(notification.RecipientId),
            Message = $"{notification.Title}: {notification.Message}",
            Priority = notification.Priority
        };

        return await _smsService.SendAsync(sms);
    }

    private async Task<bool> SendPushViaNotificationAsync(NotificationDto notification)
    {
        return await _pushService.SendToUserAsync(notification.RecipientId, notification.Title, notification.Message, notification.Data);
    }

    private async Task<bool> SendWebSocketNotificationAsync(NotificationDto notification)
    {
        var realTimeNotification = new RealTimeNotificationDto
        {
            Id = notification.Id.ToString(),
            Title = notification.Title,
            Message = notification.Message,
            Type = notification.Type.ToString(),
            Priority = notification.Priority.ToString(),
            Data = notification.Data,
            ActionUrl = notification.ActionUrl,
            ImageUrl = notification.ImageUrl,
            Timestamp = notification.CreatedAt
        };

        await _realTimeService.SendToUserAsync(notification.RecipientId, realTimeNotification);
        return true;
    }

    private async Task<string> GetUserEmailAsync(string userId)
    {
        // Simuler la récupération de l'email utilisateur
        await Task.Delay(10);
        return $"user{userId}@nafaplace.com";
    }

    private async Task<string> GetUserPhoneAsync(string userId)
    {
        // Simuler la récupération du téléphone utilisateur
        await Task.Delay(10);
        return $"+224{userId.PadLeft(8, '0')}";
    }

    // Méthodes non implémentées - à compléter selon les besoins
    public Task<int> CreateTemplateAsync(NotificationTemplateDto template) => throw new NotImplementedException();
    public Task<NotificationTemplateDto?> GetTemplateAsync(int templateId) => throw new NotImplementedException();
    public Task<List<NotificationTemplateDto>> GetTemplatesAsync(NotificationType? type = null) => throw new NotImplementedException();
    public Task<bool> UpdateTemplateAsync(NotificationTemplateDto template) => throw new NotImplementedException();
    public Task<bool> DeleteTemplateAsync(int templateId) => throw new NotImplementedException();
    public Task<string> RenderTemplateAsync(int templateId, Dictionary<string, object> data) => Task.FromResult("Template rendu");
    public Task<List<NotificationPreferenceDto>> GetUserPreferencesAsync(string userId) => throw new NotImplementedException();
    public Task<bool> UpdateUserPreferenceAsync(string userId, NotificationPreferenceDto preference) => throw new NotImplementedException();
    public Task<bool> UpdateUserPreferencesAsync(string userId, List<NotificationPreferenceDto> preferences) => throw new NotImplementedException();
    public Task<bool> CanSendNotificationAsync(string userId, NotificationType type, NotificationChannel channel) => Task.FromResult(true);
    public Task<NotificationStatsDto> GetNotificationStatsAsync(DateTime? startDate = null, DateTime? endDate = null, string? userId = null) => throw new NotImplementedException();
    public Task<Dictionary<string, object>> GetDeliveryMetricsAsync(DateTime? startDate = null, DateTime? endDate = null) => throw new NotImplementedException();
    public Task<List<NotificationDeliveryDto>> GetDeliveryHistoryAsync(int notificationId) => throw new NotImplementedException();
    public Task<bool> RetryFailedNotificationAsync(int notificationId) => throw new NotImplementedException();
    public Task<int> RetryFailedNotificationsAsync(DateTime? olderThan = null) => throw new NotImplementedException();
    public Task<int> CleanupExpiredNotificationsAsync() => throw new NotImplementedException();
    public Task<int> CleanupOldNotificationsAsync(int daysToKeep = 30) => throw new NotImplementedException();
    public Task SendWelcomeNotificationAsync(string userId, string userName, string email) => throw new NotImplementedException();
    public Task SendOrderNotificationAsync(string userId, int orderId, string orderStatus, decimal amount) => throw new NotImplementedException();
    public Task SendPaymentNotificationAsync(string userId, int orderId, string paymentStatus, decimal amount) => throw new NotImplementedException();
    public Task SendProductNotificationAsync(string sellerId, int productId, string productName, string status) => throw new NotImplementedException();
    public Task SendStockAlertNotificationAsync(string sellerId, int productId, string productName, int currentStock) => throw new NotImplementedException();
    public Task SendReviewNotificationAsync(string sellerId, int productId, string productName, int rating) => throw new NotImplementedException();
    public Task SendSystemAlertNotificationAsync(string message, NotificationPriority priority = NotificationPriority.Normal) => throw new NotImplementedException();
    public Task SendMarketingNotificationAsync(List<string> userIds, string title, string message, string? actionUrl = null) => throw new NotImplementedException();
    public Task<bool> SendEmailNotificationAsync(EmailNotificationDto email) => throw new NotImplementedException();
    public Task<bool> SendSmsNotificationAsync(SmsNotificationDto sms) => throw new NotImplementedException();
    public Task<bool> SendPushNotificationAsync(string userId, string title, string body, Dictionary<string, object>? data = null) => throw new NotImplementedException();
    public Task<int> SendBulkEmailNotificationsAsync(List<EmailNotificationDto> emails) => throw new NotImplementedException();
    public Task<int> SendBulkPushNotificationsAsync(List<string> userIds, string title, string body, Dictionary<string, object>? data = null) => throw new NotImplementedException();
    public Task<bool> SubscribeToPushAsync(string userId, PushSubscriptionDto subscription) => throw new NotImplementedException();
    public Task<bool> UnsubscribeFromPushAsync(string userId, string endpoint) => throw new NotImplementedException();
    public Task<List<PushSubscriptionDto>> GetUserPushSubscriptionsAsync(string userId) => throw new NotImplementedException();
    public Task<int> CleanupInactivePushSubscriptionsAsync(int daysInactive = 30) => throw new NotImplementedException();
    public Task<bool> ScheduleNotificationAsync(CreateNotificationDto notification, DateTime scheduledAt) => throw new NotImplementedException();
    public Task<bool> ScheduleBulkNotificationAsync(BulkNotificationDto bulkNotification, DateTime scheduledAt) => throw new NotImplementedException();
    public Task<bool> CancelScheduledNotificationAsync(int notificationId) => throw new NotImplementedException();
    public Task<List<NotificationDto>> GetScheduledNotificationsAsync(DateTime? scheduledBefore = null) => throw new NotImplementedException();
    public Task HandleOrderCreatedAsync(int orderId, string userId, decimal amount) => throw new NotImplementedException();
    public Task HandleOrderUpdatedAsync(int orderId, string userId, string status) => throw new NotImplementedException();
    public Task HandlePaymentCompletedAsync(int orderId, string userId, decimal amount) => throw new NotImplementedException();
    public Task HandleProductApprovedAsync(int productId, string sellerId, string productName) => throw new NotImplementedException();
    public Task HandleStockLowAsync(int productId, string sellerId, string productName, int currentStock) => throw new NotImplementedException();
    public Task HandleNewReviewAsync(int productId, string sellerId, string productName, int rating) => throw new NotImplementedException();
    public Task HandleUserRegisteredAsync(string userId, string userName, string email) => throw new NotImplementedException();
    public Task<Dictionary<string, object>> GetNotificationConfigAsync() => throw new NotImplementedException();
    public Task<bool> UpdateNotificationConfigAsync(Dictionary<string, object> config) => throw new NotImplementedException();
    public Task<bool> TestNotificationChannelAsync(NotificationChannel channel, string testRecipient) => throw new NotImplementedException();
    public Task<Dictionary<string, bool>> GetChannelHealthAsync() => throw new NotImplementedException();
    public Task WarmupNotificationServiceAsync() => throw new NotImplementedException();
}
