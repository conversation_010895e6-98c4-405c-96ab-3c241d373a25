﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using NafaPlace.Notifications.Infrastructure.Data;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace NafaPlace.Notifications.Infrastructure.Migrations
{
    [DbContext(typeof(NotificationsDbContext))]
    [Migration("20250710123520_InitialCreate")]
    partial class InitialCreate
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("NafaPlace.Notifications.Domain.Models.Notification", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ActionUrl")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Data")
                        .HasColumnType("text");

                    b.Property<DateTime?>("ExpiresAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ImageUrl")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("IsRead")
                        .HasColumnType("boolean");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int>("Priority")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("ReadAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("IsRead");

                    b.HasIndex("UserId");

                    b.HasIndex("UserId", "IsRead");

                    b.ToTable("Notifications");
                });

            modelBuilder.Entity("NafaPlace.Notifications.Domain.Models.NotificationLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("Channel")
                        .HasColumnType("integer");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("DeliveredAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("MaxRetries")
                        .HasColumnType("integer");

                    b.Property<string>("Metadata")
                        .HasColumnType("text");

                    b.Property<int>("Priority")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("ReadAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Recipient")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Reference")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("RetryCount")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("ScheduledAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("SentAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("Subject")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("TemplateCode")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int?>("TemplateId")
                        .HasColumnType("integer");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.HasIndex("TemplateId");

                    b.ToTable("NotificationLog");
                });

            modelBuilder.Entity("NafaPlace.Notifications.Domain.Models.NotificationTemplate", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Body")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Channel")
                        .HasColumnType("integer");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("Language")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<string>("MessageTemplate")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("Priority")
                        .HasColumnType("integer");

                    b.Property<string>("Subject")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("TitleTemplate")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Variables")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("NotificationTemplates");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Body = "Le statut de votre commande #{OrderId} a été mis à jour vers: {Status}",
                            Channel = 1,
                            Code = "ORDER_STATUS_CHANGE",
                            CreatedAt = new DateTime(2025, 7, 10, 12, 35, 20, 288, DateTimeKind.Utc).AddTicks(9273),
                            IsActive = true,
                            Language = "fr",
                            MessageTemplate = "Le statut de votre commande #{OrderId} a été mis à jour vers: {Status}",
                            Name = "OrderStatusChange",
                            Priority = 2,
                            Subject = "Mise à jour de votre commande",
                            TitleTemplate = "Mise à jour de votre commande #{OrderId}",
                            Type = 3,
                            UpdatedAt = new DateTime(2025, 7, 10, 12, 35, 20, 288, DateTimeKind.Utc).AddTicks(9348)
                        },
                        new
                        {
                            Id = 2,
                            Body = "Votre paiement de {Amount} GNF pour la commande #{OrderId} a été confirmé avec succès.",
                            Channel = 1,
                            Code = "PAYMENT_CONFIRMATION",
                            CreatedAt = new DateTime(2025, 7, 10, 12, 35, 20, 288, DateTimeKind.Utc).AddTicks(9413),
                            IsActive = true,
                            Language = "fr",
                            MessageTemplate = "Votre paiement de {Amount} GNF pour la commande #{OrderId} a été confirmé avec succès.",
                            Name = "PaymentConfirmation",
                            Priority = 3,
                            Subject = "Paiement confirmé",
                            TitleTemplate = "Paiement confirmé",
                            Type = 4,
                            UpdatedAt = new DateTime(2025, 7, 10, 12, 35, 20, 288, DateTimeKind.Utc).AddTicks(9414)
                        },
                        new
                        {
                            Id = 3,
                            Body = "Un client a laissé un avis {Rating}/5 étoiles sur votre produit {ProductName}.",
                            Channel = 1,
                            Code = "NEW_REVIEW",
                            CreatedAt = new DateTime(2025, 7, 10, 12, 35, 20, 288, DateTimeKind.Utc).AddTicks(9416),
                            IsActive = true,
                            Language = "fr",
                            MessageTemplate = "Un client a laissé un avis {Rating}/5 étoiles sur votre produit {ProductName}.",
                            Name = "NewReview",
                            Priority = 2,
                            Subject = "Nouvel avis sur votre produit",
                            TitleTemplate = "Nouvel avis sur {ProductName}",
                            Type = 17,
                            UpdatedAt = new DateTime(2025, 7, 10, 12, 35, 20, 288, DateTimeKind.Utc).AddTicks(9416)
                        },
                        new
                        {
                            Id = 4,
                            Body = "Attention! Il ne reste que {CurrentStock} unités de {ProductName} en stock.",
                            Channel = 1,
                            Code = "LOW_STOCK",
                            CreatedAt = new DateTime(2025, 7, 10, 12, 35, 20, 288, DateTimeKind.Utc).AddTicks(9418),
                            IsActive = true,
                            Language = "fr",
                            MessageTemplate = "Attention! Il ne reste que {CurrentStock} unités de {ProductName} en stock.",
                            Name = "LowStock",
                            Priority = 3,
                            Subject = "Stock faible",
                            TitleTemplate = "Stock faible: {ProductName}",
                            Type = 9,
                            UpdatedAt = new DateTime(2025, 7, 10, 12, 35, 20, 288, DateTimeKind.Utc).AddTicks(9419)
                        });
                });

            modelBuilder.Entity("NafaPlace.Notifications.Domain.Models.NotificationLog", b =>
                {
                    b.HasOne("NafaPlace.Notifications.Domain.Models.NotificationTemplate", "Template")
                        .WithMany("Notifications")
                        .HasForeignKey("TemplateId");

                    b.Navigation("Template");
                });

            modelBuilder.Entity("NafaPlace.Notifications.Domain.Models.NotificationTemplate", b =>
                {
                    b.Navigation("Notifications");
                });
#pragma warning restore 612, 618
        }
    }
}
