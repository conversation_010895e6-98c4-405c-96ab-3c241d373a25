using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Http;

namespace NafaPlace.Catalog.Application.DTOs.Product;

public class CreateProductRequest
{
    [Required]
    [StringLength(100)]
    public required string Name { get; set; }

    [Required]
    [StringLength(500)]
    public required string Description { get; set; }

    [Required]
    [Range(0, double.MaxValue)]
    public decimal Price { get; set; }

    [Required]
    [StringLength(3)]
    public required string Currency { get; set; } = "GNF";

    [Range(0, int.MaxValue)]
    public int StockQuantity { get; set; }

    [Required]
    public int? CategoryId { get; set; }

    [Range(1, int.MaxValue, ErrorMessage = "Le vendeur est obligatoire.")]
    public int? SellerId { get; set; }

    [StringLength(50)]
    public string? Brand { get; set; }

    [StringLength(50)]
    public string? Model { get; set; }

    [Range(0, double.MaxValue)]
    public decimal Weight { get; set; }

    [StringLength(20)]
    public string? Dimensions { get; set; }

    public bool IsActive { get; set; } = true;
    public bool IsFeatured { get; set; }

    public List<IFormFile> Images { get; set; } = new();
    public List<CreateProductVariantRequest> Variants { get; set; } = new();
    public List<CreateProductAttributeRequest> Attributes { get; set; } = new();
}
