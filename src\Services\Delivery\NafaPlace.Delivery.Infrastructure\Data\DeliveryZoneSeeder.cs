using NafaPlace.Delivery.Domain.Models;

namespace NafaPlace.Delivery.Infrastructure.Data;

public static class DeliveryZoneSeeder
{
    public static List<DeliveryZone> GetDefaultZones()
    {
        return new List<DeliveryZone>
        {
            // Conakry - Zone principale
            new DeliveryZone
            {
                Id = 1,
                Name = "Conakry Centre",
                Code = "CKY_CENTER",
                Description = "Centre-ville de Conakry - Livraison rapide",
                Type = ZoneType.City,
                Latitude = 9.5370,
                Longitude = -13.6785,
                Radius = 5,
                IsActive = true,
                BaseDeliveryFee = 15000,
                FreeDeliveryThreshold = 100000,
                EstimatedDeliveryDays = 1,
                MaxDeliveryDays = 2,
                SameDayDeliveryAvailable = true,
                SameDayDeliveryFee = 25000,
                ExpressDeliveryAvailable = true,
                ExpressDeliveryFee = 35000,
                Currency = "GNF",
                MaxWeight = 50,
                MaxVolume = 100000,
                DeliveryStartTime = new TimeSpan(8, 0, 0),
                DeliveryEndTime = new TimeSpan(18, 0, 0),
                DeliveryDays = "[\"Monday\",\"Tuesday\",\"Wednesday\",\"Thursday\",\"Friday\",\"Saturday\"]",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },

            new DeliveryZone
            {
                Id = 2,
                Name = "Kaloum",
                Code = "CKY_KALOUM",
                Description = "Commune de Kaloum - Zone administrative",
                Type = ZoneType.District,
                ParentZoneCode = "CKY_CENTER",
                Latitude = 9.5092,
                Longitude = -13.7122,
                Radius = 3,
                IsActive = true,
                BaseDeliveryFee = 12000,
                FreeDeliveryThreshold = 80000,
                EstimatedDeliveryDays = 1,
                MaxDeliveryDays = 1,
                SameDayDeliveryAvailable = true,
                SameDayDeliveryFee = 20000,
                ExpressDeliveryAvailable = true,
                ExpressDeliveryFee = 30000,
                Currency = "GNF",
                MaxWeight = 50,
                DeliveryStartTime = new TimeSpan(8, 0, 0),
                DeliveryEndTime = new TimeSpan(18, 0, 0),
                DeliveryDays = "[\"Monday\",\"Tuesday\",\"Wednesday\",\"Thursday\",\"Friday\",\"Saturday\"]",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },

            new DeliveryZone
            {
                Id = 3,
                Name = "Dixinn",
                Code = "CKY_DIXINN",
                Description = "Commune de Dixinn",
                Type = ZoneType.District,
                ParentZoneCode = "CKY_CENTER",
                Latitude = 9.5515,
                Longitude = -13.6918,
                Radius = 4,
                IsActive = true,
                BaseDeliveryFee = 18000,
                FreeDeliveryThreshold = 120000,
                EstimatedDeliveryDays = 1,
                MaxDeliveryDays = 2,
                SameDayDeliveryAvailable = true,
                SameDayDeliveryFee = 28000,
                ExpressDeliveryAvailable = true,
                ExpressDeliveryFee = 38000,
                Currency = "GNF",
                MaxWeight = 45,
                DeliveryStartTime = new TimeSpan(8, 0, 0),
                DeliveryEndTime = new TimeSpan(17, 0, 0),
                DeliveryDays = "[\"Monday\",\"Tuesday\",\"Wednesday\",\"Thursday\",\"Friday\",\"Saturday\"]",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },

            new DeliveryZone
            {
                Id = 4,
                Name = "Matam",
                Code = "CKY_MATAM",
                Description = "Commune de Matam",
                Type = ZoneType.District,
                ParentZoneCode = "CKY_CENTER",
                Latitude = 9.5515,
                Longitude = -13.6918,
                Radius = 6,
                IsActive = true,
                BaseDeliveryFee = 20000,
                FreeDeliveryThreshold = 150000,
                EstimatedDeliveryDays = 1,
                MaxDeliveryDays = 2,
                SameDayDeliveryAvailable = false,
                ExpressDeliveryAvailable = true,
                ExpressDeliveryFee = 40000,
                Currency = "GNF",
                MaxWeight = 40,
                DeliveryStartTime = new TimeSpan(9, 0, 0),
                DeliveryEndTime = new TimeSpan(17, 0, 0),
                DeliveryDays = "[\"Monday\",\"Tuesday\",\"Wednesday\",\"Thursday\",\"Friday\",\"Saturday\"]",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },

            new DeliveryZone
            {
                Id = 5,
                Name = "Ratoma",
                Code = "CKY_RATOMA",
                Description = "Commune de Ratoma",
                Type = ZoneType.District,
                ParentZoneCode = "CKY_CENTER",
                Latitude = 9.5515,
                Longitude = -13.6918,
                Radius = 8,
                IsActive = true,
                BaseDeliveryFee = 25000,
                FreeDeliveryThreshold = 200000,
                EstimatedDeliveryDays = 2,
                MaxDeliveryDays = 3,
                SameDayDeliveryAvailable = false,
                ExpressDeliveryAvailable = true,
                ExpressDeliveryFee = 45000,
                Currency = "GNF",
                MaxWeight = 35,
                DeliveryStartTime = new TimeSpan(9, 0, 0),
                DeliveryEndTime = new TimeSpan(16, 0, 0),
                DeliveryDays = "[\"Monday\",\"Tuesday\",\"Wednesday\",\"Thursday\",\"Friday\"]",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },

            // Autres villes principales
            new DeliveryZone
            {
                Id = 6,
                Name = "Kankan",
                Code = "KANKAN",
                Description = "Ville de Kankan - Haute Guinée",
                Type = ZoneType.City,
                Latitude = 10.3853,
                Longitude = -9.3064,
                Radius = 10,
                IsActive = true,
                BaseDeliveryFee = 50000,
                FreeDeliveryThreshold = 300000,
                EstimatedDeliveryDays = 3,
                MaxDeliveryDays = 5,
                SameDayDeliveryAvailable = false,
                ExpressDeliveryAvailable = false,
                Currency = "GNF",
                MaxWeight = 30,
                DeliveryStartTime = new TimeSpan(9, 0, 0),
                DeliveryEndTime = new TimeSpan(16, 0, 0),
                DeliveryDays = "[\"Monday\",\"Tuesday\",\"Wednesday\",\"Thursday\",\"Friday\"]",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },

            new DeliveryZone
            {
                Id = 7,
                Name = "Labé",
                Code = "LABE",
                Description = "Ville de Labé - Moyenne Guinée",
                Type = ZoneType.City,
                Latitude = 11.3180,
                Longitude = -12.2830,
                Radius = 8,
                IsActive = true,
                BaseDeliveryFee = 45000,
                FreeDeliveryThreshold = 250000,
                EstimatedDeliveryDays = 3,
                MaxDeliveryDays = 5,
                SameDayDeliveryAvailable = false,
                ExpressDeliveryAvailable = false,
                Currency = "GNF",
                MaxWeight = 25,
                DeliveryStartTime = new TimeSpan(9, 0, 0),
                DeliveryEndTime = new TimeSpan(16, 0, 0),
                DeliveryDays = "[\"Monday\",\"Tuesday\",\"Wednesday\",\"Thursday\",\"Friday\"]",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },

            new DeliveryZone
            {
                Id = 8,
                Name = "N'Zérékoré",
                Code = "NZEREKORE",
                Description = "Ville de N'Zérékoré - Guinée Forestière",
                Type = ZoneType.City,
                Latitude = 7.7562,
                Longitude = -8.8179,
                Radius = 10,
                IsActive = true,
                BaseDeliveryFee = 55000,
                FreeDeliveryThreshold = 350000,
                EstimatedDeliveryDays = 4,
                MaxDeliveryDays = 7,
                SameDayDeliveryAvailable = false,
                ExpressDeliveryAvailable = false,
                Currency = "GNF",
                MaxWeight = 25,
                DeliveryStartTime = new TimeSpan(9, 0, 0),
                DeliveryEndTime = new TimeSpan(15, 0, 0),
                DeliveryDays = "[\"Monday\",\"Tuesday\",\"Wednesday\",\"Thursday\",\"Friday\"]",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },

            new DeliveryZone
            {
                Id = 9,
                Name = "Boké",
                Code = "BOKE",
                Description = "Ville de Boké - Basse Guinée",
                Type = ZoneType.City,
                Latitude = 10.9324,
                Longitude = -14.2920,
                Radius = 8,
                IsActive = true,
                BaseDeliveryFee = 40000,
                FreeDeliveryThreshold = 200000,
                EstimatedDeliveryDays = 2,
                MaxDeliveryDays = 4,
                SameDayDeliveryAvailable = false,
                ExpressDeliveryAvailable = false,
                Currency = "GNF",
                MaxWeight = 30,
                DeliveryStartTime = new TimeSpan(9, 0, 0),
                DeliveryEndTime = new TimeSpan(16, 0, 0),
                DeliveryDays = "[\"Monday\",\"Tuesday\",\"Wednesday\",\"Thursday\",\"Friday\"]",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },

            // Zone par défaut pour les autres localités
            new DeliveryZone
            {
                Id = 10,
                Name = "Autres localités",
                Code = "OTHER_LOCATIONS",
                Description = "Autres villes et villages de Guinée",
                Type = ZoneType.Custom,
                IsActive = true,
                BaseDeliveryFee = 60000,
                FreeDeliveryThreshold = 400000,
                EstimatedDeliveryDays = 5,
                MaxDeliveryDays = 10,
                SameDayDeliveryAvailable = false,
                ExpressDeliveryAvailable = false,
                Currency = "GNF",
                MaxWeight = 20,
                DeliveryStartTime = new TimeSpan(9, 0, 0),
                DeliveryEndTime = new TimeSpan(15, 0, 0),
                DeliveryDays = "[\"Monday\",\"Tuesday\",\"Wednesday\",\"Thursday\",\"Friday\"]",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        };
    }

    public static List<Carrier> GetDefaultCarriers()
    {
        return new List<Carrier>
        {
            new Carrier
            {
                Id = 1,
                Name = "NafaPlace Express",
                Code = "NAFA_EXPRESS",
                Description = "Service de livraison interne NafaPlace - Rapide et fiable",
                LogoUrl = "/images/carriers/nafa-express.png",
                ContactEmail = "<EMAIL>",
                ContactPhone = "+224 123 456 789",
                Address = "Conakry, Guinée",
                Website = "https://nafaplace.com",
                IsActive = true,
                Type = CarrierType.Internal,
                Rating = 4.5m,
                ReviewCount = 150,
                TotalDeliveries = 500,
                SuccessfulDeliveries = 475,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },

            new Carrier
            {
                Id = 2,
                Name = "Guinea Post",
                Code = "GUINEA_POST",
                Description = "Service postal national de Guinée",
                LogoUrl = "/images/carriers/guinea-post.png",
                ContactEmail = "<EMAIL>",
                ContactPhone = "+224 987 654 321",
                Address = "Conakry, Guinée",
                IsActive = true,
                Type = CarrierType.Postal,
                Rating = 3.8m,
                ReviewCount = 89,
                TotalDeliveries = 200,
                SuccessfulDeliveries = 180,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },

            new Carrier
            {
                Id = 3,
                Name = "Express Guinée",
                Code = "EXPRESS_GUINEA",
                Description = "Service de livraison express en Guinée",
                LogoUrl = "/images/carriers/express-guinea.png",
                ContactEmail = "<EMAIL>",
                ContactPhone = "+224 555 123 456",
                Address = "Conakry, Guinée",
                IsActive = true,
                Type = CarrierType.Express,
                Rating = 4.2m,
                ReviewCount = 67,
                TotalDeliveries = 150,
                SuccessfulDeliveries = 140,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        };
    }

    public static List<CarrierZone> GetDefaultCarrierZones()
    {
        return new List<CarrierZone>
        {
            // NafaPlace Express - Toutes les zones de Conakry
            new CarrierZone { Id = 1, CarrierId = 1, ZoneId = 1, DeliveryFee = 15000, FreeDeliveryThreshold = 100000, EstimatedDeliveryDays = 1, Priority = 1, IsActive = true, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
            new CarrierZone { Id = 2, CarrierId = 1, ZoneId = 2, DeliveryFee = 12000, FreeDeliveryThreshold = 80000, EstimatedDeliveryDays = 1, Priority = 1, IsActive = true, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
            new CarrierZone { Id = 3, CarrierId = 1, ZoneId = 3, DeliveryFee = 18000, FreeDeliveryThreshold = 120000, EstimatedDeliveryDays = 1, Priority = 1, IsActive = true, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
            new CarrierZone { Id = 4, CarrierId = 1, ZoneId = 4, DeliveryFee = 20000, FreeDeliveryThreshold = 150000, EstimatedDeliveryDays = 2, Priority = 1, IsActive = true, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
            new CarrierZone { Id = 5, CarrierId = 1, ZoneId = 5, DeliveryFee = 25000, FreeDeliveryThreshold = 200000, EstimatedDeliveryDays = 2, Priority = 1, IsActive = true, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },

            // Guinea Post - Toutes les zones
            new CarrierZone { Id = 6, CarrierId = 2, ZoneId = 6, DeliveryFee = 50000, FreeDeliveryThreshold = 300000, EstimatedDeliveryDays = 3, Priority = 2, IsActive = true, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
            new CarrierZone { Id = 7, CarrierId = 2, ZoneId = 7, DeliveryFee = 45000, FreeDeliveryThreshold = 250000, EstimatedDeliveryDays = 3, Priority = 2, IsActive = true, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
            new CarrierZone { Id = 8, CarrierId = 2, ZoneId = 8, DeliveryFee = 55000, FreeDeliveryThreshold = 350000, EstimatedDeliveryDays = 4, Priority = 2, IsActive = true, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
            new CarrierZone { Id = 9, CarrierId = 2, ZoneId = 9, DeliveryFee = 40000, FreeDeliveryThreshold = 200000, EstimatedDeliveryDays = 2, Priority = 2, IsActive = true, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
            new CarrierZone { Id = 10, CarrierId = 2, ZoneId = 10, DeliveryFee = 60000, FreeDeliveryThreshold = 400000, EstimatedDeliveryDays = 5, Priority = 2, IsActive = true, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },

            // Express Guinée - Zones principales seulement
            new CarrierZone { Id = 11, CarrierId = 3, ZoneId = 1, DeliveryFee = 20000, FreeDeliveryThreshold = 150000, EstimatedDeliveryDays = 1, Priority = 3, IsActive = true, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
            new CarrierZone { Id = 12, CarrierId = 3, ZoneId = 6, DeliveryFee = 60000, FreeDeliveryThreshold = 400000, EstimatedDeliveryDays = 2, Priority = 3, IsActive = true, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
            new CarrierZone { Id = 13, CarrierId = 3, ZoneId = 7, DeliveryFee = 55000, FreeDeliveryThreshold = 350000, EstimatedDeliveryDays = 2, Priority = 3, IsActive = true, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow }
        };
    }
}
