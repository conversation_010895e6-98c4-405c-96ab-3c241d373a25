// modal-handler.js
// Gestion des modals Bootstrap pour le portail vendeur

window.modalHandler = {
    initModal: function (modalId) {
        const modalElement = document.getElementById(modalId);
        if (modalElement) {
            const modal = new bootstrap.Modal(modalElement);
            modalElement.addEventListener('hidden.bs.modal', () => {
                // Supprimer le backdrop manuellement si nécessaire
                const backdrop = document.querySelector('.modal-backdrop');
                if (backdrop) {
                    backdrop.remove();
                }
            });
            return modal;
        }
        return null;
    },

    showModal: function (modalId) {
        const modalElement = document.getElementById(modalId);
        if (modalElement) {
            const modal = bootstrap.Modal.getInstance(modalElement) || this.initModal(modalId);
            if (modal) {
                modal.show();
            }
        }
    },

    hideModal: function (modalId) {
        const modalElement = document.getElementById(modalId);
        if (modalElement) {
            const modal = bootstrap.Modal.getInstance(modalElement);
            if (modal) {
                modal.hide();
            }
        }
    },

    disposeModal: function(modalId) {
        const modalElement = document.getElementById(modalId);
        if (modalElement) {
            const modal = bootstrap.Modal.getInstance(modalElement);
            if (modal) {
                modal.dispose();
            }
        }
    }
};
