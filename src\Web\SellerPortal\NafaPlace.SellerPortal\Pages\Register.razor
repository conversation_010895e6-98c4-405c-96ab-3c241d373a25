@page "/register"
@using NafaPlace.SellerPortal.Models.Auth
@using NafaPlace.SellerPortal.Services
@inject IAuthService AuthService
@inject NavigationManager NavigationManager

<PageTitle>NafaPlace - Inscription Vendeur</PageTitle>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow-lg mt-5 mb-5">
                <div class="card-header bg-primary text-white text-center py-3">
                    <h4 class="mb-0">Espace Vendeur</h4>
                </div>
                <div class="card-body p-4">
                    <h5 class="card-title text-center mb-4">Inscription</h5>
                    
                    @if (!string.IsNullOrEmpty(errorMessage))
                    {
                        <div class="alert alert-danger" role="alert">
                            @errorMessage
                        </div>
                    }
                    
                    <EditForm Model="@registerRequest" OnValidSubmit="HandleRegister">
                        <DataAnnotationsValidator />
                        <ValidationSummary />
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="firstName" class="form-label">Prénom</label>
                                <InputText id="firstName" @bind-Value="registerRequest.FirstName" class="form-control" placeholder="Entrez votre prénom" />
                                <ValidationMessage For="@(() => registerRequest.FirstName)" />
                            </div>
                            <div class="col-md-6">
                                <label for="lastName" class="form-label">Nom</label>
                                <InputText id="lastName" @bind-Value="registerRequest.LastName" class="form-control" placeholder="Entrez votre nom" />
                                <ValidationMessage For="@(() => registerRequest.LastName)" />
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="username" class="form-label">Nom d'utilisateur</label>
                            <InputText id="username" @bind-Value="registerRequest.Username" class="form-control" placeholder="Choisissez un nom d'utilisateur" />
                            <ValidationMessage For="@(() => registerRequest.Username)" />
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">Email</label>
                            <InputText id="email" @bind-Value="registerRequest.Email" class="form-control" placeholder="Entrez votre email" />
                            <ValidationMessage For="@(() => registerRequest.Email)" />
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">Mot de passe</label>
                            <InputText id="password" type="password" @bind-Value="registerRequest.Password" class="form-control" placeholder="Choisissez un mot de passe" />
                            <ValidationMessage For="@(() => registerRequest.Password)" />
                        </div>
                        
                        <div class="mb-3">
                            <label for="confirmPassword" class="form-label">Confirmer le mot de passe</label>
                            <InputText id="confirmPassword" type="password" @bind-Value="registerRequest.ConfirmPassword" class="form-control" placeholder="Confirmez votre mot de passe" />
                            <ValidationMessage For="@(() => registerRequest.ConfirmPassword)" />
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary" disabled="@isLoading">
                                @if (isLoading)
                                {
                                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                    <span class="ms-1">Inscription en cours...</span>
                                }
                                else
                                {
                                    <span>S'inscrire</span>
                                }
                            </button>
                        </div>
                    </EditForm>
                    
                    <div class="mt-3 text-center">
                        <p>Vous avez déjà un compte ? <a href="/login" class="text-primary">Connectez-vous</a></p>
                    </div>
                </div>
                <div class="card-footer text-center py-3">
                    <div class="small">
                        <a href="/">Retour au site principal</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private RegisterRequest registerRequest = new RegisterRequest();
    private bool isLoading = false;
    private string errorMessage = string.Empty;
    
    protected override async Task OnInitializedAsync()
    {
        // Rediriger vers le tableau de bord si l'utilisateur est déjà connecté
        var currentUser = await AuthService.GetCurrentUserAsync();
        if (currentUser.IsAuthenticated && currentUser.Roles.Contains("Seller"))
        {
            NavigationManager.NavigateTo("/dashboard");
        }
    }
    
    private async Task HandleRegister()
    {
        try
        {
            isLoading = true;
            errorMessage = string.Empty;
            
            var result = await AuthService.RegisterAsync(registerRequest);
            
            if (result.Success)
            {
                // Rediriger vers le tableau de bord du vendeur
                NavigationManager.NavigateTo("/dashboard", true);
            }
            else
            {
                errorMessage = result.Message;
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Erreur lors de l'inscription: {ex.Message}";
        }
        finally
        {
            isLoading = false;
        }
    }
}
