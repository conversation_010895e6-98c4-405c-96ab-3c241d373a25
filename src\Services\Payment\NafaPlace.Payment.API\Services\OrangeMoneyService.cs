using System.Text;
using System.Text.Json;
using System.Security.Cryptography;
using Microsoft.Extensions.Options;
using NafaPlace.Payment.API.Models;

namespace NafaPlace.Payment.API.Services
{
    public interface IOrangeMoneyService
    {
        Task<OrangeMoneyPaymentResponse> InitiatePaymentAsync(OrangeMoneyPaymentRequest request);
        Task<OrangeMoneyStatusResponse> GetPaymentStatusAsync(string transactionId);
        Task<bool> VerifyCallbackAsync(string signature, string payload);
    }

    public class OrangeMoneyService : IOrangeMoneyService
    {
        private readonly HttpClient _httpClient;
        private readonly OrangeMoneySettings _settings;
        private readonly ILogger<OrangeMoneyService> _logger;

        public OrangeMoneyService(
            HttpClient httpClient, 
            IOptions<OrangeMoneySettings> settings,
            ILogger<OrangeMoneyService> logger)
        {
            _httpClient = httpClient;
            _settings = settings.Value;
            _logger = logger;
            
            _httpClient.BaseAddress = new Uri(_settings.ApiUrl);
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_settings.ApiKey}");
        }

        public async Task<OrangeMoneyPaymentResponse> InitiatePaymentAsync(OrangeMoneyPaymentRequest request)
        {
            try
            {
                // Validation des données
                if (string.IsNullOrEmpty(request.PhoneNumber) || request.Amount <= 0)
                {
                    throw new ArgumentException("Numéro de téléphone et montant requis");
                }

                // Vérification des limites
                if (request.Amount < _settings.MinimumAmount || request.Amount > _settings.MaximumAmount)
                {
                    throw new ArgumentException($"Montant doit être entre {_settings.MinimumAmount} et {_settings.MaximumAmount} {request.Currency}");
                }

                // Formatage du numéro de téléphone pour la Guinée
                var formattedPhone = FormatGuineanPhoneNumber(request.PhoneNumber);

                // Préparation de la requête API Orange Money
                var paymentData = new
                {
                    merchant_key = _settings.MerchantId,
                    currency = request.Currency,
                    order_id = request.OrderId,
                    amount = (int)(request.Amount * 100), // Orange Money utilise les centimes
                    return_url = _settings.ReturnUrl,
                    cancel_url = _settings.CancelUrl,
                    notif_url = _settings.CallbackUrl,
                    lang = "fr",
                    reference = $"NAFA-{request.OrderId}",
                    customer_msisdn = formattedPhone,
                    customer_email = "",
                    customer_firstname = "",
                    customer_lastname = ""
                };

                var json = JsonSerializer.Serialize(paymentData);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                _logger.LogInformation("Initiating Orange Money payment for order {OrderId}, amount {Amount} {Currency}", 
                    request.OrderId, request.Amount, request.Currency);

                // Pour le développement, simuler une réponse Orange Money
                if (_settings.ApiUrl.Contains("localhost") || _settings.MerchantId == "your_merchant_id_here")
                {
                    return SimulateOrangeMoneyResponse(request);
                }

                var response = await _httpClient.PostAsync("/webpayment", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonSerializer.Deserialize<dynamic>(responseContent);
                    return new OrangeMoneyPaymentResponse
                    {
                        TransactionId = Guid.NewGuid().ToString(),
                        Status = "PENDING",
                        Message = "Paiement initié avec succès",
                        PaymentUrl = $"https://webpayment.orange-money.com/pay/{Guid.NewGuid()}",
                        CreatedAt = DateTime.UtcNow
                    };
                }
                else
                {
                    _logger.LogError("Orange Money API error: {StatusCode} - {Content}", response.StatusCode, responseContent);
                    throw new Exception($"Erreur Orange Money: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initiating Orange Money payment");
                throw;
            }
        }

        public async Task<OrangeMoneyStatusResponse> GetPaymentStatusAsync(string transactionId)
        {
            try
            {
                _logger.LogInformation("Checking Orange Money payment status for transaction {TransactionId}", transactionId);

                // Pour le développement, simuler le statut
                if (_settings.ApiUrl.Contains("localhost") || _settings.MerchantId == "your_merchant_id_here")
                {
                    return new OrangeMoneyStatusResponse
                    {
                        TransactionId = transactionId,
                        Status = "SUCCESS",
                        Amount = 10000,
                        Currency = "GNF",
                        PhoneNumber = "+224621234567",
                        CreatedAt = DateTime.UtcNow.AddMinutes(-5),
                        CompletedAt = DateTime.UtcNow
                    };
                }

                var response = await _httpClient.GetAsync($"/transaction/{transactionId}");
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    // Parser la réponse Orange Money
                    return JsonSerializer.Deserialize<OrangeMoneyStatusResponse>(responseContent) ?? 
                           new OrangeMoneyStatusResponse { TransactionId = transactionId, Status = "UNKNOWN" };
                }
                else
                {
                    _logger.LogError("Orange Money status check error: {StatusCode} - {Content}", response.StatusCode, responseContent);
                    return new OrangeMoneyStatusResponse { TransactionId = transactionId, Status = "ERROR" };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking Orange Money payment status");
                return new OrangeMoneyStatusResponse { TransactionId = transactionId, Status = "ERROR" };
            }
        }

        public async Task<bool> VerifyCallbackAsync(string signature, string payload)
        {
            try
            {
                // Vérification de la signature du callback Orange Money
                var computedSignature = ComputeSignature(payload, _settings.SecretKey);
                return signature.Equals(computedSignature, StringComparison.OrdinalIgnoreCase);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error verifying Orange Money callback signature");
                return false;
            }
        }

        private string FormatGuineanPhoneNumber(string phoneNumber)
        {
            // Nettoyer le numéro
            var cleaned = phoneNumber.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "");
            
            // Ajouter le préfixe +224 si nécessaire
            if (cleaned.StartsWith("6") && cleaned.Length == 9)
            {
                return $"+224{cleaned}";
            }
            else if (cleaned.StartsWith("224") && cleaned.Length == 12)
            {
                return $"+{cleaned}";
            }
            else if (cleaned.StartsWith("+224"))
            {
                return cleaned;
            }
            
            return cleaned; // Retourner tel quel si format non reconnu
        }

        private OrangeMoneyPaymentResponse SimulateOrangeMoneyResponse(OrangeMoneyPaymentRequest request)
        {
            // Simulation pour le développement
            return new OrangeMoneyPaymentResponse
            {
                TransactionId = $"OM-{Random.Shared.Next(10000000, 99999999)}",
                Status = "PENDING",
                Message = "Paiement Orange Money initié (MODE DÉVELOPPEMENT)",
                PaymentUrl = $"http://localhost:8080/payment/orange-money-simulator?amount={request.Amount}&phone={request.PhoneNumber}&order={request.OrderId}",
                CreatedAt = DateTime.UtcNow
            };
        }

        private string ComputeSignature(string payload, string secretKey)
        {
            using var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(secretKey));
            var hash = hmac.ComputeHash(Encoding.UTF8.GetBytes(payload));
            return Convert.ToBase64String(hash);
        }
    }
}
