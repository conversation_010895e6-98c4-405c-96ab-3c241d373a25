using System.Collections.Generic;

namespace NafaPlace.Catalog.Application.DTOs.Product
{
    public class UpdateProductVariantRequest
    {
        public int Id { get; set; }
        public string? Name { get; set; }
        public string? Sku { get; set; }
        public decimal? Price { get; set; }
        public int? StockQuantity { get; set; }
        public string? Color { get; set; }
        public string? Size { get; set; }
        public List<UpdateProductVariantAttributeRequest>? Attributes { get; set; }
    }
}
