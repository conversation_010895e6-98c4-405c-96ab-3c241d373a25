using NafaPlace.Inventory.Application.Services;

namespace NafaPlace.Inventory.API.Services;

// Service de tâches en arrière-plan pour exécuter les tâches de maintenance
public class InventoryMaintenanceBackgroundService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<InventoryMaintenanceBackgroundService> _logger;
    private readonly TimeSpan _period = TimeSpan.FromHours(1); // Exécuter toutes les heures

    public InventoryMaintenanceBackgroundService(
        IServiceProvider serviceProvider,
        ILogger<InventoryMaintenanceBackgroundService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        // Attendre un peu avant de commencer pour laisser l'application démarrer complètement
        await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var maintenanceService = scope.ServiceProvider.GetRequiredService<IInventoryMaintenanceService>();

                _logger.LogInformation("Starting inventory maintenance tasks");

                // Nettoyer les réservations expirées (toutes les heures)
                await maintenanceService.CleanupExpiredReservationsAsync();

                // Recalculer les niveaux de stock (toutes les heures)
                await maintenanceService.RecalculateStockLevelsAsync();

                // Traiter les alertes en attente (toutes les heures)
                await maintenanceService.ProcessPendingAlertsAsync();

                // Archiver les anciens mouvements (une fois par jour à minuit)
                if (DateTime.Now.Hour == 0)
                {
                    await maintenanceService.ArchiveOldMovementsAsync();
                }

                _logger.LogInformation("Inventory maintenance tasks completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during inventory maintenance tasks");
            }

            await Task.Delay(_period, stoppingToken);
        }
    }
}
