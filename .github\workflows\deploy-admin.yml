name: Deploy Admin Portal to Fly.io Test

on:
  push:
    branches: [ main ]
    paths:
      - 'src/Web/AdminPortal/**'
      - 'Dockerfile.admin'
      - 'fly-admin.toml'
      - '.github/workflows/deploy-admin.yml'
  workflow_dispatch:

jobs:
  deploy:
    name: Deploy Admin Portal
    runs-on: ubuntu-latest
    environment: staging

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '9.0.x'

    - name: Restore dependencies
      run: dotnet restore

    - name: Build
      run: dotnet build --no-restore --configuration Release

    - name: Test
      run: dotnet test --no-build --verbosity normal --configuration Release

    - name: Setup Fly CLI
      uses: superfly/flyctl-actions/setup-flyctl@master

    - name: Create Fly.io app if it doesn't exist
      run: |
        if ! flyctl apps list | grep -q "nafaplace-admin-test"; then
          echo "Creating nafaplace-admin-test app..."
          flyctl apps create nafaplace-admin-test --org personal
        else
          echo "App nafaplace-admin-test already exists"
        fi
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

    - name: Deploy to Fly.io
      run: flyctl deploy --config fly-admin.toml --dockerfile Dockerfile.admin
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}
