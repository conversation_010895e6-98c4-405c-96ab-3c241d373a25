using System;
using System.Collections.Generic;

namespace NafaPlace.Web.Models.Order
{
    public class OrderDto
    {
        public int Id { get; set; }
        public string UserId { get; set; } = string.Empty;
        public DateTime OrderDate { get; set; }
        public decimal TotalAmount { get; set; }
        public string Currency { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string PaymentMethod { get; set; } = string.Empty;
        public string PaymentStatus { get; set; } = string.Empty;
        public string? PaymentTransactionId { get; set; }
        public DateTime? PaymentDate { get; set; }
        public ShippingAddressDto? ShippingAddress { get; set; }
        public List<OrderItemDto> OrderItems { get; set; } = new();

        // Propriétés supplémentaires pour compatibilité
        public DateTime CreatedAt => OrderDate;
        public string? TransactionId => PaymentTransactionId;
        public List<OrderItemDto> Items => OrderItems;
    }
}