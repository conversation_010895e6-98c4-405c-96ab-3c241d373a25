using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace NafaPlace.Recommendation.Infrastructure.BackgroundServices;

public class RecommendationProcessingService : BackgroundService
{
    private readonly ILogger<RecommendationProcessingService> _logger;
    private readonly IServiceProvider _serviceProvider;

    public RecommendationProcessingService(
        ILogger<RecommendationProcessingService> logger,
        IServiceProvider serviceProvider)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Service de traitement des recommandations démarré");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await ProcessRecommendations(stoppingToken);
                
                // Attendre 5 minutes avant le prochain traitement
                await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken);
            }
            catch (OperationCanceledException)
            {
                // Service arrêté normalement
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors du traitement des recommandations");
                
                // Attendre 1 minute avant de réessayer en cas d'erreur
                await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
            }
        }

        _logger.LogInformation("Service de traitement des recommandations arrêté");
    }

    private async Task ProcessRecommendations(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        
        try
        {
            _logger.LogDebug("Début du traitement des recommandations");

            // Simuler le traitement des recommandations
            await Task.Delay(1000, cancellationToken);

            // Ici, on pourrait :
            // 1. Traiter les nouvelles interactions utilisateur
            // 2. Mettre à jour les scores de similarité
            // 3. Recalculer les recommandations personnalisées
            // 4. Nettoyer les anciennes données
            // 5. Optimiser les modèles ML

            _logger.LogDebug("Traitement des recommandations terminé");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du traitement des recommandations");
            throw;
        }
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Arrêt du service de traitement des recommandations...");
        await base.StopAsync(cancellationToken);
    }
}
