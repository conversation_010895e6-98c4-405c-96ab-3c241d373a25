# Guide de Déploiement du Système de Chat NafaPlace

## 🚀 Vue d'ensemble

Le système de chat NafaPlace est maintenant complet avec :
- ✅ **Service de chat réel** - API backend avec base de données PostgreSQL
- ✅ **SignalR temps réel** - Communication bidirectionnelle instantanée
- ✅ **Authentification JWT** - Support utilisateurs connectés et anonymes
- ✅ **Gestion des agents** - Interface d'administration et assignation automatique
- ✅ **Historique des conversations** - Persistance et récupération des discussions
- ✅ **Interface utilisateur** - Widget flottant responsive et intuitif

## 📋 Prérequis

### Logiciels requis
- Docker & Docker Compose
- .NET 8 SDK
- PostgreSQL (via Docker)
- Redis (via Docker)

### Ports utilisés
- **5007** : API Chat
- **8080** : Application Web principale
- **8081** : Portail Admin
- **8082** : Portail Vendeur
- **5432** : PostgreSQL
- **6379** : Redis

## 🔧 Configuration

### 1. Variables d'environnement

Créez un fichier `.env` à la racine du projet :

```env
# Base de données
POSTGRES_DB=nafaplace_chat
POSTGRES_USER=nafaplace
POSTGRES_PASSWORD=nafaplace123

# JWT
JWT_SECRET_KEY=your-super-secret-key-here-make-it-long-and-complex
JWT_ISSUER=NafaPlace
JWT_AUDIENCE=NafaPlace

# Redis
REDIS_URL=redis://redis:6379

# Chat API
CHAT_API_URL=http://localhost:5007
```

### 2. Configuration de la base de données

Le service de chat utilise une base de données PostgreSQL séparée avec les tables :
- `Conversations` - Conversations entre utilisateurs et agents
- `Messages` - Messages individuels
- `Agents` - Agents de support
- `Departments` - Départements de support
- `FAQs` - Questions fréquemment posées
- `ChatbotInteractions` - Historique des interactions avec le chatbot

## 🚀 Déploiement

### 1. Démarrage des services

```bash
# Démarrer tous les services
docker-compose -f docker-compose.all-services.yml up -d

# Vérifier que tous les services sont actifs
docker-compose -f docker-compose.all-services.yml ps
```

### 2. Initialisation de la base de données

La base de données du chat est automatiquement initialisée avec :
- Départements par défaut (Support Général, Technique, Commercial, Livraison)
- Agents de test
- FAQs de base

### 3. Vérification du déploiement

Exécutez le script de test :

```powershell
# Windows PowerShell
.\test-chat-integration.ps1

# Ou manuellement
curl http://localhost:5007/api/config
curl http://localhost:5007/api/faq
curl http://localhost:8080
```

## 🧪 Tests et Validation

### 1. Tests fonctionnels

#### Chat Widget
- [ ] Le widget apparaît en bas à droite de toutes les pages
- [ ] Animation d'entrée fluide
- [ ] Ouverture/fermeture du chat
- [ ] Messages de bienvenue automatiques

#### Utilisateurs Anonymes
- [ ] Peut envoyer des messages
- [ ] Reçoit des réponses du chatbot
- [ ] Suggestions rapides fonctionnelles
- [ ] Invitation à se connecter affichée

#### Utilisateurs Connectés
- [ ] Création automatique de conversations
- [ ] Historique des conversations accessible
- [ ] Demande d'agent humain disponible
- [ ] Persistance des messages

#### Agents (Admin)
- [ ] Interface de gestion des agents accessible
- [ ] Statuts des agents mis à jour en temps réel
- [ ] Assignation de conversations
- [ ] Statistiques et métriques

### 2. Tests SignalR

#### Temps réel
- [ ] Messages instantanés entre utilisateur et agent
- [ ] Indicateurs de frappe
- [ ] Notifications d'assignation d'agent
- [ ] Statuts de présence

#### Connexion/Déconnexion
- [ ] Reconnexion automatique
- [ ] Gestion des erreurs de connexion
- [ ] Fallback vers API REST si SignalR indisponible

### 3. Tests de performance

```bash
# Test de charge basique
for i in {1..10}; do
  curl -s http://localhost:5007/api/config > /dev/null &
done
wait

# Vérifier les logs
docker-compose logs chat-api
```

## 🔍 Surveillance et Logs

### Logs des services

```bash
# Logs du service de chat
docker-compose logs -f chat-api

# Logs de l'application web
docker-compose logs -f web-app

# Logs de tous les services
docker-compose logs -f
```

### Métriques importantes

- **Temps de réponse API** : < 200ms
- **Connexions SignalR actives** : Surveillées via logs
- **Conversations actives** : Visible dans l'interface admin
- **Satisfaction client** : Collectée via évaluations

## 🛠️ Dépannage

### Problèmes courants

#### 1. Service de chat inaccessible
```bash
# Vérifier le statut
docker-compose ps chat-api

# Redémarrer le service
docker-compose restart chat-api

# Vérifier les logs
docker-compose logs chat-api
```

#### 2. SignalR ne fonctionne pas
- Vérifier que le port 5007 est accessible
- Contrôler les CORS dans la configuration
- Vérifier les tokens JWT

#### 3. Base de données non initialisée
```bash
# Recréer la base de données
docker-compose down
docker volume rm nafaplace_postgres_data
docker-compose up -d
```

#### 4. Messages non persistés
- Vérifier la connexion à PostgreSQL
- Contrôler les logs d'erreur de l'API
- Vérifier l'authentification JWT

### Commandes utiles

```bash
# Redémarrer tous les services
docker-compose down && docker-compose up -d

# Nettoyer et reconstruire
docker-compose down --volumes
docker-compose build --no-cache
docker-compose up -d

# Accéder à la base de données
docker exec -it nafaplace-postgres psql -U nafaplace -d nafaplace_chat
```

## 📊 Monitoring

### Endpoints de santé

- **Chat API** : `GET /api/config/health`
- **Application Web** : `GET /health`
- **Base de données** : Connexion via API

### Métriques clés

1. **Disponibilité des services** : 99.9%
2. **Temps de réponse moyen** : < 200ms
3. **Conversations actives** : Temps réel
4. **Satisfaction client** : > 4.0/5.0

## 🔐 Sécurité

### Mesures implémentées

- **Authentification JWT** pour les utilisateurs connectés
- **CORS configuré** pour les domaines autorisés
- **Validation des entrées** côté serveur
- **Rate limiting** sur les endpoints publics
- **Chiffrement HTTPS** en production

### Recommandations

1. Changer les clés JWT par défaut
2. Configurer HTTPS en production
3. Limiter l'accès à la base de données
4. Surveiller les tentatives d'intrusion
5. Sauvegarder régulièrement les conversations

## 📈 Optimisations

### Performance

1. **Cache Redis** pour les FAQs fréquentes
2. **Pagination** des conversations et messages
3. **Compression** des réponses API
4. **CDN** pour les assets statiques

### Évolutivité

1. **Load balancing** pour l'API de chat
2. **Clustering Redis** pour la haute disponibilité
3. **Réplication PostgreSQL** pour les sauvegardes
4. **Monitoring avancé** avec Prometheus/Grafana

## 🎯 Prochaines étapes

1. **Intégration IA** - Améliorer le chatbot avec GPT/Claude
2. **Analytics avancés** - Tableaux de bord détaillés
3. **Notifications push** - Alertes en temps réel
4. **Support multilingue** - Interface en plusieurs langues
5. **API mobile** - Support pour applications mobiles

---

## 📞 Support

Pour toute question ou problème :
1. Consulter les logs des services
2. Vérifier la documentation API
3. Tester avec le script d'intégration
4. Contacter l'équipe de développement

**Le système de chat NafaPlace est maintenant prêt pour la production !** 🎉
