using NafaPlace.Identity.Domain.Models;

namespace NafaPlace.Identity.Domain.Services;

public interface IAuthenticationService
{
    Task<AuthenticationResult> RegisterAsync(string email, string username, string password);
    Task<AuthenticationResult> LoginAsync(string emailOrUsername, string password);
    Task<AuthenticationResult> RefreshTokenAsync(string accessToken, string refreshToken);
    Task<bool> RevokeTokenAsync(string refreshToken);
}
