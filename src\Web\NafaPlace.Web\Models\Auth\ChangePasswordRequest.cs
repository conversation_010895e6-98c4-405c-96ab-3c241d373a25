using System.ComponentModel.DataAnnotations;

namespace NafaPlace.Web.Models.Auth;

public class ChangePasswordRequest
{
    [Required(ErrorMessage = "Le mot de passe actuel est obligatoire")]
    [DataType(DataType.Password)]
    public string CurrentPassword { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Le nouveau mot de passe est obligatoire")]
    [StringLength(100, ErrorMessage = "Le {0} doit contenir au moins {2} caractères et au maximum {1} caractères.", MinimumLength = 6)]
    [DataType(DataType.Password)]
    public string NewPassword { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "La confirmation du mot de passe est obligatoire")]
    [Compare("NewPassword", ErrorMessage = "Le nouveau mot de passe et sa confirmation ne correspondent pas.")]
    [DataType(DataType.Password)]
    public string ConfirmPassword { get; set; } = string.Empty;
}
