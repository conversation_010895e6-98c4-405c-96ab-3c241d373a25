# Utiliser l'image de base .NET 9.0
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

# Utiliser l'image SDK pour la construction
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copier les fichiers de projet
COPY ["src/Services/Recommendation/NafaPlace.Recommendation.API/NafaPlace.Recommendation.API.csproj", "src/Services/Recommendation/NafaPlace.Recommendation.API/"]
COPY ["src/Services/Recommendation/NafaPlace.Recommendation.Application/NafaPlace.Recommendation.Application.csproj", "src/Services/Recommendation/NafaPlace.Recommendation.Application/"]
COPY ["src/Services/Recommendation/NafaPlace.Recommendation.Domain/NafaPlace.Recommendation.Domain.csproj", "src/Services/Recommendation/NafaPlace.Recommendation.Domain/"]
COPY ["src/Services/Recommendation/NafaPlace.Recommendation.Infrastructure/NafaPlace.Recommendation.Infrastructure.csproj", "src/Services/Recommendation/NafaPlace.Recommendation.Infrastructure/"]

# Restaurer les dépendances
RUN dotnet restore "src/Services/Recommendation/NafaPlace.Recommendation.API/NafaPlace.Recommendation.API.csproj"

# Copier tout le code source
COPY . .

# Construire l'application
WORKDIR "/src/src/Services/Recommendation/NafaPlace.Recommendation.API"
RUN dotnet build "NafaPlace.Recommendation.API.csproj" -c Release -o /app/build

# Publier l'application
FROM build AS publish
RUN dotnet publish "NafaPlace.Recommendation.API.csproj" -c Release -o /app/publish

# Image finale
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

# Point d'entrée
ENTRYPOINT ["dotnet", "NafaPlace.Recommendation.API.dll"]
