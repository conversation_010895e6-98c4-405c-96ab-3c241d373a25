using System.Security.Claims;
using Blazored.LocalStorage;
using Microsoft.AspNetCore.Components.Authorization;
using System.Net.Http.Headers;
using NafaPlace.AdminPortal.Models.Auth;
using System.Text.Json;

namespace NafaPlace.AdminPortal.Services;

public class CustomAuthStateProvider : AuthenticationStateProvider
{
    private readonly ILocalStorageService _localStorage;
    private readonly HttpClient _httpClient;

    public CustomAuthStateProvider(ILocalStorageService localStorage, HttpClient httpClient)
    {
        _localStorage = localStorage;
        _httpClient = httpClient;
    }

    public override async Task<AuthenticationState> GetAuthenticationStateAsync()
    {
        var token = await _localStorage.GetItemAsStringAsync("authToken");

        if (string.IsNullOrEmpty(token))
        {
            return new AuthenticationState(new ClaimsPrincipal(new ClaimsIdentity()));
        }

        try
        {
            // Supprimer les guillemets éventuels autour du token
            token = token.Trim('"');
            
            // Créer un ClaimsPrincipal à partir du token JWT sans appeler l'API
            var claims = ParseClaimsFromJwt(token);
            var identity = new ClaimsIdentity(claims, "jwt");
            var user = new ClaimsPrincipal(identity);
            
            // Vérifier si le token est expiré
            var expiry = claims.FirstOrDefault(c => c.Type == "exp")?.Value;
            if (expiry != null && long.TryParse(expiry, out var expiryTimestamp))
            {
                var expiryDate = DateTimeOffset.FromUnixTimeSeconds(expiryTimestamp).DateTime;
                if (expiryDate < DateTime.UtcNow)
                {
                    await _localStorage.RemoveItemAsync("authToken");
                    return new AuthenticationState(new ClaimsPrincipal(new ClaimsIdentity()));
                }
            }
            
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
            
            return new AuthenticationState(user);
        }
        catch (Exception)
        {
            await _localStorage.RemoveItemAsync("authToken");
            return new AuthenticationState(new ClaimsPrincipal(new ClaimsIdentity()));
        }
    }
    
    // Méthode pour parser les claims d'un token JWT sans appeler l'API
    private IEnumerable<Claim> ParseClaimsFromJwt(string jwt)
    {
        var claims = new List<Claim>();
        var payload = jwt.Split('.')[1];
        var jsonBytes = ParseBase64WithoutPadding(payload);
        var keyValuePairs = JsonSerializer.Deserialize<Dictionary<string, object>>(jsonBytes);

        if (keyValuePairs != null)
        {
            keyValuePairs.TryGetValue("sub", out var sub);
            if (sub != null)
            {
                // Ne pas convertir en int, laisser comme string
                claims.Add(new Claim(ClaimTypes.NameIdentifier, sub.ToString() ?? string.Empty));
            }

            keyValuePairs.TryGetValue("unique_name", out var name);
            if (name != null)
            {
                claims.Add(new Claim(ClaimTypes.Name, name.ToString() ?? string.Empty));
            }
            else
            {
                keyValuePairs.TryGetValue("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name", out var altName);
                if (altName != null)
                {
                    claims.Add(new Claim(ClaimTypes.Name, altName.ToString() ?? string.Empty));
                }
            }

            keyValuePairs.TryGetValue("role", out var role);
            if (role != null)
            {
                if (role.GetType().IsArray)
                {
                    var roles = (JsonElement)role;
                    foreach (var r in roles.EnumerateArray())
                    {
                        claims.Add(new Claim(ClaimTypes.Role, r.ToString() ?? string.Empty));
                    }
                }
                else
                {
                    claims.Add(new Claim(ClaimTypes.Role, role.ToString() ?? string.Empty));
                }
            }
            else
            {
                keyValuePairs.TryGetValue("http://schemas.microsoft.com/ws/2008/06/identity/claims/role", out var altRole);
                if (altRole != null)
                {
                    if (altRole.GetType().IsArray)
                    {
                        var roles = (JsonElement)altRole;
                        foreach (var r in roles.EnumerateArray())
                        {
                            claims.Add(new Claim(ClaimTypes.Role, r.ToString() ?? string.Empty));
                        }
                    }
                    else
                    {
                        claims.Add(new Claim(ClaimTypes.Role, altRole.ToString() ?? string.Empty));
                    }
                }
            }

            keyValuePairs.TryGetValue("email", out var email);
            if (email != null)
            {
                claims.Add(new Claim(ClaimTypes.Email, email.ToString() ?? string.Empty));
            }
        }

        return claims;
    }

    private byte[] ParseBase64WithoutPadding(string base64)
    {
        switch (base64.Length % 4)
        {
            case 2: base64 += "=="; break;
            case 3: base64 += "="; break;
        }
        return Convert.FromBase64String(base64);
    }
    
    public async void MarkUserAsAuthenticated(string token)
    {
        await _localStorage.SetItemAsync("authToken", token);
        _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
        
        // Forcer le rafraîchissement de l'état d'authentification
        NotifyAuthenticationStateChanged(GetAuthenticationStateAsync());
    }
    
    public async void MarkUserAsLoggedOut()
    {
        await _localStorage.RemoveItemAsync("authToken");
        _httpClient.DefaultRequestHeaders.Authorization = null;
        
        // Forcer le rafraîchissement de l'état d'authentification
        NotifyAuthenticationStateChanged(GetAuthenticationStateAsync());
    }
    
    public async Task NotifyUserAuthentication(string token)
    {
        await _localStorage.SetItemAsync("authToken", token);
        _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
        
        // Forcer le rafraîchissement de l'état d'authentification
        NotifyAuthenticationStateChanged(GetAuthenticationStateAsync());
    }
    
    // Méthode pour notifier le changement d'état d'authentification sans paramètres
    public void NotifyAuthenticationStateChanged()
    {
        NotifyAuthenticationStateChanged(GetAuthenticationStateAsync());
    }
}
