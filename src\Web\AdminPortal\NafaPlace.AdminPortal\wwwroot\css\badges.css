/* Styles pour les badges de statut dans l'admin portal */

/* Base badge styles */
.badge {
    display: inline-block !important;
    padding: 0.4em 0.8em !important;
    font-size: 0.8em !important;
    font-weight: 600 !important;
    line-height: 1 !important;
    text-align: center !important;
    white-space: nowrap !important;
    vertical-align: baseline !important;
    border-radius: 0.375rem !important;
    border: 1px solid transparent !important;
    min-width: 70px !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

/* Statuts de commande */
.badge.bg-warning {
    background-color: #ffc107 !important;
    color: #212529 !important;
    border-color: #ffca2c !important;
    box-shadow: 0 1px 3px rgba(255, 193, 7, 0.3) !important;
}

.badge.bg-info {
    background-color: #0dcaf0 !important;
    color: #000 !important;
    border-color: #3dd5f3 !important;
    box-shadow: 0 1px 3px rgba(13, 202, 240, 0.3) !important;
}

.badge.bg-primary {
    background-color: #0d6efd !important;
    color: #fff !important;
    border-color: #0a58ca !important;
    box-shadow: 0 1px 3px rgba(13, 110, 253, 0.3) !important;
}

.badge.bg-success {
    background-color: #198754 !important;
    color: #fff !important;
    border-color: #146c43 !important;
    box-shadow: 0 1px 3px rgba(25, 135, 84, 0.3) !important;
}

.badge.bg-danger {
    background-color: #dc3545 !important;
    color: #fff !important;
    border-color: #b02a37 !important;
    box-shadow: 0 1px 3px rgba(220, 53, 69, 0.3) !important;
}

.badge.bg-secondary {
    background-color: #6c757d !important;
    color: #fff !important;
    border-color: #565e64 !important;
    box-shadow: 0 1px 3px rgba(108, 117, 125, 0.3) !important;
}

.badge.bg-light {
    background-color: #f8f9fa !important;
    color: #212529 !important;
    border-color: #f0f1f2 !important;
    box-shadow: 0 1px 3px rgba(248, 249, 250, 0.3) !important;
}

/* Styles spécifiques dans les tableaux */
.table td .badge,
.table th .badge {
    font-size: 0.75em !important;
    padding: 0.35em 0.65em !important;
    margin: 0 !important;
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Force la visibilité des badges */
span.badge {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    z-index: 1 !important;
}

/* Styles pour les statuts de paiement spécifiques */
.payment-paid {
    background-color: #28a745 !important;
    color: #fff !important;
    border: 1px solid #1e7e34 !important;
}

.payment-pending {
    background-color: #ffc107 !important;
    color: #212529 !important;
    border: 1px solid #e0a800 !important;
}

.payment-failed {
    background-color: #dc3545 !important;
    color: #fff !important;
    border: 1px solid #c82333 !important;
}

.payment-refunded {
    background-color: #6c757d !important;
    color: #fff !important;
    border: 1px solid #545b62 !important;
}

/* Hover effects */
.badge:hover {
    transform: scale(1.05);
    transition: transform 0.2s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .badge {
        font-size: 0.7em !important;
        padding: 0.3em 0.6em !important;
        min-width: 60px !important;
    }
}

/* Fix pour les problèmes de visibilité spécifiques */
.table-responsive .badge {
    position: relative !important;
    z-index: 2 !important;
}

/* Assurer que les badges sont toujours visibles */
.badge[style*="color: white"],
.badge[style*="color: #fff"],
.badge[style*="color: #ffffff"] {
    color: #fff !important;
}

.badge[style*="color: black"],
.badge[style*="color: #000"],
.badge[style*="color: #000000"] {
    color: #000 !important;
}
