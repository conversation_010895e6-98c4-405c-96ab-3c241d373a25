﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="14.0.0" />
    <PackageReference Include="AutoMapper.Collection" Version="11.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Http" Version="2.3.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.2" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.2" />
    <PackageReference Include="Azure.Storage.Blobs" Version="12.19.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\NafaPlace.Catalog.Domain\NafaPlace.Catalog.Domain.csproj" />
  </ItemGroup>

</Project>
