## .NET Core
*.swp
*.*~
project.lock.json
.DS_Store
*.pyc
nupkg/

# Visual Studio Code
.vscode

# Rider
.idea

# User-specific files
*.suo
*.user
*.userosscache
*.sln.docstates

# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
build/
bld/
[Bb]in/
[Oo]bj/
[Oo]ut/
msbuild.log
msbuild.err
msbuild.wrn

# Visual Studio
.vs/
*.ncrunch*
*.userprefs
*.log
*.vspscc
*.vssscc
.builds

# NuGet Packages
*.nupkg
# The packages folder can be ignored because of Package Restore
**/[Pp]ackages/*
# except build/, which is used as an MSBuild target.
!**/[Pp]ackages/build/
# Uncomment if necessary however generally it will be regenerated when needed
#!**/[Pp]ackages/repositories.config
# NuGet v3's project.json files produces more ignorable files
*.nuget.props
*.nuget.targets

# Docker
.docker/
.docker-compose/
docker-compose.override.yml

# Environment Files
*.env
.env.*
!.env.example

# Temporary files
*.tmp
*.temp
*.swp
*~

# Logs
logs/
*.log
npm-debug.log*

# IDE
*.suo
*.ntvs*
*.njsproj
*.sln.docstates

# Test Results
[Tt]est[Rr]esult*/
[Bb]uild[Ll]og.*

# NUNIT
*.VisualState.xml
TestResult.xml
nunit-*.xml

# Build Results of an ATL Project
[Dd]ebugPS/
[Rr]eleasePS/
dlldata.c

# SQL Server files
*.mdf
*.ldf
*.ndf

# Business Intelligence projects
*.rdl.data
*.bim.layout
*.bim_*.settings
*.rptproj.rsuser
*- [Bb]ackup.rdl
*- [Bb]ackup ([0-9]).rdl
*- [Bb]ackup ([0-9][0-9]).rdl

# Microsoft Fakes
FakesAssemblies/

# Node.js
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log

# Local development settings
# appsettings.Development.json
appsettings.local.json
# *.Development.json
*.Local.json

# PostgreSQL data directories
pgdata/
postgres-data/

# Specific to NafaPlace
# Docker volumes
nafaplace_catalog-data/
nafaplace_identity-data/
nafaplace_pgadmin-data/

# Azurite data
__azurite_db_blob__/
__azurite_db_blob_extent__/
__azurite_db_queue__/
__azurite_db_queue_extent__/
__azurite_db_table__/
__azurite_db_table_extent__/

# User secrets
**/secrets.json
**/launchSettings.json

# SSL certificates
*.pfx
*.key
*.crt
*.pem

# Backup files
*.bak
*~

# Compiled CSS from SCSS/SASS
*.css.map
*.sass.map
*.scss.map

# Coverage reports
coverage/
*.coverage
.coverage*
htmlcov/

# Cache directories
.sass-cache/
.npm/
.eslintcache
.stylelintcache
.cache/

# Temporary SQL scripts
*-temp.sql
