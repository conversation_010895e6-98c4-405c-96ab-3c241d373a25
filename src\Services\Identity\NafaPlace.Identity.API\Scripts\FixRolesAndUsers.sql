-- Script pour corriger les rôles et assigner les rôles manquants aux utilisateurs
-- Ce script va :
-- 1. <PERSON><PERSON><PERSON><PERSON> le rôle "Client" par "Seller" 
-- 2. Assigner le rôle "Seller" aux utilisateurs sans rôles (vendeurs)
-- 3. Assigner le rôle "User" à l'utilisateur "user"

BEGIN;

-- 1. <PERSON><PERSON> à jour le rôle "Client" vers "Seller"
UPDATE "Roles" 
SET "Name" = 'Seller', 
    "Description" = 'Vendeur sur la plateforme',
    "UpdatedAt" = CURRENT_TIMESTAMP
WHERE "Name" = 'Client';

-- 2. Assigner le rôle "Seller" aux utilisateurs qui semblent être des vendeurs
-- (tous les utilisateurs sauf admin et user)
INSERT INTO "UserRoles" ("UserId", "RoleId", "CreatedAt")
SELECT 
    u."Id",
    3, -- ID du rôle Seller (ancien Client)
    CURRENT_TIMESTAMP
FROM "Users" u
WHERE u."Username" IN ('lamine92', 'moussa92', 'test92', 'camara92')
  AND NOT EXISTS (
    SELECT 1 FROM "UserRoles" ur WHERE ur."UserId" = u."Id"
  );

-- 3. Assigner le rôle "User" à l'utilisateur "user"
INSERT INTO "UserRoles" ("UserId", "RoleId", "CreatedAt")
SELECT 
    u."Id",
    2, -- ID du rôle User
    CURRENT_TIMESTAMP
FROM "Users" u
WHERE u."Username" = 'user'
  AND NOT EXISTS (
    SELECT 1 FROM "UserRoles" ur WHERE ur."UserId" = u."Id"
  );

-- 4. Vérification : Afficher tous les utilisateurs avec leurs rôles après correction
SELECT 
    u."Id" as "UserId",
    u."Username",
    u."Email",
    r."Name" as "RoleName",
    ur."CreatedAt" as "RoleAssignedAt"
FROM "Users" u
LEFT JOIN "UserRoles" ur ON u."Id" = ur."UserId"
LEFT JOIN "Roles" r ON ur."RoleId" = r."Id"
ORDER BY u."Id";

-- 5. Afficher les rôles disponibles
SELECT "Id", "Name", "Description" FROM "Roles" ORDER BY "Id";

COMMIT;

-- Message de confirmation
SELECT 'Correction des rôles terminée avec succès!' as "Status";
