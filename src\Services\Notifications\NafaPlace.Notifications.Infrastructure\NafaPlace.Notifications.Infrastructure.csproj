<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.0" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\NafaPlace.Notifications.Application\NafaPlace.Notifications.Application.csproj" />
    <ProjectReference Include="..\NafaPlace.Notifications.Domain\NafaPlace.Notifications.Domain.csproj" />
    <ProjectReference Include="..\..\..\BuildingBlocks\Common\NafaPlace.Common\NafaPlace.Common.csproj" />
  </ItemGroup>

</Project>
