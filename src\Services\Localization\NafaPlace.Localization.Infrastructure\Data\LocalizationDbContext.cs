using Microsoft.EntityFrameworkCore;
using NafaPlace.Localization.Domain.Entities;

namespace NafaPlace.Localization.Infrastructure.Data;

public class LocalizationDbContext : DbContext
{
    public LocalizationDbContext(DbContextOptions<LocalizationDbContext> options) : base(options)
    {
    }

    public DbSet<Language> Languages { get; set; }
    public DbSet<Translation> Translations { get; set; }
    public DbSet<TranslationKey> TranslationKeys { get; set; }
    public DbSet<Currency> Currencies { get; set; }
    public DbSet<CurrencyRate> CurrencyRates { get; set; }
    public DbSet<LocalizationResource> LocalizationResources { get; set; }
    public DbSet<TranslationMemory> TranslationMemories { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configuration Languages
        modelBuilder.Entity<Language>(entity =>
        {
            entity.HasKey(e => e.Code);
            entity.Property(e => e.Code).HasMaxLength(10);
            entity.Property(e => e.Name).HasMaxLength(100).IsRequired();
            entity.Property(e => e.NativeName).HasMaxLength(100).IsRequired();
            entity.Property(e => e.Direction).HasMaxLength(3).HasDefaultValue("ltr");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsDefault).HasDefaultValue(false);
            entity.Property(e => e.Priority).HasDefaultValue(1);
            entity.Property(e => e.DateFormat).HasMaxLength(50);
            entity.Property(e => e.TimeFormat).HasMaxLength(50);
            entity.Property(e => e.NumberFormat).HasMaxLength(50);
            entity.Property(e => e.CurrencyFormat).HasMaxLength(50);
            entity.Property(e => e.Metadata).HasColumnType("jsonb").HasDefaultValue("{}");
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

            entity.HasIndex(e => e.IsActive);
            entity.HasIndex(e => e.IsDefault);
        });

        // Configuration TranslationKeys
        modelBuilder.Entity<TranslationKey>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Key).HasMaxLength(200).IsRequired();
            entity.Property(e => e.Category).HasMaxLength(50);
            entity.Property(e => e.DefaultValue).IsRequired();
            entity.Property(e => e.Description).HasMaxLength(500);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.Tags).HasColumnType("jsonb").HasDefaultValue("[]");
            entity.Property(e => e.Metadata).HasColumnType("jsonb").HasDefaultValue("{}");
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

            entity.HasIndex(e => e.Key).IsUnique();
            entity.HasIndex(e => new { e.Category, e.IsActive });
        });

        // Configuration Translations
        modelBuilder.Entity<Translation>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.LanguageCode).HasMaxLength(10).IsRequired();
            entity.Property(e => e.Value).IsRequired();
            entity.Property(e => e.IsApproved).HasDefaultValue(false);
            entity.Property(e => e.IsAutoTranslated).HasDefaultValue(false);
            entity.Property(e => e.TranslatedBy).HasMaxLength(100);
            entity.Property(e => e.ApprovedBy).HasMaxLength(100);
            entity.Property(e => e.Quality).HasColumnType("decimal(3,2)");
            entity.Property(e => e.Context).HasMaxLength(500);
            entity.Property(e => e.Metadata).HasColumnType("jsonb").HasDefaultValue("{}");
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

            entity.HasIndex(e => new { e.TranslationKeyId, e.LanguageCode }).IsUnique();
            entity.HasIndex(e => e.LanguageCode);
            entity.HasIndex(e => e.IsApproved);

            entity.HasOne<TranslationKey>()
                .WithMany()
                .HasForeignKey(e => e.TranslationKeyId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne<Language>()
                .WithMany()
                .HasForeignKey(e => e.LanguageCode)
                .OnDelete(DeleteBehavior.Cascade);
        });

        // Configuration Currencies
        modelBuilder.Entity<Currency>(entity =>
        {
            entity.HasKey(e => e.Code);
            entity.Property(e => e.Code).HasMaxLength(3);
            entity.Property(e => e.Name).HasMaxLength(100).IsRequired();
            entity.Property(e => e.Symbol).HasMaxLength(10).IsRequired();
            entity.Property(e => e.DecimalPlaces).HasDefaultValue(2);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsDefault).HasDefaultValue(false);
            entity.Property(e => e.Priority).HasDefaultValue(1);
            entity.Property(e => e.Metadata).HasColumnType("jsonb").HasDefaultValue("{}");
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

            entity.HasIndex(e => e.IsActive);
            entity.HasIndex(e => e.IsDefault);
        });

        // Configuration CurrencyRates
        modelBuilder.Entity<CurrencyRate>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.FromCurrency).HasMaxLength(3).IsRequired();
            entity.Property(e => e.ToCurrency).HasMaxLength(3).IsRequired();
            entity.Property(e => e.Rate).HasColumnType("decimal(18,8)").IsRequired();
            entity.Property(e => e.Source).HasMaxLength(50);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.Metadata).HasColumnType("jsonb").HasDefaultValue("{}");
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

            entity.HasIndex(e => new { e.FromCurrency, e.ToCurrency, e.Date });
            entity.HasIndex(e => e.Date);

            entity.HasOne<Currency>()
                .WithMany()
                .HasForeignKey(e => e.FromCurrency)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne<Currency>()
                .WithMany()
                .HasForeignKey(e => e.ToCurrency)
                .OnDelete(DeleteBehavior.Cascade);
        });

        // Configuration LocalizationResources
        modelBuilder.Entity<LocalizationResource>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.ResourceType).HasMaxLength(50).IsRequired();
            entity.Property(e => e.ResourceKey).HasMaxLength(200).IsRequired();
            entity.Property(e => e.LanguageCode).HasMaxLength(10).IsRequired();
            entity.Property(e => e.ResourceValue).IsRequired();
            entity.Property(e => e.MimeType).HasMaxLength(100);
            entity.Property(e => e.FileSize).HasDefaultValue(0);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.Metadata).HasColumnType("jsonb").HasDefaultValue("{}");
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

            entity.HasIndex(e => new { e.ResourceType, e.ResourceKey, e.LanguageCode }).IsUnique();
            entity.HasIndex(e => e.LanguageCode);

            entity.HasOne<Language>()
                .WithMany()
                .HasForeignKey(e => e.LanguageCode)
                .OnDelete(DeleteBehavior.Cascade);
        });

        // Configuration TranslationMemories
        modelBuilder.Entity<TranslationMemory>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.SourceLanguage).HasMaxLength(10).IsRequired();
            entity.Property(e => e.TargetLanguage).HasMaxLength(10).IsRequired();
            entity.Property(e => e.SourceText).IsRequired();
            entity.Property(e => e.TargetText).IsRequired();
            entity.Property(e => e.Quality).HasColumnType("decimal(3,2)");
            entity.Property(e => e.Context).HasMaxLength(500);
            entity.Property(e => e.Domain).HasMaxLength(100);
            entity.Property(e => e.CreatedBy).HasMaxLength(100);
            entity.Property(e => e.UsageCount).HasDefaultValue(0);
            entity.Property(e => e.Metadata).HasColumnType("jsonb").HasDefaultValue("{}");
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

            entity.HasIndex(e => new { e.SourceLanguage, e.TargetLanguage });
            entity.HasIndex(e => e.Quality);
            entity.HasIndex(e => e.Domain);

            entity.HasOne<Language>()
                .WithMany()
                .HasForeignKey(e => e.SourceLanguage)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne<Language>()
                .WithMany()
                .HasForeignKey(e => e.TargetLanguage)
                .OnDelete(DeleteBehavior.Cascade);
        });
    }
}
