@page "/orders"
@using NafaPlace.AdminPortal.Models.Orders
@using NafaPlace.AdminPortal.Models.Common
@using NafaPlace.AdminPortal.Services
@using NafaPlace.AdminPortal.Components
@inject IOrderService OrderService
@inject IJSRuntime JSRuntime
@inject NavigationManager NavigationManager

<PageTitle>Gestion des Commandes</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-shopping-cart me-2"></i>Gestion des Commandes
                    </h3>
                </div>
                <div class="card-body">
                    <!-- Statistiques -->
                    @if (statistics != null)
                    {
                        <div class="row mb-4">
                            <div class="col-md-2">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <h4>@statistics.TotalOrders</h4>
                                        <small>Total</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="card bg-warning text-white">
                                    <div class="card-body text-center">
                                        <h4>@statistics.PendingOrders</h4>
                                        <small>En attente</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <h4>@statistics.ProcessingOrders</h4>
                                        <small>En cours</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h4>@statistics.DeliveredOrders</h4>
                                        <small>Livrées</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="card bg-secondary text-white">
                                    <div class="card-body text-center">
                                        <h4>@statistics.TotalRevenue.ToString("N0") GNF</h4>
                                        <small>Chiffre d'affaires</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="card bg-dark text-white">
                                    <div class="card-body text-center">
                                        <h4>@statistics.AverageOrderValue.ToString("N0") GNF</h4>
                                        <small>Panier moyen</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }

                    <!-- Filtres de recherche -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" placeholder="N° commande, client..."
                                       @bind="searchRequest.SearchTerm" @onkeypress="OnSearchKeyPress" />
                            </div>
                        </div>
                        <div class="col-md-2">
                            <select class="form-select" @bind="searchRequest.Status" @bind:after="SearchOrders">
                                <option value="">Tous les statuts</option>
                                @foreach (var status in OrderStatus.All)
                                {
                                    <option value="@status">@OrderStatus.GetDisplayName(status)</option>
                                }
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select class="form-select" @bind="searchRequest.PaymentStatus" @bind:after="SearchOrders">
                                <option value="">Tous les paiements</option>
                                @foreach (var status in PaymentStatus.All)
                                {
                                    <option value="@status">@PaymentStatus.GetDisplayName(status)</option>
                                }
                            </select>
                        </div>
                        <div class="col-md-2">
                            <input type="date" class="form-control" @bind="searchRequest.StartDate" @bind:after="SearchOrders" />
                        </div>
                        <div class="col-md-2">
                            <input type="date" class="form-control" @bind="searchRequest.EndDate" @bind:after="SearchOrders" />
                        </div>
                        <div class="col-md-1">
                            <button class="btn btn-primary w-100" @onclick="SearchOrders">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>

                    @if (isLoading)
                    {
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Chargement...</span>
                            </div>
                            <p class="mt-2">Chargement des commandes...</p>
                        </div>
                    }
                    else if (orders?.Items?.Any() == true)
                    {
                        <!-- Table des commandes -->
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>N° Commande</th>
                                        <th>Client</th>
                                        <th>Date</th>
                                        <th>Montant</th>
                                        <th>Statut</th>
                                        <th>Paiement</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var order in orders.Items)
                                    {
                                        <tr>
                                            <td>
                                                <strong>@order.OrderNumber</strong>
                                                <br />
                                                <small class="text-muted">ID: @order.Id</small>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong>@order.UserName</strong>
                                                    <br />
                                                    <small class="text-muted">@order.UserEmail</small>
                                                </div>
                                            </td>
                                            <td>
                                                @order.OrderDate.ToString("dd/MM/yyyy")
                                                <br />
                                                <small class="text-muted">@order.OrderDate.ToString("HH:mm")</small>
                                            </td>
                                            <td>
                                                <strong>@order.TotalAmount.ToString("N0") @order.Currency</strong>
                                                <br />
                                                <small class="text-muted">@order.Items.Count article(s)</small>
                                            </td>
                                            <td>
                                                <span class="@OrderStatus.GetBadgeClass(order.Status)">
                                                    @OrderStatus.GetDisplayName(order.Status)
                                                </span>
                                            </td>
                                            <td>
                                                <span class="@PaymentStatus.GetBadgeClass(order.PaymentStatus)">
                                                    @PaymentStatus.GetDisplayName(order.PaymentStatus)
                                                </span>
                                                <br />
                                                <small class="text-muted">@order.PaymentMethod</small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button class="btn btn-sm btn-outline-primary" @onclick="() => ShowOrderDetails(order)">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-success" @onclick="() => ShowUpdateStatusModal(order)">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        @if (orders.TotalPages > 1)
                        {
                            <nav aria-label="Navigation des pages">
                                <ul class="pagination justify-content-center">
                                    <li class="page-item @(orders.HasPreviousPage ? "" : "disabled")">
                                        <button class="page-link" @onclick="() => LoadOrders(orders.PageNumber - 1)" disabled="@(!orders.HasPreviousPage)">
                                            Précédent
                                        </button>
                                    </li>
                                    
                                    @for (int i = Math.Max(1, orders.PageNumber - 2); i <= Math.Min(orders.TotalPages, orders.PageNumber + 2); i++)
                                    {
                                        var pageNumber = i;
                                        <li class="page-item @(pageNumber == orders.PageNumber ? "active" : "")">
                                            <button class="page-link" @onclick="() => LoadOrders(pageNumber)">
                                                @pageNumber
                                            </button>
                                        </li>
                                    }
                                    
                                    <li class="page-item @(orders.HasNextPage ? "" : "disabled")">
                                        <button class="page-link" @onclick="() => LoadOrders(orders.PageNumber + 1)" disabled="@(!orders.HasNextPage)">
                                            Suivant
                                        </button>
                                    </li>
                                </ul>
                            </nav>
                        }
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Aucune commande trouvée</h5>
                            <p class="text-muted">Aucune commande ne correspond à vos critères de recherche.</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de mise à jour du statut -->
<OrderStatusModal Order="selectedOrder" IsVisible="showStatusModal"
                  OnStatusUpdated="OnStatusUpdated" OnHide="HideStatusModal" />

@code {
    private PagedResult<OrderDto>? orders;
    private OrderStatistics? statistics;
    private OrderSearchRequest searchRequest = new();
    private bool isLoading = true;
    private int currentPage = 1;
    private const int pageSize = 20;

    // Modal state
    private OrderDto? selectedOrder;
    private bool showStatusModal = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadStatistics();
        await LoadOrders();
    }

    private async Task LoadOrders(int page = 1)
    {
        isLoading = true;
        currentPage = page;
        searchRequest.PageNumber = page;
        searchRequest.PageSize = pageSize;

        try
        {
            orders = await OrderService.GetOrdersAsync(searchRequest);
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Erreur lors du chargement des commandes: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task LoadStatistics()
    {
        try
        {
            statistics = await OrderService.GetOrderStatisticsAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des statistiques: {ex.Message}");
        }
    }

    private async Task SearchOrders()
    {
        await LoadOrders(1);
    }

    private async Task OnSearchKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            await SearchOrders();
        }
    }

    private void ShowOrderDetails(OrderDto order)
    {
        NavigationManager.NavigateTo($"/orders/{order.OrderNumber}");
    }

    private void ShowUpdateStatusModal(OrderDto order)
    {
        selectedOrder = order;
        showStatusModal = true;
        StateHasChanged();
    }

    private void HideStatusModal()
    {
        showStatusModal = false;
        selectedOrder = null;
        StateHasChanged();
    }

    private async Task OnStatusUpdated()
    {
        await LoadOrders(currentPage);
        await LoadStatistics();
    }
}
