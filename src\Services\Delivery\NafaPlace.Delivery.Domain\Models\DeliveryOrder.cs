using System.ComponentModel.DataAnnotations;
using NafaPlace.Common.Models;

namespace NafaPlace.Delivery.Domain.Models;

public class DeliveryOrder : BaseEntity
{
    [Required]
    [MaxLength(50)]
    public required string OrderId { get; set; }

    [Required]
    [MaxLength(50)]
    public required string TrackingNumber { get; set; }

    [Required]
    public int CarrierId { get; set; }

    [Required]
    public int ZoneId { get; set; }

    [Required]
    [MaxLength(50)]
    public required string CustomerId { get; set; }

    [Required]
    [MaxLength(100)]
    public required string CustomerName { get; set; }

    [Required]
    [MaxLength(100)]
    public required string CustomerEmail { get; set; }

    [Required]
    [MaxLength(20)]
    public required string CustomerPhone { get; set; }

    // Adresse de livraison
    [Required]
    [MaxLength(500)]
    public required string DeliveryAddress { get; set; }

    [MaxLength(100)]
    public string? DeliveryCity { get; set; }

    [MaxLength(100)]
    public string? DeliveryRegion { get; set; }

    [MaxLength(20)]
    public string? DeliveryPostalCode { get; set; }

    [MaxLength(100)]
    public string? DeliveryCountry { get; set; } = "Guinea";

    public double? DeliveryLatitude { get; set; }
    public double? DeliveryLongitude { get; set; }

    // Informations de commande
    [Required]
    [Range(0, double.MaxValue)]
    public decimal OrderValue { get; set; }

    [Required]
    [Range(0, double.MaxValue)]
    public decimal DeliveryFee { get; set; }

    [Range(0, double.MaxValue)]
    public decimal? InsuranceFee { get; set; }

    [Range(0, double.MaxValue)]
    public decimal? AdditionalFees { get; set; }

    [Required]
    [Range(0, double.MaxValue)]
    public decimal TotalFee { get; set; }

    [Required]
    [MaxLength(3)]
    public required string Currency { get; set; } = "GNF";

    // Détails physiques
    [Range(0, double.MaxValue)]
    public decimal? Weight { get; set; } // En kg

    [Range(0, double.MaxValue)]
    public decimal? Volume { get; set; } // En cm³

    [Range(0, int.MaxValue)]
    public int PackageCount { get; set; } = 1;

    [MaxLength(500)]
    public string? PackageDescription { get; set; }

    // Statut et timing
    [Required]
    public DeliveryStatus Status { get; set; } = DeliveryStatus.Pending;

    [Required]
    public DeliveryType Type { get; set; } = DeliveryType.Standard;

    public DateTime? ScheduledDeliveryDate { get; set; }

    public DateTime? EstimatedDeliveryDate { get; set; }

    public DateTime? ActualDeliveryDate { get; set; }

    // Horaires préférés
    public TimeSpan? PreferredDeliveryTimeStart { get; set; }

    public TimeSpan? PreferredDeliveryTimeEnd { get; set; }

    // Instructions spéciales
    [MaxLength(1000)]
    public string? DeliveryInstructions { get; set; }

    [MaxLength(1000)]
    public string? SpecialRequirements { get; set; }

    // Informations de livraison
    [MaxLength(100)]
    public string? DeliveryPersonName { get; set; }

    [MaxLength(20)]
    public string? DeliveryPersonPhone { get; set; }

    [MaxLength(100)]
    public string? ReceivedByName { get; set; }

    [MaxLength(20)]
    public string? ReceivedByPhone { get; set; }

    [MaxLength(500)]
    public string? DeliveryNotes { get; set; }

    [MaxLength(200)]
    public string? DeliveryPhotoUrl { get; set; }

    [MaxLength(200)]
    public string? SignatureUrl { get; set; }

    // Tentatives de livraison
    public int DeliveryAttempts { get; set; } = 0;

    public int MaxDeliveryAttempts { get; set; } = 3;

    public DateTime? LastAttemptDate { get; set; }

    public DateTime? NextAttemptDate { get; set; }

    // Évaluation
    public int? CustomerRating { get; set; } // 1-5

    [MaxLength(1000)]
    public string? CustomerFeedback { get; set; }

    public DateTime? RatingDate { get; set; }

    // Métadonnées
    [MaxLength(50)]
    public string? CreatedBy { get; set; }

    [MaxLength(50)]
    public string? UpdatedBy { get; set; }

    // Navigation properties
    public virtual Carrier? Carrier { get; set; }
    public virtual DeliveryZone? Zone { get; set; }
    public virtual ICollection<DeliveryTracking> TrackingEvents { get; set; } = new List<DeliveryTracking>();
}

public enum DeliveryStatus
{
    Pending = 1,            // En attente
    Confirmed = 2,          // Confirmée
    PickedUp = 3,           // Récupérée
    InTransit = 4,          // En transit
    OutForDelivery = 5,     // En cours de livraison
    Delivered = 6,          // Livrée
    Failed = 7,             // Échec de livraison
    Returned = 8,           // Retournée
    Cancelled = 9,          // Annulée
    Lost = 10,              // Perdue
    Damaged = 11            // Endommagée
}

public enum DeliveryType
{
    Standard = 1,       // Standard
    Express = 2,        // Express
    SameDay = 3,        // Même jour
    Scheduled = 4,      // Programmée
    PickupPoint = 5     // Point de retrait
}

public class DeliveryTracking : BaseEntity
{
    [Required]
    public int DeliveryOrderId { get; set; }

    [Required]
    public DeliveryStatus Status { get; set; }

    [Required]
    [MaxLength(200)]
    public required string Description { get; set; }

    [MaxLength(100)]
    public string? Location { get; set; }

    public double? Latitude { get; set; }

    public double? Longitude { get; set; }

    [Required]
    public DateTime EventDate { get; set; } = DateTime.UtcNow;

    [MaxLength(100)]
    public string? EventBy { get; set; } // Nom de la personne/système

    [MaxLength(500)]
    public string? Notes { get; set; }

    [MaxLength(200)]
    public string? PhotoUrl { get; set; }

    public bool IsCustomerVisible { get; set; } = true;

    public bool IsAutomated { get; set; } = false;

    // Navigation properties
    public virtual DeliveryOrder? DeliveryOrder { get; set; }
}

public class DeliveryRoute : BaseEntity
{
    [Required]
    [MaxLength(100)]
    public required string Name { get; set; }

    [Required]
    [MaxLength(50)]
    public required string Code { get; set; }

    [Required]
    public int CarrierId { get; set; }

    [Required]
    [MaxLength(50)]
    public required string DriverId { get; set; }

    [Required]
    [MaxLength(100)]
    public required string DriverName { get; set; }

    [MaxLength(20)]
    public string? DriverPhone { get; set; }

    [MaxLength(50)]
    public string? VehicleId { get; set; }

    [MaxLength(20)]
    public string? VehiclePlate { get; set; }

    [Required]
    public DateTime RouteDate { get; set; }

    public DateTime? StartTime { get; set; }

    public DateTime? EndTime { get; set; }

    [Required]
    public RouteStatus Status { get; set; } = RouteStatus.Planned;

    // Statistiques
    public int TotalDeliveries { get; set; } = 0;

    public int CompletedDeliveries { get; set; } = 0;

    public int FailedDeliveries { get; set; } = 0;

    [Range(0, double.MaxValue)]
    public decimal? TotalDistance { get; set; } // En km

    [Range(0, double.MaxValue)]
    public decimal? EstimatedDuration { get; set; } // En heures

    [Range(0, double.MaxValue)]
    public decimal? ActualDuration { get; set; } // En heures

    [MaxLength(500)]
    public string? Notes { get; set; }

    [MaxLength(50)]
    public string? CreatedBy { get; set; }

    [MaxLength(50)]
    public string? UpdatedBy { get; set; }

    // Navigation properties
    public virtual Carrier? Carrier { get; set; }
    public virtual ICollection<RouteDelivery> RouteDeliveries { get; set; } = new List<RouteDelivery>();
}

public enum RouteStatus
{
    Planned = 1,        // Planifiée
    InProgress = 2,     // En cours
    Completed = 3,      // Terminée
    Cancelled = 4       // Annulée
}

public class RouteDelivery : BaseEntity
{
    [Required]
    public int RouteId { get; set; }

    [Required]
    public int DeliveryOrderId { get; set; }

    [Required]
    public int Sequence { get; set; } // Ordre dans la tournée

    public DateTime? EstimatedArrival { get; set; }

    public DateTime? ActualArrival { get; set; }

    public DateTime? EstimatedDeparture { get; set; }

    public DateTime? ActualDeparture { get; set; }

    [Required]
    public RouteDeliveryStatus Status { get; set; } = RouteDeliveryStatus.Planned;

    [MaxLength(500)]
    public string? Notes { get; set; }

    // Navigation properties
    public virtual DeliveryRoute? Route { get; set; }
    public virtual DeliveryOrder? DeliveryOrder { get; set; }
}

public enum RouteDeliveryStatus
{
    Planned = 1,        // Planifiée
    InProgress = 2,     // En cours
    Completed = 3,      // Terminée
    Failed = 4,         // Échec
    Skipped = 5         // Ignorée
}
