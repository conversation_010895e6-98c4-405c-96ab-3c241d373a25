using NafaPlace.Web.Models.Order;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace NafaPlace.Web.Services
{
    public interface IOrderService
    {
        Task<OrderDto> GetOrderByIdAsync(int id);
        Task<IEnumerable<OrderDto>> GetOrdersByUserIdAsync(string userId);
        Task<OrderDto> CreateOrderAsync(OrderCreateDto order);
        Task<OrderDto> CheckoutAsync(string userId);
        Task<OrderDto> CheckoutAsync(OrderCreateDto orderRequest);
        Task<bool> UpdatePaymentStatusAsync(int orderId, string paymentStatus, string? transactionId = null);
    }
}