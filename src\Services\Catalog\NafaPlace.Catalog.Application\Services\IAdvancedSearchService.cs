using NafaPlace.Catalog.Application.DTOs.Product;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace NafaPlace.Catalog.Application.Services
{
    public interface IAdvancedSearchService
    {
        // Recherche avancée principale
        Task<ProductSearchResultDto> SearchProductsAdvancedAsync(ProductSearchDto searchDto);
        
        // Suggestions et autocomplétion
        Task<List<SearchSuggestionDto>> GetSearchSuggestionsAsync(string query, int maxSuggestions = 10);
        Task<List<string>> GetAutocompleteSuggestionsAsync(string query, int maxSuggestions = 5);
        
        // Recherche par similarité
        Task<List<ProductDto>> FindSimilarProductsAsync(int productId, int maxResults = 10);
        Task<List<ProductDto>> SearchByImageAsync(string imageUrl, int maxResults = 10);
        
        // Facettes et filtres
        Task<List<SearchFacetDto>> GetSearchFacetsAsync(ProductSearchDto searchDto);
        Task<Dictionary<string, List<string>>> GetAvailableFiltersAsync();
        
        // Historique et tendances
        Task SaveSearchQueryAsync(string query, string? userId = null);
        Task<List<string>> GetPopularSearchesAsync(int count = 10);
        Task<List<string>> GetUserSearchHistoryAsync(string userId, int count = 10);
        
        // Indexation et maintenance
        Task RebuildSearchIndexAsync();
        Task UpdateProductSearchIndexAsync(int productId);
        Task DeleteFromSearchIndexAsync(int productId);
        
        // Statistiques de recherche
        Task<Dictionary<string, object>> GetSearchStatisticsAsync();
        Task<List<string>> GetSearchTrendsAsync(int days = 30);
    }
}
