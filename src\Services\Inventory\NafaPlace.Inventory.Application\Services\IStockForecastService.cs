using NafaPlace.Inventory.Application.DTOs;

namespace NafaPlace.Inventory.Application.Services;

public interface IStockForecastService
{
    // Prévisions individuelles
    Task<StockForecastDto> GetProductForecastAsync(int productId, ForecastParametersDto? parameters = null);
    Task<List<StockForecastDto>> GetProductsForecastAsync(List<int> productIds, ForecastParametersDto? parameters = null);
    
    // Prévisions en lot
    Task<ForecastSummaryDto> GetBulkForecastAsync(BulkForecastRequestDto request);
    Task<List<StockForecastDto>> GetSellerForecastAsync(int sellerId, ForecastParametersDto? parameters = null);
    Task<List<StockForecastDto>> GetCategoryForecastAsync(int categoryId, ForecastParametersDto? parameters = null);
    
    // Recommandations
    Task<List<StockRecommendationDto>> GetReorderRecommendationsAsync(int? sellerId = null);
    Task<List<StockForecastDto>> GetCriticalStockForecastAsync(int? sellerId = null);
    Task<List<StockForecastDto>> GetLowStockForecastAsync(int? sellerId = null, int daysThreshold = 7);
    
    // Analyse des tendances
    Task<Dictionary<string, object>> GetDemandTrendsAsync(int productId, int days = 30);
    Task<Dictionary<string, object>> GetSeasonalityAnalysisAsync(int productId);
    Task<List<DemandPredictionDto>> GetDemandPredictionsAsync(int productId, int days = 30);
    
    // Optimisation des stocks
    Task<Dictionary<string, object>> GetOptimalStockLevelsAsync(int productId);
    Task<decimal> CalculateOptimalReorderQuantityAsync(int productId);
    Task<DateTime> GetOptimalReorderDateAsync(int productId);
    
    // Validation et calibration
    Task<double> GetForecastAccuracyAsync(int productId, int days = 30);
    Task<Dictionary<string, object>> GetForecastMetricsAsync(int? sellerId = null);
    Task RecalibrateForecastModelAsync(int productId);
    
    // Maintenance
    Task UpdateForecastsAsync();
    Task<int> CleanupOldForecastsAsync(int daysToKeep = 90);
}
