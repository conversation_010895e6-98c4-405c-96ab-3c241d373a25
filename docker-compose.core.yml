services:
  # Service d'identité
  identity-api:
    build:
      context: .
      dockerfile: src/Services/Identity/Dockerfile
    image: nafaplace-identity-api:latest
    container_name: nafaplace-identity-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Host=identity-db;Database=NafaPlace.Identity;Username=postgres;Password=*****************;
    ports:
      - "5155:80"
    depends_on:
      identity-db:
        condition: service_healthy
    networks:
      - nafaplace-network

  # Base de données d'identité (PostgreSQL)
  identity-db:
    image: postgres:16
    container_name: nafaplace-identity-db
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=*****************
      - POSTGRES_DB=NafaPlace.Identity
    ports:
      - "5433:5432"
    volumes:
      - identity-data:/var/lib/postgresql/data
    networks:
      - nafaplace-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Service de catalogue
  catalog-api:
    build:
      context: .
      dockerfile: src/Services/Catalog/Dockerfile
    image: nafaplace-catalog-api:latest
    container_name: nafaplace-catalog-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Host=catalog-db;Port=5432;Database=NafaPlace.Catalog;Username=postgres;Password=*****************
      - ConnectionStrings__AzureStorage=DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;BlobEndpoint=http://azurite:10000/devstoreaccount1;QueueEndpoint=http://azurite:10001/devstoreaccount1;TableEndpoint=http://azurite:10002/devstoreaccount1;
      - IdentityUrl=http://identity-api
    ports:
      - "5244:80"
    volumes:
      - catalog-images:/app/wwwroot/uploads
    depends_on:
      catalog-db:
        condition: service_healthy
      identity-api:
        condition: service_started
      azurite:
        condition: service_started
    networks:
      - nafaplace-network

  # Base de données du catalogue (PostgreSQL)
  catalog-db:
    image: postgres:16
    container_name: nafaplace-catalog-db
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=*****************
      - POSTGRES_DB=NafaPlace.Catalog
    ports:
      - "5432:5432"
    volumes:
      - catalog-data:/var/lib/postgresql/data
    networks:
      - nafaplace-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Service de Commande
  order-api:
    build:
      context: .
      dockerfile: src/Services/Order/NafaPlace.Order.API/Dockerfile
    image: nafaplace-order-api:latest
    container_name: nafaplace-order-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Host=order-db;Database=NafaPlace.Order;Username=postgres;Password=*****************;
      - IdentityUrl=http://identity-api
    ports:
      - "5231:80"
    depends_on:
      order-db:
        condition: service_healthy
      identity-api:
        condition: service_started
    networks:
      - nafaplace-network

  # Base de données de commande (PostgreSQL)
  order-db:
    image: postgres:16
    container_name: nafaplace-order-db
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=*****************
      - POSTGRES_DB=NafaPlace.Order
    ports:
      - "5434:5432"
    volumes:
      - order-data:/var/lib/postgresql/data
    networks:
      - nafaplace-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Application Web
  web:
    build:
      context: .
      dockerfile: src/Web/Dockerfile
    image: nafaplace-web:latest
    container_name: nafaplace-web
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ApiEndpoints__IdentityApi=http://identity-api
      - ApiEndpoints__CatalogApi=http://catalog-api
      - ApiEndpoints__OrderApi=http://order-api
    ports:
      - "8080:80"
    depends_on:
      - catalog-api
      - identity-api
      - order-api
    networks:
      - nafaplace-network

  # Portail Vendeur
  seller-portal:
    build:
      context: .
      dockerfile: src/Web/SellerPortal/Dockerfile
    image: nafaplace-seller-portal:latest
    container_name: nafaplace-seller-portal
    environment:
      - CatalogApi=http://catalog-api
      - IdentityApi=http://identity-api
    ports:
      - "8082:80"
    depends_on:
      - catalog-api
      - identity-api
    networks:
      - nafaplace-network

  # Portail Administrateur
  admin-portal:
    build:
      context: .
      dockerfile: src/Web/AdminPortal/Dockerfile
    image: nafaplace-admin-portal:latest
    container_name: nafaplace-admin-portal
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - CatalogApi=http://catalog-api
      - IdentityApi=http://identity-api
    ports:
      - "8081:80"
    depends_on:
      - catalog-api
      - identity-api
    networks:
      - nafaplace-network

  # Stockage local pour le développement (Azurite)
  azurite:
    image: mcr.microsoft.com/azure-storage/azurite
    container_name: nafaplace-azurite
    ports:
      - "10000:10000"
      - "10001:10001"
      - "10002:10002"
    volumes:
      - azurite-data:/data
    networks:
      - nafaplace-network

volumes:
  catalog-data:
  identity-data:
  order-data:
  azurite-data:
  catalog-images:

networks:
  nafaplace-network:
    name: nafaplace-network
    driver: bridge
