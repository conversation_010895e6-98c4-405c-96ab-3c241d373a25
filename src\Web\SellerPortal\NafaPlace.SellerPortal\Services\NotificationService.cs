using System;
using System.Timers;

namespace NafaPlace.SellerPortal.Services
{
    public class NotificationService
    {
        public event Action<string, NotificationType, string>? OnNotification;
        private System.Timers.Timer _autoCloseTimer;

        public NotificationService()
        {
            _autoCloseTimer = new System.Timers.Timer();
            _autoCloseTimer.Elapsed += (sender, args) => ClearNotification();
            _autoCloseTimer.AutoReset = false;
        }

        public void Notify(string message, NotificationType type, int autoCloseInSeconds = 5, string details = "")
        {
            OnNotification?.Invoke(message, type, details);
            
            if (autoCloseInSeconds > 0)
            {
                _autoCloseTimer.Interval = autoCloseInSeconds * 1000;
                _autoCloseTimer.Start();
            }
        }

        public void Success(string message, int autoCloseInSeconds = 5, string details = "")
        {
            Notify(message, NotificationType.Success, autoCloseInSeconds, details);
        }

        public void Info(string message, int autoCloseInSeconds = 5, string details = "")
        {
            Notify(message, NotificationType.Info, autoCloseInSeconds, details);
        }

        public void Warning(string message, int autoCloseInSeconds = 5, string details = "")
        {
            Notify(message, NotificationType.Warning, autoCloseInSeconds, details);
        }

        public void Error(string message, int autoCloseInSeconds = 8, string details = "")
        {
            Notify(message, NotificationType.Error, autoCloseInSeconds, details);
        }
        
        // Alias pour Error pour une meilleure compatibilité avec le code existant
        public void ShowError(string message, int autoCloseInSeconds = 8, string details = "")
        {
            Error(message, autoCloseInSeconds, details);
        }
        
        // Alias pour Success pour une meilleure compatibilité avec le code existant
        public void ShowSuccess(string message, int autoCloseInSeconds = 5, string details = "")
        {
            Success(message, autoCloseInSeconds, details);
        }

        private void ClearNotification()
        {
            OnNotification?.Invoke("", NotificationType.None, "");
        }
    }

    public enum NotificationType
    {
        None,
        Success,
        Info,
        Warning,
        Error
    }
}
