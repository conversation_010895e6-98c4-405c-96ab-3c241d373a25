namespace NafaPlace.Reviews.Application.Interfaces;

public interface INotificationService
{
    Task SendNewReviewNotificationAsync(int reviewId, int productId, string userId);
    Task SendReviewReplyNotificationAsync(int replyId, int reviewId, string reviewUserId);
    Task SendReviewApprovedNotificationAsync(int reviewId, string userId);
    Task SendReviewRejectedNotificationAsync(int reviewId, string userId, string? reason = null);
}
