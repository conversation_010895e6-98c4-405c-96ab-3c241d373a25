using System.ComponentModel.DataAnnotations;

namespace NafaPlace.SellerPortal.Models.Auth;

public class RegisterRequest
{
    [Required(ErrorMessage = "Le nom d'utilisateur est requis")]
    [StringLength(50, ErrorMessage = "Le nom d'utilisateur doit contenir entre 3 et 50 caractères", MinimumLength = 3)]
    public string Username { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "L'email est requis")]
    [EmailAddress(ErrorMessage = "Format d'email invalide")]
    public string Email { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Le mot de passe est requis")]
    [StringLength(100, ErrorMessage = "Le mot de passe doit contenir au moins 6 caractères", MinimumLength = 6)]
    public string Password { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "La confirmation du mot de passe est requise")]
    [Compare("Password", ErrorMessage = "Le mot de passe et sa confirmation ne correspondent pas")]
    public string ConfirmPassword { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Le prénom est requis")]
    public string FirstName { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Le nom est requis")]
    public string LastName { get; set; } = string.Empty;
}
