using Microsoft.EntityFrameworkCore;
using NafaPlace.Reviews.Application.Interfaces;
using NafaPlace.Reviews.Domain.Models;
using NafaPlace.Reviews.Infrastructure.Data;

namespace NafaPlace.Reviews.Infrastructure.Repositories;

public partial class ReviewRepository : IReviewRepository
{
    // Méthodes pour les réponses
    
    public async Task<Reply?> GetReplyByIdAsync(int id)
    {
        return await _context.Replies
            .FirstOrDefaultAsync(r => r.Id == id);
    }
    
    public async Task<IEnumerable<Reply>> GetRepliesByReviewIdAsync(int reviewId)
    {
        return await _context.Replies
            .Where(r => r.ReviewId == reviewId)
            .OrderBy(r => r.CreatedAt)
            .ToListAsync();
    }
    
    public async Task<Reply> CreateReplyAsync(Reply reply)
    {
        _context.Replies.Add(reply);
        await _context.SaveChangesAsync();
        return reply;
    }
    
    public async Task<Reply> UpdateReplyAsync(Reply reply)
    {
        _context.Replies.Update(reply);
        await _context.SaveChangesAsync();
        return reply;
    }
    
    public async Task DeleteReplyAsync(int id)
    {
        var reply = await _context.Replies.FindAsync(id);
        if (reply != null)
        {
            _context.Replies.Remove(reply);
            await _context.SaveChangesAsync();
        }
    }
    
    // Méthodes pour les signalements
    
    public async Task<ReviewReport?> GetReportByIdAsync(int id)
    {
        return await _context.ReviewReports
            .Include(r => r.Review)
            .FirstOrDefaultAsync(r => r.Id == id);
    }
    
    public async Task<IEnumerable<ReviewReport>> GetReportsByReviewIdAsync(int reviewId)
    {
        return await _context.ReviewReports
            .Where(r => r.ReviewId == reviewId)
            .OrderByDescending(r => r.CreatedAt)
            .ToListAsync();
    }
    
    public async Task<IEnumerable<ReviewReport>> GetReportsByStatusAsync(ReportStatus status, int page = 1, int pageSize = 10)
    {
        return await _context.ReviewReports
            .Include(r => r.Review)
            .Where(r => r.Status == status)
            .OrderByDescending(r => r.CreatedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();
    }
    
    public async Task<int> CountReportsByStatusAsync(ReportStatus status)
    {
        return await _context.ReviewReports
            .CountAsync(r => r.Status == status);
    }
    
    public async Task<ReviewReport> CreateReportAsync(ReviewReport report)
    {
        _context.ReviewReports.Add(report);
        
        // Update report count on the review
        var review = await _context.Reviews.FindAsync(report.ReviewId);
        if (review != null)
        {
            review.ReportCount++;
            
            // If this is the first report, change status to Reported
            if (review.ReportCount == 1 && review.Status == ReviewStatus.Published)
            {
                review.Status = ReviewStatus.Reported;
            }
            // If there are multiple reports, you might want to automatically change to under review after a threshold
            else if (review.ReportCount >= 5 && review.Status == ReviewStatus.Reported)
            {
                review.Status = ReviewStatus.UnderReview;
            }
            
            _context.Reviews.Update(review);
        }
        
        await _context.SaveChangesAsync();
        return report;
    }
    
    public async Task<ReviewReport> UpdateReportAsync(ReviewReport report)
    {
        _context.ReviewReports.Update(report);
        await _context.SaveChangesAsync();
        return report;
    }
    
    public async Task<bool> HasUserReportedReviewAsync(int reviewId, string userId)
    {
        return await _context.ReviewReports
            .AnyAsync(r => r.ReviewId == reviewId && r.UserId == userId);
    }
    
    public async Task<IEnumerable<Review>> GetMostReportedReviewsAsync(int count = 10)
    {
        return await _context.Reviews
            .Include(r => r.Reports)
            .Where(r => r.ReportCount > 0)
            .OrderByDescending(r => r.ReportCount)
            .Take(count)
            .ToListAsync();
    }
}
