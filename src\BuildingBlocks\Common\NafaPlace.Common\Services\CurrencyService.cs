using System.Globalization;

namespace NafaPlace.Common.Services;

public interface ICurrencyService
{
    string FormatPrice(decimal amount, string currency = "GNF");
    decimal ConvertToGNF(decimal amount, string fromCurrency);
    decimal ConvertFromGNF(decimal amount, string toCurrency);
    bool IsValidCurrency(string currency);
    string GetCurrencySymbol(string currency);
    int GetDecimalPlaces(string currency);
}

public class CurrencyService : ICurrencyService
{
    private readonly Dictionary<string, CurrencyInfo> _currencies;

    public CurrencyService()
    {
        _currencies = new Dictionary<string, CurrencyInfo>
        {
            { "GNF", new CurrencyInfo { Symbol = "GNF", DecimalPlaces = 0, ExchangeRate = 1.0m } },
            { "USD", new CurrencyInfo { Symbol = "$", DecimalPlaces = 2, ExchangeRate = 0.0001m } }, // 1 USD = 10,000 GNF
            { "EUR", new CurrencyInfo { Symbol = "€", DecimalPlaces = 2, ExchangeRate = 0.00009m } }, // 1 EUR = 11,000 GNF
            { "XOF", new CurrencyInfo { Symbol = "XOF", DecimalPlaces = 0, ExchangeRate = 15.0m } } // 1 XOF = 15 GNF
        };
    }

    public string FormatPrice(decimal amount, string currency = "GNF")
    {
        if (!_currencies.TryGetValue(currency.ToUpper(), out var currencyInfo))
        {
            currency = "GNF";
            currencyInfo = _currencies["GNF"];
        }

        var culture = currency.ToUpper() switch
        {
            "GNF" => new CultureInfo("fr-GN"), // Français Guinée
            "USD" => new CultureInfo("en-US"),
            "EUR" => new CultureInfo("fr-FR"),
            "XOF" => new CultureInfo("fr-SN"), // Français Sénégal
            _ => new CultureInfo("fr-GN")
        };

        if (currencyInfo.DecimalPlaces == 0)
        {
            return $"{amount:N0} {currencyInfo.Symbol}";
        }
        else
        {
            return amount.ToString("C", culture);
        }
    }

    public decimal ConvertToGNF(decimal amount, string fromCurrency)
    {
        if (!_currencies.TryGetValue(fromCurrency.ToUpper(), out var currencyInfo))
        {
            throw new ArgumentException($"Currency {fromCurrency} is not supported");
        }

        if (fromCurrency.ToUpper() == "GNF")
        {
            return amount;
        }

        // Convert to GNF
        return amount / currencyInfo.ExchangeRate;
    }

    public decimal ConvertFromGNF(decimal amount, string toCurrency)
    {
        if (!_currencies.TryGetValue(toCurrency.ToUpper(), out var currencyInfo))
        {
            throw new ArgumentException($"Currency {toCurrency} is not supported");
        }

        if (toCurrency.ToUpper() == "GNF")
        {
            return amount;
        }

        // Convert from GNF
        return amount * currencyInfo.ExchangeRate;
    }

    public bool IsValidCurrency(string currency)
    {
        return _currencies.ContainsKey(currency.ToUpper());
    }

    public string GetCurrencySymbol(string currency)
    {
        return _currencies.TryGetValue(currency.ToUpper(), out var currencyInfo) 
            ? currencyInfo.Symbol 
            : "GNF";
    }

    public int GetDecimalPlaces(string currency)
    {
        return _currencies.TryGetValue(currency.ToUpper(), out var currencyInfo) 
            ? currencyInfo.DecimalPlaces 
            : 0;
    }
}

public class CurrencyInfo
{
    public string Symbol { get; set; } = string.Empty;
    public int DecimalPlaces { get; set; }
    public decimal ExchangeRate { get; set; } // Rate to convert TO GNF
}

public static class CurrencyConstants
{
    public const string DEFAULT_CURRENCY = "GNF";
    public const decimal FREE_SHIPPING_THRESHOLD_GNF = 500000m; // 500,000 GNF
    public const decimal DEFAULT_SHIPPING_FEE_GNF = 25000m; // 25,000 GNF
    public const decimal VAT_RATE = 0.18m; // 18% VAT
    
    // Common price ranges in GNF
    public const decimal MIN_PRODUCT_PRICE = 10000m; // 10,000 GNF
    public const decimal MAX_PRODUCT_PRICE = 100000000m; // 100,000,000 GNF
    
    // Stripe limits in GNF
    public const long STRIPE_MIN_AMOUNT = 10000; // 10,000 GNF (minimum for Stripe)
    public const long STRIPE_MAX_AMOUNT = 99999999; // 99,999,999 GNF (maximum for Stripe)
}
