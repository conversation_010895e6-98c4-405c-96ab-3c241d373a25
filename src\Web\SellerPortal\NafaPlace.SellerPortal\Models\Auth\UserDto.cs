namespace NafaPlace.SellerPortal.Models.Auth;

public class UserDto
{
    public int Id { get; set; }
    public string Username { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public List<string> Roles { get; set; } = new List<string>();
    public bool IsAuthenticated { get; set; }
    public string? ProfilePictureUrl { get; set; }
}
