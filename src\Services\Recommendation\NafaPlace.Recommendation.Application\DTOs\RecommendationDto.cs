using NafaPlace.Recommendation.Domain.Enums;

namespace NafaPlace.Recommendation.Application.DTOs;

public class ProductRecommendationDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public string ProductDescription { get; set; } = string.Empty;
    public decimal Price { get; set; }
    public decimal? DiscountedPrice { get; set; }
    public string? ImageUrl { get; set; }
    public string? ThumbnailUrl { get; set; }
    public double Rating { get; set; }
    public int ReviewCount { get; set; }
    public string Category { get; set; } = string.Empty;
    public string Brand { get; set; } = string.Empty;
    public bool IsInStock { get; set; }
    public int StockQuantity { get; set; }
    public double RecommendationScore { get; set; }
    public RecommendationType RecommendationType { get; set; }
    public string RecommendationReason { get; set; } = string.Empty;
    public Dictionary<string, object> Metadata { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
}

public class RecommendationRequestDto
{
    public string UserId { get; set; } = string.Empty;
    public RecommendationType Type { get; set; }
    public int? ProductId { get; set; }
    public int? CategoryId { get; set; }
    public List<string>? Tags { get; set; }
    public decimal? MinPrice { get; set; }
    public decimal? MaxPrice { get; set; }
    public int Limit { get; set; } = 10;
    public bool IncludeOutOfStock { get; set; } = false;
    public Dictionary<string, object> Context { get; set; } = new();
    public List<int>? ExcludeProductIds { get; set; }
    public string? Language { get; set; } = "fr";
}

public class RecommendationResponseDto
{
    public List<ProductRecommendationDto> Products { get; set; } = new();
    public RecommendationType Type { get; set; }
    public string Algorithm { get; set; } = string.Empty;
    public double Confidence { get; set; }
    public int TotalCount { get; set; }
    public string RequestId { get; set; } = string.Empty;
    public DateTime GeneratedAt { get; set; }
    public TimeSpan ProcessingTime { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class UserPreferenceDto
{
    public string UserId { get; set; } = string.Empty;
    public List<CategoryPreferenceDto> CategoryPreferences { get; set; } = new();
    public List<BrandPreferenceDto> BrandPreferences { get; set; } = new();
    public PriceRangePreferenceDto PricePreference { get; set; } = new();
    public List<string> PreferredTags { get; set; } = new();
    public List<string> DislikedTags { get; set; } = new();
    public Dictionary<string, double> FeatureWeights { get; set; } = new();
    public DateTime LastUpdated { get; set; }
    public int InteractionCount { get; set; }
}

public class CategoryPreferenceDto
{
    public int CategoryId { get; set; }
    public string CategoryName { get; set; } = string.Empty;
    public double Score { get; set; }
    public int InteractionCount { get; set; }
    public DateTime LastInteraction { get; set; }
}

public class BrandPreferenceDto
{
    public string Brand { get; set; } = string.Empty;
    public double Score { get; set; }
    public int PurchaseCount { get; set; }
    public int ViewCount { get; set; }
    public DateTime LastInteraction { get; set; }
}

public class PriceRangePreferenceDto
{
    public decimal MinPrice { get; set; }
    public decimal MaxPrice { get; set; }
    public decimal AverageSpent { get; set; }
    public decimal MedianSpent { get; set; }
    public int TransactionCount { get; set; }
}

public class UserInteractionDto
{
    public int Id { get; set; }
    public string UserId { get; set; } = string.Empty;
    public int ProductId { get; set; }
    public InteractionType Type { get; set; }
    public double Weight { get; set; }
    public DateTime Timestamp { get; set; }
    public string? SessionId { get; set; }
    public Dictionary<string, object> Context { get; set; } = new();
    public TimeSpan? Duration { get; set; }
    public string? Source { get; set; }
}

public class SimilarProductDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public double SimilarityScore { get; set; }
    public SimilarityType SimilarityType { get; set; }
    public List<string> CommonFeatures { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class RecommendationModelDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public ModelType Type { get; set; }
    public string Algorithm { get; set; } = string.Empty;
    public Dictionary<string, object> Parameters { get; set; } = new();
    public ModelStatus Status { get; set; }
    public double Accuracy { get; set; }
    public double Precision { get; set; }
    public double Recall { get; set; }
    public double F1Score { get; set; }
    public DateTime TrainedAt { get; set; }
    public DateTime? LastUsed { get; set; }
    public int UsageCount { get; set; }
    public string Version { get; set; } = string.Empty;
}

public class RecommendationCampaignDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public RecommendationType Type { get; set; }
    public List<RecommendationRuleDto> Rules { get; set; } = new();
    public List<string> TargetUserSegments { get; set; } = new();
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public bool IsActive { get; set; }
    public int Priority { get; set; }
    public Dictionary<string, object> Settings { get; set; } = new();
    public RecommendationCampaignStats Stats { get; set; } = new();
}

public class RecommendationRuleDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public List<RuleConditionDto> Conditions { get; set; } = new();
    public List<RuleActionDto> Actions { get; set; } = new();
    public int Priority { get; set; }
    public bool IsActive { get; set; }
}

public class RuleConditionDto
{
    public string Field { get; set; } = string.Empty;
    public string Operator { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty;
    public ConditionType Type { get; set; }
}

public class RuleActionDto
{
    public ActionType Type { get; set; }
    public Dictionary<string, object> Parameters { get; set; } = new();
}

public class RecommendationCampaignStats
{
    public int TotalRecommendations { get; set; }
    public int TotalViews { get; set; }
    public int TotalClicks { get; set; }
    public int TotalPurchases { get; set; }
    public double ClickThroughRate { get; set; }
    public double ConversionRate { get; set; }
    public decimal TotalRevenue { get; set; }
    public double AverageOrderValue { get; set; }
}

public class RecommendationAnalyticsDto
{
    public string Period { get; set; } = string.Empty;
    public int TotalRecommendations { get; set; }
    public int UniqueUsers { get; set; }
    public double AverageRecommendationsPerUser { get; set; }
    public Dictionary<RecommendationType, int> RecommendationsByType { get; set; } = new();
    public Dictionary<string, int> RecommendationsByAlgorithm { get; set; } = new();
    public Dictionary<string, double> PerformanceByAlgorithm { get; set; } = new();
    public double OverallClickThroughRate { get; set; }
    public double OverallConversionRate { get; set; }
    public decimal TotalRevenueFromRecommendations { get; set; }
    public List<TopPerformingProductDto> TopPerformingProducts { get; set; } = new();
    public Dictionary<string, object> AdditionalMetrics { get; set; } = new();
}

public class TopPerformingProductDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public int RecommendationCount { get; set; }
    public int ClickCount { get; set; }
    public int PurchaseCount { get; set; }
    public double ConversionRate { get; set; }
    public decimal Revenue { get; set; }
}

public class PersonalizationProfileDto
{
    public string UserId { get; set; } = string.Empty;
    public UserSegment Segment { get; set; }
    public List<string> Interests { get; set; } = new();
    public Dictionary<string, double> CategoryAffinities { get; set; } = new();
    public Dictionary<string, double> BrandAffinities { get; set; } = new();
    public PurchasingBehaviorDto PurchasingBehavior { get; set; } = new();
    public SeasonalPreferencesDto SeasonalPreferences { get; set; } = new();
    public Dictionary<string, object> CustomAttributes { get; set; } = new();
    public DateTime LastUpdated { get; set; }
    public double ProfileCompleteness { get; set; }
}

public class PurchasingBehaviorDto
{
    public decimal AverageOrderValue { get; set; }
    public int PurchaseFrequency { get; set; }
    public List<DayOfWeek> PreferredPurchaseDays { get; set; } = new();
    public List<int> PreferredPurchaseHours { get; set; } = new();
    public bool IsPriceConscious { get; set; }
    public bool PrefersPremiumBrands { get; set; }
    public bool IsImpulseBuyer { get; set; }
    public double BrandLoyalty { get; set; }
}

public class SeasonalPreferencesDto
{
    public Dictionary<string, List<string>> SeasonalCategories { get; set; } = new();
    public Dictionary<string, double> SeasonalSpending { get; set; } = new();
    public List<string> HolidayPreferences { get; set; } = new();
}

public class ABTestRecommendationDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<RecommendationVariantDto> Variants { get; set; } = new();
    public double TrafficSplit { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public ABTestStatus Status { get; set; }
    public string WinningVariant { get; set; } = string.Empty;
    public double ConfidenceLevel { get; set; }
    public ABTestResultsDto Results { get; set; } = new();
}

public class RecommendationVariantDto
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Algorithm { get; set; } = string.Empty;
    public Dictionary<string, object> Parameters { get; set; } = new();
    public double TrafficPercentage { get; set; }
}

public class ABTestResultsDto
{
    public Dictionary<string, VariantPerformanceDto> VariantPerformance { get; set; } = new();
    public string StatisticalSignificance { get; set; } = string.Empty;
    public DateTime LastUpdated { get; set; }
}

public class VariantPerformanceDto
{
    public int Impressions { get; set; }
    public int Clicks { get; set; }
    public int Conversions { get; set; }
    public double ClickThroughRate { get; set; }
    public double ConversionRate { get; set; }
    public decimal Revenue { get; set; }
    public double AverageOrderValue { get; set; }
}

// Enums
public enum RecommendationType
{
    PersonalizedForYou,
    SimilarProducts,
    FrequentlyBoughtTogether,
    TrendingNow,
    RecentlyViewed,
    BestSellers,
    NewArrivals,
    PriceDrops,
    BackInStock,
    SeasonalRecommendations,
    CategoryBased,
    BrandBased,
    CrossSell,
    UpSell,
    AbandonedCart,
    WishlistBased
}

public enum InteractionType
{
    View = 1,
    Click = 2,
    AddToCart = 3,
    Purchase = 5,
    AddToWishlist = 2,
    Share = 1,
    Review = 4,
    Search = 1,
    Compare = 2,
    RemoveFromCart = -1,
    Return = -3
}

public enum SimilarityType
{
    ContentBased,
    CollaborativeFiltering,
    Hybrid,
    Behavioral,
    Demographic
}

public enum ModelType
{
    CollaborativeFiltering,
    ContentBased,
    MatrixFactorization,
    DeepLearning,
    Hybrid,
    RuleBased,
    Clustering,
    AssociationRules
}

public enum ModelStatus
{
    Training,
    Ready,
    Deployed,
    Deprecated,
    Failed
}

public enum ConditionType
{
    UserAttribute,
    ProductAttribute,
    Behavioral,
    Contextual,
    Temporal
}

public enum ActionType
{
    BoostProducts,
    FilterProducts,
    ChangeAlgorithm,
    SetParameters,
    AddProducts,
    RemoveProducts
}

public class FrequentItemDto
{
    public int ProductId { get; set; }
    public string Name { get; set; } = string.Empty;
    public decimal Price { get; set; }
    public string ImageUrl { get; set; } = string.Empty;
    public double Confidence { get; set; }
    public int CoOccurrenceCount { get; set; }
    public bool IsInStock { get; set; } = true;
}

public class PersonalizationProfileDto
{
    public string UserId { get; set; } = string.Empty;
    public UserSegment Segment { get; set; }
    public double ProfileCompleteness { get; set; }
    public List<int> PreferredCategories { get; set; } = new();
    public List<string> PreferredBrands { get; set; } = new();
    public decimal? AveragePurchaseAmount { get; set; }
    public int TotalPurchases { get; set; }
    public DateTime? LastPurchaseDate { get; set; }
    public Dictionary<string, double>? Preferences { get; set; }
}

public class UserPreferenceDto
{
    public string UserId { get; set; } = string.Empty;
    public Dictionary<int, double> CategoryPreferences { get; set; } = new();
    public Dictionary<string, double> BrandPreferences { get; set; } = new();
    public Dictionary<string, double> FeaturePreferences { get; set; } = new();
    public decimal? MinPrice { get; set; }
    public decimal? MaxPrice { get; set; }
    public double? MinRating { get; set; }
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}

public enum UserSegment
{
    NewUser,
    CasualBuyer,
    RegularCustomer,
    VIPCustomer,
    PriceConscious,
    QualityFocused,
    TrendFollower,
    BargainHunter
}

public enum UserSegment
{
    NewUser,
    RegularCustomer,
    VIPCustomer,
    PriceConscious,
    PremiumBuyer,
    ImpulseBuyer,
    BrandLoyal,
    BargainHunter,
    TrendFollower,
    Inactive
}

public enum ABTestStatus
{
    Draft,
    Running,
    Paused,
    Completed,
    Cancelled
}
