using System;
using System.Collections.Generic;

namespace NafaPlace.Order.Domain
{
    public class Order
    {
        public int Id { get; set; }
        public string UserId { get; set; } = string.Empty;
        public DateTime OrderDate { get; set; }
        public decimal TotalAmount { get; set; }
        public string Currency { get; set; } = string.Empty;
        public OrderStatus Status { get; set; }
        public PaymentMethod PaymentMethod { get; set; }
        public PaymentStatus PaymentStatus { get; set; }
        public string? PaymentTransactionId { get; set; }
        public DateTime? PaymentDate { get; set; }
        public ShippingAddress? ShippingAddress { get; set; }
        public List<OrderItem> OrderItems { get; set; } = new();
    }

    public enum OrderStatus
    {
        Pending,
        Paid,
        Shipped,
        Delivered,
        Cancelled
    }

    public enum PaymentMethod
    {
        CashOnDelivery,
        OrangeMoney,
        Stripe
    }

    public enum PaymentStatus
    {
        Pending,
        Processing,
        Completed,
        Failed,
        Cancelled,
        Refunded
    }

    public class ShippingAddress
    {
        public string FullName { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public string City { get; set; } = string.Empty;
        public string Country { get; set; } = string.Empty;
        public string PostalCode { get; set; } = string.Empty;
        public string? PhoneNumber { get; set; }
    }
}