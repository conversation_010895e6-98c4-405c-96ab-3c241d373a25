using NafaPlace.Catalog.Application.DTOs.Seller;
using NafaPlace.Catalog.Application.Common.Interfaces;
using NafaPlace.Catalog.Domain.Models;
using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;

namespace NafaPlace.Catalog.Application.Services
{
    public class SellerService : ISellerService
    {
        private readonly ICatalogDbContext _context;
        private readonly IProductImageService _imageService;

        public SellerService(ICatalogDbContext context, IProductImageService imageService)
        {
            _context = context;
            _imageService = imageService;
        }

        public async Task<UploadProfilePictureResponse> UploadProfilePictureAsync(UploadProfilePictureRequest request)
        {
            var seller = await _context.Sellers.FirstOrDefaultAsync(s => s.UserId == request.SellerId);

            if (seller == null)
            {
                seller = new Seller
                {
                    UserId = request.SellerId,
                    Name = "",
                    Email = "",
                    PhoneNumber = "",
                    Address = ""
                };
                _context.Sellers.Add(seller);
            }

            var imageUrl = await _imageService.UploadImageAsync(request.Image);
            seller.ProfilePictureUrl = imageUrl;

            await _context.SaveChangesAsync(default);

            return new UploadProfilePictureResponse { Url = imageUrl };
        }
    }
}