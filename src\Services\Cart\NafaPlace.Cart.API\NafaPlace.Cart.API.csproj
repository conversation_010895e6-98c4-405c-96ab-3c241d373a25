<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.2" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\NafaPlace.Cart.Application\NafaPlace.Cart.Application.csproj" />
    <ProjectReference Include="..\NafaPlace.Cart.Infrastructure\NafaPlace.Cart.Infrastructure.csproj" />
  </ItemGroup>

</Project>
