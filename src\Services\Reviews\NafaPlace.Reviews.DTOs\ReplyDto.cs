namespace NafaPlace.Reviews.DTOs;
using System;

public class ReplyDto
{
    public int Id { get; set; }
    public int ReviewId { get; set; }
    public string UserId { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public bool IsFromSeller { get; set; }
}

public class CreateReplyRequest
{
    public int ReviewId { get; set; }
    public string Content { get; set; } = string.Empty;
    public bool IsFromSeller { get; set; }
}

public class UpdateReplyRequest
{
    public string Content { get; set; } = string.Empty;
} 