using NafaPlace.Cart.Application.DTOs;

namespace NafaPlace.Cart.Application.Services;

public interface ICartService
{
    Task<ShoppingCartDto> GetCartAsync(string userId);
    Task<ShoppingCartDto> AddItemToCartAsync(string userId, AddToCartRequest request);
    Task<ShoppingCartDto> UpdateItemQuantityAsync(string userId, UpdateCartItemRequest request);
    Task<ShoppingCartDto> RemoveItemFromCartAsync(string userId, int productId);
    Task<bool> ClearCartAsync(string userId);
    Task<CartSummaryDto> GetCartSummaryAsync(string userId);
    Task<bool> ValidateCartAsync(string userId);
    Task<ShoppingCartDto> ApplyCouponAsync(string userId, string couponCode);
    Task<ShoppingCartDto> RemoveCouponAsync(string userId);
    Task<decimal> CalculateShippingAsync(string userId, string shippingAddress);
}
