@page "/logout"
@inject IAuthService AuthService
@inject NavigationManager NavigationManager
@inject NotificationService NotificationService

<PageTitle>Déconnexion - NafaPlace</PageTitle>

<div class="d-flex justify-content-center mt-5">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Déconnexion en cours...</span>
    </div>
</div>

@code {
    protected override async Task OnInitializedAsync()
    {
        try
        {
            await AuthService.LogoutAsync();
            NotificationService.ShowSuccess("Vous avez été déconnecté avec succès.");
        }
        catch (Exception ex)
        {
            NotificationService.ShowError($"Erreur lors de la déconnexion: {ex.Message}");
        }
        finally
        {
            NavigationManager.NavigateTo("/login");
        }
    }
}
