@page "/wishlist"
@using NafaPlace.Web.Models.Wishlist
@using NafaPlace.Web.Models.Cart
@using NafaPlace.Web.Services
@using System.Security.Claims
@inject IWishlistService WishlistService
@inject ICartService CartService
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime
@inject AuthenticationStateProvider AuthenticationStateProvider

<PageTitle>Ma Liste de Souhaits - NafaPlace</PageTitle>

<div class="container my-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="bi bi-heart text-danger me-2"></i>Ma Liste de Souhaits</h2>
                @if (_wishlist?.Items?.Any() == true)
                {
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary" @onclick="MoveAllToCart">
                            <i class="bi bi-cart-plus me-1"></i>Tout ajouter au panier
                        </button>
                        <button class="btn btn-outline-danger" @onclick="ClearWishlist">
                            <i class="bi bi-trash me-1"></i>Vider la liste
                        </button>
                    </div>
                }
            </div>

            @if (_isLoading)
            {
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                    <p class="mt-2">Chargement de votre liste de souhaits...</p>
                </div>
            }
            else if (_wishlist?.Items?.Any() != true)
            {
                <div class="text-center py-5">
                    <i class="bi bi-heart display-1 text-muted"></i>
                    <h4 class="mt-3">Votre liste de souhaits est vide</h4>
                    <p class="text-muted">Découvrez nos produits et ajoutez vos favoris ici</p>
                    <a href="/catalog/products" class="btn btn-primary">
                        <i class="bi bi-search me-1"></i>Découvrir nos produits
                    </a>
                </div>
            }
            else
            {
                <div class="row">
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-list-ul me-2"></i>
                                    Mes Articles (@_wishlist.ItemCount)
                                </h5>
                            </div>
                            <div class="card-body p-0">
                                @foreach (var item in _wishlist.Items)
                                {
                                    <div class="border-bottom p-3">
                                        <div class="row align-items-center">
                                            <div class="col-md-2">
                                                @if (!string.IsNullOrEmpty(item.ProductImageUrl))
                                                {
                                                    <img src="@item.ProductImageUrl" alt="@item.ProductName" class="img-fluid rounded" style="max-height: 80px;">
                                                }
                                                else
                                                {
                                                    <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 80px;">
                                                        <i class="bi bi-image text-muted"></i>
                                                    </div>
                                                }
                                            </div>
                                            <div class="col-md-6">
                                                <h6 class="mb-1">
                                                    <a href="/catalog/products/@item.ProductId" class="text-decoration-none">
                                                        @item.ProductName
                                                    </a>
                                                </h6>
                                                @if (!string.IsNullOrEmpty(item.ProductBrand))
                                                {
                                                    <small class="text-muted">Marque: @item.ProductBrand</small><br>
                                                }
                                                @if (!string.IsNullOrEmpty(item.CategoryName))
                                                {
                                                    <small class="text-muted">Catégorie: @item.CategoryName</small><br>
                                                }
                                                <small class="text-muted">Ajouté le @item.AddedAt.ToString("dd/MM/yyyy")</small>
                                                @if (!item.IsAvailable)
                                                {
                                                    <br><span class="badge bg-warning">Indisponible</span>
                                                }
                                            </div>
                                            <div class="col-md-2 text-center">
                                                <h5 class="text-primary mb-0">
                                                    @item.ProductPrice.ToString("N0") @item.Currency
                                                </h5>
                                            </div>
                                            <div class="col-md-2 text-end">
                                                <div class="btn-group-vertical" role="group">
                                                    @if (item.IsAvailable)
                                                    {
                                                        <button class="btn btn-sm btn-primary" @onclick="() => AddToCart(item)">
                                                            <i class="bi bi-cart-plus"></i>
                                                        </button>
                                                    }
                                                    <button class="btn btn-sm btn-outline-danger" @onclick="() => RemoveFromWishlist(item.ProductId)">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-info-circle me-2"></i>Résumé
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Nombre d'articles:</span>
                                    <strong>@_wishlist.ItemCount</strong>
                                </div>
                                <div class="d-flex justify-content-between mb-3">
                                    <span>Valeur totale:</span>
                                    <strong class="text-primary">@_wishlist.TotalValue.ToString("N0") @_wishlist.Currency</strong>
                                </div>
                                <hr>
                                <div class="d-grid gap-2">
                                    <button class="btn btn-primary" @onclick="MoveAllToCart">
                                        <i class="bi bi-cart-plus me-1"></i>Tout ajouter au panier
                                    </button>
                                    <a href="/catalog/products" class="btn btn-outline-primary">
                                        <i class="bi bi-search me-1"></i>Continuer mes achats
                                    </a>
                                </div>
                            </div>
                        </div>

                        @if (_recentItems?.Any() == true)
                        {
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="bi bi-clock me-2"></i>Récemment ajoutés
                                    </h6>
                                </div>
                                <div class="card-body">
                                    @foreach (var item in _recentItems.Take(3))
                                    {
                                        <div class="d-flex align-items-center mb-2">
                                            <div class="me-2">
                                                @if (!string.IsNullOrEmpty(item.ProductImageUrl))
                                                {
                                                    <img src="@item.ProductImageUrl" alt="@item.ProductName" class="rounded" style="width: 40px; height: 40px; object-fit: cover;">
                                                }
                                                else
                                                {
                                                    <div class="bg-light rounded d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                        <i class="bi bi-image text-muted"></i>
                                                    </div>
                                                }
                                            </div>
                                            <div class="flex-grow-1">
                                                <small class="d-block text-truncate">@item.ProductName</small>
                                                <small class="text-muted">@item.ProductPrice.ToString("N0") @item.Currency</small>
                                            </div>
                                        </div>
                                    }
                                </div>
                            </div>
                        }
                    </div>
                </div>
            }
        </div>
    </div>
</div>

@code {
    private WishlistDto? _wishlist;
    private List<WishlistItemDto>? _recentItems;
    private bool _isLoading = true;
    private string? _userId;

    protected override async Task OnInitializedAsync()
    {
        await LoadUserInfo();
        await LoadWishlistData();
    }

    private async Task LoadUserInfo()
    {
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;

        if (user.Identity?.IsAuthenticated == true)
        {
            _userId = user.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        }
        else
        {
            // Redirect to login if not authenticated
            NavigationManager.NavigateTo("/auth/login");
        }
    }

    private async Task LoadWishlistData()
    {
        if (string.IsNullOrEmpty(_userId)) return;

        _isLoading = true;
        try
        {
            var wishlistTask = WishlistService.GetUserWishlistAsync(_userId);
            var recentTask = WishlistService.GetRecentlyAddedItemsAsync(_userId, 5);

            await Task.WhenAll(wishlistTask, recentTask);

            _wishlist = await wishlistTask;
            _recentItems = await recentTask;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement de la wishlist: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("showToast", "Erreur lors du chargement de la liste de souhaits", "error");
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    private async Task RemoveFromWishlist(int productId)
    {
        if (string.IsNullOrEmpty(_userId)) return;

        try
        {
            var success = await WishlistService.RemoveFromWishlistAsync(_userId, productId);
            if (success)
            {
                await LoadWishlistData();
                await JSRuntime.InvokeVoidAsync("showToast", "Produit retiré de la liste de souhaits", "success");
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("showToast", "Erreur lors de la suppression", "error");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la suppression: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("showToast", "Erreur lors de la suppression", "error");
        }
    }

    private async Task AddToCart(WishlistItemDto item)
    {
        if (string.IsNullOrEmpty(_userId)) return;

        try
        {
            var cartItem = new CartItemCreateDto
            {
                ProductId = item.ProductId,
                ProductName = item.ProductName,
                Price = item.ProductPrice,
                Quantity = 1
            };

            await CartService.AddItemToCartAsync(_userId, cartItem);
            await JSRuntime.InvokeVoidAsync("showToast", "Produit ajouté au panier", "success");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de l'ajout au panier: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("showToast", "Erreur lors de l'ajout au panier", "error");
        }
    }

    private async Task MoveAllToCart()
    {
        if (string.IsNullOrEmpty(_userId) || _wishlist?.Items?.Any() != true) return;

        try
        {
            var availableItems = _wishlist.Items.Where(i => i.IsAvailable).ToList();
            
            foreach (var item in availableItems)
            {
                var cartItem = new CartItemCreateDto
                {
                    ProductId = item.ProductId,
                    ProductName = item.ProductName,
                    Price = item.ProductPrice,
                    Quantity = 1
                };

                await CartService.AddItemToCartAsync(_userId, cartItem);
            }

            await JSRuntime.InvokeVoidAsync("showToast", $"{availableItems.Count} produits ajoutés au panier", "success");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de l'ajout au panier: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("showToast", "Erreur lors de l'ajout au panier", "error");
        }
    }

    private async Task ClearWishlist()
    {
        if (string.IsNullOrEmpty(_userId)) return;

        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", "Êtes-vous sûr de vouloir vider votre liste de souhaits ?");
        if (!confirmed) return;

        try
        {
            var success = await WishlistService.ClearWishlistAsync(_userId);
            if (success)
            {
                await LoadWishlistData();
                await JSRuntime.InvokeVoidAsync("showToast", "Liste de souhaits vidée", "success");
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("showToast", "Erreur lors de la suppression", "error");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la suppression: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("showToast", "Erreur lors de la suppression", "error");
        }
    }
}
