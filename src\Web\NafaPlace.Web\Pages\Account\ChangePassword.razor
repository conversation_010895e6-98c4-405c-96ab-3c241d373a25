@page "/account/change-password"
@using Microsoft.AspNetCore.Components.Authorization
@using NafaPlace.Web.Models.Auth
@using NafaPlace.Web.Services
@inject IAuthService AuthService
@inject NavigationManager NavigationManager
@inject ILocalStorageService LocalStorage

<PageTitle>NafaPlace - Changer de <PERSON>t de <PERSON>e</PageTitle>

<AuthorizeView Context="authContext">
    <Authorized>
        <div class="container py-5">
            <div class="row justify-content-center">
                <div class="col-md-8 col-lg-6">
                    <div class="card shadow">
                        <div class="card-header bg-primary text-white">
                            <h4 class="mb-0">Changer de <PERSON>t de Passe</h4>
                        </div>
                        <div class="card-body">
                            @if (!string.IsNullOrEmpty(errorMessage))
                            {
                                <div class="alert alert-danger" role="alert">
                                    @errorMessage
                                </div>
                            }

                            @if (!string.IsNullOrEmpty(successMessage))
                            {
                                <div class="alert alert-success" role="alert">
                                    @successMessage
                                </div>
                            }

                            <EditForm Model="@changePasswordRequest" OnValidSubmit="SubmitChangePassword">
                                <DataAnnotationsValidator />
                                <ValidationSummary />

                                <div class="mb-3">
                                    <label for="currentPassword" class="form-label">Mot de passe actuel</label>
                                    <InputText type="password" id="currentPassword" @bind-Value="changePasswordRequest.CurrentPassword" class="form-control" />
                                    <ValidationMessage For="@(() => changePasswordRequest.CurrentPassword)" />
                                </div>

                                <div class="mb-3">
                                    <label for="newPassword" class="form-label">Nouveau mot de passe</label>
                                    <InputText type="password" id="newPassword" @bind-Value="changePasswordRequest.NewPassword" class="form-control" />
                                    <ValidationMessage For="@(() => changePasswordRequest.NewPassword)" />
                                    <small class="text-muted">Le mot de passe doit contenir au moins 6 caractères.</small>
                                </div>

                                <div class="mb-3">
                                    <label for="confirmPassword" class="form-label">Confirmer le nouveau mot de passe</label>
                                    <InputText type="password" id="confirmPassword" @bind-Value="changePasswordRequest.ConfirmPassword" class="form-control" />
                                    <ValidationMessage For="@(() => changePasswordRequest.ConfirmPassword)" />
                                </div>

                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-primary" disabled="@isLoading">
                                        @if (isLoading)
                                        {
                                            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                            <span class="ms-2">Chargement...</span>
                                        }
                                        else
                                        {
                                            <span>Changer de mot de passe</span>
                                        }
                                    </button>
                                    <a href="/account/profile" class="btn btn-outline-secondary">Retour au profil</a>
                                </div>
                            </EditForm>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </Authorized>
    <NotAuthorized>
        <div class="container py-5 text-center">
            <h1>Accès non autorisé</h1>
            <p class="lead">Vous devez être connecté pour accéder à cette page.</p>
            <a href="/auth/login" class="btn btn-primary">Se connecter</a>
        </div>
    </NotAuthorized>
</AuthorizeView>

@code {
    private ChangePasswordRequest changePasswordRequest = new ChangePasswordRequest();
    private bool isLoading = false;
    private string errorMessage = string.Empty;
    private string successMessage = string.Empty;

    private async Task SubmitChangePassword()
    {
        try
        {
            isLoading = true;
            errorMessage = string.Empty;
            successMessage = string.Empty;

            var result = await AuthService.ChangePasswordAsync(changePasswordRequest);
            
            if (result)
            {
                successMessage = "Votre mot de passe a été changé avec succès.";
                changePasswordRequest = new ChangePasswordRequest(); // Réinitialiser le formulaire
            }
            else
            {
                errorMessage = "Échec du changement de mot de passe. Veuillez vérifier que votre mot de passe actuel est correct.";
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Une erreur s'est produite: {ex.Message}";
            Console.WriteLine(errorMessage);
        }
        finally
        {
            isLoading = false;
        }
    }
}
