using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using Blazored.LocalStorage;
using Microsoft.AspNetCore.Components.Authorization;

namespace NafaPlace.Web.Services;

public class TokenExpirationService : IDisposable
{
    private readonly ILocalStorageService _localStorage;
    private readonly AuthenticationStateProvider _authStateProvider;
    private readonly ILogger<TokenExpirationService> _logger;
    private Timer? _timer;
    private bool _disposed = false;

    public TokenExpirationService(
        ILocalStorageService localStorage,
        AuthenticationStateProvider authStateProvider,
        ILogger<TokenExpirationService> logger)
    {
        _localStorage = localStorage;
        _authStateProvider = authStateProvider;
        _logger = logger;
    }

    public void StartTokenExpirationCheck()
    {
        // Vérifier toutes les 30 secondes
        _timer = new Timer(CheckTokenExpiration, null, TimeSpan.Zero, TimeSpan.FromSeconds(30));
        _logger.LogInformation("Service de vérification d'expiration des tokens démarré");
    }

    private async void CheckTokenExpiration(object? state)
    {
        try
        {
            var token = await _localStorage.GetItemAsStringAsync("authToken");
            
            if (string.IsNullOrEmpty(token))
            {
                return; // Pas de token, rien à vérifier
            }

            // Supprimer les guillemets éventuels
            token = token.Trim('"');

            // Vérifier si le token est expiré
            if (IsTokenExpired(token))
            {
                _logger.LogWarning("Token expiré détecté, déconnexion automatique de l'utilisateur");
                
                // Supprimer le token du localStorage
                await _localStorage.RemoveItemAsync("authToken");
                
                // Déclencher la déconnexion
                if (_authStateProvider is CustomAuthStateProvider customProvider)
                {
                    customProvider.MarkUserAsLoggedOut();
                }
                
                _logger.LogInformation("Utilisateur déconnecté automatiquement suite à l'expiration du token");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la vérification d'expiration du token");
        }
    }

    private bool IsTokenExpired(string token)
    {
        try
        {
            var handler = new JwtSecurityTokenHandler();
            var jsonToken = handler.ReadJwtToken(token);
            
            var expiry = jsonToken.Claims.FirstOrDefault(c => c.Type == "exp")?.Value;
            if (expiry != null && long.TryParse(expiry, out var expiryTimestamp))
            {
                var expiryDate = DateTimeOffset.FromUnixTimeSeconds(expiryTimestamp).DateTime;
                return expiryDate < DateTime.UtcNow;
            }
            
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la vérification de l'expiration du token");
            return true; // En cas d'erreur, considérer le token comme expiré
        }
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _timer?.Dispose();
            _disposed = true;
            _logger.LogInformation("Service de vérification d'expiration des tokens arrêté");
        }
    }
}
