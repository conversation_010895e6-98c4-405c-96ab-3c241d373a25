using Microsoft.EntityFrameworkCore;
using NafaPlace.Catalog.Infrastructure.Persistence;
using NafaPlace.Catalog.Application.Services;
using NafaPlace.Catalog.Infrastructure.Services;
using NafaPlace.Catalog.Application.Common.Interfaces;
using Azure.Storage.Blobs;
using NafaPlace.Catalog.Infrastructure;
using NafaPlace.Catalog.Application;
using Microsoft.OpenApi.Models;
using NafaPlace.Catalog.Infrastructure.Persistence.Initialization; // Ajout de l'import pour CatalogDbInitializer

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
// Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
builder.Services.AddOpenApi();

// Add Swagger services
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo { Title = "NafaPlace.Catalog.API", Version = "v1" });
});

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowPortals", policy =>
    {
        policy.WithOrigins(
                "http://localhost:8080",   // Client Portal
                "http://localhost:8081",   // Admin Portal
                "http://localhost:8082",   // Seller Portal
                "https://nafaplace-web-test.fly.dev",     // Production Client Portal
                "https://nafaplace-admin-test.fly.dev",   // Production Admin Portal
                "https://nafaplace-seller-test.fly.dev"   // Production Seller Portal
            )
            .AllowAnyHeader()
            .AllowAnyMethod()
            .AllowCredentials();
    });
});

// Add Application services (including AutoMapper)
builder.Services.AddApplication();

// Add Infrastructure services (including DbContext and ICatalogDbContext)
builder.Services.AddInfrastructure(builder.Configuration);

// Add Azure Blob Storage
builder.Services.AddSingleton(x => new BlobServiceClient(builder.Configuration.GetConnectionString("AzureStorage")));

// Add controllers
builder.Services.AddControllers();

builder.Services.Configure<Microsoft.AspNetCore.Http.Features.FormOptions>(options =>
{
    options.MultipartBodyLengthLimit = int.MaxValue;
});

var app = builder.Build();

// Initialisation de la base de données
using (var scope = app.Services.CreateScope())
{
    var services = scope.ServiceProvider;
    try
    {
        var context = services.GetRequiredService<CatalogDbContext>();
        var logger = services.GetRequiredService<ILogger<Program>>();
        
        // Créer un initialiseur de base de données
        var dbInitializer = new CatalogDbInitializer(context, services.GetRequiredService<ILogger<CatalogDbInitializer>>());
        
        // Initialiser la base de données (migrations ou création)
        dbInitializer.InitializeAsync().Wait();
        
        // Ajouter les données de départ (catégories, etc.)
        dbInitializer.SeedAsync().Wait();
        
        logger.LogInformation("Base de données initialisée avec succès.");
    }
    catch (Exception ex)
    {
        var logger = services.GetRequiredService<ILogger<Program>>();
        logger.LogError(ex, "Une erreur s'est produite lors de l'initialisation de la base de données.");
    }
}

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
    app.UseSwagger();
    app.UseSwaggerUI(c => 
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "NafaPlace.Catalog.API v1");
        c.RoutePrefix = string.Empty; // Définir la page d'accueil comme Swagger
    });
}

// Use CORS before other middleware
app.UseCors("AllowPortals");

// Servir les fichiers statiques (images uploadées)
app.UseStaticFiles();

app.UseHttpsRedirection();
app.UseAuthorization();

app.MapControllers();

app.Run();
