FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

COPY ["src/Web/SellerPortal/NafaPlace.SellerPortal/NafaPlace.SellerPortal.csproj", "src/Web/SellerPortal/NafaPlace.SellerPortal/"]
RUN dotnet restore "src/Web/SellerPortal/NafaPlace.SellerPortal/NafaPlace.SellerPortal.csproj"

COPY . .
WORKDIR "/src/Web/SellerPortal/NafaPlace.SellerPortal"
RUN dotnet build "NafaPlace.SellerPortal.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "NafaPlace.SellerPortal.csproj" -c Release -o /app/publish

FROM nginx:alpine AS final
WORKDIR /usr/share/nginx/html
COPY --from=publish /app/publish/wwwroot .
COPY src/Web/SellerPortal/nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
EXPOSE 443
