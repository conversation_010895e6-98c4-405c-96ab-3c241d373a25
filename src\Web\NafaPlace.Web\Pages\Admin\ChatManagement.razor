@page "/admin/chat-management"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components
@using NafaPlace.Web.Services
@attribute [Authorize(Roles = "Admin")]
@inject IChatService ChatService
@inject IJSRuntime JSRuntime
@inject ILogger<ChatManagement> Logger

<PageTitle>Gestion du Chat - Admin NafaPlace</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="bi bi-chat-square-dots"></i> Gestion du Chat</h2>
                <div class="btn-group">
                    <button class="btn btn-outline-primary" @onclick="RefreshData">
                        <i class="bi bi-arrow-clockwise"></i> Actualiser
                    </button>
                    <button class="btn btn-primary" @onclick="ShowAgentModal">
                        <i class="bi bi-person-plus"></i> Nouvel Agent
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Conversations Actives</h6>
                            <h3>@activeConversations</h3>
                        </div>
                        <i class="bi bi-chat-dots fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Agents En Ligne</h6>
                            <h3>@onlineAgents</h3>
                        </div>
                        <i class="bi bi-person-check fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">En Attente</h6>
                            <h3>@waitingConversations</h3>
                        </div>
                        <i class="bi bi-clock fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Satisfaction Moyenne</h6>
                            <h3>@averageSatisfaction.ToString("F1")</h3>
                        </div>
                        <i class="bi bi-star fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Onglets -->
    <ul class="nav nav-tabs mb-3">
        <li class="nav-item">
            <button class="nav-link @(activeTab == "agents" ? "active" : "")" @onclick="@(() => SetActiveTab("agents"))">
                <i class="bi bi-people"></i> Agents
            </button>
        </li>
        <li class="nav-item">
            <button class="nav-link @(activeTab == "conversations" ? "active" : "")" @onclick="@(() => SetActiveTab("conversations"))">
                <i class="bi bi-chat-square"></i> Conversations
            </button>
        </li>
        <li class="nav-item">
            <button class="nav-link @(activeTab == "analytics" ? "active" : "")" @onclick="@(() => SetActiveTab("analytics"))">
                <i class="bi bi-graph-up"></i> Analytiques
            </button>
        </li>
    </ul>

    <!-- Contenu des onglets -->
    @if (activeTab == "agents")
    {
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-people"></i> Gestion des Agents</h5>
            </div>
            <div class="card-body">
                @if (agents?.Any() == true)
                {
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Agent</th>
                                    <th>Statut</th>
                                    <th>Département</th>
                                    <th>Conversations</th>
                                    <th>Dernière Activité</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var agent in agents)
                                {
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @if (!string.IsNullOrEmpty(agent.Avatar))
                                                {
                                                    <img src="@agent.Avatar" class="rounded-circle me-2" width="32" height="32" alt="Avatar">
                                                }
                                                else
                                                {
                                                    <div class="bg-secondary rounded-circle me-2 d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;">
                                                        <i class="bi bi-person text-white"></i>
                                                    </div>
                                                }
                                                <div>
                                                    <strong>@agent.Name</strong><br>
                                                    <small class="text-muted">@agent.Email</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-@GetStatusColor(agent.Status)">
                                                @GetStatusText(agent.Status)
                                            </span>
                                        </td>
                                        <td>@string.Join(", ", agent.Departments)</td>
                                        <td>
                                            <span class="badge bg-info">
                                                @agent.CurrentChatCount/@agent.MaxConcurrentChats
                                            </span>
                                        </td>
                                        <td>
                                            @if (agent.LastActiveAt.HasValue)
                                            {
                                                <small>@agent.LastActiveAt.Value.ToString("dd/MM/yyyy HH:mm")</small>
                                            }
                                            else
                                            {
                                                <small class="text-muted">Jamais</small>
                                            }
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" @onclick="() => EditAgent(agent)">
                                                    <i class="bi bi-pencil"></i>
                                                </button>
                                                <button class="btn btn-outline-info" @onclick="() => ViewAgentStats(agent)">
                                                    <i class="bi bi-graph-up"></i>
                                                </button>
                                                <button class="btn btn-outline-warning" @onclick="() => ToggleAgentStatus(agent)">
                                                    <i class="bi bi-power"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-4">
                        <i class="bi bi-people fs-1 text-muted"></i>
                        <p class="text-muted">Aucun agent trouvé</p>
                        <button class="btn btn-primary" @onclick="ShowAgentModal">
                            <i class="bi bi-person-plus"></i> Ajouter un Agent
                        </button>
                    </div>
                }
            </div>
        </div>
    }
    else if (activeTab == "conversations")
    {
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-chat-square"></i> Conversations Actives</h5>
            </div>
            <div class="card-body">
                <!-- Contenu des conversations -->
                <p class="text-muted">Fonctionnalité en cours de développement...</p>
            </div>
        </div>
    }
    else if (activeTab == "analytics")
    {
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-graph-up"></i> Analytiques du Chat</h5>
            </div>
            <div class="card-body">
                <!-- Contenu des analytiques -->
                <p class="text-muted">Fonctionnalité en cours de développement...</p>
            </div>
        </div>
    }
</div>

@code {
    private string activeTab = "agents";
    private List<ChatAgentDto>? agents;
    private int activeConversations = 0;
    private int onlineAgents = 0;
    private int waitingConversations = 0;
    private double averageSatisfaction = 4.2;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        try
        {
            agents = await ChatService.GetAvailableAgentsAsync();
            onlineAgents = agents?.Count(a => a.Status == "Online") ?? 0;
            
            // TODO: Charger les autres statistiques
            activeConversations = 12; // Exemple
            waitingConversations = 3; // Exemple
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors du chargement des données");
        }
    }

    private async Task RefreshData()
    {
        await LoadData();
        StateHasChanged();
    }

    private void SetActiveTab(string tab)
    {
        activeTab = tab;
    }

    private string GetStatusColor(string status)
    {
        return status switch
        {
            "Online" => "success",
            "Busy" => "warning",
            "Away" => "secondary",
            "Offline" => "danger",
            _ => "secondary"
        };
    }

    private string GetStatusText(string status)
    {
        return status switch
        {
            "Online" => "En ligne",
            "Busy" => "Occupé",
            "Away" => "Absent",
            "Offline" => "Hors ligne",
            _ => status
        };
    }

    private async Task ShowAgentModal()
    {
        // TODO: Implémenter la modal d'ajout d'agent
        await JSRuntime.InvokeVoidAsync("alert", "Fonctionnalité en cours de développement");
    }

    private async Task EditAgent(ChatAgentDto agent)
    {
        // TODO: Implémenter l'édition d'agent
        await JSRuntime.InvokeVoidAsync("alert", $"Édition de {agent.Name}");
    }

    private async Task ViewAgentStats(ChatAgentDto agent)
    {
        // TODO: Implémenter les statistiques d'agent
        await JSRuntime.InvokeVoidAsync("alert", $"Statistiques de {agent.Name}");
    }

    private async Task ToggleAgentStatus(ChatAgentDto agent)
    {
        // TODO: Implémenter le changement de statut
        await JSRuntime.InvokeVoidAsync("alert", $"Changement de statut pour {agent.Name}");
    }
}
