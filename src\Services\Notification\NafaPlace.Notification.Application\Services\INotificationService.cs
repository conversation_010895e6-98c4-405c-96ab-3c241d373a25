using NafaPlace.Notification.Application.DTOs;

namespace NafaPlace.Notification.Application.Services;

public interface INotificationService
{
    // Gestion des notifications
    Task<int> CreateNotificationAsync(CreateNotificationDto notification);
    Task<List<int>> CreateBulkNotificationAsync(BulkNotificationDto bulkNotification);
    Task<NotificationDto?> GetNotificationAsync(int id);
    Task<List<NotificationDto>> GetNotificationsAsync(NotificationFilterDto filter);
    Task<int> GetUnreadCountAsync(string userId);
    Task<bool> MarkAsReadAsync(int notificationId, string userId);
    Task<bool> MarkAllAsReadAsync(string userId);
    Task<bool> DeleteNotificationAsync(int notificationId, string userId);
    
    // Envoi de notifications
    Task<bool> SendNotificationAsync(int notificationId);
    Task<bool> SendImmediateNotificationAsync(CreateNotificationDto notification);
    Task<int> SendBulkNotificationsAsync(List<CreateNotificationDto> notifications);
    Task ProcessPendingNotificationsAsync();
    Task ProcessScheduledNotificationsAsync();
    
    // Notifications temps réel
    Task SendRealTimeNotificationAsync(string userId, RealTimeNotificationDto notification);
    Task SendRealTimeNotificationToGroupAsync(string groupName, RealTimeNotificationDto notification);
    Task SendRealTimeNotificationToAllAsync(RealTimeNotificationDto notification);
    Task JoinGroupAsync(string userId, string groupName);
    Task LeaveGroupAsync(string userId, string groupName);
    
    // Templates de notifications
    Task<int> CreateTemplateAsync(NotificationTemplateDto template);
    Task<NotificationTemplateDto?> GetTemplateAsync(int templateId);
    Task<List<NotificationTemplateDto>> GetTemplatesAsync(NotificationType? type = null);
    Task<bool> UpdateTemplateAsync(NotificationTemplateDto template);
    Task<bool> DeleteTemplateAsync(int templateId);
    Task<string> RenderTemplateAsync(int templateId, Dictionary<string, object> data);
    
    // Préférences utilisateur
    Task<List<NotificationPreferenceDto>> GetUserPreferencesAsync(string userId);
    Task<bool> UpdateUserPreferenceAsync(string userId, NotificationPreferenceDto preference);
    Task<bool> UpdateUserPreferencesAsync(string userId, List<NotificationPreferenceDto> preferences);
    Task<bool> CanSendNotificationAsync(string userId, NotificationType type, NotificationChannel channel);
    
    // Statistiques et analytics
    Task<NotificationStatsDto> GetNotificationStatsAsync(DateTime? startDate = null, DateTime? endDate = null, string? userId = null);
    Task<Dictionary<string, object>> GetDeliveryMetricsAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<List<NotificationDeliveryDto>> GetDeliveryHistoryAsync(int notificationId);
    
    // Gestion des erreurs et retry
    Task<bool> RetryFailedNotificationAsync(int notificationId);
    Task<int> RetryFailedNotificationsAsync(DateTime? olderThan = null);
    Task<int> CleanupExpiredNotificationsAsync();
    Task<int> CleanupOldNotificationsAsync(int daysToKeep = 30);
    
    // Notifications spécialisées
    Task SendWelcomeNotificationAsync(string userId, string userName, string email);
    Task SendOrderNotificationAsync(string userId, int orderId, string orderStatus, decimal amount);
    Task SendPaymentNotificationAsync(string userId, int orderId, string paymentStatus, decimal amount);
    Task SendProductNotificationAsync(string sellerId, int productId, string productName, string status);
    Task SendStockAlertNotificationAsync(string sellerId, int productId, string productName, int currentStock);
    Task SendReviewNotificationAsync(string sellerId, int productId, string productName, int rating);
    Task SendSystemAlertNotificationAsync(string message, NotificationPriority priority = NotificationPriority.Normal);
    Task SendMarketingNotificationAsync(List<string> userIds, string title, string message, string? actionUrl = null);
    
    // Notifications par canal
    Task<bool> SendEmailNotificationAsync(EmailNotificationDto email);
    Task<bool> SendSmsNotificationAsync(SmsNotificationDto sms);
    Task<bool> SendPushNotificationAsync(string userId, string title, string body, Dictionary<string, object>? data = null);
    Task<int> SendBulkEmailNotificationsAsync(List<EmailNotificationDto> emails);
    Task<int> SendBulkPushNotificationsAsync(List<string> userIds, string title, string body, Dictionary<string, object>? data = null);
    
    // Gestion des abonnements push
    Task<bool> SubscribeToPushAsync(string userId, PushSubscriptionDto subscription);
    Task<bool> UnsubscribeFromPushAsync(string userId, string endpoint);
    Task<List<PushSubscriptionDto>> GetUserPushSubscriptionsAsync(string userId);
    Task<int> CleanupInactivePushSubscriptionsAsync(int daysInactive = 30);
    
    // Notifications en lot et planifiées
    Task<bool> ScheduleNotificationAsync(CreateNotificationDto notification, DateTime scheduledAt);
    Task<bool> ScheduleBulkNotificationAsync(BulkNotificationDto bulkNotification, DateTime scheduledAt);
    Task<bool> CancelScheduledNotificationAsync(int notificationId);
    Task<List<NotificationDto>> GetScheduledNotificationsAsync(DateTime? scheduledBefore = null);
    
    // Intégration avec autres services
    Task HandleOrderCreatedAsync(int orderId, string userId, decimal amount);
    Task HandleOrderUpdatedAsync(int orderId, string userId, string status);
    Task HandlePaymentCompletedAsync(int orderId, string userId, decimal amount);
    Task HandleProductApprovedAsync(int productId, string sellerId, string productName);
    Task HandleStockLowAsync(int productId, string sellerId, string productName, int currentStock);
    Task HandleNewReviewAsync(int productId, string sellerId, string productName, int rating);
    Task HandleUserRegisteredAsync(string userId, string userName, string email);
    
    // Configuration et maintenance
    Task<Dictionary<string, object>> GetNotificationConfigAsync();
    Task<bool> UpdateNotificationConfigAsync(Dictionary<string, object> config);
    Task<bool> TestNotificationChannelAsync(NotificationChannel channel, string testRecipient);
    Task<Dictionary<string, bool>> GetChannelHealthAsync();
    Task WarmupNotificationServiceAsync();
}
