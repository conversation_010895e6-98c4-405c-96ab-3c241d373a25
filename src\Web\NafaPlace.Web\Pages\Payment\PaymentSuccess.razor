@page "/payment/success"
@using NafaPlace.Web.Models.Order
@using NafaPlace.Web.Services
@using Microsoft.AspNetCore.Components.Authorization
@using System.Security.Claims
@inject NavigationManager NavigationManager
@inject IOrderService OrderService
@inject ICartService CartService
@inject IGuestCartMergeService GuestCartMergeService
@inject IJSRuntime JSRuntime

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow-lg border-success">
                <div class="card-header bg-success text-white text-center">
                    <h3><i class="fas fa-check-circle me-2"></i>Paiement Réussi !</h3>
                </div>
                <div class="card-body text-center">
                    @if (_loading)
                    {
                        <div class="spinner-border text-success" role="status">
                            <span class="visually-hidden">Chargement...</span>
                        </div>
                        <p class="mt-2">Vérification du paiement...</p>
                    }
                    else
                    {
                        <div class="mb-4">
                            <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                        </div>
                        
                        <h4 class="text-success mb-3">Votre paiement a été traité avec succès !</h4>
                        
                        @if (!string.IsNullOrEmpty(_orderId))
                        {
                            <div class="alert alert-success">
                                <strong>Numéro de commande :</strong> #@_orderId.PadLeft(8, '0')
                            </div>
                        }
                        
                        <p class="mb-4">
                            Merci pour votre achat ! Vous recevrez un email de confirmation sous peu.
                            Votre commande est maintenant en cours de traitement.
                        </p>
                        
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary btn-lg" @onclick="ViewOrder">
                                <i class="fas fa-eye me-2"></i>
                                Voir ma commande
                            </button>
                            <button class="btn btn-outline-secondary" @onclick="ContinueShopping">
                                <i class="fas fa-shopping-bag me-2"></i>
                                Continuer mes achats
                            </button>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private bool _loading = true;
    private string _orderId = string.Empty;

    [CascadingParameter]
    private Task<AuthenticationState>? AuthenticationStateTask { get; set; }

    protected override async Task OnInitializedAsync()
    {
        try
        {
            // Récupérer l'orderId depuis les paramètres de l'URL
            var uri = NavigationManager.ToAbsoluteUri(NavigationManager.Uri);
            var query = System.Web.HttpUtility.ParseQueryString(uri.Query);

            var orderIdValue = query["orderId"];
            if (!string.IsNullOrEmpty(orderIdValue))
            {
                _orderId = orderIdValue;

                // Mettre à jour le statut de la commande à "Completed"
                if (int.TryParse(_orderId, out int orderIdInt))
                {
                    await OrderService.UpdatePaymentStatusAsync(orderIdInt, "Completed", $"stripe_success_{DateTime.Now.Ticks}");
                }
            }

            // Vider le panier après un paiement réussi
            await ClearCartAfterPayment();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du traitement du succès de paiement: {ex.Message}");
        }
        finally
        {
            _loading = false;
        }
    }

    private async Task ClearCartAfterPayment()
    {
        try
        {
            Console.WriteLine("🛒 Vidage du panier après paiement réussi...");

            // Déterminer l'ID utilisateur (connecté ou invité)
            string userId = await GetCurrentUserId();

            if (!string.IsNullOrEmpty(userId))
            {
                // Vider le panier de l'utilisateur actuel
                var cartCleared = await CartService.ClearCartAsync(userId);
                if (cartCleared)
                {
                    Console.WriteLine($"✅ Panier vidé avec succès pour l'utilisateur: {userId}");
                }
                else
                {
                    Console.WriteLine($"⚠️ Échec du vidage du panier pour l'utilisateur: {userId}");
                }
            }

            // Si c'est un utilisateur invité, nettoyer aussi le localStorage
            var authState = AuthenticationStateTask != null ? await AuthenticationStateTask : null;
            if (authState?.User?.Identity?.IsAuthenticated != true)
            {
                // Utilisateur invité - nettoyer le localStorage
                await GuestCartMergeService.ClearGuestCartAsync();
                Console.WriteLine("✅ ID invité nettoyé du localStorage");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Erreur lors du vidage du panier: {ex.Message}");
        }
    }

    private async Task<string> GetCurrentUserId()
    {
        try
        {
            if (AuthenticationStateTask != null)
            {
                var authState = await AuthenticationStateTask;
                if (authState.User.Identity?.IsAuthenticated == true)
                {
                    // Utilisateur connecté
                    var userId = authState.User.FindFirst(c => c.Type == ClaimTypes.NameIdentifier)?.Value;
                    if (!string.IsNullOrEmpty(userId))
                    {
                        Console.WriteLine($"🔍 Utilisateur connecté trouvé: {userId}");
                        return userId;
                    }
                }
            }

            // Utilisateur invité - récupérer l'ID depuis le localStorage
            var guestId = await JSRuntime.InvokeAsync<string>("localStorage.getItem", "guestUserId");
            if (!string.IsNullOrEmpty(guestId))
            {
                Console.WriteLine($"🔍 Utilisateur invité trouvé: {guestId}");
                return guestId;
            }

            Console.WriteLine("⚠️ Aucun ID utilisateur trouvé");
            return string.Empty;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Erreur lors de la récupération de l'ID utilisateur: {ex.Message}");
            return string.Empty;
        }
    }

    private void ViewOrder()
    {
        if (!string.IsNullOrEmpty(_orderId))
        {
            NavigationManager.NavigateTo($"/order-confirmation/{_orderId}");
        }
        else
        {
            NavigationManager.NavigateTo("/orders");
        }
    }

    private void ContinueShopping()
    {
        NavigationManager.NavigateTo("/");
    }
}
