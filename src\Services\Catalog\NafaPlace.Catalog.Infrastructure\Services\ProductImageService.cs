using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using Azure.Storage.Blobs;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using NafaPlace.Catalog.Application.Common.Interfaces;
using NafaPlace.Catalog.Application.DTOs.Product;
using NafaPlace.Catalog.Domain.Models;
using NafaPlace.Catalog.Infrastructure.Persistence;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Processing;

namespace NafaPlace.Catalog.Infrastructure.Services;

public class ProductImageService : IProductImageService
{
    private readonly CatalogDbContext _context;
    private readonly BlobServiceClient _blobServiceClient;
    private readonly string _containerName;
    private readonly HttpClient _httpClient;

    public ProductImageService(
        CatalogDbContext context,
        BlobServiceClient blobServiceClient,
        IConfiguration configuration)
    {
        _context = context;
        _blobServiceClient = blobServiceClient;
        _containerName = configuration["Storage:ProductImagesContainer"] ?? "product-images";
        _httpClient = new HttpClient();
    }


    public async Task<string> UploadImageAsync(string base64Image)
    {
        var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);
        await containerClient.CreateIfNotExistsAsync();

        var imageName = $"{Guid.NewGuid()}.jpg";
        var blobClient = containerClient.GetBlobClient(imageName);

        using (var imageStream = new MemoryStream(Convert.FromBase64String(base64Image)))
        {
            using (var optimizedStream = await OptimizeImageAsync(imageStream, 1920, 1080))
            {
                await blobClient.UploadAsync(optimizedStream, true);
            }
        }

        return blobClient.Uri.ToString();
    }

    public async Task<ProductImageDto> AddProductImageAsync(int productId, CreateProductImageRequest request)
    {
        var product = await _context.Products.FindAsync(productId);
        if (product == null)
        {
            throw new Exception("Le produit est introuvable.");
        }

        var imageUrl = await UploadImageAsync(request.Image);
        var thumbnailUrl = await GenerateThumbnailAsync(imageUrl);

        var productImage = new ProductImage
        {
            ProductId = productId,
            ImageUrl = ConvertAzuriteUrlToPublic(imageUrl),
            ThumbnailUrl = ConvertAzuriteUrlToPublic(thumbnailUrl),
            IsMain = request.IsMain
        };

        if (request.IsMain)
        {
            var existingMainImages = await _context.ProductImages
                .Where(pi => pi.ProductId == productId && pi.IsMain)
                .ToListAsync();

            foreach (var img in existingMainImages)
            {
                img.IsMain = false;
            }
        }

        _context.ProductImages.Add(productImage);
        await _context.SaveChangesAsync();

        return new ProductImageDto
        {
            Id = productImage.Id,
            Url = productImage.ImageUrl,
            ThumbnailUrl = productImage.ThumbnailUrl,
            IsMain = productImage.IsMain,
            ProductId = productImage.ProductId
        };
    }

    public async Task<bool> UpdateProductImageAsync(int imageId, UpdateProductImageRequest request)
    {
        var productImage = await _context.ProductImages.FindAsync(imageId);
        if (productImage == null)
        {
            return false;
        }

        if (!string.IsNullOrEmpty(request.Image))
        {
            var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);
            var imageName = $"{productImage.ProductId}_{Guid.NewGuid()}.jpg";
            var blobClient = containerClient.GetBlobClient(imageName);
            var thumbnailBlobClient = containerClient.GetBlobClient($"thumbnails/{imageName}");

            using (var imageStream = new MemoryStream(Convert.FromBase64String(request.Image)))
            {
                using (var optimizedStream = await OptimizeImageAsync(imageStream, 1920, 1080))
                {
                    await blobClient.UploadAsync(optimizedStream, true);
                }

                imageStream.Position = 0;

                using (var thumbnailStream = await GenerateThumbnailStreamAsync(imageStream))
                {
                    await thumbnailBlobClient.UploadAsync(thumbnailStream, true);
                }
            }

            // Delete old images
            if (!string.IsNullOrEmpty(productImage.ImageUrl))
            {
                var oldBlobName = new Uri(productImage.ImageUrl).Segments.Last();
                await containerClient.GetBlobClient(oldBlobName).DeleteIfExistsAsync();
            }
            if (!string.IsNullOrEmpty(productImage.ThumbnailUrl))
            {
                var oldThumbnailBlobName = new Uri(productImage.ThumbnailUrl).Segments.Last();
                await containerClient.GetBlobClient(oldThumbnailBlobName).DeleteIfExistsAsync();
            }

            productImage.ImageUrl = ConvertAzuriteUrlToPublic(blobClient.Uri.ToString());
            productImage.ThumbnailUrl = ConvertAzuriteUrlToPublic(thumbnailBlobClient.Uri.ToString());
        }

        productImage.IsMain = request.IsMain;

        _context.ProductImages.Update(productImage);
        await _context.SaveChangesAsync();

        return true;
    }

    public async Task DeleteProductImageAsync(int imageId)
    {
        var productImage = await _context.ProductImages.FindAsync(imageId);
        if (productImage == null)
        {
            return;
        }

        // Supprimer les fichiers du stockage blob
        try
        {
            var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);
            
            // Extraire les noms des blobs à partir des URLs
            var imageUri = new Uri(productImage.ImageUrl);
            
            var imageBlobName = imageUri.Segments.Last();
            
            var imageBlobClient = containerClient.GetBlobClient(imageBlobName);
            
            await imageBlobClient.DeleteIfExistsAsync();
        }
        catch (Exception)
        {
            // Ignorer les erreurs de suppression des blobs
        }

        _context.ProductImages.Remove(productImage);
        await _context.SaveChangesAsync();
    }

    public async Task SetMainImageAsync(int productId, int imageId)
    {
        var productImages = await _context.ProductImages
            .Where(pi => pi.ProductId == productId)
            .ToListAsync();

        if (!productImages.Any())
        {
            return;
        }

        foreach (var image in productImages)
        {
            image.IsMain = image.Id == imageId;
        }

        await _context.SaveChangesAsync();
    }

    public async Task<bool> UpdateImageOrderAsync(int imageId, int displayOrder)
    {
        var productImage = await _context.ProductImages.FindAsync(imageId);
        if (productImage == null)
        {
            return false;
        }

        // La propriété DisplayOrder n'existe pas dans le modèle ProductImage
        // Nous ne pouvons pas mettre à jour cette propriété

        _context.ProductImages.Update(productImage);
        await _context.SaveChangesAsync();

        return true;
    }

    public async Task<IEnumerable<ProductImageDto>> GetProductImagesAsync(int productId)
    {
        var productImages = await _context.ProductImages
            .Where(pi => pi.ProductId == productId)
            .ToListAsync();

        return productImages.Select(pi => new ProductImageDto
        {
            Id = pi.Id,
            Url = pi.ImageUrl,
            ThumbnailUrl = pi.ThumbnailUrl,
            IsMain = pi.IsMain,
            ProductId = pi.ProductId
        });
    }

    public async Task<ProductImageDto> GetProductImageAsync(int imageId)
    {
        var productImage = await _context.ProductImages.FindAsync(imageId);
        if (productImage == null)
        {
            return new ProductImageDto
            {
                Id = 0,
                Url = string.Empty,
                IsMain = false,
                ProductId = 0,
                ThumbnailUrl = string.Empty
            };
        }

        return new ProductImageDto
        {
            Id = productImage.Id,
            Url = productImage.ImageUrl,
            ThumbnailUrl = productImage.ThumbnailUrl,
            IsMain = productImage.IsMain,
            ProductId = productImage.ProductId
        };
    }

    public async Task<string> GenerateThumbnailAsync(string imageUrl)
    {
        using (var imageStream = await DownloadImageAsync(imageUrl))
        {
            using (var thumbnailStream = await GenerateThumbnailStreamAsync(imageStream))
            {
                var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);
                await containerClient.CreateIfNotExistsAsync();
                
                var thumbnailBlobName = $"thumbnails/{new Random().Next(1, int.MaxValue)}.jpg";
                var thumbnailBlobClient = containerClient.GetBlobClient(thumbnailBlobName);

                await thumbnailBlobClient.UploadAsync(thumbnailStream, true);
                return ConvertAzuriteUrlToPublic(thumbnailBlobClient.Uri.ToString());
            }
        }
    }

    public async Task<bool> ValidateImageAsync(string imageUrl)
    {
        try
        {
            using (var imageStream = await DownloadImageAsync(imageUrl))
            {
                // Vérifier si le fichier est une image valide
                using (var image = await Image.LoadAsync(imageStream))
                {
                    // Si l'image peut être chargée, elle est valide
                    return true;
                }
            }
        }
        catch (Exception)
        {
            return false;
        }
    }

    private async Task<Stream> DownloadImageAsync(string imageUrl)
    {
        var response = await _httpClient.GetAsync(imageUrl);
        response.EnsureSuccessStatusCode();

        var memoryStream = new MemoryStream();
        await response.Content.CopyToAsync(memoryStream);
        memoryStream.Position = 0;

        return memoryStream;
    }

    private async Task<Stream> OptimizeImageAsync(Stream imageStream, int maxWidth, int maxHeight)
    {
        var outputStream = new MemoryStream();
        
        using (var image = await Image.LoadAsync(imageStream))
        {
            // Redimensionner l'image si nécessaire
            if (image.Width > maxWidth || image.Height > maxHeight)
            {
                image.Mutate(x => x.Resize(new ResizeOptions
                {
                    Mode = ResizeMode.Max,
                    Size = new Size(maxWidth, maxHeight)
                }));
            }

            // Optimiser la qualité pour réduire la taille du fichier
            await image.SaveAsJpegAsync(outputStream, new SixLabors.ImageSharp.Formats.Jpeg.JpegEncoder
            {
                Quality = 80 // Qualité réduite pour optimiser la taille
            });
        }

        outputStream.Position = 0;
        return outputStream;
    }

    private async Task<Stream> GenerateThumbnailStreamAsync(Stream imageStream)
    {
        var outputStream = new MemoryStream();
        
        using (var image = await Image.LoadAsync(imageStream))
        {
            // Créer une miniature de taille fixe
            image.Mutate(x => x.Resize(new ResizeOptions
            {
                Mode = ResizeMode.Crop,
                Size = new Size(300, 300)
            }));

            // Enregistrer avec une qualité réduite
            await image.SaveAsJpegAsync(outputStream, new SixLabors.ImageSharp.Formats.Jpeg.JpegEncoder
            {
                Quality = 70
            });
        }

        outputStream.Position = 0;
        return outputStream;
    }
    public async Task<IEnumerable<ProductImageDto>> AddBulkProductImagesAsync(int productId, IEnumerable<CreateProductImageRequest> request)
    {
        var product = await _context.Products.FindAsync(productId);
        if (product == null)
        {
            throw new Exception("Le produit est introuvable.");
        }

        var newImages = new List<ProductImageDto>();

        foreach (var imageRequest in request)
        {
            var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);
            await containerClient.CreateIfNotExistsAsync();

            var imageName = $"{productId}_{Guid.NewGuid()}.jpg";
            var blobClient = containerClient.GetBlobClient(imageName);
            var thumbnailBlobClient = containerClient.GetBlobClient($"thumbnails/{imageName}");

            using (var imageStream = new MemoryStream(Convert.FromBase64String(imageRequest.Image)))
            {
                using (var optimizedStream = await OptimizeImageAsync(imageStream, 1920, 1080))
                {
                    await blobClient.UploadAsync(optimizedStream, true);
                }

                imageStream.Position = 0;

                using (var thumbnailStream = await GenerateThumbnailStreamAsync(imageStream))
                {
                    await thumbnailBlobClient.UploadAsync(thumbnailStream, true);
                }
            }

            var productImage = new ProductImage
            {
                ProductId = productId,
                ImageUrl = ConvertAzuriteUrlToPublic(blobClient.Uri.ToString()),
                ThumbnailUrl = ConvertAzuriteUrlToPublic(thumbnailBlobClient.Uri.ToString()),
                IsMain = imageRequest.IsMain
            };

            if (imageRequest.IsMain)
            {
                var existingMainImages = await _context.ProductImages
                    .Where(pi => pi.ProductId == productId && pi.IsMain)
                    .ToListAsync();

                foreach (var img in existingMainImages)
                {
                    img.IsMain = false;
                }
            }

            _context.ProductImages.Add(productImage);
            newImages.Add(new ProductImageDto
            {
                Id = productImage.Id,
                Url = productImage.ImageUrl,
                ThumbnailUrl = productImage.ThumbnailUrl,
                IsMain = productImage.IsMain,
                ProductId = productImage.ProductId
            });
        }
        
        await _context.SaveChangesAsync();
        return newImages;
    }

    private string ConvertAzuriteUrlToPublic(string azuriteUrl)
    {
        // Convertir les URLs Azurite en URLs d'API proxy
        if (string.IsNullOrEmpty(azuriteUrl))
            return azuriteUrl;

        // Extraire le chemin du blob depuis l'URL Azurite
        // Format: http://azurite:10000/devstoreaccount1/product-images/path/to/image.jpg
        // Résultat: /api/v1/images/path/to/image.jpg

        try
        {
            var uri = new Uri(azuriteUrl);
            var pathSegments = uri.AbsolutePath.Split('/', StringSplitOptions.RemoveEmptyEntries);

            // Ignorer 'devstoreaccount1' et 'product-images', prendre le reste
            if (pathSegments.Length >= 3)
            {
                var imagePath = string.Join("/", pathSegments.Skip(2));
                return $"http://localhost:5243/api/v1/images/{imagePath}";
            }
        }
        catch
        {
            // En cas d'erreur, retourner une image placeholder
            return "http://localhost:5244/api/v1/images/placeholder";
        }

        return "http://localhost:5244/api/v1/images/placeholder";
    }

}
