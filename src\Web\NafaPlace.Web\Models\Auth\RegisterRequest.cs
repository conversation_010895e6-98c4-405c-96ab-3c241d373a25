using System.ComponentModel.DataAnnotations;

namespace NafaPlace.Web.Models.Auth;

public class RegisterRequest
{
    [Required(ErrorMessage = "Le prénom est requis")]
    [StringLength(50, ErrorMessage = "Le prénom ne doit pas dépasser 50 caractères")]
    public string FirstName { get; set; } = string.Empty;

    [Required(ErrorMessage = "Le nom est requis")]
    [StringLength(50, ErrorMessage = "Le nom ne doit pas dépasser 50 caractères")]
    public string LastName { get; set; } = string.Empty;

    [Required(ErrorMessage = "Le nom d'utilisateur est requis")]
    [StringLength(50, ErrorMessage = "Le nom d'utilisateur ne doit pas dépasser 50 caractères")]
    public string Username { get; set; } = string.Empty;

    [Required(ErrorMessage = "L'email est requis")]
    [EmailAddress(ErrorMessage = "Format d'email invalide")]
    public string Email { get; set; } = string.Empty;

    [Required(ErrorMessage = "Le numéro de téléphone est requis")]
    [Phone(ErrorMessage = "Format de numéro de téléphone invalide")]
    public string PhoneNumber { get; set; } = string.Empty;

    [Required(ErrorMessage = "Le mot de passe est requis")]
    [StringLength(100, ErrorMessage = "Le mot de passe doit contenir entre {2} et {1} caractères", MinimumLength = 8)]
    [RegularExpression(@"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^\da-zA-Z]).{8,}$", 
        ErrorMessage = "Le mot de passe doit contenir au moins une minuscule, une majuscule, un chiffre et un caractère spécial")]
    public string Password { get; set; } = string.Empty;

    [Required(ErrorMessage = "La confirmation du mot de passe est requise")]
    [Compare("Password", ErrorMessage = "Le mot de passe et sa confirmation ne correspondent pas")]
    public string ConfirmPassword { get; set; } = string.Empty;

    [Required(ErrorMessage = "Vous devez accepter les conditions générales")]
    [Range(typeof(bool), "true", "true", ErrorMessage = "Vous devez accepter les conditions générales")]
    public bool TermsAccepted { get; set; }
}
