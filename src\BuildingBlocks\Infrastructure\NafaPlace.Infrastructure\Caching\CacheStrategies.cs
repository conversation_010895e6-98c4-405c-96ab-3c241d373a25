using Microsoft.Extensions.Logging;
using NafaPlace.Common.CQRS;

namespace NafaPlace.Infrastructure.Caching;

/// <summary>
/// Stratégies de cache intelligentes
/// </summary>
public static class CacheStrategies
{
    /// <summary>
    /// Stratégie de cache pour les produits
    /// </summary>
    public static class Products
    {
        public const string KeyPrefix = "products";
        public const int DefaultTtlSeconds = 300; // 5 minutes
        public const int FeaturedTtlSeconds = 600; // 10 minutes
        public const int DetailsTtlSeconds = 900; // 15 minutes

        public static string GetListKey(int? categoryId = null, int? sellerId = null, string? searchTerm = null)
        {
            var keyParts = new List<string> { KeyPrefix, "list" };
            
            if (categoryId.HasValue) keyParts.Add($"cat:{categoryId}");
            if (sellerId.HasValue) keyParts.Add($"seller:{sellerId}");
            if (!string.IsNullOrEmpty(searchTerm)) keyParts.Add($"search:{searchTerm.ToLower()}");
            
            return string.Join(":", keyParts);
        }

        public static string GetDetailsKey(int productId) => $"{KeyPrefix}:details:{productId}";
        public static string GetFeaturedKey() => $"{KeyPrefix}:featured";
        public static string GetNewKey() => $"{KeyPrefix}:new";
        public static string GetByCategoryKey(int categoryId) => $"{KeyPrefix}:category:{categoryId}";
        public static string GetBySellerKey(int sellerId) => $"{KeyPrefix}:seller:{sellerId}";
        
        public static string[] GetInvalidationPatterns(int productId, int? categoryId = null, int? sellerId = null)
        {
            var patterns = new List<string>
            {
                $"{KeyPrefix}:details:{productId}",
                $"{KeyPrefix}:list*",
                $"{KeyPrefix}:featured",
                $"{KeyPrefix}:new"
            };

            if (categoryId.HasValue)
                patterns.Add($"{KeyPrefix}:category:{categoryId}");
            
            if (sellerId.HasValue)
                patterns.Add($"{KeyPrefix}:seller:{sellerId}");

            return patterns.ToArray();
        }
    }

    /// <summary>
    /// Stratégie de cache pour les catégories
    /// </summary>
    public static class Categories
    {
        public const string KeyPrefix = "categories";
        public const int DefaultTtlSeconds = 1800; // 30 minutes

        public static string GetAllKey() => $"{KeyPrefix}:all";
        public static string GetDetailsKey(int categoryId) => $"{KeyPrefix}:details:{categoryId}";
        public static string GetHierarchyKey() => $"{KeyPrefix}:hierarchy";
        
        public static string[] GetInvalidationPatterns(int categoryId)
        {
            return new[]
            {
                $"{KeyPrefix}:all",
                $"{KeyPrefix}:details:{categoryId}",
                $"{KeyPrefix}:hierarchy",
                $"{Products.KeyPrefix}:category:{categoryId}",
                $"{Products.KeyPrefix}:list*"
            };
        }
    }

    /// <summary>
    /// Stratégie de cache pour les utilisateurs
    /// </summary>
    public static class Users
    {
        public const string KeyPrefix = "users";
        public const int DefaultTtlSeconds = 900; // 15 minutes
        public const int ProfileTtlSeconds = 1800; // 30 minutes

        public static string GetDetailsKey(string userId) => $"{KeyPrefix}:details:{userId}";
        public static string GetProfileKey(string userId) => $"{KeyPrefix}:profile:{userId}";
        public static string GetPermissionsKey(string userId) => $"{KeyPrefix}:permissions:{userId}";
        
        public static string[] GetInvalidationPatterns(string userId)
        {
            return new[]
            {
                $"{KeyPrefix}:details:{userId}",
                $"{KeyPrefix}:profile:{userId}",
                $"{KeyPrefix}:permissions:{userId}"
            };
        }
    }

    /// <summary>
    /// Stratégie de cache pour les commandes
    /// </summary>
    public static class Orders
    {
        public const string KeyPrefix = "orders";
        public const int DefaultTtlSeconds = 300; // 5 minutes
        public const int StatsTtlSeconds = 600; // 10 minutes

        public static string GetDetailsKey(int orderId) => $"{KeyPrefix}:details:{orderId}";
        public static string GetByUserKey(string userId) => $"{KeyPrefix}:user:{userId}";
        public static string GetBySellerKey(int sellerId) => $"{KeyPrefix}:seller:{sellerId}";
        public static string GetStatsKey(int? sellerId = null) => 
            sellerId.HasValue ? $"{KeyPrefix}:stats:seller:{sellerId}" : $"{KeyPrefix}:stats:global";
        
        public static string[] GetInvalidationPatterns(int orderId, string? userId = null, int? sellerId = null)
        {
            var patterns = new List<string>
            {
                $"{KeyPrefix}:details:{orderId}",
                $"{KeyPrefix}:stats:*"
            };

            if (!string.IsNullOrEmpty(userId))
                patterns.Add($"{KeyPrefix}:user:{userId}");
            
            if (sellerId.HasValue)
                patterns.Add($"{KeyPrefix}:seller:{sellerId}");

            return patterns.ToArray();
        }
    }

    /// <summary>
    /// Stratégie de cache pour les avis
    /// </summary>
    public static class Reviews
    {
        public const string KeyPrefix = "reviews";
        public const int DefaultTtlSeconds = 600; // 10 minutes

        public static string GetByProductKey(int productId) => $"{KeyPrefix}:product:{productId}";
        public static string GetByUserKey(string userId) => $"{KeyPrefix}:user:{userId}";
        public static string GetStatsKey(int productId) => $"{KeyPrefix}:stats:{productId}";
        
        public static string[] GetInvalidationPatterns(int productId, string? userId = null)
        {
            var patterns = new List<string>
            {
                $"{KeyPrefix}:product:{productId}",
                $"{KeyPrefix}:stats:{productId}",
                $"{Products.KeyPrefix}:details:{productId}"
            };

            if (!string.IsNullOrEmpty(userId))
                patterns.Add($"{KeyPrefix}:user:{userId}");

            return patterns.ToArray();
        }
    }
}

/// <summary>
/// Service de gestion intelligente du cache
/// </summary>
public class SmartCacheService : ICacheService
{
    private readonly RedisCacheService _cacheService;
    private readonly ILogger<SmartCacheService> _logger;

    public SmartCacheService(RedisCacheService cacheService, ILogger<SmartCacheService> logger)
    {
        _cacheService = cacheService;
        _logger = logger;
    }

    public Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default)
    {
        return _cacheService.GetAsync<T>(key, cancellationToken);
    }

    public Task SetAsync<T>(string key, T value, TimeSpan expiration, CancellationToken cancellationToken = default)
    {
        return _cacheService.SetAsync(key, value, expiration, cancellationToken);
    }

    public Task RemoveAsync(string key, CancellationToken cancellationToken = default)
    {
        return _cacheService.RemoveAsync(key, cancellationToken);
    }

    public Task RemoveByPatternAsync(string pattern, CancellationToken cancellationToken = default)
    {
        return _cacheService.RemoveByPatternAsync(pattern, cancellationToken);
    }

    /// <summary>
    /// Invalide le cache pour un produit
    /// </summary>
    public async Task InvalidateProductCacheAsync(int productId, int? categoryId = null, int? sellerId = null, CancellationToken cancellationToken = default)
    {
        var patterns = CacheStrategies.Products.GetInvalidationPatterns(productId, categoryId, sellerId);
        
        foreach (var pattern in patterns)
        {
            await RemoveByPatternAsync(pattern, cancellationToken);
        }
        
        _logger.LogInformation("Cache invalidé pour le produit {ProductId}", productId);
    }

    /// <summary>
    /// Invalide le cache pour une catégorie
    /// </summary>
    public async Task InvalidateCategoryCacheAsync(int categoryId, CancellationToken cancellationToken = default)
    {
        var patterns = CacheStrategies.Categories.GetInvalidationPatterns(categoryId);
        
        foreach (var pattern in patterns)
        {
            await RemoveByPatternAsync(pattern, cancellationToken);
        }
        
        _logger.LogInformation("Cache invalidé pour la catégorie {CategoryId}", categoryId);
    }

    /// <summary>
    /// Invalide le cache pour un utilisateur
    /// </summary>
    public async Task InvalidateUserCacheAsync(string userId, CancellationToken cancellationToken = default)
    {
        var patterns = CacheStrategies.Users.GetInvalidationPatterns(userId);
        
        foreach (var pattern in patterns)
        {
            await RemoveByPatternAsync(pattern, cancellationToken);
        }
        
        _logger.LogInformation("Cache invalidé pour l'utilisateur {UserId}", userId);
    }

    /// <summary>
    /// Invalide le cache pour une commande
    /// </summary>
    public async Task InvalidateOrderCacheAsync(int orderId, string? userId = null, int? sellerId = null, CancellationToken cancellationToken = default)
    {
        var patterns = CacheStrategies.Orders.GetInvalidationPatterns(orderId, userId, sellerId);
        
        foreach (var pattern in patterns)
        {
            await RemoveByPatternAsync(pattern, cancellationToken);
        }
        
        _logger.LogInformation("Cache invalidé pour la commande {OrderId}", orderId);
    }

    /// <summary>
    /// Invalide le cache pour les avis d'un produit
    /// </summary>
    public async Task InvalidateReviewCacheAsync(int productId, string? userId = null, CancellationToken cancellationToken = default)
    {
        var patterns = CacheStrategies.Reviews.GetInvalidationPatterns(productId, userId);
        
        foreach (var pattern in patterns)
        {
            await RemoveByPatternAsync(pattern, cancellationToken);
        }
        
        _logger.LogInformation("Cache invalidé pour les avis du produit {ProductId}", productId);
    }

    /// <summary>
    /// Préchauffe le cache avec les données les plus utilisées
    /// </summary>
    public async Task WarmupCacheAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Début du préchauffage du cache");

        try
        {
            // Ici, vous pourriez précharger :
            // - Les catégories principales
            // - Les produits en vedette
            // - Les produits les plus populaires
            // - Les données de configuration
            
            _logger.LogInformation("Préchauffage du cache terminé avec succès");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du préchauffage du cache");
        }
    }

    /// <summary>
    /// Nettoie le cache expiré et optimise l'utilisation mémoire
    /// </summary>
    public async Task CleanupCacheAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Début du nettoyage du cache");

        try
        {
            // Logique de nettoyage personnalisée
            // Par exemple, supprimer les clés expirées ou peu utilisées
            
            _logger.LogInformation("Nettoyage du cache terminé avec succès");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du nettoyage du cache");
        }
    }
}
