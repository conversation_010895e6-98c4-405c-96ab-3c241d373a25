using System.ComponentModel.DataAnnotations;

namespace NafaPlace.AdminPortal.Models.Products
{
    public class SellerDto
    {
        public int Id { get; set; }
        
        [Required(ErrorMessage = "Le nom du vendeur est obligatoire")]
        [StringLength(100, ErrorMessage = "Le nom du vendeur ne doit pas dépasser 100 caractères")]
        public string Name { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "L'email du vendeur est obligatoire")]
        [EmailAddress(ErrorMessage = "Format d'email invalide")]
        public string Email { get; set; } = string.Empty;
        
        public string PhoneNumber { get; set; } = string.Empty;
        
        public string Address { get; set; } = string.Empty;
        
        public bool IsActive { get; set; } = true;
        
        public bool IsVerified { get; set; } = false;
    }
}
