using NafaPlace.Notifications.Domain.Models;

namespace NafaPlace.Notifications.Infrastructure.Data;

public static class NotificationTemplateSeeder
{
    public static List<NotificationTemplate> GetDefaultTemplates()
    {
        return new List<NotificationTemplate>
        {
            // Email Templates
            new NotificationTemplate
            {
                Id = 1,
                Name = "Bienvenue sur NafaPlace",
                Code = "WELCOME_EMAIL",
                Type = NotificationType.Welcome,
                Channel = NotificationChannel.Email,
                Subject = "Bienvenue sur NafaPlace, {{CustomerName}} ! 🎉",
                Body = @"
                <h1>Bienvenue sur NafaPlace !</h1>
                <p>Bonjour {{CustomerName}},</p>
                <p>Nous sommes ravis de vous accueillir sur NafaPlace, votre marketplace de confiance en Guinée !</p>
                <p>Découvrez des milliers de produits de qualité, livrés directement chez vous.</p>
                <p><a href='{{WebsiteUrl}}' style='background-color: #E73C30; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Commencer mes achats</a></p>
                <p>L'équipe <PERSON></p>
                ",
                Description = "Email de bienvenue pour les nouveaux utilisateurs",
                IsActive = true,
                Language = "fr",
                Variables = "CustomerName,WebsiteUrl",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },

            new NotificationTemplate
            {
                Id = 2,
                Name = "Confirmation de commande",
                Code = "ORDER_CONFIRMATION_EMAIL",
                Type = NotificationType.OrderConfirmation,
                Channel = NotificationChannel.Email,
                Subject = "Commande confirmée #{{OrderNumber}} - {{OrderTotal}} {{Currency}}",
                Body = @"
                <h1>Votre commande est confirmée ! ✅</h1>
                <p>Bonjour {{CustomerName}},</p>
                <p>Nous avons bien reçu votre commande #{{OrderNumber}} d'un montant de {{OrderTotal}} {{Currency}}.</p>
                <h3>Détails de la commande :</h3>
                <ul>
                {{#OrderItems}}
                <li>{{ProductName}} - Quantité: {{Quantity}} - Prix: {{Price}} {{Currency}}</li>
                {{/OrderItems}}
                </ul>
                <p><strong>Total : {{OrderTotal}} {{Currency}}</strong></p>
                <p><strong>Adresse de livraison :</strong><br>{{DeliveryAddress}}</p>
                <p>Votre commande sera traitée dans les plus brefs délais.</p>
                <p><a href='{{OrderTrackingUrl}}'>Suivre ma commande</a></p>
                <p>Merci de votre confiance !</p>
                ",
                Description = "Email de confirmation de commande",
                IsActive = true,
                Language = "fr",
                Variables = "CustomerName,OrderNumber,OrderTotal,Currency,OrderItems,DeliveryAddress,OrderTrackingUrl",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },

            new NotificationTemplate
            {
                Id = 3,
                Name = "Commande expédiée",
                Code = "ORDER_SHIPPED_EMAIL",
                Type = NotificationType.ShippingNotification,
                Channel = NotificationChannel.Email,
                Subject = "📦 Votre commande #{{OrderNumber}} a été expédiée !",
                Body = @"
                <h1>Votre commande est en route ! 🚚</h1>
                <p>Bonjour {{CustomerName}},</p>
                <p>Bonne nouvelle ! Votre commande #{{OrderNumber}} a été expédiée.</p>
                <p><strong>Numéro de suivi :</strong> {{TrackingNumber}}</p>
                <p><strong>Transporteur :</strong> {{CarrierName}}</p>
                <p><strong>Date d'expédition :</strong> {{ShippingDate}}</p>
                <p><strong>Livraison estimée :</strong> {{EstimatedDeliveryDate}}</p>
                <p><a href='{{TrackingUrl}}' style='background-color: #F96302; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Suivre mon colis</a></p>
                <p>Nous vous tiendrons informé de l'avancement de votre livraison.</p>
                ",
                Description = "Email d'expédition de commande",
                IsActive = true,
                Language = "fr",
                Variables = "CustomerName,OrderNumber,TrackingNumber,CarrierName,ShippingDate,EstimatedDeliveryDate,TrackingUrl",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },

            new NotificationTemplate
            {
                Id = 4,
                Name = "Commande livrée",
                Code = "ORDER_DELIVERED_EMAIL",
                Type = NotificationType.DeliveryConfirmation,
                Channel = NotificationChannel.Email,
                Subject = "✅ Votre commande #{{OrderNumber}} a été livrée !",
                Body = @"
                <h1>Livraison réussie ! 🎉</h1>
                <p>Bonjour {{CustomerName}},</p>
                <p>Votre commande #{{OrderNumber}} a été livrée avec succès le {{DeliveryDate}} à {{DeliveryTime}}.</p>
                <p><strong>Reçue par :</strong> {{ReceivedBy}}</p>
                <p>Nous espérons que vous êtes satisfait(e) de votre achat !</p>
                <p><a href='{{ReviewUrl}}' style='background-color: #E73C30; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Laisser un avis</a></p>
                <p>Merci de faire confiance à NafaPlace !</p>
                ",
                Description = "Email de confirmation de livraison",
                IsActive = true,
                Language = "fr",
                Variables = "CustomerName,OrderNumber,DeliveryDate,DeliveryTime,ReceivedBy,ReviewUrl",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },

            // SMS Templates
            new NotificationTemplate
            {
                Id = 5,
                Name = "SMS Confirmation commande",
                Code = "ORDER_CONFIRMATION_SMS",
                Type = NotificationType.OrderConfirmation,
                Channel = NotificationChannel.SMS,
                Subject = "Commande confirmée",
                Body = "NafaPlace: Votre commande #{{OrderNumber}} de {{OrderTotal}} {{Currency}} est confirmée. Livraison prévue le {{EstimatedDeliveryDate}}. Suivi: {{TrackingUrl}}",
                Description = "SMS de confirmation de commande",
                IsActive = true,
                Language = "fr",
                Variables = "OrderNumber,OrderTotal,Currency,EstimatedDeliveryDate,TrackingUrl",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },

            new NotificationTemplate
            {
                Id = 6,
                Name = "SMS Expédition",
                Code = "ORDER_SHIPPED_SMS",
                Type = NotificationType.ShippingNotification,
                Channel = NotificationChannel.SMS,
                Subject = "Commande expédiée",
                Body = "NafaPlace: Votre commande #{{OrderNumber}} a été expédiée via {{CarrierName}}. Suivi: {{TrackingNumber}}. Livraison prévue: {{EstimatedDeliveryDate}}",
                Description = "SMS d'expédition de commande",
                IsActive = true,
                Language = "fr",
                Variables = "OrderNumber,CarrierName,TrackingNumber,EstimatedDeliveryDate",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },

            new NotificationTemplate
            {
                Id = 7,
                Name = "SMS Livraison",
                Code = "ORDER_DELIVERED_SMS",
                Type = NotificationType.DeliveryConfirmation,
                Channel = NotificationChannel.SMS,
                Subject = "Commande livrée",
                Body = "NafaPlace: Votre commande #{{OrderNumber}} a été livrée le {{DeliveryDate}}. Merci de votre confiance ! Laissez un avis: {{ReviewUrl}}",
                Description = "SMS de confirmation de livraison",
                IsActive = true,
                Language = "fr",
                Variables = "OrderNumber,DeliveryDate,ReviewUrl",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },

            // Stock Alerts
            new NotificationTemplate
            {
                Id = 8,
                Name = "Alerte stock faible",
                Code = "LOW_STOCK_ALERT_EMAIL",
                Type = NotificationType.LowStockWarning,
                Channel = NotificationChannel.Email,
                Subject = "⚠️ Stock faible pour {{ProductName}}",
                Body = @"
                <h1>Alerte Stock Faible</h1>
                <p>Bonjour {{SellerName}},</p>
                <p>Le stock du produit <strong>{{ProductName}}</strong> est faible.</p>
                <p><strong>Stock actuel :</strong> {{CurrentStock}} unités</p>
                <p><strong>Seuil d'alerte :</strong> {{ThresholdStock}} unités</p>
                <p>Nous vous recommandons de réapprovisionner ce produit rapidement.</p>
                <p><a href='{{ProductManagementUrl}}'>Gérer le stock</a></p>
                ",
                Description = "Alerte de stock faible pour les vendeurs",
                IsActive = true,
                Language = "fr",
                Variables = "SellerName,ProductName,CurrentStock,ThresholdStock,ProductManagementUrl",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },

            new NotificationTemplate
            {
                Id = 9,
                Name = "Produit de nouveau disponible",
                Code = "PRODUCT_AVAILABLE_EMAIL",
                Type = NotificationType.ProductAvailable,
                Channel = NotificationChannel.Email,
                Subject = "🎉 {{ProductName}} est de nouveau disponible !",
                Body = @"
                <h1>Bonne nouvelle !</h1>
                <p>Bonjour {{CustomerName}},</p>
                <p>Le produit <strong>{{ProductName}}</strong> que vous aviez ajouté à votre liste de souhaits est maintenant disponible !</p>
                <p><strong>Prix :</strong> {{ProductPrice}} {{Currency}}</p>
                <p>Dépêchez-vous, les stocks sont limités !</p>
                <p><a href='{{ProductUrl}}' style='background-color: #E73C30; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Acheter maintenant</a></p>
                ",
                Description = "Notification de disponibilité de produit",
                IsActive = true,
                Language = "fr",
                Variables = "CustomerName,ProductName,ProductPrice,Currency,ProductUrl",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },

            // Promotional Templates
            new NotificationTemplate
            {
                Id = 10,
                Name = "Offre promotionnelle",
                Code = "PROMOTIONAL_OFFER_EMAIL",
                Type = NotificationType.PromotionalOffer,
                Channel = NotificationChannel.Email,
                Subject = "🔥 {{OfferTitle}} - Jusqu'à {{DiscountPercent}}% de réduction !",
                Body = @"
                <h1>{{OfferTitle}}</h1>
                <p>Bonjour {{CustomerName}},</p>
                <p>Profitez de notre offre exceptionnelle :</p>
                <h2>Jusqu'à {{DiscountPercent}}% de réduction !</h2>
                <p>{{OfferDescription}}</p>
                <p><strong>Code promo :</strong> <span style='background-color: #f0f0f0; padding: 5px 10px; font-weight: bold;'>{{PromoCode}}</span></p>
                <p><strong>Valable jusqu'au :</strong> {{ExpiryDate}}</p>
                <p><a href='{{ShopUrl}}' style='background-color: #F96302; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-size: 18px;'>Profiter de l'offre</a></p>
                <p><small>Offre limitée dans le temps. Conditions générales applicables.</small></p>
                ",
                Description = "Email d'offre promotionnelle",
                IsActive = true,
                Language = "fr",
                Variables = "CustomerName,OfferTitle,DiscountPercent,OfferDescription,PromoCode,ExpiryDate,ShopUrl",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        };
    }
}
