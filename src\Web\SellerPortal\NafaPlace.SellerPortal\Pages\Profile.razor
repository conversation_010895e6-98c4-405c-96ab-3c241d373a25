@page "/profile"
@using NafaPlace.SellerPortal.Models.Auth
@using System.ComponentModel.DataAnnotations
@inject IAuthService AuthService
@inject NavigationManager NavigationManager
@inject NafaPlace.SellerPortal.Services.NotificationService NotificationService
@inject NafaPlace.SellerPortal.Services.ImageService ImageService
@attribute [Authorize]

<PageTitle>Profil Vendeur - NafaPlace</PageTitle>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">Profil Vendeur</h3>
                </div>
                <div class="card-body">
                    @if (isLoading)
                    {
                        <div class="d-flex justify-content-center my-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Chargement...</span>
                            </div>
                        </div>
                    }
                    else if (currentUser != null)
                    {
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <h4 class="text-primary">Informations personnelles</h4>
                                <hr />
                                <dl class="row">
                                    <dt class="col-sm-4">Nom d'utilisateur</dt>
                                    <dd class="col-sm-8">@currentUser.Username</dd>
                                    
                                    <dt class="col-sm-4">Email</dt>
                                    <dd class="col-sm-8">@currentUser.Email</dd>
                                    
                                    <dt class="col-sm-4">Rôle</dt>
                                    <dd class="col-sm-8">@(currentUser.Roles.FirstOrDefault() ?? "Vendeur")</dd>
                                </dl>
                            </div>
                        </div>
                        
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <h4 class="text-primary">Photo de profil</h4>
                                <hr />
                                <div class="d-flex align-items-center">
                                    @if (!string.IsNullOrEmpty(profileImageUrl))
                                    {
                                        <img src="@profileImageUrl" alt="Photo de profil" class="img-thumbnail rounded-circle" style="width: 150px; height: 150px; object-fit: cover;" />
                                    }
                                    else
                                    {
                                        <div class="border rounded-circle d-flex justify-content-center align-items-center" style="width: 150px; height: 150px;">
                                            <i class="bi bi-person-fill" style="font-size: 4rem;"></i>
                                        </div>
                                    }
                                    <div class="ms-4">
                                        <InputFile OnChange="OnFileSelected" accept="image/*" class="form-control" />
                                        <small class="text-muted">Taille max: 5 Mo. Formats: JPG, PNG, GIF.</small>
                                        @if (isUploading)
                                        {
                                            <div class="progress mt-2">
                                                <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 100%"></div>
                                            </div>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <h4 class="text-primary">Modifier le mot de passe</h4>
                                <hr />
                                <EditForm Model="@passwordModel" OnValidSubmit="ChangePassword">
                                    <DataAnnotationsValidator />
                                    <ValidationSummary class="text-danger" />
                                    
                                    <div class="mb-3">
                                        <label for="currentPassword" class="form-label">Mot de passe actuel</label>
                                        <InputText type="password" id="currentPassword" class="form-control" @bind-Value="passwordModel.CurrentPassword" />
                                        <ValidationMessage For="@(() => passwordModel.CurrentPassword)" class="text-danger" />
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="newPassword" class="form-label">Nouveau mot de passe</label>
                                        <InputText type="password" id="newPassword" class="form-control" @bind-Value="passwordModel.NewPassword" />
                                        <ValidationMessage For="@(() => passwordModel.NewPassword)" class="text-danger" />
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="confirmPassword" class="form-label">Confirmer le nouveau mot de passe</label>
                                        <InputText type="password" id="confirmPassword" class="form-control" @bind-Value="passwordModel.ConfirmPassword" />
                                        <ValidationMessage For="@(() => passwordModel.ConfirmPassword)" class="text-danger" />
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary">Modifier le mot de passe</button>
                                </EditForm>
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="alert alert-danger">
                            Une erreur est survenue lors du chargement de votre profil. Veuillez vous reconnecter.
                        </div>
                        <button class="btn btn-primary" @onclick="NavigateToLogin">Se reconnecter</button>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private UserDto? currentUser;
    private bool isLoading = true;
    private PasswordChangeModel passwordModel = new();
    private string? profileImageUrl;
    private bool isUploading = false;

    protected override async Task OnInitializedAsync()
    {
        try
        {
            isLoading = true;
            currentUser = await AuthService.GetCurrentUserAsync();
            if (currentUser != null)
            {
                profileImageUrl = currentUser.ProfilePictureUrl;
            }
        }
        catch (Exception ex)
        {
            NotificationService.ShowError($"Erreur: {ex.Message}");
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task OnFileSelected(InputFileChangeEventArgs e)
    {
        var file = e.File;
        if (file == null) return;

        isUploading = true;
        StateHasChanged();

        try
        {
            var newImageUrl = await ImageService.UploadProfileImageAsync(file);
            if (!string.IsNullOrEmpty(newImageUrl))
            {
                profileImageUrl = newImageUrl;
                NotificationService.ShowSuccess("Photo de profil mise à jour avec succès.");
            }
            else
            {
                NotificationService.ShowError("Erreur lors du téléversement de l'image.");
            }
        }
        catch (Exception ex)
        {
            NotificationService.ShowError($"Erreur: {ex.Message}");
        }
        finally
        {
            isUploading = false;
            StateHasChanged();
        }
    }

    private void ChangePassword()
    {
        try
        {
            // Implémentation à venir pour le changement de mot de passe
            NotificationService.ShowSuccess("Mot de passe modifié avec succès.");
            passwordModel = new PasswordChangeModel();
        }
        catch (Exception ex)
        {
            NotificationService.ShowError($"Erreur lors de la modification du mot de passe: {ex.Message}");
        }
    }

    private void NavigateToLogin()
    {
        NavigationManager.NavigateTo("/login");
    }

    public class PasswordChangeModel
    {
        [Required(ErrorMessage = "Le mot de passe actuel est requis")]
        [Display(Name = "Mot de passe actuel")]
        public string CurrentPassword { get; set; } = string.Empty;

        [Required(ErrorMessage = "Le nouveau mot de passe est requis")]
        [StringLength(100, ErrorMessage = "Le {0} doit faire au moins {2} caractères de long.", MinimumLength = 6)]
        [Display(Name = "Nouveau mot de passe")]
        public string NewPassword { get; set; } = string.Empty;

        [Required(ErrorMessage = "La confirmation du mot de passe est requise")]
        [Compare("NewPassword", ErrorMessage = "Le nouveau mot de passe et sa confirmation ne correspondent pas.")]
        [Display(Name = "Confirmer le nouveau mot de passe")]
        public string ConfirmPassword { get; set; } = string.Empty;
    }
}
