using Blazored.LocalStorage;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace NafaPlace.Web.Services;

public interface IChatStorageService
{
    Task SaveConversationAsync(int conversationId, string subject, List<ChatMessage> messages);
    Task<List<ChatMessage>?> LoadConversationAsync(int conversationId);
    Task<List<ConversationSummary>> GetConversationSummariesAsync();
    Task DeleteConversationAsync(int conversationId);
    Task ClearAllConversationsAsync();
    Task SaveDraftMessageAsync(int conversationId, string message);
    Task<string?> LoadDraftMessageAsync(int conversationId);
    Task DeleteDraftMessageAsync(int conversationId);
}

public class ChatStorageService : IChatStorageService
{
    private readonly ILocalStorageService _localStorage;
    private readonly ILogger<ChatStorageService> _logger;
    private const string CONVERSATIONS_KEY = "chat_conversations";
    private const string DRAFTS_KEY = "chat_drafts";

    public ChatStorageService(ILocalStorageService localStorage, ILogger<ChatStorageService> logger)
    {
        _localStorage = localStorage;
        _logger = logger;
    }

    public async Task SaveConversationAsync(int conversationId, string subject, List<ChatMessage> messages)
    {
        try
        {
            var conversations = await GetStoredConversationsAsync();
            
            var conversation = new StoredConversation
            {
                Id = conversationId,
                Subject = subject,
                Messages = messages,
                LastUpdated = DateTime.Now,
                MessageCount = messages.Count
            };

            conversations[conversationId] = conversation;
            
            await _localStorage.SetItemAsync(CONVERSATIONS_KEY, conversations);
            
            _logger.LogDebug("Conversation {ConversationId} sauvegardée avec {MessageCount} messages", 
                conversationId, messages.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la sauvegarde de la conversation {ConversationId}", conversationId);
        }
    }

    public async Task<List<ChatMessage>?> LoadConversationAsync(int conversationId)
    {
        try
        {
            var conversations = await GetStoredConversationsAsync();
            
            if (conversations.TryGetValue(conversationId, out var conversation))
            {
                _logger.LogDebug("Conversation {ConversationId} chargée avec {MessageCount} messages", 
                    conversationId, conversation.Messages.Count);
                return conversation.Messages;
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du chargement de la conversation {ConversationId}", conversationId);
            return null;
        }
    }

    public async Task<List<ConversationSummary>> GetConversationSummariesAsync()
    {
        try
        {
            var conversations = await GetStoredConversationsAsync();
            
            return conversations.Values
                .Select(c => new ConversationSummary
                {
                    Id = c.Id,
                    Subject = c.Subject,
                    LastUpdated = c.LastUpdated,
                    MessageCount = c.MessageCount,
                    LastMessage = c.Messages.LastOrDefault()?.Content ?? ""
                })
                .OrderByDescending(c => c.LastUpdated)
                .ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des résumés de conversations");
            return new List<ConversationSummary>();
        }
    }

    public async Task DeleteConversationAsync(int conversationId)
    {
        try
        {
            var conversations = await GetStoredConversationsAsync();
            
            if (conversations.Remove(conversationId))
            {
                await _localStorage.SetItemAsync(CONVERSATIONS_KEY, conversations);
                _logger.LogDebug("Conversation {ConversationId} supprimée", conversationId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression de la conversation {ConversationId}", conversationId);
        }
    }

    public async Task ClearAllConversationsAsync()
    {
        try
        {
            await _localStorage.RemoveItemAsync(CONVERSATIONS_KEY);
            await _localStorage.RemoveItemAsync(DRAFTS_KEY);
            _logger.LogDebug("Toutes les conversations supprimées");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression de toutes les conversations");
        }
    }

    public async Task SaveDraftMessageAsync(int conversationId, string message)
    {
        try
        {
            var drafts = await GetStoredDraftsAsync();
            drafts[conversationId] = new DraftMessage
            {
                ConversationId = conversationId,
                Message = message,
                SavedAt = DateTime.Now
            };
            
            await _localStorage.SetItemAsync(DRAFTS_KEY, drafts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la sauvegarde du brouillon pour la conversation {ConversationId}", conversationId);
        }
    }

    public async Task<string?> LoadDraftMessageAsync(int conversationId)
    {
        try
        {
            var drafts = await GetStoredDraftsAsync();
            
            if (drafts.TryGetValue(conversationId, out var draft))
            {
                // Supprimer les brouillons de plus de 24h
                if (draft.SavedAt < DateTime.Now.AddDays(-1))
                {
                    await DeleteDraftMessageAsync(conversationId);
                    return null;
                }
                
                return draft.Message;
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du chargement du brouillon pour la conversation {ConversationId}", conversationId);
            return null;
        }
    }

    public async Task DeleteDraftMessageAsync(int conversationId)
    {
        try
        {
            var drafts = await GetStoredDraftsAsync();
            
            if (drafts.Remove(conversationId))
            {
                await _localStorage.SetItemAsync(DRAFTS_KEY, drafts);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression du brouillon pour la conversation {ConversationId}", conversationId);
        }
    }

    private async Task<Dictionary<int, StoredConversation>> GetStoredConversationsAsync()
    {
        try
        {
            var conversations = await _localStorage.GetItemAsync<Dictionary<int, StoredConversation>>(CONVERSATIONS_KEY);
            return conversations ?? new Dictionary<int, StoredConversation>();
        }
        catch
        {
            return new Dictionary<int, StoredConversation>();
        }
    }

    private async Task<Dictionary<int, DraftMessage>> GetStoredDraftsAsync()
    {
        try
        {
            var drafts = await _localStorage.GetItemAsync<Dictionary<int, DraftMessage>>(DRAFTS_KEY);
            return drafts ?? new Dictionary<int, DraftMessage>();
        }
        catch
        {
            return new Dictionary<int, DraftMessage>();
        }
    }
}

// Classes pour le stockage local
public class StoredConversation
{
    public int Id { get; set; }
    public string Subject { get; set; } = string.Empty;
    public List<ChatMessage> Messages { get; set; } = new();
    public DateTime LastUpdated { get; set; }
    public int MessageCount { get; set; }
}

public class ConversationSummary
{
    public int Id { get; set; }
    public string Subject { get; set; } = string.Empty;
    public DateTime LastUpdated { get; set; }
    public int MessageCount { get; set; }
    public string LastMessage { get; set; } = string.Empty;
}

public class DraftMessage
{
    public int ConversationId { get; set; }
    public string Message { get; set; } = string.Empty;
    public DateTime SavedAt { get; set; }
}

public class ChatMessage
{
    public string Content { get; set; } = string.Empty;
    public bool IsFromUser { get; set; }
    public DateTime Timestamp { get; set; }
    public string SenderName { get; set; } = string.Empty;
}
