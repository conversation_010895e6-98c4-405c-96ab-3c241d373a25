using NafaPlace.SellerPortal.Models.Reviews;

namespace NafaPlace.SellerPortal.Services;

public interface IReviewService
{
    Task<ReviewsPagedResponse> GetSellerReviewsAsync(ReviewFilterRequest request);
    Task<ReviewStatsDto> GetSellerReviewStatsAsync();
    Task<List<ProductSummaryDto>> GetSellerProductsAsync();
    Task<ReviewDto?> GetReviewByIdAsync(int reviewId);
    Task<bool> ReplyToReviewAsync(CreateReviewReplyRequest request);
    Task<List<ReviewReplyDto>> GetReviewRepliesAsync(int reviewId);
}
