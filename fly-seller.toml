# Configuration pour NafaPlace Seller Portal (Blazor WebAssembly)
app = "nafaplace-seller-test"
primary_region = "cdg"

[build]
  dockerfile = "Dockerfile.seller"

[env]
  ASPNETCORE_ENVIRONMENT = "Staging"
  TZ = "Africa/Conakry"
  LANG = "fr_GN.UTF-8"

[[services]]
  protocol = "tcp"
  internal_port = 80
  processes = ["app"]

  [[services.ports]]
    port = 80
    handlers = ["http"]
    force_https = true

  [[services.ports]]
    port = 443
    handlers = ["tls", "http"]

  [services.concurrency]
    type = "connections"
    hard_limit = 25
    soft_limit = 20

  [[services.tcp_checks]]
    interval = "15s"
    timeout = "2s"
    grace_period = "1s"

  [[services.http_checks]]
    interval = "10s"
    timeout = "2s"
    grace_period = "5s"
    method = "get"
    path = "/"
    protocol = "http"
    tls_skip_verify = false

[http_service]
  internal_port = 80
  force_https = true
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 0
  processes = ["app"]

[[vm]]
  cpu_kind = "shared"
  cpus = 1
  memory_mb = 256
