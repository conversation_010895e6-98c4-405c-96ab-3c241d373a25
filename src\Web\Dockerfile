# Build stage
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copier les fichiers csproj et restaurer les dépendances
COPY ["src/Web/NafaPlace.Web/NafaPlace.Web.csproj", "Web/NafaPlace.Web/"]
RUN dotnet restore "Web/NafaPlace.Web/NafaPlace.Web.csproj"

# Copier le reste des fichiers et construire
COPY ["src/Web/NafaPlace.Web/", "Web/NafaPlace.Web/"]
COPY ["src/Services/Reviews/NafaPlace.Reviews.DTOs/", "Services/Reviews/NafaPlace.Reviews.DTOs/"]
WORKDIR "/src/Web/NafaPlace.Web"
RUN dotnet build "NafaPlace.Web.csproj" -c Release -o /app/build

# Publish stage
FROM build AS publish
RUN dotnet publish "NafaPlace.Web.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Final stage - using Nginx to serve static files
FROM nginx:alpine AS final
WORKDIR /usr/share/nginx/html
COPY --from=publish /app/publish/wwwroot .
COPY src/Web/nginx.conf /etc/nginx/nginx.conf

# Configuration pour le marché africain
ENV TZ=Africa/Dakar
ENV LANG=fr_FR.UTF-8
ENV LANGUAGE=fr_FR.UTF-8
ENV LC_ALL=fr_FR.UTF-8

EXPOSE 80
EXPOSE 443
