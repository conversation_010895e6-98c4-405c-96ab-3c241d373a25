@page "/"
@page "/home"
@inject NavigationManager NavigationManager
@inject IAuthService AuthService

<PageTitle>Accueil - NafaPlace Admin</PageTitle>

<div class="container-fluid px-4">
    <h1 class="mt-4">Bienvenue sur NafaPlace Admin</h1>

    <AuthorizeView>
        <Authorized>
            <div class="alert alert-success">
                <h4>Connexion réussie !</h4>
                <p>Vous êtes connecté en tant que : <strong>@context.User.Identity.Name</strong></p>
                <p>Redirection vers le dashboard en cours...</p>
            </div>
        </Authorized>
        <NotAuthorized>
            <div class="alert alert-warning">
                <p>Vous devez vous connecter pour accéder au portail d'administration.</p>
                <a href="/login" class="btn btn-primary">Se connecter</a>
            </div>
        </NotAuthorized>
    </AuthorizeView>
</div>

@code {
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            var currentUser = await AuthService.GetCurrentUserAsync();
            if (currentUser.IsAuthenticated)
            {
                // Attendre 2 secondes puis rediriger vers le dashboard
                await Task.Delay(2000);
                NavigationManager.NavigateTo("/dashboard", replace: true);
            }
        }
    }
}
