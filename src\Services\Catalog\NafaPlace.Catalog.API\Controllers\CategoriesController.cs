using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NafaPlace.Catalog.Application.Common.Interfaces;
using NafaPlace.Catalog.Application.DTOs.Category;

namespace NafaPlace.Catalog.API.Controllers;

[ApiController]
[Route("api/v1/[controller]")]
[Produces("application/json")]
public class CategoriesController : ControllerBase
{
    private readonly ICategoryService _categoryService;

    public CategoriesController(ICategoryService categoryService)
    {
        _categoryService = categoryService;
    }

    /// <summary>
    /// Récupère toutes les catégories
    /// </summary>
    /// <returns>Liste des catégories</returns>
    /// <response code="200">Retourne la liste des catégories</response>
    [HttpGet]
    [ProducesResponseType(typeof(IEnumerable<CategoryDto>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<CategoryDto>>> GetCategories()
    {
        var categories = await _categoryService.GetAllCategoriesAsync();
        return Ok(categories);
    }

    /// <summary>
    /// Récupère les catégories principales
    /// </summary>
    /// <returns>Liste des catégories principales</returns>
    /// <response code="200">Retourne la liste des catégories principales</response>
    [HttpGet("main")]
    [ProducesResponseType(typeof(IEnumerable<CategoryDto>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<CategoryDto>>> GetMainCategories()
    {
        var categories = await _categoryService.GetMainCategoriesAsync();
        return Ok(categories);
    }

    /// <summary>
    /// Récupère une catégorie par son ID
    /// </summary>
    /// <param name="id">ID de la catégorie</param>
    /// <returns>Détails de la catégorie</returns>
    /// <response code="200">Retourne la catégorie demandée</response>
    /// <response code="404">Catégorie non trouvée</response>
    [HttpGet("{id:int}")]
    [ProducesResponseType(typeof(CategoryDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<CategoryDto>> GetCategory(int id)
    {
        try
        {
            var category = await _categoryService.GetCategoryByIdAsync(id);
            return Ok(category);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
    }

    /// <summary>
    /// Récupère les sous-catégories d'une catégorie
    /// </summary>
    /// <param name="id">ID de la catégorie parente</param>
    /// <returns>Liste des sous-catégories</returns>
    /// <response code="200">Retourne la liste des sous-catégories</response>
    [HttpGet("{id}/subcategories")]
    [ProducesResponseType(typeof(IEnumerable<CategoryDto>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<CategoryDto>>> GetSubcategories(int id)
    {
        var categories = await _categoryService.GetSubcategoriesAsync(id);
        return Ok(categories);
    }

    /// <summary>
    /// Crée une nouvelle catégorie
    /// </summary>
    /// <param name="request">Détails de la catégorie à créer</param>
    /// <returns>Catégorie créée</returns>
    /// <response code="200">Retourne la catégorie créée</response>
    /// <response code="400">Requête invalide</response>
    /// <response code="401">Non autorisé</response>
    [HttpPost]
    [Authorize(Roles = "Admin")]
    [ProducesResponseType(typeof(CategoryDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<CategoryDto>> CreateCategory(CreateCategoryRequest request)
    {
        var category = await _categoryService.CreateCategoryAsync(request);
        return CreatedAtAction(nameof(GetCategory), new { id = category.Id }, category);
    }

    /// <summary>
    /// Met à jour une catégorie existante
    /// </summary>
    /// <param name="id">ID de la catégorie</param>
    /// <param name="request">Nouvelles données de la catégorie</param>
    /// <returns>Catégorie mise à jour</returns>
    /// <response code="200">Retourne la catégorie mise à jour</response>
    /// <response code="400">Requête invalide</response>
    /// <response code="401">Non autorisé</response>
    /// <response code="404">Catégorie non trouvée</response>
    [HttpPut("{id:int}")]
    [Authorize(Roles = "Admin")]
    [ProducesResponseType(typeof(CategoryDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<CategoryDto>> UpdateCategory(int id, UpdateCategoryRequest request)
    {
        try
        {
            var category = await _categoryService.UpdateCategoryAsync(id, request);
            return Ok(category);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
    }

    /// <summary>
    /// Supprime une catégorie
    /// </summary>
    /// <param name="id">ID de la catégorie à supprimer</param>
    /// <returns>Aucun contenu</returns>
    /// <response code="204">Catégorie supprimée avec succès</response>
    /// <response code="401">Non autorisé</response>
    /// <response code="404">Catégorie non trouvée</response>
    [HttpDelete("{id:int}")]
    [Authorize(Roles = "Admin")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult> DeleteCategory(int id)
    {
        var result = await _categoryService.DeleteCategoryAsync(id);
        if (!result)
        {
            return NotFound();
        }

        return NoContent();
    }

    /// <summary>
    /// Récupère les statistiques d'une catégorie
    /// </summary>
    /// <param name="id">ID de la catégorie</param>
    /// <returns>Statistiques de la catégorie</returns>
    /// <response code="200">Retourne les statistiques de la catégorie</response>
    /// <response code="404">Catégorie non trouvée</response>
    [HttpGet("{id}/statistics")]
    [ProducesResponseType(typeof(CategoryStatisticsDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<CategoryStatisticsDto>> GetCategoryStatistics(int id)
    {
        var statistics = await _categoryService.GetCategoryStatisticsAsync(id);
        if (statistics == null)
            return NotFound();

        return Ok(statistics);
    }

    /// <summary>
    /// Recherche des catégories
    /// </summary>
    /// <param name="searchTerm">Terme de recherche</param>
    /// <returns>Liste des catégories correspondantes</returns>
    /// <response code="200">Retourne la liste des catégories</response>
    [HttpGet("search")]
    [ProducesResponseType(typeof(IEnumerable<CategoryDto>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<CategoryDto>>> SearchCategories([FromQuery] string searchTerm)
    {
        var categories = await _categoryService.SearchCategoriesAsync(searchTerm);
        return Ok(categories);
    }
}
