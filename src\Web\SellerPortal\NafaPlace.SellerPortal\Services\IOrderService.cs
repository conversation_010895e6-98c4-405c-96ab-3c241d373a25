using NafaPlace.SellerPortal.Models.Orders;

namespace NafaPlace.SellerPortal.Services;

public interface IOrderService
{
    Task<OrdersPagedResponse> GetOrdersAsync(OrderFilterRequest request);
    Task<Order?> GetOrderByIdAsync(int orderId);
    Task<bool> UpdateOrderStatusAsync(UpdateOrderStatusRequest request);
    Task<bool> UpdatePaymentStatusAsync(UpdatePaymentStatusRequest request);
    Task<bool> UpdateOrderNotesAsync(int orderId, string notes);
    Task<bool> CancelOrderAsync(int orderId);
    Task<byte[]> ExportOrdersAsync(OrderFilterRequest request, string format = "excel");
}
