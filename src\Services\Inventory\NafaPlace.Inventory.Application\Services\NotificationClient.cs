using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace NafaPlace.Inventory.Application.Services;

public interface INotificationClient
{
    Task NotifyLowStockAsync(string sellerId, string productName, int currentStock);
    Task NotifyOutOfStockAsync(string sellerId, string productName);
    Task NotifyStockMovementAsync(string userId, string productName, string movementType, int quantity);
}

public class NotificationClient : INotificationClient
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<NotificationClient> _logger;
    private readonly JsonSerializerOptions _jsonOptions;

    public NotificationClient(HttpClient httpClient, IConfiguration configuration, ILogger<NotificationClient> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
        
        var notificationApiUrl = configuration["ApiSettings:NotificationApiUrl"];
        if (!string.IsNullOrEmpty(notificationApiUrl))
        {
            _httpClient.BaseAddress = new Uri(notificationApiUrl);
        }

        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            PropertyNameCaseInsensitive = true
        };
    }

    public async Task NotifyLowStockAsync(string sellerId, string productName, int currentStock)
    {
        try
        {
            var request = new CreateNotificationRequest
            {
                UserId = sellerId,
                Title = "Alerte Stock Faible",
                Message = $"Le stock du produit '{productName}' est faible ({currentStock} unités restantes). Veuillez réapprovisionner.",
                Type = "StockAlert",
                Priority = "High",
                ActionUrl = "/inventory"
            };

            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("api/notifications", content);
            
            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning("Failed to send low stock notification. Status: {StatusCode}", response.StatusCode);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending low stock notification for product {ProductName}", productName);
        }
    }

    public async Task NotifyOutOfStockAsync(string sellerId, string productName)
    {
        try
        {
            var request = new CreateNotificationRequest
            {
                UserId = sellerId,
                Title = "Rupture de Stock",
                Message = $"Le produit '{productName}' est en rupture de stock. Action immédiate requise.",
                Type = "StockAlert",
                Priority = "Critical",
                ActionUrl = "/inventory"
            };

            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("api/notifications", content);
            
            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning("Failed to send out of stock notification. Status: {StatusCode}", response.StatusCode);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending out of stock notification for product {ProductName}", productName);
        }
    }

    public async Task NotifyStockMovementAsync(string userId, string productName, string movementType, int quantity)
    {
        try
        {
            var request = new CreateNotificationRequest
            {
                UserId = userId,
                Title = "Mouvement de Stock",
                Message = $"Mouvement de stock pour '{productName}': {movementType} de {quantity} unités.",
                Type = "StockMovement",
                Priority = "Normal",
                ActionUrl = "/inventory"
            };

            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("api/notifications", content);
            
            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning("Failed to send stock movement notification. Status: {StatusCode}", response.StatusCode);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending stock movement notification for product {ProductName}", productName);
        }
    }

    private class CreateNotificationRequest
    {
        public string UserId { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Priority { get; set; } = string.Empty;
        public string? ActionUrl { get; set; }
    }
}
