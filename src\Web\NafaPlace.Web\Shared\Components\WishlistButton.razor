@using NafaPlace.Web.Models.Wishlist
@using NafaPlace.Web.Models.Catalog
@using NafaPlace.Web.Services
@using System.Security.Claims
@using Microsoft.AspNetCore.Components.Authorization
@inject IWishlistService WishlistService
@inject IJSRuntime JSRuntime
@inject AuthenticationStateProvider AuthenticationStateProvider

<button class="btn @(_isInWishlist ? "btn-danger" : "btn-outline-secondary") @CssClass" 
        @onclick="ToggleWishlist" 
        disabled="@_isLoading"
        title="@(_isInWishlist ? "Retirer des favoris" : "Ajouter aux favoris")">
    @if (_isLoading)
    {
        <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
    }
    else
    {
        <i class="@(_isInWishlist ? "bi bi-heart-fill" : "bi bi-heart")"></i>
    }
    @if (ShowText)
    {
        <span class="ms-1">@(_isInWishlist ? "Retirer" : "Favoris")</span>
    }
</button>

@code {
    [Parameter] public ProductDto Product { get; set; } = new();
    [Parameter] public string CssClass { get; set; } = "";
    [Parameter] public bool ShowText { get; set; } = false;
    [Parameter] public EventCallback OnWishlistChanged { get; set; }

    private bool _isInWishlist = false;
    private bool _isLoading = false;
    private string? _userId;

    [CascadingParameter]
    private Task<AuthenticationState>? AuthenticationStateTask { get; set; }

    protected override async Task OnInitializedAsync()
    {
        await LoadUserInfo();
        await CheckWishlistStatus();
    }

    protected override async Task OnParametersSetAsync()
    {
        if (Product?.Id > 0)
        {
            await CheckWishlistStatus();
        }
    }

    private async Task LoadUserInfo()
    {
        if (AuthenticationStateTask != null)
        {
            var authState = await AuthenticationStateTask;
            var user = authState.User;

            if (user.Identity?.IsAuthenticated == true)
            {
                _userId = user.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            }
            else
            {
                _userId = await GetOrCreateGuestUserId();
            }
        }
        else
        {
            _userId = await GetOrCreateGuestUserId();
        }
    }

    private async Task CheckWishlistStatus()
    {
        if (string.IsNullOrEmpty(_userId) || Product?.Id <= 0) return;

        try
        {
            _isInWishlist = await WishlistService.IsProductInWishlistAsync(_userId, Product.Id);
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la vérification du statut wishlist: {ex.Message}");
        }
    }

    private async Task ToggleWishlist()
    {
        if (string.IsNullOrEmpty(_userId) || Product?.Id <= 0) return;

        _isLoading = true;
        StateHasChanged();

        try
        {
            if (_isInWishlist)
            {
                // Retirer de la wishlist
                var success = await WishlistService.RemoveFromWishlistAsync(_userId, Product.Id);
                if (success)
                {
                    _isInWishlist = false;
                    await JSRuntime.InvokeVoidAsync("showToast", "Produit retiré des favoris", "success");
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("showToast", "Erreur lors de la suppression", "error");
                }
            }
            else
            {
                // Ajouter à la wishlist
                var request = new AddToWishlistRequest
                {
                    ProductId = Product.Id,
                    ProductName = Product.Name,
                    ProductPrice = Product.Price,
                    Currency = Product.Currency,
                    ProductImageUrl = Product.Images?.FirstOrDefault()?.Url,
                    ProductBrand = Product.Brand,
                    CategoryId = Product.CategoryId,
                    CategoryName = Product.Category?.Name
                };

                var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
                var user = authState.User;

                if (user.Identity?.IsAuthenticated == true)
                {
                    await WishlistService.AddToWishlistAsync(_userId, request);
                }
                else
                {
                    var guestId = _userId.Replace("guest_", "");
                    await WishlistService.AddToGuestWishlistAsync(guestId, request);
                }

                _isInWishlist = true;
                await JSRuntime.InvokeVoidAsync("showToast", "Produit ajouté aux favoris", "success");
            }

            // Notifier le parent du changement
            if (OnWishlistChanged.HasDelegate)
            {
                await OnWishlistChanged.InvokeAsync();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la modification de la wishlist: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("showToast", $"Erreur: {ex.Message}", "error");
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    private async Task<string> GetOrCreateGuestUserId()
    {
        var guestId = await JSRuntime.InvokeAsync<string>("localStorage.getItem", "guestUserId");

        if (string.IsNullOrEmpty(guestId))
        {
            guestId = $"guest_{Random.Shared.Next(1, int.MaxValue)}";
            await JSRuntime.InvokeVoidAsync("localStorage.setItem", "guestUserId", guestId);
        }

        return guestId;
    }
}
