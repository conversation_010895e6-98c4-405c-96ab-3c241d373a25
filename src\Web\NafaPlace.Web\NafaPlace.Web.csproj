<Project Sdk="Microsoft.NET.Sdk.BlazorWebAssembly">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <BlazorWebAssemblyLoadAllGlobalizationData>false</BlazorWebAssemblyLoadAllGlobalizationData>
    <PublishTrimmed>false</PublishTrimmed>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Blazored.LocalStorage" Version="4.5.0" />
    <PackageReference Include="Microsoft.AspNetCore.Components.Authorization" Version="9.0.0-preview.1.24081.5" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="9.0.0-preview.1.24081.5" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.DevServer" Version="9.0.0-preview.1.24081.5" PrivateAssets="all" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.6.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="../../Services/Reviews/NafaPlace.Reviews.DTOs/NafaPlace.Reviews.DTOs.csproj" />
  </ItemGroup>

</Project>
