using System.ComponentModel.DataAnnotations;
using NafaPlace.Common.Models;

namespace NafaPlace.Wishlist.Domain.Models;

public class Wishlist : BaseEntity
{
    [Required]
    [MaxLength(50)]
    public required string UserId { get; set; }

    [MaxLength(100)]
    public string? Name { get; set; } = "Ma liste de souhaits";

    [MaxLength(500)]
    public string? Description { get; set; }

    public bool IsPublic { get; set; } = false;

    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

    // Navigation properties
    public virtual ICollection<WishlistItem> Items { get; set; } = new List<WishlistItem>();

    // Computed properties
    public int ItemCount => Items?.Count ?? 0;
    public decimal TotalValue => Items?.Sum(i => i.ProductPrice) ?? 0;
}
