using NafaPlace.Delivery.Domain.Models;

namespace NafaPlace.Delivery.Application.DTOs;

public class DeliveryZoneDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public string? Description { get; set; }
    public ZoneType Type { get; set; }
    public string? ParentZoneCode { get; set; }
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
    public double? Radius { get; set; }
    public bool IsActive { get; set; }
    public decimal BaseDeliveryFee { get; set; }
    public decimal? FreeDeliveryThreshold { get; set; }
    public int EstimatedDeliveryDays { get; set; }
    public int MaxDeliveryDays { get; set; }
    public bool SameDayDeliveryAvailable { get; set; }
    public decimal? SameDayDeliveryFee { get; set; }
    public bool ExpressDeliveryAvailable { get; set; }
    public decimal? ExpressDeliveryFee { get; set; }
    public string Currency { get; set; } = "GNF";
    public decimal? MaxWeight { get; set; }
    public decimal? MaxVolume { get; set; }
    public TimeSpan? DeliveryStartTime { get; set; }
    public TimeSpan? DeliveryEndTime { get; set; }
    public List<string> DeliveryDays { get; set; } = new();
    public DateTime CreatedAt { get; set; }
}

public class CarrierDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string? LogoUrl { get; set; }
    public string? ContactEmail { get; set; }
    public string? ContactPhone { get; set; }
    public string? Address { get; set; }
    public string? Website { get; set; }
    public bool IsActive { get; set; }
    public CarrierType Type { get; set; }
    public decimal Rating { get; set; }
    public int ReviewCount { get; set; }
    public int TotalDeliveries { get; set; }
    public int SuccessfulDeliveries { get; set; }
    public decimal SuccessRate { get; set; }
    public DateTime CreatedAt { get; set; }
    public List<CarrierZoneDto> Zones { get; set; } = new();
}

public class CarrierZoneDto
{
    public int Id { get; set; }
    public int CarrierId { get; set; }
    public int ZoneId { get; set; }
    public string CarrierName { get; set; } = string.Empty;
    public string ZoneName { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public decimal DeliveryFee { get; set; }
    public decimal? FreeDeliveryThreshold { get; set; }
    public int EstimatedDeliveryDays { get; set; }
    public int MaxDeliveryDays { get; set; }
    public bool SameDayDeliveryAvailable { get; set; }
    public decimal? SameDayDeliveryFee { get; set; }
    public bool ExpressDeliveryAvailable { get; set; }
    public decimal? ExpressDeliveryFee { get; set; }
    public decimal? MaxWeight { get; set; }
    public decimal? MaxVolume { get; set; }
    public int Priority { get; set; }
}

public class DeliveryOrderDto
{
    public int Id { get; set; }
    public string OrderId { get; set; } = string.Empty;
    public string TrackingNumber { get; set; } = string.Empty;
    public int CarrierId { get; set; }
    public string CarrierName { get; set; } = string.Empty;
    public int ZoneId { get; set; }
    public string ZoneName { get; set; } = string.Empty;
    public string CustomerId { get; set; } = string.Empty;
    public string CustomerName { get; set; } = string.Empty;
    public string CustomerEmail { get; set; } = string.Empty;
    public string CustomerPhone { get; set; } = string.Empty;
    public string DeliveryAddress { get; set; } = string.Empty;
    public string? DeliveryCity { get; set; }
    public string? DeliveryRegion { get; set; }
    public decimal OrderValue { get; set; }
    public decimal DeliveryFee { get; set; }
    public decimal TotalFee { get; set; }
    public string Currency { get; set; } = "GNF";
    public decimal? Weight { get; set; }
    public int PackageCount { get; set; }
    public DeliveryStatus Status { get; set; }
    public DeliveryType Type { get; set; }
    public DateTime? ScheduledDeliveryDate { get; set; }
    public DateTime? EstimatedDeliveryDate { get; set; }
    public DateTime? ActualDeliveryDate { get; set; }
    public string? DeliveryInstructions { get; set; }
    public string? DeliveryPersonName { get; set; }
    public string? ReceivedByName { get; set; }
    public int DeliveryAttempts { get; set; }
    public int? CustomerRating { get; set; }
    public string? CustomerFeedback { get; set; }
    public DateTime CreatedAt { get; set; }
    public List<DeliveryTrackingDto> TrackingEvents { get; set; } = new();
}

public class DeliveryTrackingDto
{
    public int Id { get; set; }
    public int DeliveryOrderId { get; set; }
    public DeliveryStatus Status { get; set; }
    public string Description { get; set; } = string.Empty;
    public string? Location { get; set; }
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
    public DateTime EventDate { get; set; }
    public string? EventBy { get; set; }
    public string? Notes { get; set; }
    public string? PhotoUrl { get; set; }
    public bool IsCustomerVisible { get; set; }
    public bool IsAutomated { get; set; }
}

// Request DTOs
public class CreateDeliveryOrderRequest
{
    public string OrderId { get; set; } = string.Empty;
    public string CustomerId { get; set; } = string.Empty;
    public string CustomerName { get; set; } = string.Empty;
    public string CustomerEmail { get; set; } = string.Empty;
    public string CustomerPhone { get; set; } = string.Empty;
    public string DeliveryAddress { get; set; } = string.Empty;
    public string? DeliveryCity { get; set; }
    public string? DeliveryRegion { get; set; }
    public double? DeliveryLatitude { get; set; }
    public double? DeliveryLongitude { get; set; }
    public decimal OrderValue { get; set; }
    public decimal? Weight { get; set; }
    public decimal? Volume { get; set; }
    public int PackageCount { get; set; } = 1;
    public string? PackageDescription { get; set; }
    public DeliveryType Type { get; set; } = DeliveryType.Standard;
    public DateTime? ScheduledDeliveryDate { get; set; }
    public TimeSpan? PreferredDeliveryTimeStart { get; set; }
    public TimeSpan? PreferredDeliveryTimeEnd { get; set; }
    public string? DeliveryInstructions { get; set; }
    public string? SpecialRequirements { get; set; }
    public int? PreferredCarrierId { get; set; }
    public string? PreferredZoneCode { get; set; }
}

public class DeliveryQuoteRequest
{
    public string DeliveryAddress { get; set; } = string.Empty;
    public string? DeliveryCity { get; set; }
    public string? DeliveryRegion { get; set; }
    public double? DeliveryLatitude { get; set; }
    public double? DeliveryLongitude { get; set; }
    public decimal OrderValue { get; set; }
    public decimal? Weight { get; set; }
    public decimal? Volume { get; set; }
    public DeliveryType Type { get; set; } = DeliveryType.Standard;
    public DateTime? ScheduledDeliveryDate { get; set; }
}

public class DeliveryQuoteDto
{
    public int CarrierId { get; set; }
    public string CarrierName { get; set; } = string.Empty;
    public int ZoneId { get; set; }
    public string ZoneName { get; set; } = string.Empty;
    public decimal DeliveryFee { get; set; }
    public decimal? InsuranceFee { get; set; }
    public decimal TotalFee { get; set; }
    public string Currency { get; set; } = "GNF";
    public int EstimatedDeliveryDays { get; set; }
    public int MaxDeliveryDays { get; set; }
    public DateTime EstimatedDeliveryDate { get; set; }
    public bool IsAvailable { get; set; }
    public string? UnavailabilityReason { get; set; }
    public bool SameDayAvailable { get; set; }
    public decimal? SameDayFee { get; set; }
    public bool ExpressAvailable { get; set; }
    public decimal? ExpressFee { get; set; }
}

public class UpdateDeliveryStatusRequest
{
    public DeliveryStatus Status { get; set; }
    public string Description { get; set; } = string.Empty;
    public string? Location { get; set; }
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
    public string? Notes { get; set; }
    public string? PhotoUrl { get; set; }
    public string? EventBy { get; set; }
    public bool IsCustomerVisible { get; set; } = true;
}

public class DeliveryStatsDto
{
    public int TotalDeliveries { get; set; }
    public int PendingDeliveries { get; set; }
    public int InTransitDeliveries { get; set; }
    public int CompletedDeliveries { get; set; }
    public int FailedDeliveries { get; set; }
    public decimal SuccessRate { get; set; }
    public decimal AverageDeliveryTime { get; set; } // En heures
    public decimal TotalRevenue { get; set; }
    public string Currency { get; set; } = "GNF";
    public Dictionary<DeliveryStatus, int> StatusBreakdown { get; set; } = new();
    public Dictionary<string, int> CarrierBreakdown { get; set; } = new();
    public Dictionary<string, int> ZoneBreakdown { get; set; } = new();
    public List<DailyDeliveryStatsDto> DailyStats { get; set; } = new();
}

public class DailyDeliveryStatsDto
{
    public DateTime Date { get; set; }
    public int TotalDeliveries { get; set; }
    public int CompletedDeliveries { get; set; }
    public int FailedDeliveries { get; set; }
    public decimal Revenue { get; set; }
}

public class CreateCarrierRequest
{
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string? LogoUrl { get; set; }
    public string? ContactEmail { get; set; }
    public string? ContactPhone { get; set; }
    public string? Address { get; set; }
    public string? Website { get; set; }
    public CarrierType Type { get; set; }
    public string? ApiEndpoint { get; set; }
    public string? ApiKey { get; set; }
    public string? ApiSecret { get; set; }
}

public class UpdateCarrierRequest
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string? LogoUrl { get; set; }
    public string? ContactEmail { get; set; }
    public string? ContactPhone { get; set; }
    public string? Address { get; set; }
    public string? Website { get; set; }
    public CarrierType Type { get; set; }
    public string? ApiEndpoint { get; set; }
    public string? ApiKey { get; set; }
    public string? ApiSecret { get; set; }
    public bool IsActive { get; set; } = true;
}

public class CreateCarrierZoneRequest
{
    public int CarrierId { get; set; }
    public int ZoneId { get; set; }
    public decimal DeliveryFee { get; set; }
    public decimal? FreeDeliveryThreshold { get; set; }
    public int EstimatedDeliveryDays { get; set; } = 1;
    public int MaxDeliveryDays { get; set; } = 7;
    public bool SameDayDeliveryAvailable { get; set; } = false;
    public decimal? SameDayDeliveryFee { get; set; }
    public bool ExpressDeliveryAvailable { get; set; } = false;
    public decimal? ExpressDeliveryFee { get; set; }
}

public class UpdateCarrierZoneRequest
{
    public decimal DeliveryFee { get; set; }
    public decimal? FreeDeliveryThreshold { get; set; }
    public int EstimatedDeliveryDays { get; set; } = 1;
    public int MaxDeliveryDays { get; set; } = 7;
    public bool SameDayDeliveryAvailable { get; set; } = false;
    public decimal? SameDayDeliveryFee { get; set; }
    public bool ExpressDeliveryAvailable { get; set; } = false;
    public decimal? ExpressDeliveryFee { get; set; }
    public bool IsActive { get; set; } = true;
}

public class CreateDeliveryZoneRequest
{
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public string? Description { get; set; }
    public ZoneType Type { get; set; }
    public string? ParentZoneCode { get; set; }
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
    public double? Radius { get; set; }
    public decimal BaseDeliveryFee { get; set; }
    public decimal? FreeDeliveryThreshold { get; set; }
    public int EstimatedDeliveryDays { get; set; } = 1;
    public int MaxDeliveryDays { get; set; } = 7;
    public bool SameDayDeliveryAvailable { get; set; } = false;
    public decimal? SameDayDeliveryFee { get; set; }
    public bool ExpressDeliveryAvailable { get; set; } = false;
    public decimal? ExpressDeliveryFee { get; set; }
    public string? Currency { get; set; } = "GNF";
    public decimal? MaxWeight { get; set; }
    public decimal? MaxVolume { get; set; }
    public decimal? MaxOrderValue { get; set; }
    public decimal? MinOrderValue { get; set; }
    public TimeSpan? DeliveryStartTime { get; set; }
    public TimeSpan? DeliveryEndTime { get; set; }
    public string? DeliveryDays { get; set; }
}

public class UpdateDeliveryZoneRequest
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public decimal BaseDeliveryFee { get; set; }
    public decimal? FreeDeliveryThreshold { get; set; }
    public int EstimatedDeliveryDays { get; set; } = 1;
    public int MaxDeliveryDays { get; set; } = 7;
    public bool SameDayDeliveryAvailable { get; set; } = false;
    public decimal? SameDayDeliveryFee { get; set; }
    public bool ExpressDeliveryAvailable { get; set; } = false;
    public decimal? ExpressDeliveryFee { get; set; }
    public decimal? MaxWeight { get; set; }
    public decimal? MaxVolume { get; set; }
    public decimal? MaxOrderValue { get; set; }
    public decimal? MinOrderValue { get; set; }
    public TimeSpan? DeliveryStartTime { get; set; }
    public TimeSpan? DeliveryEndTime { get; set; }
    public string? DeliveryDays { get; set; }
    public bool IsActive { get; set; } = true;
}

public class AdvancedFeeCalculationRequest
{
    public int ZoneId { get; set; }
    public decimal OrderAmount { get; set; }
    public decimal? Weight { get; set; }
    public decimal? Volume { get; set; }
    public DeliveryType DeliveryType { get; set; } = DeliveryType.Standard;
}

// Additional DTOs for advanced features
public class DeliveryRouteDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public int CarrierId { get; set; }
    public string CarrierName { get; set; } = string.Empty;
    public string DriverId { get; set; } = string.Empty;
    public string DriverName { get; set; } = string.Empty;
    public string? DriverPhone { get; set; }
    public DateTime RouteDate { get; set; }
    public DateTime? StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public RouteStatus Status { get; set; }
    public int TotalDeliveries { get; set; }
    public int CompletedDeliveries { get; set; }
    public int FailedDeliveries { get; set; }
    public decimal? TotalDistance { get; set; }
    public decimal? EstimatedDuration { get; set; }
    public decimal? ActualDuration { get; set; }
}

public class CreateDeliveryRouteRequest
{
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public int CarrierId { get; set; }
    public string DriverId { get; set; } = string.Empty;
    public string DriverName { get; set; } = string.Empty;
    public string? DriverPhone { get; set; }
    public DateTime RouteDate { get; set; }
    public List<int> DeliveryOrderIds { get; set; } = new();
}

public class CarrierPerformanceDto
{
    public int CarrierId { get; set; }
    public string CarrierName { get; set; } = string.Empty;
    public int TotalDeliveries { get; set; }
    public int SuccessfulDeliveries { get; set; }
    public decimal SuccessRate { get; set; }
    public decimal AverageDeliveryTime { get; set; }
    public decimal AverageRating { get; set; }
    public decimal TotalRevenue { get; set; }
}

public class ZonePerformanceDto
{
    public int ZoneId { get; set; }
    public string ZoneName { get; set; } = string.Empty;
    public int TotalDeliveries { get; set; }
    public int SuccessfulDeliveries { get; set; }
    public decimal SuccessRate { get; set; }
    public decimal AverageDeliveryTime { get; set; }
    public decimal TotalRevenue { get; set; }
}

public enum RouteStatus
{
    Planned = 1,
    InProgress = 2,
    Completed = 3,
    Cancelled = 4
}

public enum NotificationChannel
{
    Email = 1,
    SMS = 2,
    Push = 3,
    InApp = 4
}
