using NafaPlace.Web.Models;

namespace NafaPlace.Web.Services;

public interface ICouponService
{
    Task<CouponValidationResult?> ValidateCouponAsync(string couponCode, CartSummary cart);
    Task<CouponApplicationResult?> ApplyCouponAsync(string couponCode, CartSummary cart);
    Task<List<CouponDto>> GetAvailableCouponsAsync(string userId, CartSummary cart);
    Task<bool> RecordCouponUsageAsync(int couponId, string userId, string orderId, decimal discountAmount);
}
