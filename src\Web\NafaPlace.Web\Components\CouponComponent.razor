@using NafaPlace.Web.Services
@using NafaPlace.Web.Models
@inject ICouponService CouponService
@inject IJSRuntime JSRuntime

<div class="coupon-section">
    <div class="card">
        <div class="card-header">
            <h6 class="mb-0">
                <i class="fas fa-ticket-alt me-2"></i>
                Code Promo
            </h6>
        </div>
        <div class="card-body">
            @if (appliedCoupon != null)
            {
                <!-- Coupon appliqué -->
                <div class="alert alert-success d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>@appliedCoupon.Code</strong> - @appliedCoupon.Name
                        <br>
                        <small class="text-muted">
                            Réduction: @GetDiscountText(appliedCoupon, DiscountAmount)
                        </small>
                    </div>
                    <button class="btn btn-sm btn-outline-danger" @onclick="RemoveCoupon">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            }
            else
            {
                <!-- Formulaire d'application de coupon -->
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="Entrez votre code promo" 
                           @bind="couponCode" @onkeypress="OnKeyPress" disabled="@isApplying">
                    <button class="btn btn-primary" @onclick="ApplyCoupon" disabled="@(isApplying || string.IsNullOrWhiteSpace(couponCode))">
                        @if (isApplying)
                        {
                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                        }
                        else
                        {
                            <i class="fas fa-plus me-2"></i>
                        }
                        Appliquer
                    </button>
                </div>

                @if (!string.IsNullOrEmpty(errorMessage))
                {
                    <div class="alert alert-danger mt-2 mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        @errorMessage
                    </div>
                }

                @if (availableCoupons.Any())
                {
                    <div class="mt-3">
                        <small class="text-muted">Coupons disponibles:</small>
                        <div class="available-coupons mt-2">
                            @foreach (var coupon in availableCoupons.Take(3))
                            {
                                <div class="coupon-suggestion" @onclick="() => SelectCoupon(coupon.Code)">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong>@coupon.Code</strong>
                                            <br>
                                            <small class="text-muted">@coupon.Name</small>
                                        </div>
                                        <div class="text-end">
                                            <span class="badge bg-primary">
                                                @GetDiscountText(coupon, 0)
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                }
            }
        </div>
    </div>
</div>

<style>
    .coupon-section .card {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
    }

    .coupon-section .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #e0e0e0;
        padding: 0.75rem 1rem;
    }

    .available-coupons .coupon-suggestion {
        background: #f8f9fa;
        border: 1px solid #e0e0e0;
        border-radius: 6px;
        padding: 0.75rem;
        margin-bottom: 0.5rem;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .available-coupons .coupon-suggestion:hover {
        background: #e9ecef;
        border-color: #007bff;
        transform: translateY(-1px);
    }

    .available-coupons .coupon-suggestion:last-child {
        margin-bottom: 0;
    }
</style>

@code {
    [Parameter] public CartSummary? Cart { get; set; }
    [Parameter] public string? UserId { get; set; }
    [Parameter] public EventCallback<CouponApplicationResult> OnCouponApplied { get; set; }
    [Parameter] public EventCallback OnCouponRemoved { get; set; }

    private string couponCode = "";
    private string errorMessage = "";
    private bool isApplying = false;
    private List<CouponDto> availableCoupons = new();
    private CouponDto? appliedCoupon = null;
    private decimal DiscountAmount = 0;

    protected override async Task OnParametersSetAsync()
    {
        if (Cart != null && !string.IsNullOrEmpty(UserId))
        {
            await LoadAvailableCoupons();
        }
    }

    private async Task LoadAvailableCoupons()
    {
        try
        {
            if (Cart != null && !string.IsNullOrEmpty(UserId))
            {
                availableCoupons = await CouponService.GetAvailableCouponsAsync(UserId, Cart);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading available coupons: {ex.Message}");
        }
    }

    private async Task ApplyCoupon()
    {
        if (string.IsNullOrWhiteSpace(couponCode) || Cart == null)
            return;

        isApplying = true;
        errorMessage = "";

        try
        {
            var result = await CouponService.ApplyCouponAsync(couponCode.Trim().ToUpper(), Cart);
            
            if (result != null && result.Success)
            {
                appliedCoupon = result.AppliedCoupon;
                DiscountAmount = result.DiscountAmount;
                couponCode = "";

                await OnCouponApplied.InvokeAsync(result);
                await JSRuntime.InvokeVoidAsync("showToast", "success", "Coupon appliqué avec succès!");
            }
            else
            {
                errorMessage = result?.ErrorMessage ?? "Code promo invalide ou expiré";
            }
        }
        catch (Exception ex)
        {
            errorMessage = "Erreur lors de l'application du coupon";
            Console.WriteLine($"Error applying coupon: {ex.Message}");
        }
        finally
        {
            isApplying = false;
            StateHasChanged();
        }
    }

    private async Task RemoveCoupon()
    {
        appliedCoupon = null;
        DiscountAmount = 0;
        errorMessage = "";
        
        await OnCouponRemoved.InvokeAsync();
        await JSRuntime.InvokeVoidAsync("showToast", "info", "Coupon retiré");
        StateHasChanged();
    }

    private async Task SelectCoupon(string code)
    {
        couponCode = code;
        await ApplyCoupon();
    }

    private async Task OnKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            await ApplyCoupon();
        }
    }

    private string GetDiscountText(CouponDto coupon, decimal discountAmount)
    {
        return coupon.Type switch
        {
            CouponType.FixedAmount => $"{coupon.Value:N0} GNF",
            CouponType.Percentage => $"{coupon.Value}%",
            CouponType.FreeShipping => "Livraison gratuite",
            _ => discountAmount > 0 ? $"{discountAmount:N0} GNF" : "Réduction"
        };
    }

    public void SetAppliedCoupon(CouponDto coupon, decimal discount)
    {
        appliedCoupon = coupon;
        DiscountAmount = discount;
        StateHasChanged();
    }

    public void ClearCoupon()
    {
        appliedCoupon = null;
        DiscountAmount = 0;
        couponCode = "";
        errorMessage = "";
        StateHasChanged();
    }
}
