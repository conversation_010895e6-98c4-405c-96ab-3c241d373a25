using Microsoft.EntityFrameworkCore;
using NafaPlace.Coupon.Application.DTOs;
using NafaPlace.Coupon.Application.Interfaces;
using NafaPlace.Coupon.Domain.Models;
using NafaPlace.Coupon.Infrastructure.Data;

namespace NafaPlace.Coupon.Infrastructure.Repositories;

public class CouponRepository : ICouponRepository
{
    private readonly CouponDbContext _context;

    public CouponRepository(CouponDbContext context)
    {
        _context = context;
    }

    public async Task<Domain.Models.Coupon> CreateAsync(Domain.Models.Coupon coupon)
    {
        _context.Coupons.Add(coupon);
        await _context.SaveChangesAsync();
        return coupon;
    }

    public async Task<Domain.Models.Coupon> UpdateAsync(Domain.Models.Coupon coupon)
    {
        _context.Coupons.Update(coupon);
        await _context.SaveChangesAsync();
        return coupon;
    }

    public async Task<IEnumerable<Domain.Models.Coupon>> GetAllAsync()
    {
        return await _context.Coupons
            .Include(c => c.Usages)
            .ToListAsync();
    }

    public async Task<IEnumerable<Domain.Models.Coupon>> GetActiveAsync()
    {
        return await _context.Coupons
            .Where(c => c.IsActive && (!c.ExpiresAt.HasValue || c.ExpiresAt > DateTime.UtcNow))
            .Include(c => c.Usages)
            .ToListAsync();
    }

    public async Task DeleteAsync(int id)
    {
        var coupon = await _context.Coupons.FindAsync(id);
        if (coupon == null) return;

        _context.Coupons.Remove(coupon);
        await _context.SaveChangesAsync();
    }

    public async Task<Domain.Models.Coupon?> GetByIdAsync(int id)
    {
        return await _context.Coupons
            .Include(c => c.Usages)
            .FirstOrDefaultAsync(c => c.Id == id);
    }

    public async Task<Domain.Models.Coupon?> GetByCodeAsync(string code)
    {
        return await _context.Coupons
            .Include(c => c.Usages)
            .FirstOrDefaultAsync(c => c.Code == code.ToUpper());
    }

    public async Task<List<Domain.Models.Coupon>> GetCouponsAsync(int page = 1, int pageSize = 20, bool? isActive = null)
    {
        var query = _context.Coupons.AsQueryable();

        if (isActive.HasValue)
        {
            query = query.Where(c => c.IsActive == isActive.Value);
        }

        return await query
            .OrderByDescending(c => c.CreatedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();
    }

    public async Task<IEnumerable<Domain.Models.Coupon>> GetActiveCouponsAsync()
    {
        var now = DateTime.UtcNow;
        return await _context.Coupons
            .Where(c => c.IsActive && c.StartDate <= now && c.EndDate >= now)
            .Where(c => !c.UsageLimit.HasValue || c.UsageCount < c.UsageLimit.Value)
            .OrderBy(c => c.EndDate)
            .ToListAsync();
    }

    public async Task<IEnumerable<Domain.Models.Coupon>> GetExpiredCouponsAsync()
    {
        var now = DateTime.UtcNow;
        return await _context.Coupons
            .Where(c => c.EndDate < now)
            .OrderByDescending(c => c.EndDate)
            .ToListAsync();
    }

    public async Task<bool> RecordUsageAsync(CouponUsage usage)
    {
        _context.CouponUsages.Add(usage);
        await _context.SaveChangesAsync();
        return true;
    }





    public async Task<CouponStatsDto> GetCouponStatsAsync(int couponId)
    {
        var coupon = await _context.Coupons.FindAsync(couponId);
        var usages = await _context.CouponUsages
            .Where(u => u.CouponId == couponId)
            .ToListAsync();

        return new CouponStatsDto
        {
            Id = couponId,
            Code = coupon?.Code ?? "",
            Name = coupon?.Name ?? "",
            TotalUsages = usages.Count,
            MaxUsages = coupon?.MaxUsages ?? 0,
            TotalDiscountAmount = usages.Sum(u => u.DiscountAmount),
            TotalDiscountGiven = usages.Sum(u => u.DiscountAmount),
            UniqueUsers = usages.Select(u => u.UserId).Distinct().Count(),
            LastUsed = usages.Any() ? usages.Max(u => u.UsedAt) : null,
            CreatedAt = coupon?.CreatedAt ?? DateTime.UtcNow,
            ExpiresAt = coupon?.ExpiresAt,
            IsActive = coupon?.IsActive ?? false,
            IsExpired = coupon?.ExpiresAt < DateTime.UtcNow,
            UsagePercentage = coupon?.MaxUsages > 0 ? (double)usages.Count / coupon.MaxUsages.Value * 100 : 0
        };
    }





    // Additional methods required by interface
    public async Task<bool> IsCodeUniqueAsync(string code, int? excludeId = null)
    {
        var query = _context.Coupons.Where(c => c.Code == code.ToUpper());
        if (excludeId.HasValue)
        {
            query = query.Where(c => c.Id != excludeId.Value);
        }
        return !await query.AnyAsync();
    }

    public async Task<bool> CanUseCouponAsync(string code, string userId, decimal orderAmount)
    {
        var coupon = await GetByCodeAsync(code);
        if (coupon == null || !coupon.IsActive) return false;

        if (coupon.ExpiresAt.HasValue && coupon.ExpiresAt < DateTime.UtcNow) return false;
        if (coupon.MinimumOrderAmount.HasValue && orderAmount < coupon.MinimumOrderAmount) return false;
        if (coupon.MaxUsages.HasValue)
        {
            var usageCount = await GetUsageCountAsync(coupon.Id);
            if (usageCount >= coupon.MaxUsages) return false;
        }
        if (coupon.MaxUsagesPerUser.HasValue)
        {
            var userUsageCount = await GetUserUsageCountAsync(coupon.Id, userId);
            if (userUsageCount >= coupon.MaxUsagesPerUser) return false;
        }

        return true;
    }

    public async Task<CouponUsage> RecordUsageAsync(int couponId, string userId, decimal discountAmount)
    {
        var usage = new CouponUsage
        {
            CouponId = couponId,
            UserId = userId,
            DiscountAmount = discountAmount,
            Currency = "GNF",
            UsedAt = DateTime.UtcNow
        };

        _context.CouponUsages.Add(usage);
        await _context.SaveChangesAsync();
        return usage;
    }

    public async Task<IEnumerable<CouponUsage>> GetUsageHistoryAsync(int couponId)
    {
        return await _context.CouponUsages
            .Where(u => u.CouponId == couponId)
            .OrderByDescending(u => u.UsedAt)
            .ToListAsync();
    }

    public async Task<int> GetUsageCountAsync(int couponId)
    {
        return await _context.CouponUsages
            .Where(u => u.CouponId == couponId)
            .CountAsync();
    }

    public async Task<int> GetUserUsageCountAsync(int couponId, string userId)
    {
        return await _context.CouponUsages
            .Where(u => u.CouponId == couponId && u.UserId == userId)
            .CountAsync();
    }

    public async Task<IEnumerable<Domain.Models.Coupon>> GetCouponsAsync()
    {
        return await GetAllAsync();
    }

    public async Task IncrementUsageCountAsync(int couponId)
    {
        var coupon = await _context.Coupons.FindAsync(couponId);
        if (coupon != null)
        {
            coupon.UsageCount++;
            await _context.SaveChangesAsync();
        }
    }

    public async Task<UsageStatsDto> GetUsageStatsAsync(int couponId)
    {
        var coupon = await _context.Coupons.FindAsync(couponId);
        var usages = await _context.CouponUsages
            .Where(u => u.CouponId == couponId)
            .OrderBy(u => u.UsedAt)
            .ToListAsync();

        return new UsageStatsDto
        {
            CouponId = couponId,
            CouponCode = coupon?.Code ?? "",
            TotalUsages = usages.Count,
            TotalDiscountAmount = usages.Sum(u => u.DiscountAmount),
            FirstUsage = usages.Any() ? usages.Min(u => u.UsedAt) : DateTime.UtcNow,
            LastUsage = usages.Any() ? usages.Max(u => u.UsedAt) : DateTime.UtcNow,
            UsagesByDate = usages.GroupBy(u => u.UsedAt.Date)
                .Select(g => new UsageByDateDto
                {
                    Date = g.Key,
                    Count = g.Count(),
                    TotalDiscount = g.Sum(u => u.DiscountAmount)
                }).ToList(),
            TopUsers = usages.GroupBy(u => u.UserId)
                .Select(g => new TopUserDto
                {
                    UserId = g.Key,
                    UsageCount = g.Count(),
                    TotalDiscount = g.Sum(u => u.DiscountAmount)
                })
                .OrderByDescending(u => u.UsageCount)
                .Take(10)
                .ToList()
        };
    }

    public async Task<List<CouponUsageStatsDto>> GetUsageStatsAsync(DateTime? startDate, DateTime? endDate)
    {
        var query = _context.CouponUsages.AsQueryable();

        if (startDate.HasValue)
            query = query.Where(u => u.UsedAt >= startDate.Value);
        if (endDate.HasValue)
            query = query.Where(u => u.UsedAt <= endDate.Value);

        var usages = await query
            .Include(u => u.Coupon)
            .ToListAsync();

        return usages.GroupBy(u => new { u.CouponId, u.UsedAt.Date })
            .Select(g => new CouponUsageStatsDto
            {
                CouponId = g.Key.CouponId,
                CouponCode = g.First().Coupon?.Code ?? "",
                CouponName = g.First().Coupon?.Name ?? "",
                UsageCount = g.Count(),
                TotalDiscountAmount = g.Sum(u => u.DiscountAmount),
                Date = g.Key.Date,
                Period = "Daily"
            })
            .OrderBy(s => s.Date)
            .ToList();
    }

    public async Task<int> CleanupExpiredCouponsAsync()
    {
        var expiredCoupons = await _context.Coupons
            .Where(c => c.ExpiresAt.HasValue && c.ExpiresAt < DateTime.UtcNow && c.IsActive)
            .ToListAsync();

        foreach (var coupon in expiredCoupons)
        {
            coupon.IsActive = false;
        }

        await _context.SaveChangesAsync();
        return expiredCoupons.Count;
    }
}
