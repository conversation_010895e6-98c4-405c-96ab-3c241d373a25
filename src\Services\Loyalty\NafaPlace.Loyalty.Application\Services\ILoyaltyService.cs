using NafaPlace.Loyalty.Application.DTOs;

namespace NafaPlace.Loyalty.Application.Services;

public interface ILoyaltyService
{
    // Gestion des comptes de fidélité
    Task<LoyaltyAccountDto> GetLoyaltyAccountAsync(string userId);
    Task<bool> CreateLoyaltyAccountAsync(string userId, string userName, string email);
    Task<bool> UpdateLoyaltyAccountAsync(LoyaltyAccountDto account);
    Task<bool> DeactivateLoyaltyAccountAsync(string userId);
    Task<bool> ReactivateLoyaltyAccountAsync(string userId);
    Task<List<LoyaltyAccountDto>> GetLoyaltyAccountsAsync(LoyaltyTier? tier = null, bool? isActive = null, int page = 1, int pageSize = 50);
    
    // Gestion des points
    Task<bool> EarnPointsAsync(string userId, int points, string description, string? referenceId = null, string? referenceType = null);
    Task<bool> RedeemPointsAsync(string userId, int points, string description, string? referenceId = null);
    Task<bool> AdjustPointsAsync(string userId, int points, string reason, string adjustedBy);
    Task<bool> TransferPointsAsync(string fromUserId, string toUserId, int points, string reason);
    Task<List<LoyaltyPointTransactionDto>> GetPointTransactionsAsync(string userId, TransactionType? type = null, int page = 1, int pageSize = 50);
    Task<int> GetAvailablePointsAsync(string userId);
    Task<int> GetPendingPointsAsync(string userId);
    Task<bool> ExpirePointsAsync(string userId, int points, string reason);
    Task ProcessPointExpirationAsync();
    
    // Gestion des niveaux (tiers)
    Task<LoyaltyTierDto> GetCurrentTierAsync(string userId);
    Task<List<LoyaltyTierDto>> GetAllTiersAsync();
    Task<LoyaltyTierDto?> GetTierAsync(LoyaltyTier tier);
    Task<bool> UpdateTierAsync(LoyaltyTierDto tierDto);
    Task<bool> CheckTierUpgradeAsync(string userId);
    Task<bool> UpgradeTierAsync(string userId, LoyaltyTier newTier, string reason);
    Task<bool> DowngradeTierAsync(string userId, LoyaltyTier newTier, string reason);
    Task<int> GetPointsToNextTierAsync(string userId);
    Task ProcessTierMaintenanceAsync();
    
    // Gestion des récompenses
    Task<int> CreateRewardAsync(RewardDto reward);
    Task<List<RewardDto>> GetRewardsAsync(RewardCategory? category = null, LoyaltyTier? minTier = null, bool? isActive = null);
    Task<RewardDto?> GetRewardAsync(int rewardId);
    Task<bool> UpdateRewardAsync(RewardDto reward);
    Task<bool> DeleteRewardAsync(int rewardId);
    Task<List<RewardDto>> GetEligibleRewardsAsync(string userId);
    Task<List<RewardDto>> GetFeaturedRewardsAsync(string userId);
    Task<bool> CanRedeemRewardAsync(string userId, int rewardId);
    
    // Rachats de récompenses
    Task<int> RedeemRewardAsync(string userId, int rewardId);
    Task<List<RewardRedemptionDto>> GetRedemptionsAsync(string userId, RedemptionStatus? status = null);
    Task<RewardRedemptionDto?> GetRedemptionAsync(int redemptionId);
    Task<bool> UseRedemptionAsync(int redemptionId, int? orderId = null);
    Task<bool> CancelRedemptionAsync(int redemptionId, string reason);
    Task<bool> RefundRedemptionAsync(int redemptionId, string reason);
    Task ProcessRedemptionExpirationAsync();
    
    // Badges et achievements
    Task<int> CreateBadgeAsync(LoyaltyBadgeDto badge);
    Task<List<LoyaltyBadgeDto>> GetBadgesAsync(BadgeCategory? category = null);
    Task<LoyaltyBadgeDto?> GetBadgeAsync(int badgeId);
    Task<bool> UpdateBadgeAsync(LoyaltyBadgeDto badge);
    Task<bool> DeleteBadgeAsync(int badgeId);
    Task<List<LoyaltyBadgeDto>> GetUserBadgesAsync(string userId);
    Task<bool> AwardBadgeAsync(string userId, int badgeId, string reason);
    Task<bool> CheckBadgeEligibilityAsync(string userId, int badgeId);
    Task ProcessBadgeAwardsAsync();
    
    // Défis et challenges
    Task<int> CreateChallengeAsync(LoyaltyChallengeDto challenge);
    Task<List<LoyaltyChallengeDto>> GetChallengesAsync(ChallengeType? type = null, bool? isActive = null);
    Task<LoyaltyChallengeDto?> GetChallengeAsync(int challengeId);
    Task<bool> UpdateChallengeAsync(LoyaltyChallengeDto challenge);
    Task<bool> DeleteChallengeAsync(int challengeId);
    Task<List<LoyaltyChallengeDto>> GetUserChallengesAsync(string userId);
    Task<bool> JoinChallengeAsync(string userId, int challengeId);
    Task<bool> UpdateChallengeProgressAsync(string userId, int challengeId, Dictionary<string, int> progress);
    Task<bool> CompleteChallengeAsync(string userId, int challengeId);
    Task ProcessChallengeCompletionAsync();
    
    // Programme de parrainage
    Task<int> CreateReferralProgramAsync(ReferralProgramDto program);
    Task<List<ReferralProgramDto>> GetReferralProgramsAsync(bool? isActive = null);
    Task<ReferralProgramDto?> GetReferralProgramAsync(int programId);
    Task<bool> UpdateReferralProgramAsync(ReferralProgramDto program);
    Task<string> GenerateReferralCodeAsync(string userId);
    Task<bool> ProcessReferralAsync(string referralCode, string refereeId);
    Task<List<ReferralDto>> GetUserReferralsAsync(string userId);
    Task<ReferralDto?> GetReferralAsync(int referralId);
    Task<bool> CompleteReferralAsync(int referralId);
    
    // Événements et promotions
    Task<int> CreateLoyaltyEventAsync(LoyaltyEventDto loyaltyEvent);
    Task<List<LoyaltyEventDto>> GetLoyaltyEventsAsync(bool? isActive = null);
    Task<LoyaltyEventDto?> GetLoyaltyEventAsync(int eventId);
    Task<bool> UpdateLoyaltyEventAsync(LoyaltyEventDto loyaltyEvent);
    Task<bool> DeleteLoyaltyEventAsync(int eventId);
    Task<List<LoyaltyEventDto>> GetActiveLoyaltyEventsAsync(string userId);
    Task<double> GetEventPointsMultiplierAsync(string userId, string category);
    Task<int> GetEventBonusPointsAsync(string userId, string category);
    
    // Règles de gain de points
    Task<int> CreateEarnRuleAsync(PointsEarnRuleDto rule);
    Task<List<PointsEarnRuleDto>> GetEarnRulesAsync(EarnRuleType? type = null, bool? isActive = null);
    Task<PointsEarnRuleDto?> GetEarnRuleAsync(int ruleId);
    Task<bool> UpdateEarnRuleAsync(PointsEarnRuleDto rule);
    Task<bool> DeleteEarnRuleAsync(int ruleId);
    Task<int> CalculatePointsForActionAsync(string userId, EarnRuleType actionType, decimal? amount = null, string? category = null);
    Task<bool> ProcessEarnRuleAsync(string userId, EarnRuleType actionType, decimal? amount = null, string? category = null, string? referenceId = null);
    
    // Analytics et statistiques
    Task<LoyaltyAnalyticsDto> GetLoyaltyAnalyticsAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<Dictionary<string, object>> GetUserLoyaltyStatsAsync(string userId);
    Task<Dictionary<string, object>> GetTierDistributionAsync();
    Task<Dictionary<string, object>> GetRewardPopularityAsync();
    Task<Dictionary<string, object>> GetPointsFlowAnalysisAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<List<LoyaltyAccountDto>> GetTopLoyaltyMembersAsync(int limit = 50);
    Task<Dictionary<string, object>> GetRetentionAnalysisAsync();
    Task<Dictionary<string, object>> GetEngagementMetricsAsync();
    
    // Notifications
    Task<bool> SendLoyaltyNotificationAsync(LoyaltyNotificationDto notification);
    Task<List<LoyaltyNotificationDto>> GetUserNotificationsAsync(string userId, bool? isRead = null);
    Task<bool> MarkNotificationAsReadAsync(int notificationId);
    Task<bool> MarkAllNotificationsAsReadAsync(string userId);
    Task<int> GetUnreadNotificationCountAsync(string userId);
    Task ProcessLoyaltyNotificationsAsync();
    
    // Intégrations avec d'autres services
    Task HandleOrderCompletedAsync(string userId, int orderId, decimal amount, List<string> categories);
    Task HandleProductReviewAsync(string userId, int productId, int rating);
    Task HandleUserRegistrationAsync(string userId, string userName, string email);
    Task HandleSocialShareAsync(string userId, string platform, string contentType);
    Task HandleNewsletterSubscriptionAsync(string userId);
    Task HandleBirthdayAsync(string userId);
    Task HandleLoginStreakAsync(string userId, int streakDays);
    Task HandleSurveyCompletionAsync(string userId, string surveyId);
    
    // Import/Export et migration
    Task<bool> ImportLoyaltyDataAsync(Stream dataStream, string format = "json");
    Task<byte[]> ExportLoyaltyDataAsync(DateTime? startDate = null, DateTime? endDate = null, string format = "csv");
    Task<bool> MigrateLoyaltyAccountAsync(string oldUserId, string newUserId);
    Task<bool> MergeLoyaltyAccountsAsync(string primaryUserId, string secondaryUserId);
    Task<Dictionary<string, object>> ValidateLoyaltyDataAsync();
    
    // Configuration et maintenance
    Task<Dictionary<string, object>> GetLoyaltyConfigAsync();
    Task<bool> UpdateLoyaltyConfigAsync(Dictionary<string, object> config);
    Task<bool> RecalculateUserTierAsync(string userId);
    Task<bool> RecalculateAllUserTiersAsync();
    Task<int> CleanupExpiredDataAsync(int daysToKeep = 365);
    Task<bool> TestLoyaltyServiceAsync();
    Task<Dictionary<string, bool>> GetServiceHealthAsync();
    
    // Gamification avancée
    Task<Dictionary<string, object>> GetUserGameStatsAsync(string userId);
    Task<List<string>> GetUserAchievementsAsync(string userId);
    Task<Dictionary<string, int>> GetLeaderboardAsync(string category, int limit = 100);
    Task<int> GetUserRankAsync(string userId, string category);
    Task<bool> UpdateUserStreakAsync(string userId, string streakType);
    Task<Dictionary<string, int>> GetUserStreaksAsync(string userId);
    
    // Personnalisation et recommandations
    Task<List<RewardDto>> GetPersonalizedRewardsAsync(string userId, int limit = 10);
    Task<List<LoyaltyChallengeDto>> GetRecommendedChallengesAsync(string userId, int limit = 5);
    Task<Dictionary<string, object>> GetPersonalizationDataAsync(string userId);
    Task<bool> UpdatePersonalizationPreferencesAsync(string userId, Dictionary<string, object> preferences);
    
    // API et webhooks
    Task<bool> ProcessWebhookAsync(string eventType, Dictionary<string, object> data);
    Task<Dictionary<string, object>> GetLoyaltyAPIStatsAsync();
    Task<bool> ValidateAPIKeyAsync(string apiKey);
    Task<List<string>> GetAPIEndpointsAsync();
    
    // Rapports et exports avancés
    Task<byte[]> GenerateLoyaltyReportAsync(string reportType, Dictionary<string, object> parameters, string format = "pdf");
    Task<byte[]> ExportUserLoyaltyDataAsync(string userId, string format = "json");
    Task<Dictionary<string, object>> GetCustomAnalyticsAsync(string query, Dictionary<string, object> parameters);
    Task<bool> ScheduleReportAsync(string reportType, string schedule, List<string> recipients);
    
    // Tests A/B et expérimentation
    Task<int> CreateLoyaltyExperimentAsync(Dictionary<string, object> experiment);
    Task<bool> AssignUserToExperimentAsync(string userId, int experimentId, string variant);
    Task<Dictionary<string, object>> GetExperimentResultsAsync(int experimentId);
    Task<bool> EndExperimentAsync(int experimentId);
    
    // Sécurité et fraude
    Task<bool> DetectFraudulentActivityAsync(string userId, string activityType, Dictionary<string, object> context);
    Task<bool> FlagSuspiciousAccountAsync(string userId, string reason);
    Task<List<string>> GetFlaggedAccountsAsync();
    Task<bool> ReviewFlaggedAccountAsync(string userId, bool isValid, string reviewedBy);
    
    // Intégration sociale
    Task<bool> ConnectSocialAccountAsync(string userId, string platform, string socialId);
    Task<List<string>> GetConnectedSocialAccountsAsync(string userId);
    Task<bool> ShareLoyaltyAchievementAsync(string userId, string achievementType, string platform);
    Task<Dictionary<string, object>> GetSocialLoyaltyStatsAsync(string userId);
    
    // Automatisation et workflows
    Task<bool> CreateLoyaltyWorkflowAsync(Dictionary<string, object> workflow);
    Task<bool> ExecuteWorkflowAsync(int workflowId, string userId, Dictionary<string, object> context);
    Task<List<Dictionary<string, object>>> GetActiveWorkflowsAsync();
    Task<bool> PauseWorkflowAsync(int workflowId);
    Task<bool> ResumeWorkflowAsync(int workflowId);
}
