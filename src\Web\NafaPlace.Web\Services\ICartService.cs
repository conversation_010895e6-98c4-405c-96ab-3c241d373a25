using System.Threading.Tasks;
using NafaPlace.Web.Models.Cart;

namespace NafaPlace.Web.Services
{
    public interface ICartService
    {
        Task<CartDto> GetCartAsync(string userId);
        Task<CartDto> AddItemToCartAsync(string userId, CartItemCreateDto item);
        Task<CartDto> UpdateItemInCartAsync(string userId, CartItemDto item);
        Task<CartDto> RemoveItemFromCartAsync(string userId, int productId);
        Task<bool> ClearCartAsync(string userId);
        Task<CartSummaryDto> GetCartSummaryAsync(string userId);
        Task<CartDto> ApplyCouponAsync(string userId, string couponCode);
        Task<CartDto> RemoveCouponAsync(string userId);
    }

    public class CartSummaryDto
    {
        public int ItemCount { get; set; }
        public decimal SubTotal { get; set; }
        public decimal Total { get; set; }
        public string Currency { get; set; } = "GNF";
    }
}
