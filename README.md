# NafaPlace - Plateforme E-commerce Guinéenne

NafaPlace est une plateforme e-commerce moderne construite avec une architecture microservices en utilisant .NET 9.0, spécialement adaptée pour le marché guinéen avec support du Franc Guinéen (GNF).

## Architecture

Le projet est structuré en plusieurs composants :

### Building Blocks
- Common
- EventBus
- Infrastructure

### Microservices
- Identity Service (Authentification et gestion des utilisateurs)
- Catalog Service (Gestion des produits et catégories)
- Cart Service (Panier d'achat avec calculs en GNF)
- Order Service (Gestion des commandes)
- Payment Service (Paiements Stripe en GNF)
- Reviews Service (Avis et évaluations)
- Notifications Service (Notifications en temps réel)

### Applications Web
- WebApp (Blazor WebAssembly)
- AdminPortal (Blazor Server)
- SellerPortal (Blazor WebAssembly)

### Infrastructure
- PostgreSQL (Base de données principale)
- Redis (Cache pour le panier)
- API Gateway (Ocelot)

## 💰 Devise et Localisation

- **Devise principale** : <PERSON><PERSON><PERSON> (GNF)
- **TVA** : 18% (standard guinéen)
- **Livraison gratuite** : Commandes > 500,000 GNF
- **Frais de livraison** : 25,000 GNF
- **Support multi-devises** : GNF, USD, EUR, XOF

## Configuration requise

- .NET 9.0
- Docker
- Docker Compose

## Installation

1. Cloner le repository
```bash
git clone https://github.com/diakitelamine/nafaplace.git
```

2. Restaurer les packages NuGet
```bash
dotnet restore
```

3. Lancer les services d'infrastructure
```bash
docker-compose up -d
```

4. Lancer le projet
```bash
dotnet run
```
# nafaplace
