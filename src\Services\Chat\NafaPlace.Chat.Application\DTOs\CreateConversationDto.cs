namespace NafaPlace.Chat.Application.DTOs;

public class CreateConversationDto
{
    public string Subject { get; set; } = string.Empty;
    public string Priority { get; set; } = "Normal";
    public string Category { get; set; } = "General";
    public string UserId { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public string UserEmail { get; set; } = string.Empty;
    public int? DepartmentId { get; set; }
    public string InitialMessage { get; set; } = string.Empty;
    public List<string> Tags { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class SendMessageDto
{
    public int ConversationId { get; set; }
    public string Content { get; set; } = string.Empty;
    public MessageType MessageType { get; set; } = MessageType.Text;
    public SenderType SenderType { get; set; } = SenderType.Customer;
    public string SenderId { get; set; } = string.Empty;
    public string SenderName { get; set; } = string.Empty;
    public List<MessageAttachmentDto> Attachments { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class MessageAttachmentDto
{
    public string Url { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public long Size { get; set; }
}

public class ChatFilterDto
{
    public string? UserId { get; set; }
    public string? AgentId { get; set; }
    public ConversationStatus? Status { get; set; }
    public ConversationPriority? Priority { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public List<string>? Tags { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 20;
}

public class ChatNotificationDto
{
    public string Id { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Priority { get; set; } = "Normal";
    public Dictionary<string, object> Data { get; set; } = new();
    public string? ActionUrl { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

public class ChatPresenceDto
{
    public string UserId { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public UserPresenceStatus Status { get; set; }
    public DateTime LastSeen { get; set; }
    public bool IsTyping { get; set; }
}

public class SatisfactionFeedbackDto
{
    public int ConversationId { get; set; }
    public int Rating { get; set; }
    public string? Comment { get; set; }
    public string UserId { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public DateTime SubmittedAt { get; set; }
}

public class ConversationSummaryDto
{
    public int ConversationId { get; set; }
    public string Subject { get; set; } = string.Empty;
    public int MessageCount { get; set; }
    public TimeSpan Duration { get; set; }
    public List<string> Keywords { get; set; } = new();
    public string Summary { get; set; } = string.Empty;
    public int? SatisfactionRating { get; set; }
}

// Enums
public enum ConversationStatus
{
    Open,
    InProgress,
    Waiting,
    Closed,
    Archived
}

public enum ConversationPriority
{
    Low,
    Normal,
    High,
    Urgent
}

public enum ConversationCategory
{
    General,
    Technical,
    Billing,
    Sales,
    Complaint
}

public enum MessageType
{
    Text,
    Image,
    File,
    System,
    Notification
}

public enum SenderType
{
    Customer,
    Agent,
    System,
    Bot
}

public enum AgentStatus
{
    Offline,
    Online,
    Busy,
    Away
}

public enum UserPresenceStatus
{
    Offline,
    Online,
    Away,
    Busy
}
