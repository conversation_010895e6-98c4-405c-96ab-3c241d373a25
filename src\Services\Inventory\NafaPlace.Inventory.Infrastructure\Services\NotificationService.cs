using Microsoft.Extensions.Logging;
using NafaPlace.Inventory.Application.Interfaces;
using System.Text;
using System.Text.Json;

namespace NafaPlace.Inventory.Infrastructure.Services;

public class NotificationService : INotificationService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<NotificationService> _logger;
    private readonly JsonSerializerOptions _jsonOptions;

    public NotificationService(HttpClient httpClient, ILogger<NotificationService> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            PropertyNameCaseInsensitive = true
        };
    }

    public async Task SendLowStockNotificationAsync(int productId, string productName, int currentStock, int threshold, string sellerId)
    {
        try
        {
            var notification = new
            {
                UserId = sellerId,
                Title = "⚠️ Stock Faible",
                Message = $"Le produit '{productName}' a un stock faible. Stock actuel: {currentStock}, Seuil: {threshold}",
                Type = "warning",
                Priority = "high",
                ActionUrl = $"/products/edit/{productId}",
                Data = new
                {
                    ProductId = productId,
                    ProductName = productName,
                    CurrentStock = currentStock,
                    Threshold = threshold,
                    AlertType = "low_stock"
                }
            };

            var json = JsonSerializer.Serialize(notification, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PostAsync("api/notifications", content);
            
            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Notification de stock faible envoyée pour le produit {ProductId} au vendeur {SellerId}", productId, sellerId);
            }
            else
            {
                _logger.LogWarning("Échec de l'envoi de la notification de stock faible pour le produit {ProductId}", productId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de la notification de stock faible pour le produit {ProductId}", productId);
        }
    }

    public async Task SendOutOfStockNotificationAsync(int productId, string productName, string sellerId)
    {
        try
        {
            var notification = new
            {
                UserId = sellerId,
                Title = "🚨 Rupture de Stock",
                Message = $"Le produit '{productName}' est en rupture de stock. Action immédiate requise.",
                Type = "error",
                Priority = "urgent",
                ActionUrl = $"/products/edit/{productId}",
                Data = new
                {
                    ProductId = productId,
                    ProductName = productName,
                    CurrentStock = 0,
                    AlertType = "out_of_stock"
                }
            };

            var json = JsonSerializer.Serialize(notification, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PostAsync("api/notifications", content);
            
            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Notification de rupture de stock envoyée pour le produit {ProductId} au vendeur {SellerId}", productId, sellerId);
            }
            else
            {
                _logger.LogWarning("Échec de l'envoi de la notification de rupture de stock pour le produit {ProductId}", productId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de la notification de rupture de stock pour le produit {ProductId}", productId);
        }
    }

    public async Task SendStockMovementNotificationAsync(int productId, string productName, int quantity, string movementType, string sellerId)
    {
        try
        {
            var notification = new
            {
                UserId = sellerId,
                Title = "📦 Mouvement de Stock",
                Message = $"Mouvement de stock pour '{productName}': {movementType} de {quantity} unité(s)",
                Type = "info",
                Priority = "normal",
                ActionUrl = $"/products/edit/{productId}",
                Data = new
                {
                    ProductId = productId,
                    ProductName = productName,
                    Quantity = quantity,
                    MovementType = movementType,
                    AlertType = "stock_movement"
                }
            };

            var json = JsonSerializer.Serialize(notification, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PostAsync("api/notifications", content);
            
            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Notification de mouvement de stock envoyée pour le produit {ProductId} au vendeur {SellerId}", productId, sellerId);
            }
            else
            {
                _logger.LogWarning("Échec de l'envoi de la notification de mouvement de stock pour le produit {ProductId}", productId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de la notification de mouvement de stock pour le produit {ProductId}", productId);
        }
    }

    public async Task SendBulkNotificationAsync(List<string> userIds, string title, string message, string type = "info")
    {
        try
        {
            var bulkNotification = new
            {
                UserIds = userIds,
                Title = title,
                Message = message,
                Type = type,
                Priority = "normal"
            };

            var json = JsonSerializer.Serialize(bulkNotification, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PostAsync("api/notifications/bulk", content);
            
            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Notification en lot envoyée à {UserCount} utilisateurs", userIds.Count);
            }
            else
            {
                _logger.LogWarning("Échec de l'envoi de la notification en lot à {UserCount} utilisateurs", userIds.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de la notification en lot");
        }
    }
}
