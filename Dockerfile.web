# Build stage
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copier les fichiers csproj et restaurer les dépendances
COPY ["src/Web/NafaPlace.Web/NafaPlace.Web.csproj", "Web/NafaPlace.Web/"]
COPY ["src/Services/Reviews/NafaPlace.Reviews.DTOs/NafaPlace.Reviews.DTOs.csproj", "Services/Reviews/NafaPlace.Reviews.DTOs/"]
RUN dotnet restore "Web/NafaPlace.Web/NafaPlace.Web.csproj"

# Copier le reste des fichiers et construire
COPY ["src/Web/NafaPlace.Web/", "Web/NafaPlace.Web/"]
COPY ["src/Services/Reviews/NafaPlace.Reviews.DTOs/", "Services/Reviews/NafaPlace.Reviews.DTOs/"]
WORKDIR "/src/Web/NafaPlace.Web"
RUN dotnet build "NafaPlace.Web.csproj" -c Release -o /app/build

# Publish stage
FROM build AS publish
WORKDIR "/src/Web/NafaPlace.Web"

# Restore dependencies
RUN dotnet restore "NafaPlace.Web.csproj"

# Publish with specific Blazor WebAssembly settings - Disable service worker to fix SRI issues
RUN dotnet publish "NafaPlace.Web.csproj" \
    -c Release \
    -o /app/publish \
    /p:UseAppHost=false \
    /p:PublishTrimmed=false \
    /p:BlazorWebAssemblyLoadAllGlobalizationData=false \
    /p:RunAOTCompilation=false \
    /p:BlazorEnableCompression=false \
    /p:ServiceWorkerAssetsManifest= \
    /p:BlazorCacheBootResources=false

# Clean up service worker files
RUN find /app/publish/wwwroot -name "*service-worker*" -type f -delete || true

# Final stage - nginx pour servir les fichiers statiques
FROM nginx:alpine AS final
WORKDIR /usr/share/nginx/html

# Copier les fichiers publiés
COPY --from=publish /app/publish/wwwroot .

# Copier la configuration nginx optimisée
COPY src/Web/nginx.conf /etc/nginx/nginx.conf

# Remove any service worker files
RUN find /usr/share/nginx/html -name "*service-worker*" -type f -delete || true


EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
