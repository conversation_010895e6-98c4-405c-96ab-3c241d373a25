using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace NafaPlace.Recommendation.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class InitialRecommendationCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Table des interactions utilisateur
            migrationBuilder.CreateTable(
                name: "UserInteractions",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    UserId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    ProductId = table.Column<int>(type: "integer", nullable: false),
                    InteractionType = table.Column<int>(type: "integer", nullable: false),
                    Weight = table.Column<double>(type: "double precision", nullable: false),
                    SessionId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Duration = table.Column<TimeSpan>(type: "interval", nullable: true),
                    Source = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    Context = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "{}"),
                    Timestamp = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserInteractions", x => x.Id);
                });

            // Table des préférences utilisateur
            migrationBuilder.CreateTable(
                name: "UserPreferences",
                columns: table => new
                {
                    UserId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    CategoryPreferences = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "[]"),
                    BrandPreferences = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "[]"),
                    PriceRangeMin = table.Column<decimal>(type: "numeric(18,2)", nullable: false, defaultValue: 0),
                    PriceRangeMax = table.Column<decimal>(type: "numeric(18,2)", nullable: false, defaultValue: 999999),
                    AverageSpent = table.Column<decimal>(type: "numeric(18,2)", nullable: false, defaultValue: 0),
                    PreferredTags = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "[]"),
                    DislikedTags = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "[]"),
                    FeatureWeights = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "{}"),
                    InteractionCount = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    LastUpdated = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserPreferences", x => x.UserId);
                });

            // Table des profils de personnalisation
            migrationBuilder.CreateTable(
                name: "PersonalizationProfiles",
                columns: table => new
                {
                    UserId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Segment = table.Column<int>(type: "integer", nullable: false),
                    Interests = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "[]"),
                    CategoryAffinities = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "{}"),
                    BrandAffinities = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "{}"),
                    PurchasingBehavior = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "{}"),
                    SeasonalPreferences = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "{}"),
                    CustomAttributes = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "{}"),
                    ProfileCompleteness = table.Column<double>(type: "double precision", nullable: false, defaultValue: 0),
                    LastUpdated = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PersonalizationProfiles", x => x.UserId);
                });

            // Table des modèles de recommandation
            migrationBuilder.CreateTable(
                name: "RecommendationModels",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    Type = table.Column<int>(type: "integer", nullable: false),
                    Algorithm = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Parameters = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "{}"),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    Accuracy = table.Column<double>(type: "double precision", nullable: false, defaultValue: 0),
                    Precision = table.Column<double>(type: "double precision", nullable: false, defaultValue: 0),
                    Recall = table.Column<double>(type: "double precision", nullable: false, defaultValue: 0),
                    F1Score = table.Column<double>(type: "double precision", nullable: false, defaultValue: 0),
                    TrainedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    LastUsed = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    UsageCount = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    Version = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RecommendationModels", x => x.Id);
                });

            // Table des campagnes de recommandation
            migrationBuilder.CreateTable(
                name: "RecommendationCampaigns",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    Type = table.Column<int>(type: "integer", nullable: false),
                    Rules = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "[]"),
                    TargetUserSegments = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "[]"),
                    StartDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    EndDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    Priority = table.Column<int>(type: "integer", nullable: false, defaultValue: 1),
                    Settings = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "{}"),
                    TotalRecommendations = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalViews = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalClicks = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalPurchases = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalRevenue = table.Column<decimal>(type: "numeric(18,2)", nullable: false, defaultValue: 0),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RecommendationCampaigns", x => x.Id);
                });

            // Table des tests A/B
            migrationBuilder.CreateTable(
                name: "ABTestRecommendations",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    Variants = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "[]"),
                    TrafficSplit = table.Column<double>(type: "double precision", nullable: false, defaultValue: 0.5),
                    StartDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    EndDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    WinningVariant = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    ConfidenceLevel = table.Column<double>(type: "double precision", nullable: false, defaultValue: 0),
                    Results = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "{}"),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ABTestRecommendations", x => x.Id);
                });

            // Table des logs de recommandations
            migrationBuilder.CreateTable(
                name: "RecommendationLogs",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    RequestId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    UserId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    RecommendationType = table.Column<int>(type: "integer", nullable: false),
                    Algorithm = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    ProductIds = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "[]"),
                    Confidence = table.Column<double>(type: "double precision", nullable: false),
                    ProcessingTime = table.Column<TimeSpan>(type: "interval", nullable: false),
                    Context = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "{}"),
                    Timestamp = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RecommendationLogs", x => x.Id);
                });

            // Table de similarité des produits
            migrationBuilder.CreateTable(
                name: "ProductSimilarities",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    ProductId1 = table.Column<int>(type: "integer", nullable: false),
                    ProductId2 = table.Column<int>(type: "integer", nullable: false),
                    SimilarityScore = table.Column<double>(type: "double precision", nullable: false),
                    SimilarityType = table.Column<int>(type: "integer", nullable: false),
                    CommonFeatures = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "[]"),
                    CalculatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ProductSimilarities", x => x.Id);
                });

            // Table des features de produits pour ML
            migrationBuilder.CreateTable(
                name: "ProductFeatures",
                columns: table => new
                {
                    ProductId = table.Column<int>(type: "integer", nullable: false),
                    Features = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "{}"),
                    FeatureVector = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "[]"),
                    LastUpdated = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ProductFeatures", x => x.ProductId);
                });

            // Index pour les performances
            migrationBuilder.CreateIndex(
                name: "IX_UserInteractions_UserId_Timestamp",
                table: "UserInteractions",
                columns: new[] { "UserId", "Timestamp" });

            migrationBuilder.CreateIndex(
                name: "IX_UserInteractions_ProductId_InteractionType",
                table: "UserInteractions",
                columns: new[] { "ProductId", "InteractionType" });

            migrationBuilder.CreateIndex(
                name: "IX_UserInteractions_SessionId",
                table: "UserInteractions",
                column: "SessionId");

            migrationBuilder.CreateIndex(
                name: "IX_PersonalizationProfiles_Segment",
                table: "PersonalizationProfiles",
                column: "Segment");

            migrationBuilder.CreateIndex(
                name: "IX_RecommendationModels_Type_Status",
                table: "RecommendationModels",
                columns: new[] { "Type", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_RecommendationCampaigns_IsActive_StartDate_EndDate",
                table: "RecommendationCampaigns",
                columns: new[] { "IsActive", "StartDate", "EndDate" });

            migrationBuilder.CreateIndex(
                name: "IX_ABTestRecommendations_Status_StartDate_EndDate",
                table: "ABTestRecommendations",
                columns: new[] { "Status", "StartDate", "EndDate" });

            migrationBuilder.CreateIndex(
                name: "IX_RecommendationLogs_UserId_Timestamp",
                table: "RecommendationLogs",
                columns: new[] { "UserId", "Timestamp" });

            migrationBuilder.CreateIndex(
                name: "IX_RecommendationLogs_RequestId",
                table: "RecommendationLogs",
                column: "RequestId");

            migrationBuilder.CreateIndex(
                name: "IX_RecommendationLogs_RecommendationType_Timestamp",
                table: "RecommendationLogs",
                columns: new[] { "RecommendationType", "Timestamp" });

            migrationBuilder.CreateIndex(
                name: "IX_ProductSimilarities_ProductId1_SimilarityScore",
                table: "ProductSimilarities",
                columns: new[] { "ProductId1", "SimilarityScore" });

            migrationBuilder.CreateIndex(
                name: "IX_ProductSimilarities_ProductId2_SimilarityScore",
                table: "ProductSimilarities",
                columns: new[] { "ProductId2", "SimilarityScore" });

            migrationBuilder.CreateIndex(
                name: "IX_ProductSimilarities_SimilarityType",
                table: "ProductSimilarities",
                column: "SimilarityType");

            // Index unique pour éviter les doublons de similarité
            migrationBuilder.CreateIndex(
                name: "IX_ProductSimilarities_ProductId1_ProductId2_SimilarityType",
                table: "ProductSimilarities",
                columns: new[] { "ProductId1", "ProductId2", "SimilarityType" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(name: "UserInteractions");
            migrationBuilder.DropTable(name: "UserPreferences");
            migrationBuilder.DropTable(name: "PersonalizationProfiles");
            migrationBuilder.DropTable(name: "RecommendationModels");
            migrationBuilder.DropTable(name: "RecommendationCampaigns");
            migrationBuilder.DropTable(name: "ABTestRecommendations");
            migrationBuilder.DropTable(name: "RecommendationLogs");
            migrationBuilder.DropTable(name: "ProductSimilarities");
            migrationBuilder.DropTable(name: "ProductFeatures");
        }
    }
}
