# Fichiers et dossiers à ignorer lors de la construction des images Docker

# Dossiers de build et de packages
**/bin/
**/obj/
**/out/
**/node_modules/
**/bower_components/

# Fichiers de développement
**/.vs/
**/.vscode/
**/.idea/
**/*.user
**/*.userosscache
**/*.dbmdl
**/*.jfm
**/*.suo
**/*.cache
**/*.dll.config
**/*.exe.config

# Fichiers de logs
**/*.log
**/*.tlog

# Fichiers temporaires
**/[Tt]emp/
**/[Tt]mp/
**/*.tmp
**/*.temp

# Fichiers de sauvegarde
**/*~
**/*.bak
**/*.swp
**/*.swo

# Fichiers de test
**/TestResults/
**/[Tt]est[Rr]esult*/
**/*.TestAdapter.dll
**/*.trx
**/*.coverage

# Fichiers spécifiques à Git
**/.git/
**/.gitignore
**/.gitattributes

# Fichiers Docker
**/Dockerfile*
**/.dockerignore
**/docker-compose*
