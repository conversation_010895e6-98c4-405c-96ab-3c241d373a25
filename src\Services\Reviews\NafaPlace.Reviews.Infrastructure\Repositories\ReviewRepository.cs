using Microsoft.EntityFrameworkCore;
using NafaPlace.Reviews.Application.Interfaces;
using NafaPlace.Reviews.Domain.Models;
using NafaPlace.Reviews.Infrastructure.Data;

namespace NafaPlace.Reviews.Infrastructure.Repositories;

public partial class ReviewRepository : IReviewRepository
{
    private readonly ReviewsDbContext _context;

    public ReviewRepository(ReviewsDbContext context)
    {
        _context = context;
    }

    public async Task<Review?> GetByIdAsync(int id)
    {
        return await _context.Reviews
            .Include(r => r.ReviewHelpfuls)
            .FirstOrDefaultAsync(r => r.Id == id);
    }

    public async Task<IEnumerable<Review>> GetByProductIdAsync(int productId, int page = 1, int pageSize = 10)
    {
        return await _context.Reviews
            .Where(r => r.ProductId == productId && r.IsApproved)
            .Include(r => r.ReviewHelpfuls)
            .OrderByDescending(r => r.CreatedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();
    }

    public async Task<IEnumerable<Review>> GetByUserIdAsync(string userId, int page = 1, int pageSize = 10)
    {
        return await _context.Reviews
            .Where(r => r.UserId == userId)
            .Include(r => r.ReviewHelpfuls)
            .OrderByDescending(r => r.CreatedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();
    }

    public async Task<Review> CreateAsync(Review review)
    {
        _context.Reviews.Add(review);
        await _context.SaveChangesAsync();
        return review;
    }

    public async Task<Review> UpdateAsync(Review review)
    {
        _context.Reviews.Update(review);
        await _context.SaveChangesAsync();
        return review;
    }

    public async Task DeleteAsync(int id)
    {
        var review = await _context.Reviews.FindAsync(id);
        if (review != null)
        {
            _context.Reviews.Remove(review);
            await _context.SaveChangesAsync();
        }
    }

    public async Task<bool> ExistsAsync(int productId, string userId)
    {
        return await _context.Reviews
            .AnyAsync(r => r.ProductId == productId && r.UserId == userId);
    }

    public async Task<double> GetAverageRatingAsync(int productId)
    {
        var reviews = await _context.Reviews
            .Where(r => r.ProductId == productId && r.IsApproved)
            .ToListAsync();

        return reviews.Any() ? reviews.Average(r => r.Rating) : 0;
    }

    public async Task<int> GetTotalReviewsCountAsync(int productId)
    {
        return await _context.Reviews
            .CountAsync(r => r.ProductId == productId && r.IsApproved);
    }

    public async Task<Dictionary<int, int>> GetRatingDistributionAsync(int productId)
    {
        var distribution = await _context.Reviews
            .Where(r => r.ProductId == productId && r.IsApproved)
            .GroupBy(r => r.Rating)
            .Select(g => new { Rating = g.Key, Count = g.Count() })
            .ToDictionaryAsync(x => x.Rating, x => x.Count);

        // Ensure all ratings 1-5 are represented
        for (int i = 1; i <= 5; i++)
        {
            if (!distribution.ContainsKey(i))
                distribution[i] = 0;
        }

        return distribution;
    }

    public async Task<bool> MarkHelpfulAsync(int reviewId, string userId)
    {
        var existingMark = await _context.ReviewHelpfuls
            .FirstOrDefaultAsync(rh => rh.ReviewId == reviewId && rh.UserId == userId);

        if (existingMark != null)
            return false; // Already marked

        var reviewHelpful = new ReviewHelpful
        {
            ReviewId = reviewId,
            UserId = userId,
            CreatedAt = DateTime.UtcNow
        };

        _context.ReviewHelpfuls.Add(reviewHelpful);

        // Update helpful count
        var review = await _context.Reviews.FindAsync(reviewId);
        if (review != null)
        {
            review.HelpfulCount++;
            _context.Reviews.Update(review);
        }

        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> UnmarkHelpfulAsync(int reviewId, string userId)
    {
        var existingMark = await _context.ReviewHelpfuls
            .FirstOrDefaultAsync(rh => rh.ReviewId == reviewId && rh.UserId == userId);

        if (existingMark == null)
            return false; // Not marked

        _context.ReviewHelpfuls.Remove(existingMark);

        // Update helpful count
        var review = await _context.Reviews.FindAsync(reviewId);
        if (review != null)
        {
            review.HelpfulCount = Math.Max(0, review.HelpfulCount - 1);
            _context.Reviews.Update(review);
        }

        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> HasUserMarkedHelpfulAsync(int reviewId, string userId)
    {
        return await _context.ReviewHelpfuls
            .AnyAsync(rh => rh.ReviewId == reviewId && rh.UserId == userId);
    }
}
