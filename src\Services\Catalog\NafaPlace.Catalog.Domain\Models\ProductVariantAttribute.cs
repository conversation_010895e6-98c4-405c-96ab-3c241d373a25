using System;
using System.ComponentModel.DataAnnotations;
using NafaPlace.Catalog.Domain.Common;

namespace NafaPlace.Catalog.Domain.Models;

public class ProductVariantAttribute : BaseEntity
{
    [Required]
    [MaxLength(100)]
    public required string Name { get; set; }

    [Required]
    [MaxLength(500)]
    public required string Value { get; set; }

    public int ProductVariantId { get; set; }
    public ProductVariant? ProductVariant { get; set; }

    public int DisplayOrder { get; set; }
}
