<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.2" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\NafaPlace.Reviews.Application\NafaPlace.Reviews.Application.csproj" />
    <ProjectReference Include="..\NafaPlace.Reviews.Infrastructure\NafaPlace.Reviews.Infrastructure.csproj" />
  </ItemGroup>

</Project>
