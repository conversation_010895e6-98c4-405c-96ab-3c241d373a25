# Build stage
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copier les fichiers csproj et restaurer les dépendances
COPY ["src/Services/Notifications/NafaPlace.Notifications.API/NafaPlace.Notifications.API.csproj", "Services/Notifications/NafaPlace.Notifications.API/"]
COPY ["src/Services/Notifications/NafaPlace.Notifications.Application/NafaPlace.Notifications.Application.csproj", "Services/Notifications/NafaPlace.Notifications.Application/"]
COPY ["src/Services/Notifications/NafaPlace.Notifications.Domain/NafaPlace.Notifications.Domain.csproj", "Services/Notifications/NafaPlace.Notifications.Domain/"]
COPY ["src/Services/Notifications/NafaPlace.Notifications.Infrastructure/NafaPlace.Notifications.Infrastructure.csproj", "Services/Notifications/NafaPlace.Notifications.Infrastructure/"]
COPY ["src/BuildingBlocks/Common/NafaPlace.Common/NafaPlace.Common.csproj", "BuildingBlocks/Common/NafaPlace.Common/"]
RUN dotnet restore "Services/Notifications/NafaPlace.Notifications.API/NafaPlace.Notifications.API.csproj"

# Copier le reste des fichiers et construire
COPY ["src/Services/Notifications/", "Services/Notifications/"]
COPY ["src/BuildingBlocks/Common/", "BuildingBlocks/Common/"]
WORKDIR "/src/Services/Notifications/NafaPlace.Notifications.API"
RUN dotnet build "NafaPlace.Notifications.API.csproj" -c Release -o /app/build

# Publish stage
FROM build AS publish
RUN dotnet publish "NafaPlace.Notifications.API.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Final stage
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS final
WORKDIR /app
COPY --from=publish /app/publish .

# Configuration pour le marché africain
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false
ENV TZ=Africa/Conakry
ENV LANG=fr_GN.UTF-8
ENV LANGUAGE=fr_GN.UTF-8
ENV LC_ALL=fr_GN.UTF-8

EXPOSE 80
ENTRYPOINT ["dotnet", "NafaPlace.Notifications.API.dll"]
