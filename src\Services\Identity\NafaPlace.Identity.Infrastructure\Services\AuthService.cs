using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using NafaPlace.Identity.Application.Common.Exceptions;
using NafaPlace.Identity.Application.Common.Interfaces;
using NafaPlace.Identity.Application.DTOs;
using NafaPlace.Identity.Application.DTOs.Auth;
using NafaPlace.Identity.Domain.Models;
using NafaPlace.Identity.Infrastructure.Data;
using NafaPlace.Identity.Infrastructure.Utils;
using System.Security.Claims;
using System.Text;
using System.Text.Json;

namespace NafaPlace.Identity.Infrastructure.Services;

public class AuthService : IAuthService
{
    private readonly IdentityDbContext _context;
    private readonly IJwtService _jwtService;
    private readonly ILogger<AuthService> _logger;
    private readonly Microsoft.AspNetCore.Identity.IPasswordHasher<User> _passwordHasher;
    private readonly HttpClient _httpClient;
    private readonly IConfiguration _configuration;

    public AuthService(IdentityDbContext context, IJwtService jwtService, ILogger<AuthService> logger, Microsoft.AspNetCore.Identity.IPasswordHasher<User> passwordHasher, HttpClient httpClient, IConfiguration configuration)
    {
        _context = context;
        _jwtService = jwtService;
        _logger = logger;
        _passwordHasher = passwordHasher;
        _httpClient = httpClient;
        _configuration = configuration;
    }

    public async Task<AuthResponse> RegisterAsync(RegisterRequest request)
    {
        // Vérifier si l'email existe déjà
        var existingUserByEmail = await _context.Users.FirstOrDefaultAsync(u => u.Email == request.Email);
        if (existingUserByEmail != null)
        {
            throw new ValidationException("Un utilisateur avec cet email existe déjà.");
        }

        // Vérifier si le nom d'utilisateur existe déjà
        var existingUserByUsername = await _context.Users.FirstOrDefaultAsync(u => u.Username == request.Username);
        if (existingUserByUsername != null)
        {
            throw new ValidationException("Ce nom d'utilisateur est déjà pris.");
        }

        var user = new User
        {
            Email = request.Email,
            Username = request.Username,
            FirstName = request.FirstName,
            LastName = request.LastName,
            Roles = "User", // Rôle par défaut
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };
        user.PasswordHash = _passwordHasher.HashPassword(user, request.Password);

        _context.Users.Add(user);

        // Générer un refresh token
        var refreshToken = _jwtService.GenerateRefreshToken();
        user.RefreshToken = refreshToken;
        user.RefreshTokenExpiryTime = DateTime.UtcNow.AddDays(7);

        await _context.SaveChangesAsync();

        // Assigner le rôle User par défaut
        var userRole = await _context.Roles.FirstOrDefaultAsync(r => r.Name == "User");
        if (userRole != null)
        {
            var userRoleAssignment = new UserRole
            {
                UserId = user.Id,
                RoleId = userRole.Id
            };
            _context.UserRoles.Add(userRoleAssignment);
            await _context.SaveChangesAsync();
        }

        // Générer un access token
        var roles = new[] { "User" };
        var accessToken = _jwtService.GenerateAccessToken(user.Id, user.Username, roles);

        return new AuthResponse
        {
            AccessToken = accessToken,
            RefreshToken = refreshToken,
            User = MapToUserDto(user)
        };
    }

    public async Task<AuthResponse> RegisterAsync(RegisterRequest request, string role)
    {
        // Vérifier si l'email existe déjà
        var existingUserByEmail = await _context.Users.FirstOrDefaultAsync(u => u.Email == request.Email);
        if (existingUserByEmail != null)
        {
            throw new ValidationException("Un utilisateur avec cet email existe déjà.");
        }

        // Vérifier si le nom d'utilisateur existe déjà
        var existingUserByUsername = await _context.Users.FirstOrDefaultAsync(u => u.Username == request.Username);
        if (existingUserByUsername != null)
        {
            throw new ValidationException("Ce nom d'utilisateur est déjà pris.");
        }

        var user = new User
        {
            Email = request.Email,
            Username = request.Username,
            FirstName = request.FirstName,
            LastName = request.LastName,
            Roles = role, // Utilisation du rôle spécifié
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };
        user.PasswordHash = _passwordHasher.HashPassword(user, request.Password);

        _context.Users.Add(user);

        // Générer un refresh token
        var refreshToken = _jwtService.GenerateRefreshToken();
        user.RefreshToken = refreshToken;
        user.RefreshTokenExpiryTime = DateTime.UtcNow.AddDays(7);

        await _context.SaveChangesAsync();

        // Assigner le rôle spécifié
        var userRole = await _context.Roles.FirstOrDefaultAsync(r => r.Name == role);
        if (userRole != null)
        {
            var userRoleAssignment = new UserRole
            {
                UserId = user.Id,
                RoleId = userRole.Id
            };
            _context.UserRoles.Add(userRoleAssignment);
            await _context.SaveChangesAsync();
        }

        // Si c'est un vendeur, créer l'entité Seller dans le service Catalog
        if (role == "Seller")
        {
            var sellerCreated = await CreateSellerInCatalogAsync(user);
            if (!sellerCreated)
            {
                _logger.LogWarning($"L'utilisateur {user.Id} a été créé mais la création du vendeur dans Catalog a échoué");
                // On continue quand même car l'utilisateur est créé
            }
        }

        // Générer un access token
        var roles = new[] { role };

        // Si l'utilisateur est un vendeur, récupérer son SellerId
        int? sellerId = null;
        if (role == "Seller")
        {
            sellerId = await GetSellerIdByUserIdAsync(user.Id);
        }

        var accessToken = _jwtService.GenerateAccessToken(user.Id, user.Username, roles, sellerId);

        return new AuthResponse
        {
            AccessToken = accessToken,
            RefreshToken = refreshToken,
            User = MapToUserDto(user)
        };
    }

    public async Task<AuthResponse> LoginAsync(LoginRequest request)
    {
        try
        {
            // Déterminer l'identifiant à utiliser (Email ou Username)
            var identifier = !string.IsNullOrEmpty(request.Email) ? request.Email : request.Username;
            _logger.LogInformation("Tentative de connexion pour l'utilisateur: {Identifier}", identifier);

            // Vérifier si l'utilisateur existe par nom d'utilisateur ou email et charger ses rôles
            var user = await _context.Users
                .Include(u => u.UserRoles)
                .ThenInclude(ur => ur.Role)
                .FirstOrDefaultAsync(u =>
                    u.Username == identifier ||
                    u.Email == identifier ||
                    (!string.IsNullOrEmpty(request.Username) && u.Username == request.Username) ||
                    (!string.IsNullOrEmpty(request.Email) && u.Email == request.Email));
            
            if (user == null)
            {
                _logger.LogWarning("Échec de connexion: utilisateur non trouvé pour {Identifier}", identifier);
                throw new AuthenticationException("Nom d'utilisateur ou mot de passe incorrect.");
            }

            _logger.LogInformation("Utilisateur trouvé: {UserId}, vérification du mot de passe", user.Id);
            
            if (_passwordHasher.VerifyHashedPassword(user, user.PasswordHash, request.Password) == Microsoft.AspNetCore.Identity.PasswordVerificationResult.Failed)
            {
                _logger.LogWarning("Échec de connexion: mot de passe incorrect pour {Identifier}", identifier);
                throw new AuthenticationException("Nom d'utilisateur ou mot de passe incorrect.");
            }

            if (!user.IsActive)
            {
                _logger.LogWarning("Échec de connexion: compte désactivé pour {Identifier}", identifier);
                throw new AuthenticationException("Ce compte est désactivé.");
            }

            _logger.LogInformation("Authentification réussie pour {Identifier}, génération des tokens", identifier);
            
            // Générer un refresh token
            var refreshToken = _jwtService.GenerateRefreshToken();
            user.RefreshToken = refreshToken;
            user.RefreshTokenExpiryTime = DateTime.UtcNow.AddDays(7);

            // Mettre à jour la date de dernière connexion
            user.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            // Générer un access token avec les rôles de l'utilisateur
            var roles = user.UserRoles.Select(ur => ur.Role.Name).ToArray();

            // Si l'utilisateur est un vendeur, récupérer son SellerId
            int? sellerId = null;
            if (roles.Contains("Seller"))
            {
                sellerId = await GetSellerIdByUserIdAsync(user.Id);
            }

            var accessToken = _jwtService.GenerateAccessToken(user.Id, user.Username, roles, sellerId);

            _logger.LogInformation("Connexion réussie pour {Identifier}, tokens générés", identifier);
            
            return new AuthResponse
            {
                Success = true,
                AccessToken = accessToken,
                RefreshToken = refreshToken,
                User = MapToUserDto(user),
                Token = accessToken // Assurer la compatibilité avec le client
            };
        }
        catch (Exception ex) when (ex is not AuthenticationException)
        {
            var identifier = !string.IsNullOrEmpty(request.Email) ? request.Email : request.Username;
            _logger.LogError(ex, "Erreur inattendue lors de la connexion pour {Identifier}", identifier);
            throw new AuthenticationException($"Une erreur s'est produite lors de la connexion: {ex.Message}");
        }
    }

    public async Task<AuthResponse> RefreshTokenAsync(RefreshTokenRequest request)
    {
        var principal = _jwtService.GetPrincipalFromExpiredToken(request.AccessToken);
        var userId = int.Parse(principal.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? throw new AuthenticationException("Token invalide"));

        var user = await _context.Users
            .Include(u => u.UserRoles)
            .ThenInclude(ur => ur.Role)
            .FirstOrDefaultAsync(u => u.Id == userId);
        if (user == null || user.RefreshToken != request.RefreshToken || user.RefreshTokenExpiryTime <= DateTime.UtcNow)
        {
            throw new AuthenticationException("Token de rafraîchissement invalide ou expiré");
        }

        // Générer un nouveau refresh token
        var newRefreshToken = _jwtService.GenerateRefreshToken();
        user.RefreshToken = newRefreshToken;
        user.RefreshTokenExpiryTime = DateTime.UtcNow.AddDays(7);

        await _context.SaveChangesAsync();

        // Générer un nouveau access token avec les rôles de l'utilisateur
        var roles = user.UserRoles.Select(ur => ur.Role.Name).ToArray();

        // Si l'utilisateur est un vendeur, récupérer son SellerId
        int? sellerId = null;
        if (roles.Contains("Seller"))
        {
            sellerId = await GetSellerIdByUserIdAsync(user.Id);
        }

        var newAccessToken = _jwtService.GenerateAccessToken(user.Id, user.Username, roles, sellerId);

        return new AuthResponse
        {
            AccessToken = newAccessToken,
            RefreshToken = newRefreshToken,
            User = MapToUserDto(user)
        };
    }

    public async Task LogoutAsync(int userId)
    {
        var user = await _context.Users.FirstOrDefaultAsync(u => u.Id == userId);
        if (user != null)
        {
            // Révoquer le refresh token
            user.RefreshToken = null;
            user.UpdatedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();
        }
    }

    public async Task<UserDto> GetUserByIdAsync(int userId)
    {
        var user = await _context.Users
            .Include(u => u.UserRoles)
            .ThenInclude(ur => ur.Role)
            .FirstOrDefaultAsync(u => u.Id == userId);
        if (user == null)
        {
            throw new NotFoundException($"Utilisateur avec l'ID {userId} non trouvé");
        }

        return MapToUserDto(user);
    }

    /// <summary>
    /// Crée un vendeur dans le service Catalog
    /// </summary>
    private async Task<bool> CreateSellerInCatalogAsync(User user)
    {
        try
        {
            var sellerData = new
            {
                Name = $"{user.FirstName} {user.LastName}".Trim(),
                Email = user.Email,
                PhoneNumber = "", // Sera mis à jour plus tard par le vendeur
                Address = "", // Sera mis à jour plus tard par le vendeur
                IsActive = true,
                UserId = user.Id.ToString()
            };

            var json = JsonSerializer.Serialize(sellerData);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            // URL du service Catalog (à configurer via appsettings)
            var catalogServiceUrl = "http://localhost:5243/api/v1/sellers";

            _logger.LogInformation($"Tentative de création du vendeur dans Catalog pour l'utilisateur {user.Id}");

            var response = await _httpClient.PostAsync(catalogServiceUrl, content);

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation($"Vendeur créé avec succès dans Catalog pour l'utilisateur {user.Id}");
                return true;
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogError($"Erreur lors de la création du vendeur dans Catalog: {response.StatusCode} - {errorContent}");
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Exception lors de la création du vendeur dans Catalog pour l'utilisateur {user.Id}");
            return false;
        }
    }

    /// <summary>
    /// Récupère l'ID du vendeur associé à un utilisateur
    /// </summary>
    private async Task<int?> GetSellerIdByUserIdAsync(int userId)
    {
        try
        {
            var catalogApiUrl = _configuration["ServiceUrls:CatalogApi"];
            if (string.IsNullOrEmpty(catalogApiUrl))
            {
                _logger.LogWarning("CatalogApi URL not configured");
                return null;
            }

            var response = await _httpClient.GetAsync($"{catalogApiUrl}/api/v1/sellers/by-user/{userId}");
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var seller = JsonSerializer.Deserialize<JsonElement>(content, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

                if (seller.TryGetProperty("id", out var idProperty))
                {
                    return idProperty.GetInt32();
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération du SellerId pour l'utilisateur {UserId}", userId);
        }

        return null;
    }

    private UserDto MapToUserDto(User user)
    {
        return new UserDto
        {
            Id = user.Id,
            Username = user.Username,
            Email = user.Email,
            FirstName = user.FirstName,
            LastName = user.LastName,
            Roles = user.UserRoles?.Select(ur => ur.Role.Name).ToList() ?? new List<string>(),
            IsActive = user.IsActive,
            CreatedAt = user.CreatedAt,
            UpdatedAt = user.UpdatedAt
        };
    }
}
