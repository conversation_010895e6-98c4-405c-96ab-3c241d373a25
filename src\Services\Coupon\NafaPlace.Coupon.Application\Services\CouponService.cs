using Microsoft.Extensions.Logging;
using NafaPlace.Coupon.Application.DTOs;
using NafaPlace.Coupon.Application.Interfaces;
using NafaPlace.Coupon.Domain.Models;

namespace NafaPlace.Coupon.Application.Services;

public class CouponService : ICouponService
{
    private readonly ICouponRepository _couponRepository;
    private readonly ILogger<CouponService> _logger;

    public CouponService(ICouponRepository couponRepository, ILogger<CouponService> logger)
    {
        _couponRepository = couponRepository;
        _logger = logger;
    }

    public async Task<CouponDto> CreateCouponAsync(CreateCouponRequest request, string createdBy)
    {
        // Vérifier que le code n'existe pas déjà
        var existingCoupon = await _couponRepository.GetByCodeAsync(request.Code);
        if (existingCoupon != null)
        {
            throw new InvalidOperationException($"Un coupon avec le code '{request.Code}' existe déjà");
        }

        var coupon = new Domain.Models.Coupon
        {
            Code = request.Code.ToUpper(),
            Name = request.Name,
            Description = request.Description,
            Type = request.Type,
            Value = request.Value,
            MinimumOrderAmount = request.MinimumOrderAmount,
            MaximumDiscountAmount = request.MaximumDiscountAmount,
            StartDate = DateTime.SpecifyKind(request.StartDate, DateTimeKind.Utc),
            EndDate = DateTime.SpecifyKind(request.EndDate, DateTimeKind.Utc),
            UsageLimit = request.UsageLimit,
            UsageLimitPerUser = request.UsageLimitPerUser,
            IsActive = request.IsActive,
            Currency = request.Currency,
            ApplicableToAllProducts = request.ApplicableToAllProducts,
            ApplicableProductIds = request.ApplicableProductIds,
            ApplicableCategoryIds = request.ApplicableCategoryIds,
            ApplicableSellerIds = request.ApplicableSellerIds,
            ExcludedProductIds = request.ExcludedProductIds,
            ExcludedCategoryIds = request.ExcludedCategoryIds,
            ExcludedSellerIds = request.ExcludedSellerIds,
            CreatedBy = createdBy
        };

        var createdCoupon = await _couponRepository.CreateAsync(coupon);
        return MapToDto(createdCoupon);
    }

    public async Task<CouponDto> UpdateCouponAsync(int id, UpdateCouponRequest request, string updatedBy)
    {
        var coupon = await _couponRepository.GetByIdAsync(id);
        if (coupon == null)
        {
            throw new ArgumentException("Coupon non trouvé");
        }

        // Mettre à jour les propriétés modifiables
        if (!string.IsNullOrEmpty(request.Name))
            coupon.Name = request.Name;
        
        if (request.Description != null)
            coupon.Description = request.Description;
        
        if (request.Value.HasValue)
            coupon.Value = request.Value.Value;
        
        if (request.MinimumOrderAmount.HasValue)
            coupon.MinimumOrderAmount = request.MinimumOrderAmount;
        
        if (request.MaximumDiscountAmount.HasValue)
            coupon.MaximumDiscountAmount = request.MaximumDiscountAmount;
        
        if (request.StartDate.HasValue)
            coupon.StartDate = DateTime.SpecifyKind(request.StartDate.Value, DateTimeKind.Utc);

        if (request.EndDate.HasValue)
            coupon.EndDate = DateTime.SpecifyKind(request.EndDate.Value, DateTimeKind.Utc);
        
        if (request.UsageLimit.HasValue)
            coupon.UsageLimit = request.UsageLimit;
        
        if (request.UsageLimitPerUser.HasValue)
            coupon.UsageLimitPerUser = request.UsageLimitPerUser;
        
        if (request.IsActive.HasValue)
            coupon.IsActive = request.IsActive.Value;
        
        if (request.ApplicableToAllProducts.HasValue)
            coupon.ApplicableToAllProducts = request.ApplicableToAllProducts.Value;
        
        if (request.ApplicableProductIds != null)
            coupon.ApplicableProductIds = request.ApplicableProductIds;
        
        if (request.ApplicableCategoryIds != null)
            coupon.ApplicableCategoryIds = request.ApplicableCategoryIds;
        
        if (request.ApplicableSellerIds != null)
            coupon.ApplicableSellerIds = request.ApplicableSellerIds;
        
        if (request.ExcludedProductIds != null)
            coupon.ExcludedProductIds = request.ExcludedProductIds;
        
        if (request.ExcludedCategoryIds != null)
            coupon.ExcludedCategoryIds = request.ExcludedCategoryIds;
        
        if (request.ExcludedSellerIds != null)
            coupon.ExcludedSellerIds = request.ExcludedSellerIds;

        coupon.UpdatedBy = updatedBy;

        var updatedCoupon = await _couponRepository.UpdateAsync(coupon);
        return MapToDto(updatedCoupon);
    }

    public async Task<bool> DeleteCouponAsync(int id)
    {
        try
        {
            await _couponRepository.DeleteAsync(id);
            return true;
        }
        catch
        {
            return false;
        }
    }

    public async Task<CouponDto?> GetCouponByIdAsync(int id)
    {
        var coupon = await _couponRepository.GetByIdAsync(id);
        return coupon != null ? MapToDto(coupon) : null;
    }

    public async Task<CouponDto?> GetCouponByCodeAsync(string code)
    {
        var coupon = await _couponRepository.GetByCodeAsync(code.ToUpper());
        return coupon != null ? MapToDto(coupon) : null;
    }

    public async Task<List<CouponDto>> GetCouponsAsync(int page = 1, int pageSize = 20, bool? isActive = null)
    {
        var coupons = isActive.HasValue && isActive.Value
            ? await _couponRepository.GetActiveCouponsAsync()
            : await _couponRepository.GetAllAsync();
        return coupons.Select(MapToDto).ToList();
    }

    public async Task<List<CouponDto>> GetActiveCouponsAsync()
    {
        var coupons = await _couponRepository.GetActiveCouponsAsync();
        return coupons.Select(MapToDto).ToList();
    }

    public async Task<CouponValidationResult> ValidateCouponAsync(string couponCode, CartForCouponValidation cart)
    {
        var coupon = await _couponRepository.GetByCodeAsync(couponCode.ToUpper());

        if (coupon == null)
        {
            return new CouponValidationResult
            {
                IsValid = false,
                ErrorMessage = "Code coupon invalide"
            };
        }

        if (!coupon.IsValid)
        {
            var reason = coupon.IsExpired ? "expiré" :
                        coupon.IsNotStarted ? "pas encore actif" :
                        !coupon.IsActive ? "désactivé" :
                        coupon.IsUsageLimitReached ? "limite d'utilisation atteinte" : "invalide";

            return new CouponValidationResult
            {
                IsValid = false,
                ErrorMessage = $"Ce coupon est {reason}"
            };
        }

        // Vérifier le montant minimum de commande
        if (coupon.MinimumOrderAmount.HasValue && cart.SubTotal < coupon.MinimumOrderAmount.Value)
        {
            return new CouponValidationResult
            {
                IsValid = false,
                ErrorMessage = $"Montant minimum de {coupon.MinimumOrderAmount.Value:N0} {coupon.Currency} requis"
            };
        }

        // Vérifier la limite d'utilisation par utilisateur
        if (coupon.UsageLimitPerUser.HasValue)
        {
            var userUsageCount = await _couponRepository.GetUserUsageCountAsync(coupon.Id, cart.UserId);
            if (userUsageCount >= coupon.UsageLimitPerUser.Value)
            {
                return new CouponValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "Vous avez déjà utilisé ce coupon le nombre maximum de fois autorisé"
                };
            }
        }

        // Vérifier l'applicabilité aux produits
        if (!IsApplicableToCart(coupon, cart))
        {
            return new CouponValidationResult
            {
                IsValid = false,
                ErrorMessage = "Ce coupon n'est pas applicable aux produits de votre panier"
            };
        }

        var discountAmount = await CalculateDiscountAsync(MapToDto(coupon), cart);

        return new CouponValidationResult
        {
            IsValid = true,
            DiscountAmount = discountAmount,
            Coupon = MapToDto(coupon)
        };
    }

    public async Task<CouponApplicationResult> ApplyCouponAsync(string couponCode, CartForCouponValidation cart)
    {
        var validationResult = await ValidateCouponAsync(couponCode, cart);

        if (!validationResult.IsValid)
        {
            return new CouponApplicationResult
            {
                Success = false,
                ErrorMessage = validationResult.ErrorMessage
            };
        }

        var discountAmount = validationResult.DiscountAmount;
        var newSubTotal = Math.Max(0, cart.SubTotal - discountAmount);

        // Recalculer les frais de livraison et taxes sur le nouveau sous-total
        var shippingFee = newSubTotal > 500000 ? 0 : 25000; // Livraison gratuite > 500,000 GNF
        var tax = newSubTotal * 0.18m; // 18% TVA
        var newTotal = newSubTotal + shippingFee + tax;

        return new CouponApplicationResult
        {
            Success = true,
            DiscountAmount = discountAmount,
            NewSubTotal = newSubTotal,
            NewTotal = newTotal,
            AppliedCoupon = validationResult.Coupon
        };
    }

    public async Task<decimal> CalculateDiscountAsync(CouponDto coupon, CartForCouponValidation cart)
    {
        decimal discountAmount = 0;

        // Calculer le montant éligible (produits applicables)
        var eligibleAmount = GetEligibleAmount(coupon, cart);

        switch (coupon.Type)
        {
            case CouponType.FixedAmount:
                discountAmount = Math.Min(coupon.Value, eligibleAmount);
                break;

            case CouponType.Percentage:
                discountAmount = eligibleAmount * (coupon.Value / 100);
                break;

            case CouponType.FreeShipping:
                // La livraison gratuite sera gérée dans le calcul du panier
                discountAmount = eligibleAmount > 500000 ? 0 : 25000; // Frais de livraison standard
                break;

            case CouponType.BuyXGetY:
                // Logique pour "Achetez X obtenez Y gratuit" - à implémenter selon les besoins
                discountAmount = 0;
                break;
        }

        // Appliquer la limite de réduction maximale si définie
        if (coupon.MaximumDiscountAmount.HasValue)
        {
            discountAmount = Math.Min(discountAmount, coupon.MaximumDiscountAmount.Value);
        }

        return Math.Round(discountAmount, 0); // Arrondir au GNF le plus proche
    }

    public async Task<bool> RecordCouponUsageAsync(int couponId, string userId, string? orderId, decimal discountAmount)
    {
        try
        {
            var usage = await _couponRepository.RecordUsageAsync(couponId, userId, discountAmount);

            // Incrémenter le compteur d'utilisation du coupon
            await _couponRepository.IncrementUsageCountAsync(couponId);

            return true;
        }
        catch
        {
            return false;
        }
    }

    public async Task<int> GetUserCouponUsageCountAsync(int couponId, string userId)
    {
        return await _couponRepository.GetUserUsageCountAsync(couponId, userId);
    }

    public async Task<List<CouponDto>> GetUserAvailableCouponsAsync(string userId, CartForCouponValidation cart)
    {
        var allActiveCoupons = await _couponRepository.GetActiveCouponsAsync();
        var availableCoupons = new List<CouponDto>();

        foreach (var coupon in allActiveCoupons)
        {
            var validation = await ValidateCouponAsync(coupon.Code, cart);
            if (validation.IsValid)
            {
                availableCoupons.Add(MapToDto(coupon));
            }
        }

        return availableCoupons;
    }

    public async Task<bool> DeactivateCouponAsync(int id)
    {
        var coupon = await _couponRepository.GetByIdAsync(id);
        if (coupon == null) return false;

        coupon.IsActive = false;
        await _couponRepository.UpdateAsync(coupon);
        return true;
    }

    public async Task<bool> ActivateCouponAsync(int id)
    {
        var coupon = await _couponRepository.GetByIdAsync(id);
        if (coupon == null) return false;

        coupon.IsActive = true;
        await _couponRepository.UpdateAsync(coupon);
        return true;
    }

    public async Task<List<CouponDto>> GetExpiredCouponsAsync()
    {
        var expiredCoupons = await _couponRepository.GetExpiredCouponsAsync();
        return expiredCoupons.Select(MapToDto).ToList();
    }

    public async Task<int> CleanupExpiredCouponsAsync()
    {
        return await _couponRepository.CleanupExpiredCouponsAsync();
    }

    public async Task<CouponStatsDto> GetCouponStatsAsync(int couponId)
    {
        var coupon = await _couponRepository.GetByIdAsync(couponId);
        if (coupon == null)
        {
            throw new ArgumentException("Coupon non trouvé");
        }

        var stats = await _couponRepository.GetCouponStatsAsync(couponId);

        return new CouponStatsDto
        {
            CouponId = coupon.Id,
            Code = coupon.Code,
            Name = coupon.Name,
            TotalUsages = stats.TotalUsages,
            TotalDiscountGiven = stats.TotalDiscountGiven,
            Currency = coupon.Currency,
            UniqueUsers = stats.UniqueUsers,
            LastUsed = stats.LastUsed,
            AverageDiscountPerUse = stats.TotalUsages > 0 ? stats.TotalDiscountGiven / stats.TotalUsages : 0
        };
    }

    public async Task<List<DTOs.CouponUsageStatsDto>> GetCouponUsageStatsAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        return await _couponRepository.GetUsageStatsAsync(startDate, endDate);
    }

    // Méthodes privées d'aide
    private bool IsApplicableToCart(Domain.Models.Coupon coupon, CartForCouponValidation cart)
    {
        if (coupon.ApplicableToAllProducts)
        {
            // Vérifier les exclusions
            foreach (var item in cart.Items)
            {
                if (IsProductExcluded(coupon, item))
                {
                    continue; // Ce produit est exclu, mais d'autres peuvent être éligibles
                }
                return true; // Au moins un produit est éligible
            }
            return false; // Tous les produits sont exclus
        }

        // Vérifier les produits/catégories/vendeurs spécifiques
        foreach (var item in cart.Items)
        {
            if (IsProductIncluded(coupon, item) && !IsProductExcluded(coupon, item))
            {
                return true; // Au moins un produit est éligible
            }
        }

        return false;
    }

    private bool IsProductIncluded(Domain.Models.Coupon coupon, CartItemForCoupon item)
    {
        // Si applicable à tous les produits, retourner true
        if (coupon.ApplicableToAllProducts)
            return true;

        // Vérifier les produits spécifiques
        if (coupon.ApplicableProductIds?.Contains(item.ProductId) == true)
            return true;

        // Vérifier les catégories spécifiques
        if (item.CategoryId.HasValue && coupon.ApplicableCategoryIds?.Contains(item.CategoryId.Value) == true)
            return true;

        // Vérifier les vendeurs spécifiques
        if (item.SellerId.HasValue && coupon.ApplicableSellerIds?.Contains(item.SellerId.Value) == true)
            return true;

        return false;
    }

    private bool IsProductExcluded(Domain.Models.Coupon coupon, CartItemForCoupon item)
    {
        // Vérifier les exclusions de produits
        if (coupon.ExcludedProductIds?.Contains(item.ProductId) == true)
            return true;

        // Vérifier les exclusions de catégories
        if (item.CategoryId.HasValue && coupon.ExcludedCategoryIds?.Contains(item.CategoryId.Value) == true)
            return true;

        // Vérifier les exclusions de vendeurs
        if (item.SellerId.HasValue && coupon.ExcludedSellerIds?.Contains(item.SellerId.Value) == true)
            return true;

        return false;
    }

    private decimal GetEligibleAmount(CouponDto coupon, CartForCouponValidation cart)
    {
        decimal eligibleAmount = 0;

        foreach (var item in cart.Items)
        {
            if (IsProductIncluded(MapToDomain(coupon), item) && !IsProductExcluded(MapToDomain(coupon), item))
            {
                eligibleAmount += item.LineTotal;
            }
        }

        return eligibleAmount;
    }

    private static CouponDto MapToDto(Domain.Models.Coupon coupon)
    {
        return new CouponDto
        {
            Id = coupon.Id,
            Code = coupon.Code,
            Name = coupon.Name,
            Description = coupon.Description,
            Type = coupon.Type,
            Value = coupon.Value,
            MinimumOrderAmount = coupon.MinimumOrderAmount,
            MaximumDiscountAmount = coupon.MaximumDiscountAmount,
            StartDate = coupon.StartDate,
            EndDate = coupon.EndDate,
            UsageLimit = coupon.UsageLimit,
            UsageLimitPerUser = coupon.UsageLimitPerUser,
            UsageCount = coupon.UsageCount,
            IsActive = coupon.IsActive,
            Currency = coupon.Currency,
            ApplicableToAllProducts = coupon.ApplicableToAllProducts,
            ApplicableProductIds = coupon.ApplicableProductIds,
            ApplicableCategoryIds = coupon.ApplicableCategoryIds,
            ApplicableSellerIds = coupon.ApplicableSellerIds,
            ExcludedProductIds = coupon.ExcludedProductIds,
            ExcludedCategoryIds = coupon.ExcludedCategoryIds,
            ExcludedSellerIds = coupon.ExcludedSellerIds,
            CreatedBy = coupon.CreatedBy,
            CreatedAt = coupon.CreatedAt,
            IsExpired = coupon.IsExpired,
            IsValid = coupon.IsValid
        };
    }

    private static Domain.Models.Coupon MapToDomain(CouponDto dto)
    {
        return new Domain.Models.Coupon
        {
            Id = dto.Id,
            Code = dto.Code,
            Name = dto.Name,
            Description = dto.Description,
            Type = dto.Type,
            Value = dto.Value,
            MinimumOrderAmount = dto.MinimumOrderAmount,
            MaximumDiscountAmount = dto.MaximumDiscountAmount,
            StartDate = dto.StartDate,
            EndDate = dto.EndDate,
            UsageLimit = dto.UsageLimit,
            UsageLimitPerUser = dto.UsageLimitPerUser,
            UsageCount = dto.UsageCount,
            IsActive = dto.IsActive,
            Currency = dto.Currency,
            ApplicableToAllProducts = dto.ApplicableToAllProducts,
            ApplicableProductIds = dto.ApplicableProductIds,
            ApplicableCategoryIds = dto.ApplicableCategoryIds,
            ApplicableSellerIds = dto.ApplicableSellerIds,
            ExcludedProductIds = dto.ExcludedProductIds,
            ExcludedCategoryIds = dto.ExcludedCategoryIds,
            ExcludedSellerIds = dto.ExcludedSellerIds,
            CreatedBy = dto.CreatedBy
        };
    }
}
