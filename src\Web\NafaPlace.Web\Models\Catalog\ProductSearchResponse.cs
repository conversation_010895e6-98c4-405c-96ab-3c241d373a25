namespace NafaPlace.Web.Models.Catalog;

public class ProductSearchResponse
{
    public IEnumerable<ProductDto>? Products { get; set; }
    public int TotalItems { get; set; }
    public int Page { get; set; }
    public int PageSize { get; set; }
    public int TotalPages => (int)Math.Ceiling(TotalItems / (double)PageSize);
    
    public Dictionary<string, Dictionary<string, int>>? Facets { get; set; }
}
