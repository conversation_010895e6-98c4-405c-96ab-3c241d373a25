@page "/reviews"
@using NafaPlace.SellerPortal.Models.Reviews
@using NafaPlace.SellerPortal.Services
@inject IReviewService ReviewService
@inject IJSRuntime JSRuntime

<h1 class="visually-hidden">Gestion des Avis - NafaPlace Vendeur</h1>

<div class="container-fluid px-4">
    <h1 class="mt-4">Gestion des Avis</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="/dashboard">Dashboard</a></li>
        <li class="breadcrumb-item active">Avis</li>
    </ol>

    <!-- Statistiques des avis -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="text-xs font-weight-bold text-uppercase mb-1">Total Avis</div>
                            <div class="h5 mb-0 font-weight-bold">@_reviewStats.TotalReviews</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-star fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="text-xs font-weight-bold text-uppercase mb-1">Note Moyenne</div>
                            <div class="h5 mb-0 font-weight-bold">@_reviewStats.AverageRating.ToString("F1")</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-star-half-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-info text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="text-xs font-weight-bold text-uppercase mb-1">Avis Récents</div>
                            <div class="h5 mb-0 font-weight-bold">@_reviewStats.RecentReviews</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-warning text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="text-xs font-weight-bold text-uppercase mb-1">En Attente</div>
                            <div class="h5 mb-0 font-weight-bold">@_reviewStats.PendingReviews</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-hourglass-half fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtres -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-filter me-1"></i>
            Filtres
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <label for="productFilter" class="form-label">Produit</label>
                    <select class="form-select" id="productFilter" @bind="_selectedProductId">
                        <option value="0">Tous les produits</option>
                        @foreach (var product in _products)
                        {
                            <option value="@product.Id">@product.Name</option>
                        }
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="ratingFilter" class="form-label">Note</label>
                    <select class="form-select" id="ratingFilter" @bind="_selectedRating">
                        <option value="0">Toutes les notes</option>
                        <option value="5">5 étoiles</option>
                        <option value="4">4 étoiles</option>
                        <option value="3">3 étoiles</option>
                        <option value="2">2 étoiles</option>
                        <option value="1">1 étoile</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="statusFilter" class="form-label">Statut</label>
                    <select class="form-select" id="statusFilter" @bind="_selectedStatus">
                        <option value="">Tous les statuts</option>
                        <option value="approved">Approuvé</option>
                        <option value="pending">En attente</option>
                        <option value="rejected">Rejeté</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="searchInput" class="form-label">Recherche</label>
                    <input type="text" class="form-control" id="searchInput" placeholder="Rechercher..." 
                           @bind="_searchTerm" @onkeyup="OnSearchKeyUp">
                </div>
            </div>
        </div>
    </div>

    <!-- Liste des avis -->
    <div class="card">
        <div class="card-header">
            <i class="fas fa-comments me-1"></i>
            Avis des clients (@_reviews.Count)
        </div>
        <div class="card-body">
            @if (_loading)
            {
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                </div>
            }
            else if (_reviews.Any())
            {
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead>
                            <tr>
                                <th>Produit</th>
                                <th>Client</th>
                                <th>Note</th>
                                <th>Commentaire</th>
                                <th>Date</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var review in _reviews)
                            {
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if (!string.IsNullOrEmpty(review.ProductImageUrl))
                                            {
                                                <img src="@review.ProductImageUrl" alt="@review.ProductName" 
                                                     class="me-2" style="width: 40px; height: 40px; object-fit: cover;">
                                            }
                                            <div>
                                                <strong>@review.ProductName</strong>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>@review.UserName</strong>
                                            @if (review.IsVerifiedPurchase)
                                            {
                                                <br><small class="text-success">
                                                    <i class="fas fa-check-circle me-1"></i>Achat vérifié
                                                </small>
                                            }
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @for (int i = 1; i <= 5; i++)
                                            {
                                                <i class="fas fa-star @(i <= review.Rating ? "text-warning" : "text-muted")"></i>
                                            }
                                            <span class="ms-2">(@review.Rating)</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            @if (!string.IsNullOrEmpty(review.Title))
                                            {
                                                <strong>@review.Title</strong><br>
                                            }
                                            <span>@(review.Comment.Length > 100 ? review.Comment.Substring(0, 100) + "..." : review.Comment)</span>
                                        </div>
                                    </td>
                                    <td>@review.CreatedAt.ToString("dd/MM/yyyy")</td>
                                    <td>
                                        <span class="badge @GetStatusBadgeClass(review.IsApproved)">
                                            @GetStatusText(review.IsApproved)
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button class="btn btn-sm btn-outline-primary" 
                                                    @onclick="() => ViewReviewDetails(review)">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-info" 
                                                    @onclick="() => ReplyToReview(review)">
                                                <i class="fas fa-reply"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if (_totalPages > 1)
                {
                    <nav aria-label="Reviews pagination" class="mt-3">
                        <ul class="pagination justify-content-center">
                            @if (_currentPage > 1)
                            {
                                <li class="page-item">
                                    <button class="page-link" @onclick="() => ChangePage(_currentPage - 1)">
                                        Précédent
                                    </button>
                                </li>
                            }
                            
                            @for (int i = 1; i <= _totalPages; i++)
                            {
                                var pageNumber = i;
                                <li class="page-item @(_currentPage == pageNumber ? "active" : "")">
                                    <button class="page-link" @onclick="() => ChangePage(pageNumber)">
                                        @pageNumber
                                    </button>
                                </li>
                            }
                            
                            @if (_currentPage < _totalPages)
                            {
                                <li class="page-item">
                                    <button class="page-link" @onclick="() => ChangePage(_currentPage + 1)">
                                        Suivant
                                    </button>
                                </li>
                            }
                        </ul>
                    </nav>
                }
            }
            else
            {
                <div class="text-center py-4">
                    <i class="fas fa-star fa-3x text-muted mb-3"></i>
                    <h5>Aucun avis trouvé</h5>
                    <p class="text-muted">Aucun avis ne correspond aux critères de recherche.</p>
                </div>
            }
        </div>
    </div>
</div>

@code {
    private List<ReviewDto> _reviews = new();
    private List<ProductSummaryDto> _products = new();
    private ReviewStatsDto _reviewStats = new();
    private bool _loading = true;
    
    // Filters
    private int _selectedProductId = 0;
    private int _selectedRating = 0;
    private string _selectedStatus = string.Empty;
    private string _searchTerm = string.Empty;
    
    // Pagination
    private int _currentPage = 1;
    private int _totalPages = 1;
    private const int _pageSize = 10;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        _loading = true;
        try
        {
            // Load products, stats, and reviews
            await Task.WhenAll(
                LoadProducts(),
                LoadReviewStats(),
                LoadReviews()
            );
        }
        finally
        {
            _loading = false;
        }
    }

    private async Task LoadProducts()
    {
        try
        {
            _products = await ReviewService.GetSellerProductsAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des produits: {ex.Message}");
        }
    }

    private async Task LoadReviewStats()
    {
        try
        {
            _reviewStats = await ReviewService.GetSellerReviewStatsAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des statistiques: {ex.Message}");
        }
    }

    private async Task LoadReviews()
    {
        try
        {
            var request = new ReviewFilterRequest
            {
                ProductId = _selectedProductId > 0 ? _selectedProductId : null,
                Rating = _selectedRating > 0 ? _selectedRating : null,
                Status = !string.IsNullOrEmpty(_selectedStatus) ? _selectedStatus : null,
                SearchTerm = !string.IsNullOrEmpty(_searchTerm) ? _searchTerm : null,
                Page = _currentPage,
                PageSize = _pageSize
            };

            var response = await ReviewService.GetSellerReviewsAsync(request);
            _reviews = response.Reviews;
            _totalPages = response.TotalPages;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des avis: {ex.Message}");
        }
    }

    private async Task OnSearchKeyUp(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            _currentPage = 1;
            await LoadReviews();
        }
    }

    private async Task ChangePage(int page)
    {
        _currentPage = page;
        await LoadReviews();
    }

    private void ViewReviewDetails(ReviewDto review)
    {
        // TODO: Implement review details modal
    }

    private void ReplyToReview(ReviewDto review)
    {
        // TODO: Implement reply functionality
    }

    private string GetStatusBadgeClass(bool? isApproved)
    {
        return isApproved switch
        {
            true => "bg-success",
            false => "bg-danger",
            null => "bg-warning"
        };
    }

    private string GetStatusText(bool? isApproved)
    {
        return isApproved switch
        {
            true => "Approuvé",
            false => "Rejeté",
            null => "En attente"
        };
    }
}
