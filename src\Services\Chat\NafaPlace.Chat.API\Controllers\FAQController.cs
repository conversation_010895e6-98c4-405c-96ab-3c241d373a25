using Microsoft.AspNetCore.Mvc;
using NafaPlace.Chat.Application.Services;

namespace NafaPlace.Chat.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class FAQController : ControllerBase
{
    private readonly IFAQService _faqService;
    private readonly ILogger<FAQController> _logger;

    public FAQController(IFAQService faqService, ILogger<FAQController> logger)
    {
        _faqService = faqService;
        _logger = logger;
    }

    [HttpGet]
    public async Task<IActionResult> GetFAQs([FromQuery] string? category = null, [FromQuery] string? search = null)
    {
        try
        {
            var faqs = await _faqService.GetFAQsAsync(category, search);
            return Ok(faqs);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des FAQs");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    [HttpGet("categories")]
    public async Task<IActionResult> GetCategories()
    {
        try
        {
            var categories = await _faqService.GetCategoriesAsync();
            return Ok(categories);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des catégories FAQ");
            return Ok(new List<string> { "Général", "Commandes", "Livraison", "Paiement", "Retours" });
        }
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetFAQ(int id)
    {
        try
        {
            var faq = await _faqService.GetFAQAsync(id);
            
            if (faq == null)
            {
                return NotFound("FAQ non trouvée");
            }

            return Ok(faq);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de la FAQ {FAQId}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    [HttpPost("{id}/helpful")]
    public async Task<IActionResult> MarkAsHelpful(int id, [FromBody] HelpfulRequest request)
    {
        try
        {
            var success = await _faqService.MarkAsHelpfulAsync(id, request.IsHelpful);
            
            if (success)
            {
                return Ok(new { message = "Feedback enregistré" });
            }

            return BadRequest("Impossible d'enregistrer le feedback");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'enregistrement du feedback FAQ {FAQId}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    [HttpGet("search")]
    public async Task<IActionResult> SearchFAQs([FromQuery] string query, [FromQuery] int limit = 10)
    {
        try
        {
            var results = await _faqService.SearchFAQsAsync(query, limit);
            return Ok(results);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la recherche FAQ");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    [HttpGet("popular")]
    public async Task<IActionResult> GetPopularFAQs([FromQuery] int limit = 10)
    {
        try
        {
            var faqs = await _faqService.GetPopularFAQsAsync(limit);
            return Ok(faqs);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des FAQs populaires");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }
}

public class HelpfulRequest
{
    public bool IsHelpful { get; set; }
}
