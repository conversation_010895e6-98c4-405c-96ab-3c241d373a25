using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NafaPlace.Catalog.Application.DTOs.Product;
using NafaPlace.Catalog.Application.Services;
using System.Security.Claims;

namespace NafaPlace.Catalog.API.Controllers
{
    [ApiController]
    [Route("api/v1/[controller]")]
    public class SearchController : ControllerBase
    {
        private readonly IAdvancedSearchService _searchService;
        private readonly ILogger<SearchController> _logger;

        public SearchController(IAdvancedSearchService searchService, ILogger<SearchController> logger)
        {
            _searchService = searchService;
            _logger = logger;
        }

        [HttpPost("advanced")]
        [AllowAnonymous]
        public async Task<ActionResult<ProductSearchResultDto>> SearchAdvanced([FromBody] ProductSearchDto searchDto)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                
                // Sauvegarder la requête de recherche
                if (!string.IsNullOrEmpty(searchDto.SearchTerm))
                {
                    await _searchService.SaveSearchQueryAsync(searchDto.SearchTerm, userId);
                }

                var result = await _searchService.SearchProductsAdvancedAsync(searchDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la recherche avancée");
                return StatusCode(500, new { error = "Erreur interne du serveur" });
            }
        }

        [HttpGet("suggestions")]
        [AllowAnonymous]
        public async Task<ActionResult<List<SearchSuggestionDto>>> GetSuggestions(
            [FromQuery] string query, 
            [FromQuery] int maxSuggestions = 10)
        {
            try
            {
                if (string.IsNullOrEmpty(query) || query.Length < 2)
                {
                    return Ok(new List<SearchSuggestionDto>());
                }

                var suggestions = await _searchService.GetSearchSuggestionsAsync(query, maxSuggestions);
                return Ok(suggestions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération des suggestions");
                return StatusCode(500, new { error = "Erreur interne du serveur" });
            }
        }

        [HttpGet("autocomplete")]
        [AllowAnonymous]
        public async Task<ActionResult<List<string>>> GetAutocomplete(
            [FromQuery] string query, 
            [FromQuery] int maxSuggestions = 5)
        {
            try
            {
                if (string.IsNullOrEmpty(query) || query.Length < 2)
                {
                    return Ok(new List<string>());
                }

                var suggestions = await _searchService.GetAutocompleteSuggestionsAsync(query, maxSuggestions);
                return Ok(suggestions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de l'autocomplétion");
                return StatusCode(500, new { error = "Erreur interne du serveur" });
            }
        }

        [HttpGet("similar/{productId}")]
        [AllowAnonymous]
        public async Task<ActionResult<List<ProductDto>>> GetSimilarProducts(
            int productId, 
            [FromQuery] int maxResults = 10)
        {
            try
            {
                var similarProducts = await _searchService.FindSimilarProductsAsync(productId, maxResults);
                return Ok(similarProducts);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la recherche de produits similaires pour {ProductId}", productId);
                return StatusCode(500, new { error = "Erreur interne du serveur" });
            }
        }

        [HttpPost("by-image")]
        [AllowAnonymous]
        public async Task<ActionResult<List<ProductDto>>> SearchByImage(
            [FromBody] ImageSearchRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.ImageUrl))
                {
                    return BadRequest(new { error = "URL de l'image requise" });
                }

                var products = await _searchService.SearchByImageAsync(request.ImageUrl, request.MaxResults);
                return Ok(products);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la recherche par image");
                return StatusCode(500, new { error = "Erreur interne du serveur" });
            }
        }

        [HttpGet("facets")]
        [AllowAnonymous]
        public async Task<ActionResult<List<SearchFacetDto>>> GetSearchFacets([FromQuery] ProductSearchDto searchDto)
        {
            try
            {
                var facets = await _searchService.GetSearchFacetsAsync(searchDto);
                return Ok(facets);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération des facettes");
                return StatusCode(500, new { error = "Erreur interne du serveur" });
            }
        }

        [HttpGet("filters")]
        [AllowAnonymous]
        public async Task<ActionResult<Dictionary<string, List<string>>>> GetAvailableFilters()
        {
            try
            {
                var filters = await _searchService.GetAvailableFiltersAsync();
                return Ok(filters);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération des filtres");
                return StatusCode(500, new { error = "Erreur interne du serveur" });
            }
        }

        [HttpGet("popular")]
        [AllowAnonymous]
        public async Task<ActionResult<List<string>>> GetPopularSearches([FromQuery] int count = 10)
        {
            try
            {
                var popularSearches = await _searchService.GetPopularSearchesAsync(count);
                return Ok(popularSearches);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération des recherches populaires");
                return StatusCode(500, new { error = "Erreur interne du serveur" });
            }
        }

        [HttpGet("history")]
        [Authorize]
        public async Task<ActionResult<List<string>>> GetSearchHistory([FromQuery] int count = 10)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized();
                }

                var history = await _searchService.GetUserSearchHistoryAsync(userId, count);
                return Ok(history);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération de l'historique de recherche");
                return StatusCode(500, new { error = "Erreur interne du serveur" });
            }
        }

        [HttpGet("trends")]
        [AllowAnonymous]
        public async Task<ActionResult<List<string>>> GetSearchTrends([FromQuery] int days = 30)
        {
            try
            {
                var trends = await _searchService.GetSearchTrendsAsync(days);
                return Ok(trends);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération des tendances");
                return StatusCode(500, new { error = "Erreur interne du serveur" });
            }
        }

        [HttpGet("statistics")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<Dictionary<string, object>>> GetSearchStatistics()
        {
            try
            {
                var stats = await _searchService.GetSearchStatisticsAsync();
                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération des statistiques");
                return StatusCode(500, new { error = "Erreur interne du serveur" });
            }
        }

        [HttpPost("rebuild-index")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult> RebuildSearchIndex()
        {
            try
            {
                await _searchService.RebuildSearchIndexAsync();
                return Ok(new { message = "Index de recherche reconstruit avec succès" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la reconstruction de l'index");
                return StatusCode(500, new { error = "Erreur interne du serveur" });
            }
        }
    }

    public class ImageSearchRequest
    {
        public string ImageUrl { get; set; } = string.Empty;
        public int MaxResults { get; set; } = 10;
    }
}
