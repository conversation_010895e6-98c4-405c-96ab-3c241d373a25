@page "/login"
@using NafaPlace.AdminPortal.Models.Auth
@using NafaPlace.AdminPortal.Services
@inject IAuthService AuthService
@inject NavigationManager NavigationManager

<PageTitle>NafaPlace Admin - Connexion</PageTitle>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow-lg mt-5">
                <div class="card-header bg-primary text-white text-center py-3">
                    <h4 class="mb-0">Portail d'Administration</h4>
                </div>
                <div class="card-body p-4">
                    <h5 class="card-title text-center mb-4">Connexion</h5>
                    
                    @if (!string.IsNullOrEmpty(errorMessage))
                    {
                        <div class="alert alert-danger" role="alert">
                            @errorMessage
                        </div>
                    }
                    
                    <EditForm Model="@loginModel" OnValidSubmit="HandleLogin">
                        <DataAnnotationsValidator />
                        <ValidationSummary />
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">Email</label>
                            <InputText id="email" @bind-Value="loginModel.Email" class="form-control" placeholder="Entrez votre email" />
                            <ValidationMessage For="@(() => loginModel.Email)" />
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">Mot de passe</label>
                            <InputText id="password" type="password" @bind-Value="loginModel.Password" class="form-control" placeholder="Entrez votre mot de passe" />
                            <ValidationMessage For="@(() => loginModel.Password)" />
                        </div>
                        
                        <div class="mb-3 form-check">
                            <InputCheckbox id="rememberMe" @bind-Value="loginModel.RememberMe" class="form-check-input" />
                            <label class="form-check-label" for="rememberMe">Se souvenir de moi</label>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary" disabled="@isLoading">
                                @if (isLoading)
                                {
                                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                    <span class="ms-1">Connexion en cours...</span>
                                }
                                else
                                {
                                    <span>Se connecter</span>
                                }
                            </button>
                        </div>
                    </EditForm>
                </div>
                <div class="card-footer text-center py-3">
                    <div class="small">
                        <a href="/">Retour au site principal</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private LoginModel loginModel = new LoginModel();
    private bool isLoading = false;
    private string errorMessage = string.Empty;
    
    protected override async Task OnInitializedAsync()
    {
        // Rediriger vers le dashboard si l'utilisateur est déjà connecté
        var currentUser = await AuthService.GetCurrentUserAsync();
        if (currentUser.IsAuthenticated)
        {
            NavigationManager.NavigateTo("/dashboard");
        }
    }
    
    private async Task HandleLogin()
    {
        try
        {
            isLoading = true;
            errorMessage = string.Empty;
            
            var result = await AuthService.LoginAsync(loginModel.Email, loginModel.Password);
            
            if (result.Success)
            {
                // Rediriger directement vers le dashboard après connexion réussie
                NavigationManager.NavigateTo("/dashboard", true);
            }
            else
            {
                errorMessage = result.Message;
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Erreur lors de la connexion: {ex.Message}";
        }
        finally
        {
            isLoading = false;
        }
    }
    
    private class LoginModel
    {
        [Required(ErrorMessage = "L'email est requis")]
        [EmailAddress(ErrorMessage = "Format d'email invalide")]
        public string Email { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "Le mot de passe est requis")]
        public string Password { get; set; } = string.Empty;
        
        public bool RememberMe { get; set; }
    }
}
