using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace NafaPlace.AdminPortal.Models.Products
{
    public class ProductDto
    {
        public int Id { get; set; }
        
        [Required(ErrorMessage = "Le nom du produit est obligatoire")]
        [StringLength(100, ErrorMessage = "Le nom du produit ne doit pas dépasser 100 caractères")]
        public string Name { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "La description du produit est obligatoire")]
        [StringLength(1000, ErrorMessage = "La description du produit ne doit pas dépasser 1000 caractères")]
        public string Description { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "Le prix du produit est obligatoire")]
        [Range(0.01, 1000000, ErrorMessage = "Le prix doit être compris entre 0.01 et 1000000")]
        public decimal Price { get; set; }
        
        [Range(0, 1000000, ErrorMessage = "Le stock doit être compris entre 0 et 1000000")]
        public int Stock { get; set; }
        
        [Required(ErrorMessage = "La catégorie du produit est obligatoire")]
        public int CategoryId { get; set; }
        
        public string CategoryName { get; set; } = string.Empty;
        
        [Range(1, int.MaxValue, ErrorMessage = "Le vendeur du produit est obligatoire")]
        public int? SellerId { get; set; }
        
        public string SellerName { get; set; } = string.Empty;
        
        public string Brand { get; set; } = string.Empty;
        
        public string Model { get; set; } = string.Empty;
        
        public string Dimensions { get; set; } = string.Empty;
        
        public string Currency { get; set; } = "GNF";
        
        public bool IsActive { get; set; } = true;
        
        public bool IsFeatured { get; set; } = false;
        
        public bool IsApproved { get; set; } = false;
        
        public string ApprovalStatus { get; set; } = "En attente";

        public string? RejectionReason { get; set; }

        public DateTime? ApprovedAt { get; set; }

        public string? ApprovedBy { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        public DateTime? UpdatedAt { get; set; }
        
        public List<ProductImageDto> Images { get; set; } = new List<ProductImageDto>();
        public List<ProductVariantDto> Variants { get; set; } = new List<ProductVariantDto>();
    }
}
