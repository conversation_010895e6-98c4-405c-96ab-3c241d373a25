@page "/recommendations"
@inject IJSRuntime JSRuntime

<PageTitle>Recommandations IA - NafaPlace</PageTitle>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-gradient-primary text-white">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-robot fs-4 me-3"></i>
                        <div>
                            <h4 class="mb-0">Recommandations Personnalisées</h4>
                            <small class="opacity-75">Découvrez des produits sélectionnés spécialement pour vous</small>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    @if (isLoading)
                    {
                        <div class="text-center py-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Chargement...</span>
                            </div>
                            <p class="mt-3 text-muted">Génération de vos recommandations personnalisées...</p>
                        </div>
                    }

                    else
                    {
                        <!-- Onglets de recommandations -->
                        <ul class="nav nav-pills mb-4" id="recommendationTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="personalized-tab" data-bs-toggle="pill" 
                                        data-bs-target="#personalized" type="button" role="tab">
                                    <i class="bi bi-person-heart me-2"></i>Pour Vous
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="trending-tab" data-bs-toggle="pill" 
                                        data-bs-target="#trending" type="button" role="tab">
                                    <i class="bi bi-graph-up-arrow me-2"></i>Tendances
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="bestsellers-tab" data-bs-toggle="pill" 
                                        data-bs-target="#bestsellers" type="button" role="tab">
                                    <i class="bi bi-trophy me-2"></i>Meilleures Ventes
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="new-arrivals-tab" data-bs-toggle="pill" 
                                        data-bs-target="#new-arrivals" type="button" role="tab">
                                    <i class="bi bi-stars me-2"></i>Nouveautés
                                </button>
                            </li>
                        </ul>

                        <!-- Contenu des onglets -->
                        <div class="tab-content" id="recommendationTabsContent">
                            <!-- Recommandations personnalisées -->
                            <div class="tab-pane fade show active" id="personalized" role="tabpanel">
                                <div class="row">
                                    @if (personalizedRecommendations?.Any() == true)
                                    {
                                        @foreach (var product in personalizedRecommendations)
                                        {
                                            <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                                                <div class="card h-100 product-card">
                                                    <div class="position-relative">
                                                        <img src="@product.ImageUrl" class="card-img-top" alt="@product.Name" style="height: 200px; object-fit: cover;">
                                                        <div class="position-absolute top-0 end-0 m-2">
                                                            <span class="badge bg-success">@((product.Score * 100).ToString("F0"))% match</span>
                                                        </div>
                                                    </div>
                                                    <div class="card-body d-flex flex-column">
                                                        <h6 class="card-title">@product.Name</h6>
                                                        <p class="card-text text-muted small">@product.Reason</p>
                                                        <div class="mt-auto">
                                                            <div class="d-flex justify-content-between align-items-center">
                                                                <span class="h6 text-primary mb-0">@product.Price.ToString("N0") GNF</span>
                                                                <div class="rating">
                                                                    @for (int i = 1; i <= 5; i++)
                                                                    {
                                                                        <i class="bi @(i <= product.Rating ? "bi-star-fill" : "bi-star") text-warning"></i>
                                                                    }
                                                                </div>
                                                            </div>
                                                            <button class="btn btn-primary btn-sm w-100 mt-2">
                                                                <i class="bi bi-cart-plus me-1"></i>Ajouter au panier
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        }
                                    }
                                    else
                                    {
                                        <div class="col-12 text-center py-5">
                                            <i class="bi bi-robot fs-1 text-muted"></i>
                                            <p class="text-muted mt-3">Aucune recommandation personnalisée disponible pour le moment.</p>
                                        </div>
                                    }
                                </div>
                            </div>

                            <!-- Produits tendance -->
                            <div class="tab-pane fade" id="trending" role="tabpanel">
                                <div class="row">
                                    @if (trendingProducts?.Any() == true)
                                    {
                                        @foreach (var product in trendingProducts)
                                        {
                                            <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                                                <div class="card h-100 product-card">
                                                    <div class="position-relative">
                                                        <img src="@product.ImageUrl" class="card-img-top" alt="@product.Name" style="height: 200px; object-fit: cover;">
                                                        <div class="position-absolute top-0 start-0 m-2">
                                                            <span class="badge bg-danger">
                                                                <i class="bi bi-fire me-1"></i>Tendance
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <div class="card-body d-flex flex-column">
                                                        <h6 class="card-title">@product.Name</h6>
                                                        <p class="card-text text-muted small">@product.Reason</p>
                                                        <div class="mt-auto">
                                                            <div class="d-flex justify-content-between align-items-center">
                                                                <span class="h6 text-primary mb-0">@product.Price.ToString("N0") GNF</span>
                                                                <div class="rating">
                                                                    @for (int i = 1; i <= 5; i++)
                                                                    {
                                                                        <i class="bi @(i <= product.Rating ? "bi-star-fill" : "bi-star") text-warning"></i>
                                                                    }
                                                                </div>
                                                            </div>
                                                            <button class="btn btn-primary btn-sm w-100 mt-2">
                                                                <i class="bi bi-cart-plus me-1"></i>Ajouter au panier
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        }
                                    }
                                    else
                                    {
                                        <div class="col-12 text-center py-5">
                                            <i class="bi bi-graph-up-arrow fs-1 text-muted"></i>
                                            <p class="text-muted mt-3">Aucun produit tendance disponible pour le moment.</p>
                                        </div>
                                    }
                                </div>
                            </div>

                            <!-- Meilleures ventes -->
                            <div class="tab-pane fade" id="bestsellers" role="tabpanel">
                                <div class="row">
                                    @if (bestSellers?.Any() == true)
                                    {
                                        @foreach (var product in bestSellers)
                                        {
                                            <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                                                <div class="card h-100 product-card">
                                                    <div class="position-relative">
                                                        <img src="@product.ImageUrl" class="card-img-top" alt="@product.Name" style="height: 200px; object-fit: cover;">
                                                        <div class="position-absolute top-0 start-0 m-2">
                                                            <span class="badge bg-warning text-dark">
                                                                <i class="bi bi-trophy me-1"></i>Best-seller
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <div class="card-body d-flex flex-column">
                                                        <h6 class="card-title">@product.Name</h6>
                                                        <p class="card-text text-muted small">@product.Reason</p>
                                                        <div class="mt-auto">
                                                            <div class="d-flex justify-content-between align-items-center">
                                                                <span class="h6 text-primary mb-0">@product.Price.ToString("N0") GNF</span>
                                                                <div class="rating">
                                                                    @for (int i = 1; i <= 5; i++)
                                                                    {
                                                                        <i class="bi @(i <= product.Rating ? "bi-star-fill" : "bi-star") text-warning"></i>
                                                                    }
                                                                </div>
                                                            </div>
                                                            <button class="btn btn-primary btn-sm w-100 mt-2">
                                                                <i class="bi bi-cart-plus me-1"></i>Ajouter au panier
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        }
                                    }
                                    else
                                    {
                                        <div class="col-12 text-center py-5">
                                            <i class="bi bi-trophy fs-1 text-muted"></i>
                                            <p class="text-muted mt-3">Aucune meilleure vente disponible pour le moment.</p>
                                        </div>
                                    }
                                </div>
                            </div>

                            <!-- Nouveautés -->
                            <div class="tab-pane fade" id="new-arrivals" role="tabpanel">
                                <div class="row">
                                    @if (newArrivals?.Any() == true)
                                    {
                                        @foreach (var product in newArrivals)
                                        {
                                            <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                                                <div class="card h-100 product-card">
                                                    <div class="position-relative">
                                                        <img src="@product.ImageUrl" class="card-img-top" alt="@product.Name" style="height: 200px; object-fit: cover;">
                                                        <div class="position-absolute top-0 start-0 m-2">
                                                            <span class="badge bg-info">
                                                                <i class="bi bi-stars me-1"></i>Nouveau
                                                            </span>
                                                        </div>
                                                        @if (product.DiscountPercentage > 0)
                                                        {
                                                            <div class="position-absolute top-0 end-0 m-2">
                                                                <span class="badge bg-danger">-@product.DiscountPercentage%</span>
                                                            </div>
                                                        }
                                                    </div>
                                                    <div class="card-body d-flex flex-column">
                                                        <h6 class="card-title">@product.Name</h6>
                                                        <p class="card-text text-muted small">@product.Reason</p>
                                                        <div class="mt-auto">
                                                            <div class="d-flex justify-content-between align-items-center">
                                                                <span class="h6 text-primary mb-0">@product.Price.ToString("N0") GNF</span>
                                                                <div class="rating">
                                                                    @for (int i = 1; i <= 5; i++)
                                                                    {
                                                                        <i class="bi @(i <= product.Rating ? "bi-star-fill" : "bi-star") text-warning"></i>
                                                                    }
                                                                </div>
                                                            </div>
                                                            <button class="btn btn-primary btn-sm w-100 mt-2">
                                                                <i class="bi bi-cart-plus me-1"></i>Ajouter au panier
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        }
                                    }
                                    else
                                    {
                                        <div class="col-12 text-center py-5">
                                            <i class="bi bi-stars fs-1 text-muted"></i>
                                            <p class="text-muted mt-3">Aucune nouveauté disponible pour le moment.</p>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .product-card {
        transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        border: none;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .product-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .rating i {
        font-size: 0.8rem;
    }

    .nav-pills .nav-link {
        border-radius: 25px;
        margin-right: 0.5rem;
    }

    .nav-pills .nav-link.active {
        background: linear-gradient(135deg, var(--bs-primary), var(--bs-info));
    }
</style>

@code {
    private bool isLoading = true;
    private List<ProductRecommendation> personalizedRecommendations = new();
    private List<ProductRecommendation> trendingProducts = new();
    private List<ProductRecommendation> bestSellers = new();
    private List<ProductRecommendation> newArrivals = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadRecommendations();
    }

    private async Task LoadRecommendations()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            // Simuler un délai de chargement
            await Task.Delay(1000);

            // Générer des données de démonstration
            personalizedRecommendations = GeneratePersonalizedRecommendations();
            trendingProducts = GenerateTrendingProducts();
            bestSellers = GenerateBestSellers();
            newArrivals = GenerateNewArrivals();
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private List<ProductRecommendation> GeneratePersonalizedRecommendations()
    {
        return new List<ProductRecommendation>
        {
            new() { Id = 1, Name = "Smartphone Samsung Galaxy", Price = 850000, ImageUrl = "/images/products/phone1.jpg", Score = 0.95, Reason = "Basé sur vos achats précédents", Rating = 4.5 },
            new() { Id = 2, Name = "Laptop Dell Inspiron", Price = 1200000, ImageUrl = "/images/products/laptop1.jpg", Score = 0.88, Reason = "Populaire dans votre catégorie", Rating = 4.3 },
            new() { Id = 3, Name = "Casque Audio Sony", Price = 180000, ImageUrl = "/images/products/headphones1.jpg", Score = 0.82, Reason = "Souvent acheté ensemble", Rating = 4.7 },
            new() { Id = 4, Name = "Montre Connectée Apple", Price = 650000, ImageUrl = "/images/products/watch1.jpg", Score = 0.79, Reason = "Tendance actuelle", Rating = 4.4 },
            new() { Id = 5, Name = "Tablette iPad Air", Price = 950000, ImageUrl = "/images/products/tablet1.jpg", Score = 0.75, Reason = "Recommandé pour vous", Rating = 4.6 },
            new() { Id = 6, Name = "Appareil Photo Canon", Price = 1500000, ImageUrl = "/images/products/camera1.jpg", Score = 0.71, Reason = "Basé sur vos intérêts", Rating = 4.8 },
            new() { Id = 7, Name = "Console PlayStation 5", Price = 1800000, ImageUrl = "/images/products/ps5.jpg", Score = 0.68, Reason = "Très demandé", Rating = 4.9 },
            new() { Id = 8, Name = "Drone DJI Mini", Price = 750000, ImageUrl = "/images/products/drone1.jpg", Score = 0.65, Reason = "Innovation technologique", Rating = 4.2 }
        };
    }

    private List<ProductRecommendation> GenerateTrendingProducts()
    {
        return new List<ProductRecommendation>
        {
            new() { Id = 11, Name = "iPhone 15 Pro", Price = 1950000, ImageUrl = "/images/products/iphone15.jpg", Score = 0.98, Reason = "Très populaire cette semaine", Rating = 4.8 },
            new() { Id = 12, Name = "MacBook Pro M3", Price = 2500000, ImageUrl = "/images/products/macbook.jpg", Score = 0.94, Reason = "Tendance technologie", Rating = 4.9 },
            new() { Id = 13, Name = "AirPods Pro 2", Price = 420000, ImageUrl = "/images/products/airpods.jpg", Score = 0.91, Reason = "Accessoire tendance", Rating = 4.6 },
            new() { Id = 14, Name = "Tesla Model Y", Price = 85000000, ImageUrl = "/images/products/tesla.jpg", Score = 0.87, Reason = "Révolution automobile", Rating = 4.7 },
            new() { Id = 15, Name = "Meta Quest 3", Price = 1200000, ImageUrl = "/images/products/quest3.jpg", Score = 0.84, Reason = "VR en plein essor", Rating = 4.4 },
            new() { Id = 16, Name = "Steam Deck", Price = 950000, ImageUrl = "/images/products/steamdeck.jpg", Score = 0.81, Reason = "Gaming portable", Rating = 4.5 },
            new() { Id = 17, Name = "ChatGPT Plus", Price = 35000, ImageUrl = "/images/products/chatgpt.jpg", Score = 0.78, Reason = "IA révolutionnaire", Rating = 4.3 },
            new() { Id = 18, Name = "Starlink Internet", Price = 125000, ImageUrl = "/images/products/starlink.jpg", Score = 0.75, Reason = "Internet satellite", Rating = 4.1 }
        };
    }

    private List<ProductRecommendation> GenerateBestSellers()
    {
        return new List<ProductRecommendation>
        {
            new() { Id = 21, Name = "Samsung Galaxy S24", Price = 1450000, ImageUrl = "/images/products/galaxy24.jpg", Score = 0.96, Reason = "Meilleure vente du mois", Rating = 4.7 },
            new() { Id = 22, Name = "Nike Air Max 270", Price = 285000, ImageUrl = "/images/products/nike.jpg", Score = 0.93, Reason = "Chaussures les plus vendues", Rating = 4.5 },
            new() { Id = 23, Name = "Adidas Ultraboost", Price = 320000, ImageUrl = "/images/products/adidas.jpg", Score = 0.89, Reason = "Sport et confort", Rating = 4.6 },
            new() { Id = 24, Name = "Rolex Submariner", Price = 15000000, ImageUrl = "/images/products/rolex.jpg", Score = 0.86, Reason = "Luxe intemporel", Rating = 4.9 },
            new() { Id = 25, Name = "Louis Vuitton Sac", Price = 2800000, ImageUrl = "/images/products/lv.jpg", Score = 0.83, Reason = "Accessoire de luxe", Rating = 4.8 },
            new() { Id = 26, Name = "Dyson V15 Detect", Price = 1100000, ImageUrl = "/images/products/dyson.jpg", Score = 0.80, Reason = "Aspirateur révolutionnaire", Rating = 4.4 },
            new() { Id = 27, Name = "Nespresso Vertuo", Price = 380000, ImageUrl = "/images/products/nespresso.jpg", Score = 0.77, Reason = "Café de qualité", Rating = 4.3 },
            new() { Id = 28, Name = "KitchenAid Mixer", Price = 850000, ImageUrl = "/images/products/kitchenaid.jpg", Score = 0.74, Reason = "Cuisine professionnelle", Rating = 4.6 }
        };
    }

    private List<ProductRecommendation> GenerateNewArrivals()
    {
        return new List<ProductRecommendation>
        {
            new() { Id = 31, Name = "Vision Pro Apple", Price = 6500000, ImageUrl = "/images/products/visionpro.jpg", Score = 0.92, Reason = "Nouveau sur NafaPlace", Rating = 4.2, DiscountPercentage = 15 },
            new() { Id = 32, Name = "Google Pixel 8 Pro", Price = 1350000, ImageUrl = "/images/products/pixel8.jpg", Score = 0.88, Reason = "Dernière innovation", Rating = 4.4, DiscountPercentage = 10 },
            new() { Id = 33, Name = "Framework Laptop", Price = 1800000, ImageUrl = "/images/products/framework.jpg", Score = 0.85, Reason = "Ordinateur modulaire", Rating = 4.1, DiscountPercentage = 0 },
            new() { Id = 34, Name = "Nothing Phone 2", Price = 950000, ImageUrl = "/images/products/nothing.jpg", Score = 0.82, Reason = "Design unique", Rating = 4.0, DiscountPercentage = 20 },
            new() { Id = 35, Name = "Rivian R1T", Price = 95000000, ImageUrl = "/images/products/rivian.jpg", Score = 0.79, Reason = "Pickup électrique", Rating = 4.3, DiscountPercentage = 0 },
            new() { Id = 36, Name = "Humane AI Pin", Price = 1200000, ImageUrl = "/images/products/humane.jpg", Score = 0.76, Reason = "Assistant IA portable", Rating = 3.8, DiscountPercentage = 25 },
            new() { Id = 37, Name = "Rabbit R1", Price = 350000, ImageUrl = "/images/products/rabbit.jpg", Score = 0.73, Reason = "Compagnon IA", Rating = 3.9, DiscountPercentage = 15 },
            new() { Id = 38, Name = "Neuralink Chip", Price = 25000000, ImageUrl = "/images/products/neuralink.jpg", Score = 0.70, Reason = "Interface cerveau-machine", Rating = 4.5, DiscountPercentage = 0 }
        };
    }

    public class ProductRecommendation
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public decimal Price { get; set; }
        public string ImageUrl { get; set; } = string.Empty;
        public double Score { get; set; }
        public string Reason { get; set; } = string.Empty;
        public double Rating { get; set; }
        public int DiscountPercentage { get; set; }
    }
}


