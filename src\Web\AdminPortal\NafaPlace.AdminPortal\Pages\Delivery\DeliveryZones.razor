@page "/delivery/zones"
@using NafaPlace.AdminPortal.Models.Delivery
@using NafaPlace.AdminPortal.Services
@inject IDeliveryService DeliveryService
@inject IJSRuntime JSRuntime
@inject ILogger<DeliveryZones> Logger

<PageTitle>Gestion des Zones de Livraison</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-map-marked-alt text-primary me-2"></i>
                    Zones de Livraison
                </h2>
                <button class="btn btn-primary" @onclick="ShowCreateModal">
                    <i class="fas fa-plus me-2"></i>Nouvelle Zone
                </button>
            </div>

            @if (isLoading)
            {
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                </div>
            }
            else
            {
                <div class="card">
                    <div class="card-header">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h5 class="card-title mb-0">Liste des Zones (@zones.Count)</h5>
                            </div>
                            <div class="col-md-6">
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="Rechercher une zone..." @bind="searchTerm" @oninput="FilterZones">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        @if (filteredZones.Any())
                        {
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Nom</th>
                                            <th>Code</th>
                                            <th>Type</th>
                                            <th>Frais de Base</th>
                                            <th>Livraison Gratuite</th>
                                            <th>Délai Estimé</th>
                                            <th>Statut</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var zone in filteredZones)
                                        {
                                            <tr>
                                                <td>
                                                    <div>
                                                        <strong>@zone.Name</strong>
                                                        @if (!string.IsNullOrEmpty(zone.Description))
                                                        {
                                                            <br><small class="text-muted">@zone.Description</small>
                                                        }
                                                    </div>
                                                </td>
                                                <td><code>@zone.Code</code></td>
                                                <td>
                                                    <span class="badge bg-info">@GetZoneTypeText(zone.Type)</span>
                                                </td>
                                                <td>@zone.BaseDeliveryFee.ToString("N0") GNF</td>
                                                <td>
                                                    @if (zone.FreeDeliveryThreshold.HasValue)
                                                    {
                                                        <span>@zone.FreeDeliveryThreshold.Value.ToString("N0") GNF</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="text-muted">Non défini</span>
                                                    }
                                                </td>
                                                <td>@zone.EstimatedDeliveryDays jour(s)</td>
                                                <td>
                                                    @if (zone.IsActive)
                                                    {
                                                        <span class="badge bg-success">Actif</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="badge bg-danger">Inactif</span>
                                                    }
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <button class="btn btn-outline-primary" @onclick="() => EditZone(zone)" title="Modifier">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button class="btn btn-outline-info" @onclick="() => ViewZoneDetails(zone)" title="Détails">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <button class="btn btn-outline-danger" @onclick="() => DeleteZone(zone)" title="Supprimer">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        }
                        else
                        {
                            <div class="text-center py-5">
                                <i class="fas fa-map-marked-alt fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">Aucune zone trouvée</h5>
                                <p class="text-muted">
                                    @if (string.IsNullOrEmpty(searchTerm))
                                    {
                                        <span>Créez votre première zone de livraison pour commencer.</span>
                                    }
                                    else
                                    {
                                        <span>Aucune zone ne correspond à votre recherche.</span>
                                    }
                                </p>
                            </div>
                        }
                    </div>
                </div>
            }
        </div>
    </div>
</div>

<!-- Modal Création/Édition Zone -->
@if (showModal)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        @(isEditMode ? "Modifier la Zone" : "Nouvelle Zone de Livraison")
                    </h5>
                    <button type="button" class="btn-close" @onclick="CloseModal"></button>
                </div>
                <EditForm Model="currentZone" OnValidSubmit="SaveZone">
                    <DataAnnotationsValidator />
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Nom de la zone *</label>
                                    <InputText class="form-control" @bind-Value="currentZone.Name" placeholder="Ex: Conakry Centre" />
                                    <ValidationMessage For="@(() => currentZone.Name)" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Code de la zone *</label>
                                    <InputText class="form-control" @bind-Value="currentZone.Code" placeholder="Ex: CKY-CTR" />
                                    <ValidationMessage For="@(() => currentZone.Code)" />
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <InputTextArea class="form-control" @bind-Value="currentZone.Description" rows="3" placeholder="Description de la zone..." />
                            <ValidationMessage For="@(() => currentZone.Description)" />
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Type de zone</label>
                                    <InputSelect class="form-select" @bind-Value="currentZone.Type">
                                        <option value="@ZoneType.Country">Pays</option>
                                        <option value="@ZoneType.Region">Région</option>
                                        <option value="@ZoneType.Prefecture">Préfecture</option>
                                        <option value="@ZoneType.City">Ville</option>
                                        <option value="@ZoneType.District">District</option>
                                        <option value="@ZoneType.Custom">Personnalisé</option>
                                    </InputSelect>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Frais de livraison de base (GNF) *</label>
                                    <InputNumber class="form-control" @bind-Value="currentZone.BaseDeliveryFee" />
                                    <ValidationMessage For="@(() => currentZone.BaseDeliveryFee)" />
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Seuil livraison gratuite (GNF)</label>
                                    <InputNumber class="form-control" @bind-Value="currentZone.FreeDeliveryThreshold" />
                                    <ValidationMessage For="@(() => currentZone.FreeDeliveryThreshold)" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Délai estimé (jours) *</label>
                                    <InputNumber class="form-control" @bind-Value="currentZone.EstimatedDeliveryDays" />
                                    <ValidationMessage For="@(() => currentZone.EstimatedDeliveryDays)" />
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Délai maximum (jours) *</label>
                                    <InputNumber class="form-control" @bind-Value="currentZone.MaxDeliveryDays" />
                                    <ValidationMessage For="@(() => currentZone.MaxDeliveryDays)" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check mt-4">
                                        <InputCheckbox class="form-check-input" @bind-Value="currentZone.IsActive" />
                                        <label class="form-check-label">Zone active</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <InputCheckbox class="form-check-input" @bind-Value="currentZone.SameDayDeliveryAvailable" />
                                    <label class="form-check-label">Livraison le jour même disponible</label>
                                </div>
                                @if (currentZone.SameDayDeliveryAvailable)
                                {
                                    <div class="mb-3">
                                        <label class="form-label">Frais livraison jour même (GNF)</label>
                                        <InputNumber class="form-control" @bind-Value="currentZone.SameDayDeliveryFee" />
                                        <ValidationMessage For="@(() => currentZone.SameDayDeliveryFee)" />
                                    </div>
                                }
                            </div>
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <InputCheckbox class="form-check-input" @bind-Value="currentZone.ExpressDeliveryAvailable" />
                                    <label class="form-check-label">Livraison express disponible</label>
                                </div>
                                @if (currentZone.ExpressDeliveryAvailable)
                                {
                                    <div class="mb-3">
                                        <label class="form-label">Frais livraison express (GNF)</label>
                                        <InputNumber class="form-control" @bind-Value="currentZone.ExpressDeliveryFee" />
                                        <ValidationMessage For="@(() => currentZone.ExpressDeliveryFee)" />
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" @onclick="CloseModal">Annuler</button>
                        <button type="submit" class="btn btn-primary" disabled="@isSaving">
                            @if (isSaving)
                            {
                                <span class="spinner-border spinner-border-sm me-2"></span>
                            }
                            @(isEditMode ? "Modifier" : "Créer")
                        </button>
                    </div>
                </EditForm>
            </div>
        </div>
    </div>
}

@code {
    private List<DeliveryZoneDto> zones = new();
    private List<DeliveryZoneDto> filteredZones = new();
    private DeliveryZoneDto currentZone = new();
    private bool isLoading = true;
    private bool showModal = false;
    private bool isEditMode = false;
    private bool isSaving = false;
    private string searchTerm = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        await LoadZones();
    }

    private async Task LoadZones()
    {
        try
        {
            isLoading = true;
            zones = await DeliveryService.GetDeliveryZonesAsync();
            filteredZones = zones;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading delivery zones");
            await JSRuntime.InvokeVoidAsync("alert", "Erreur lors du chargement des zones de livraison");
        }
        finally
        {
            isLoading = false;
        }
    }

    private void FilterZones(ChangeEventArgs e)
    {
        searchTerm = e.Value?.ToString() ?? string.Empty;

        if (string.IsNullOrWhiteSpace(searchTerm))
        {
            filteredZones = zones;
        }
        else
        {
            filteredZones = zones.Where(z =>
                z.Name.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                z.Code.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                (z.Description?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ?? false)
            ).ToList();
        }
    }

    private void ShowCreateModal()
    {
        currentZone = new DeliveryZoneDto
        {
            EstimatedDeliveryDays = 1,
            MaxDeliveryDays = 7,
            Currency = "GNF",
            IsActive = true
        };
        isEditMode = false;
        showModal = true;
    }

    private void EditZone(DeliveryZoneDto zone)
    {
        currentZone = new DeliveryZoneDto
        {
            Id = zone.Id,
            Name = zone.Name,
            Code = zone.Code,
            Description = zone.Description,
            Type = zone.Type,
            BaseDeliveryFee = zone.BaseDeliveryFee,
            FreeDeliveryThreshold = zone.FreeDeliveryThreshold,
            EstimatedDeliveryDays = zone.EstimatedDeliveryDays,
            MaxDeliveryDays = zone.MaxDeliveryDays,
            SameDayDeliveryAvailable = zone.SameDayDeliveryAvailable,
            SameDayDeliveryFee = zone.SameDayDeliveryFee,
            ExpressDeliveryAvailable = zone.ExpressDeliveryAvailable,
            ExpressDeliveryFee = zone.ExpressDeliveryFee,
            Currency = zone.Currency,
            IsActive = zone.IsActive
        };
        isEditMode = true;
        showModal = true;
    }

    private async Task SaveZone()
    {
        try
        {
            isSaving = true;

            if (isEditMode)
            {
                await DeliveryService.UpdateDeliveryZoneAsync(currentZone.Id, currentZone);
                await JSRuntime.InvokeVoidAsync("alert", "Zone de livraison modifiée avec succès");
            }
            else
            {
                await DeliveryService.CreateDeliveryZoneAsync(currentZone);
                await JSRuntime.InvokeVoidAsync("alert", "Zone de livraison créée avec succès");
            }

            CloseModal();
            await LoadZones();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error saving delivery zone");
            await JSRuntime.InvokeVoidAsync("alert", "Erreur lors de la sauvegarde de la zone");
        }
        finally
        {
            isSaving = false;
        }
    }

    private async Task DeleteZone(DeliveryZoneDto zone)
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", $"Êtes-vous sûr de vouloir supprimer la zone '{zone.Name}' ?");
        if (!confirmed) return;

        try
        {
            var success = await DeliveryService.DeleteDeliveryZoneAsync(zone.Id);
            if (success)
            {
                await JSRuntime.InvokeVoidAsync("alert", "Zone supprimée avec succès");
                await LoadZones();
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", "Erreur lors de la suppression de la zone");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error deleting delivery zone {ZoneId}", zone.Id);
            await JSRuntime.InvokeVoidAsync("alert", "Erreur lors de la suppression de la zone");
        }
    }

    private void ViewZoneDetails(DeliveryZoneDto zone)
    {
        // TODO: Implement zone details view
        JSRuntime.InvokeVoidAsync("alert", $"Détails de la zone: {zone.Name}");
    }

    private void CloseModal()
    {
        showModal = false;
        currentZone = new();
        isEditMode = false;
        isSaving = false;
    }

    private string GetZoneTypeText(ZoneType type)
    {
        return type switch
        {
            ZoneType.Country => "Pays",
            ZoneType.Region => "Région",
            ZoneType.Prefecture => "Préfecture",
            ZoneType.City => "Ville",
            ZoneType.District => "District",
            ZoneType.Custom => "Personnalisé",
            _ => "Inconnu"
        };
    }
}
