@using NafaPlace.Web.Services
@using NafaPlace.Web.Models.Catalog
@inject IAdvancedSearchService AdvancedSearchService
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime

<div class="search-autocomplete-container position-relative">
    <div class="input-group">
        <input type="text" 
               class="form-control search-input @CssClass" 
               placeholder="@Placeholder"
               @bind="searchQuery" 
               @bind:event="oninput"
               @onkeydown="OnKeyDown"
               @onfocus="OnFocus"
               @onblur="OnBlur"
               autocomplete="off" />
        
        @if (ShowSearchButton)
        {
            <button class="btn btn-primary search-btn" type="button" @onclick="PerformSearch">
                <i class="bi bi-search"></i>
            </button>
        }
    </div>

    @if (showSuggestions && (suggestions.Any() || popularSearches.Any()))
    {
        <div class="search-suggestions-dropdown position-absolute w-100 bg-white border rounded shadow-lg mt-1" style="z-index: 1050;">
            @if (suggestions.Any())
            {
                <div class="suggestions-section">
                    <div class="suggestions-header px-3 py-2 bg-light border-bottom">
                        <small class="text-muted fw-semibold">Suggestions</small>
                    </div>
                    @foreach (var suggestion in suggestions.Take(MaxSuggestions))
                    {
                        <div class="suggestion-item px-3 py-2 cursor-pointer hover-bg-light d-flex align-items-center"
                             @onclick="() => SelectSuggestion(suggestion.Text)"
                             @onmousedown:preventDefault="true">
                            <i class="bi @GetSuggestionIcon(suggestion.Type) me-2 text-muted"></i>
                            <div class="flex-grow-1">
                                <div class="suggestion-text">@HighlightMatch(suggestion.Text, searchQuery)</div>
                                @if (suggestion.Type != "product")
                                {
                                    <small class="text-muted">@suggestion.ResultCount résultat(s) dans @GetTypeDisplayName(suggestion.Type)</small>
                                }
                            </div>
                        </div>
                    }
                </div>
            }

            @if (popularSearches.Any() && string.IsNullOrEmpty(searchQuery))
            {
                <div class="popular-searches-section">
                    <div class="suggestions-header px-3 py-2 bg-light border-bottom">
                        <small class="text-muted fw-semibold">Recherches populaires</small>
                    </div>
                    @foreach (var popular in popularSearches.Take(5))
                    {
                        <div class="suggestion-item px-3 py-2 cursor-pointer hover-bg-light d-flex align-items-center"
                             @onclick="() => SelectSuggestion(popular)"
                             @onmousedown:preventDefault="true">
                            <i class="bi bi-trending-up me-2 text-success"></i>
                            <span>@popular</span>
                        </div>
                    }
                </div>
            }

            @if (searchHistory.Any() && string.IsNullOrEmpty(searchQuery))
            {
                <div class="search-history-section">
                    <div class="suggestions-header px-3 py-2 bg-light border-bottom">
                        <small class="text-muted fw-semibold">Recherches récentes</small>
                    </div>
                    @foreach (var history in searchHistory.Take(3))
                    {
                        <div class="suggestion-item px-3 py-2 cursor-pointer hover-bg-light d-flex align-items-center"
                             @onclick="() => SelectSuggestion(history)"
                             @onmousedown:preventDefault="true">
                            <i class="bi bi-clock-history me-2 text-muted"></i>
                            <span>@history</span>
                        </div>
                    }
                </div>
            }
        </div>
    }
</div>

<style>
    .search-autocomplete-container .search-input:focus {
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        border-color: #80bdff;
    }

    .search-suggestions-dropdown {
        max-height: 400px;
        overflow-y: auto;
    }

    .suggestion-item:hover,
    .hover-bg-light:hover {
        background-color: #f8f9fa !important;
    }

    .cursor-pointer {
        cursor: pointer;
    }

    .suggestion-text {
        font-weight: 500;
    }

    .suggestion-highlight {
        background-color: #fff3cd;
        font-weight: bold;
    }
</style>

@code {
    [Parameter] public string Placeholder { get; set; } = "Rechercher des produits...";
    [Parameter] public string CssClass { get; set; } = "";
    [Parameter] public bool ShowSearchButton { get; set; } = true;
    [Parameter] public int MaxSuggestions { get; set; } = 8;
    [Parameter] public EventCallback<string> OnSearch { get; set; }

    private List<SearchSuggestion> suggestions = new();
    private List<string> popularSearches = new();
    private List<string> searchHistory = new();
    private bool showSuggestions = false;
    private Timer? debounceTimer;

    protected override async Task OnInitializedAsync()
    {
        await LoadPopularSearches();
        await LoadSearchHistory();
    }

    private async Task OnKeyDown(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            await PerformSearch();
        }
        else if (e.Key == "Escape")
        {
            showSuggestions = false;
            StateHasChanged();
        }
    }

    private async Task OnFocus()
    {
        showSuggestions = true;
        if (string.IsNullOrEmpty(searchQuery))
        {
            await LoadPopularSearches();
            await LoadSearchHistory();
        }
        StateHasChanged();
    }

    private async Task OnBlur()
    {
        // Délai pour permettre le clic sur les suggestions
        await Task.Delay(200);
        showSuggestions = false;
        StateHasChanged();
    }

    private async Task OnSearchQueryChanged()
    {
        debounceTimer?.Dispose();

        if (string.IsNullOrEmpty(searchQuery) || searchQuery.Length < 2)
        {
            suggestions.Clear();
            showSuggestions = true;
            StateHasChanged();
            return;
        }

        debounceTimer = new Timer(async _ =>
        {
            await InvokeAsync(async () =>
            {
                await LoadSuggestions();
                StateHasChanged();
            });
        }, null, 300, Timeout.Infinite);
    }

    // Déclencher la recherche de suggestions quand la valeur change
    private string _searchQuery = "";
    private string searchQuery
    {
        get => _searchQuery;
        set
        {
            if (_searchQuery != value)
            {
                _searchQuery = value;
                _ = OnSearchQueryChanged();
            }
        }
    }

    private async Task LoadSuggestions()
    {
        try
        {
            suggestions = await AdvancedSearchService.GetSearchSuggestionsAsync(searchQuery, MaxSuggestions);
            showSuggestions = true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des suggestions: {ex.Message}");
        }
    }

    private async Task LoadPopularSearches()
    {
        try
        {
            popularSearches = await AdvancedSearchService.GetPopularSearchesAsync(5);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des recherches populaires: {ex.Message}");
        }
    }

    private async Task LoadSearchHistory()
    {
        try
        {
            searchHistory = await AdvancedSearchService.GetUserSearchHistoryAsync(3);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement de l'historique: {ex.Message}");
        }
    }

    private async Task SelectSuggestion(string suggestion)
    {
        searchQuery = suggestion;
        showSuggestions = false;
        await PerformSearch();
    }

    private async Task PerformSearch()
    {
        if (string.IsNullOrWhiteSpace(searchQuery))
            return;

        showSuggestions = false;
        
        if (OnSearch.HasDelegate)
        {
            await OnSearch.InvokeAsync(searchQuery);
        }
        else
        {
            Navigation.NavigateTo($"/catalog?search={Uri.EscapeDataString(searchQuery)}");
        }
    }

    private string GetSuggestionIcon(string type)
    {
        return type switch
        {
            "product" => "bi-box",
            "category" => "bi-grid",
            "brand" => "bi-award",
            _ => "bi-search"
        };
    }

    private string GetTypeDisplayName(string type)
    {
        return type switch
        {
            "product" => "produits",
            "category" => "catégories",
            "brand" => "marques",
            _ => "résultats"
        };
    }

    private MarkupString HighlightMatch(string text, string query)
    {
        if (string.IsNullOrEmpty(query) || string.IsNullOrEmpty(text))
            return new MarkupString(text);

        var index = text.IndexOf(query, StringComparison.OrdinalIgnoreCase);
        if (index == -1)
            return new MarkupString(text);

        var before = text.Substring(0, index);
        var match = text.Substring(index, query.Length);
        var after = text.Substring(index + query.Length);

        return new MarkupString($"{before}<span class=\"suggestion-highlight\">{match}</span>{after}");
    }

    public void Dispose()
    {
        debounceTimer?.Dispose();
    }
}
