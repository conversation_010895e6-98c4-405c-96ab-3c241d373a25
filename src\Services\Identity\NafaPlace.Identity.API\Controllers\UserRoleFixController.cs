using Microsoft.AspNetCore.Mvc;
using NafaPlace.Identity.API.Services;

namespace NafaPlace.Identity.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class UserRoleFixController : ControllerBase
{
    private readonly IUserRoleFixService _userRoleFixService;
    private readonly ILogger<UserRoleFixController> _logger;

    public UserRoleFixController(IUserRoleFixService userRoleFixService, ILogger<UserRoleFixController> logger)
    {
        _userRoleFixService = userRoleFixService;
        _logger = logger;
    }

    /// <summary>
    /// Corrige automatiquement les rôles des utilisateurs qui n'en ont pas
    /// </summary>
    [HttpPost("fix-roles")]
    public async Task<IActionResult> FixUserRoles()
    {
        try
        {
            _logger.LogInformation("Début de la correction des rôles utilisateurs");
            
            var result = await _userRoleFixService.FixUserRolesAsync();
            
            _logger.LogInformation("Correction des rôles terminée");
            
            return Ok(new { 
                success = true, 
                message = "Correction des rôles terminée avec succès", 
                details = result 
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la correction des rôles");
            return StatusCode(500, new { 
                success = false, 
                message = "Erreur lors de la correction des rôles", 
                error = ex.Message 
            });
        }
    }

    /// <summary>
    /// Récupère la liste de tous les utilisateurs avec leurs rôles
    /// </summary>
    [HttpGet("users-with-roles")]
    public async Task<IActionResult> GetUsersWithRoles()
    {
        try
        {
            var users = await _userRoleFixService.GetUsersWithRolesAsync();
            
            return Ok(new { 
                success = true, 
                data = users,
                count = users.Count
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des utilisateurs avec rôles");
            return StatusCode(500, new { 
                success = false, 
                message = "Erreur lors de la récupération des utilisateurs", 
                error = ex.Message 
            });
        }
    }

    /// <summary>
    /// Endpoint de test pour vérifier que le service fonctionne
    /// </summary>
    [HttpGet("health")]
    public IActionResult Health()
    {
        return Ok(new { 
            status = "healthy", 
            service = "UserRoleFixService",
            timestamp = DateTime.UtcNow
        });
    }
}
