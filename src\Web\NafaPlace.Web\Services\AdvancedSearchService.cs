using NafaPlace.Web.Models.Catalog;
using System.Net.Http.Json;
using System.Text.Json;

namespace NafaPlace.Web.Services;

public class AdvancedSearchService : IAdvancedSearchService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<AdvancedSearchService> _logger;

    public AdvancedSearchService(HttpClient httpClient, ILogger<AdvancedSearchService> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
    }

    public async Task<ProductSearchResult> SearchProductsAdvancedAsync(ProductSearchRequest request)
    {
        try
        {
            var searchDto = MapToSearchDto(request);
            var response = await _httpClient.PostAsJsonAsync("/api/v1/search/advanced", searchDto);
            
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<ProductSearchResult>();
                return result ?? new ProductSearchResult();
            }
            
            _logger.LogWarning("Échec de la recherche avancée: {StatusCode}", response.StatusCode);
            return new ProductSearchResult();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la recherche avancée");
            return new ProductSearchResult();
        }
    }

    public async Task<List<SearchSuggestion>> GetSearchSuggestionsAsync(string query, int maxSuggestions = 10)
    {
        try
        {
            if (string.IsNullOrEmpty(query) || query.Length < 2)
                return new List<SearchSuggestion>();

            var response = await _httpClient.GetFromJsonAsync<List<SearchSuggestion>>(
                $"/api/v1/search/suggestions?query={Uri.EscapeDataString(query)}&maxSuggestions={maxSuggestions}");
            
            return response ?? new List<SearchSuggestion>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des suggestions");
            return new List<SearchSuggestion>();
        }
    }

    public async Task<List<string>> GetAutocompleteSuggestionsAsync(string query, int maxSuggestions = 5)
    {
        try
        {
            if (string.IsNullOrEmpty(query) || query.Length < 2)
                return new List<string>();

            var response = await _httpClient.GetFromJsonAsync<List<string>>(
                $"/api/v1/search/autocomplete?query={Uri.EscapeDataString(query)}&maxSuggestions={maxSuggestions}");
            
            return response ?? new List<string>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'autocomplétion");
            return new List<string>();
        }
    }

    public async Task<List<ProductDto>> FindSimilarProductsAsync(int productId, int maxResults = 10)
    {
        try
        {
            var response = await _httpClient.GetFromJsonAsync<List<ProductDto>>(
                $"/api/v1/search/similar/{productId}?maxResults={maxResults}");
            
            return response ?? new List<ProductDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la recherche de produits similaires");
            return new List<ProductDto>();
        }
    }

    public async Task<List<ProductDto>> SearchByImageAsync(string imageUrl, int maxResults = 10)
    {
        try
        {
            var request = new ImageSearchRequest
            {
                ImageUrl = imageUrl,
                MaxResults = maxResults
            };

            var response = await _httpClient.PostAsJsonAsync("/api/v1/search/by-image", request);
            
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<List<ProductDto>>();
                return result ?? new List<ProductDto>();
            }
            
            return new List<ProductDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la recherche par image");
            return new List<ProductDto>();
        }
    }

    public async Task<List<SearchFacet>> GetSearchFacetsAsync(ProductSearchRequest request)
    {
        try
        {
            var queryParams = BuildQueryParams(request);
            var response = await _httpClient.GetFromJsonAsync<List<SearchFacet>>(
                $"/api/v1/search/facets?{queryParams}");
            
            return response ?? new List<SearchFacet>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des facettes");
            return new List<SearchFacet>();
        }
    }

    public async Task<Dictionary<string, List<string>>> GetAvailableFiltersAsync()
    {
        try
        {
            var response = await _httpClient.GetFromJsonAsync<Dictionary<string, List<string>>>(
                "/api/v1/search/filters");
            
            return response ?? new Dictionary<string, List<string>>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des filtres");
            return new Dictionary<string, List<string>>();
        }
    }

    public async Task<List<string>> GetPopularSearchesAsync(int count = 10)
    {
        try
        {
            var response = await _httpClient.GetFromJsonAsync<List<string>>(
                $"/api/v1/products/search/popular?count={count}");
            return response ?? new List<string>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des recherches populaires");
            return new List<string>();
        }
    }

    public async Task<List<string>> GetUserSearchHistoryAsync(int count = 10)
    {
        try
        {
            var response = await _httpClient.GetFromJsonAsync<List<string>>(
                $"/api/v1/products/search/history?count={count}");
            return response ?? new List<string>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de l'historique");
            return new List<string>();
        }
    }

    public async Task<List<string>> GetSearchTrendsAsync(int days = 30)
    {
        try
        {
            var response = await _httpClient.GetFromJsonAsync<List<string>>(
                $"/api/v1/search/trends?days={days}");
            
            return response ?? new List<string>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des tendances");
            return new List<string>();
        }
    }

    private object MapToSearchDto(ProductSearchRequest request)
    {
        return new
        {
            searchTerm = request.SearchTerm,
            categoryId = request.CategoryId,
            categoryIds = request.CategoryIds,
            minPrice = request.MinPrice,
            maxPrice = request.MaxPrice,
            brand = request.Brand,
            brands = request.Brands,
            inStockOnly = request.InStockOnly,
            sellerId = request.SellerId,
            sellerIds = request.SellerIds,
            sortBy = request.SortBy,
            sortDescending = request.SortDescending,
            page = request.Page,
            pageSize = request.PageSize,
            minRating = request.MinRating,
            maxRating = request.MaxRating,
            createdAfter = request.CreatedAfter,
            createdBefore = request.CreatedBefore,
            tags = request.Tags,
            attributes = request.Attributes,
            color = request.Color,
            size = request.Size,
            material = request.Material,
            hasDiscount = request.HasDiscount,
            minDiscountPercentage = request.MinDiscountPercentage,
            isNewArrival = request.IsNewArrival,
            hasFreeShipping = request.HasFreeShipping,
            useFullTextSearch = request.UseFullTextSearch,
            includeSimilarProducts = request.IncludeSimilarProducts,
            searchMode = request.SearchMode,
            maxSuggestions = request.MaxSuggestions
        };
    }

    private string BuildQueryParams(ProductSearchRequest request)
    {
        var queryParams = new List<string>();

        if (!string.IsNullOrEmpty(request.SearchTerm))
            queryParams.Add($"searchTerm={Uri.EscapeDataString(request.SearchTerm)}");
        
        if (request.CategoryId.HasValue)
            queryParams.Add($"categoryId={request.CategoryId}");
        
        if (request.CategoryIds?.Any() == true)
        {
            foreach (var id in request.CategoryIds)
                queryParams.Add($"categoryIds={id}");
        }

        if (request.MinPrice.HasValue)
            queryParams.Add($"minPrice={request.MinPrice}");
        
        if (request.MaxPrice.HasValue)
            queryParams.Add($"maxPrice={request.MaxPrice}");

        if (!string.IsNullOrEmpty(request.Brand))
            queryParams.Add($"brand={Uri.EscapeDataString(request.Brand)}");

        if (request.InStockOnly)
            queryParams.Add("inStockOnly=true");

        queryParams.Add($"page={request.Page}");
        queryParams.Add($"pageSize={request.PageSize}");

        return string.Join("&", queryParams);
    }
}
