services:
  # API Gateway
  api-gateway:
    build:
      context: .
      dockerfile: src/ApiGateways/Web/NafaPlace.ApiGateway/Dockerfile
    image: lamyas92/nafaplace-api-gateway:latest
    container_name: nafaplace-api-gateway
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
    ports:
      - "5000:80"
    depends_on:
      - identity-api
      - catalog-api
      - cart-api
      - order-api
      - payment-api
      - reviews-api
      - notifications-api
      - wishlist-api
      - inventory-api
      - coupon-api
      - delivery-api
    networks:
      - nafaplace-network

  # Service de catalogue
  catalog-api:
    build:
      context: .
      dockerfile: src/Services/Catalog/Dockerfile
    image: lamyas92/nafaplace-catalog-api:latest
    container_name: nafaplace-catalog-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Host=catalog-db;Port=5432;Database=NafaPlace.Catalog;Username=postgres;Password=*****************
      - ConnectionStrings__AzureStorage=DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;BlobEndpoint=http://azurite:10000/devstoreaccount1;QueueEndpoint=http://azurite:10001/devstoreaccount1;TableEndpoint=http://azurite:10002/devstoreaccount1;
      - IdentityUrl=http://identity-api
      - AllowedOrigins__0=http://localhost:59825
      - AllowedOrigins__1=https://localhost:59824
      - AllowedOrigins__2=http://localhost:59826
      - AllowedOrigins__3=http://localhost:59827
      - AllowedOrigins__4=http://web
      - AllowedOrigins__5=http://seller-portal
      - AllowedOrigins__6=http://admin-portal
    ports:
      - "5243:80"
    depends_on:
      - catalog-db
      - identity-api
      - azurite
    networks:
      - nafaplace-network

  # Base de données du catalogue (PostgreSQL)
  catalog-db:
    image: postgres:16
    container_name: nafaplace-catalog-db
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=*****************
      - POSTGRES_DB=NafaPlace.Catalog
    ports:
      - "5432:5432"
    volumes:
      - catalog-data:/var/lib/postgresql/data
    networks:
      - nafaplace-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Service d'identité
  identity-api:
    build:
      context: .
      dockerfile: src/Services/Identity/Dockerfile
    image: lamyas92/nafaplace-identity-api:latest
    container_name: nafaplace-identity-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Host=identity-db;Database=NafaPlace.Identity;Username=postgres;Password=*****************;
    ports:
      - "5155:80"
    depends_on:
      - identity-db
    networks:
      - nafaplace-network

  # Base de données d'identité (PostgreSQL)
  identity-db:
    image: postgres:16
    container_name: nafaplace-identity-db
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=*****************
      - POSTGRES_DB=NafaPlace.Identity
    ports:
      - "5433:5432"
    volumes:
      - identity-data:/var/lib/postgresql/data
    networks:
      - nafaplace-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Service de Panier
  cart-api:
    build:
      context: .
      dockerfile: src/Services/Cart/NafaPlace.Cart.API/Dockerfile
    image: lamyas92/nafaplace-cart-api:latest
    container_name: nafaplace-cart-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__Redis=redis:6379
      - CatalogApiUrl=http://catalog-api
      - IdentityUrl=http://identity-api
    ports:
      - "5003:80"
    depends_on:
      - identity-api
      - redis
    networks:
      - nafaplace-network

  # Service de Commande
  order-api:
    build:
      context: .
      dockerfile: src/Services/Order/NafaPlace.Order.API/Dockerfile
    image: lamyas92/nafaplace-order-api:latest
    container_name: nafaplace-order-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Host=order-db;Database=NafaPlace.Order;Username=postgres;Password=*****************;
      - ServiceUrls__CartApi=http://cart-api
      - IdentityUrl=http://identity-api
    ports:
      - "5004:80"
    depends_on:
      - identity-api
      - order-db
    networks:
      - nafaplace-network

  # Base de données de commande (PostgreSQL)
  order-db:
    image: postgres:16
    container_name: nafaplace-order-db
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=*****************
      - POSTGRES_DB=NafaPlace.Order
    ports:
      - "5434:5432"
    volumes:
      - order-data:/var/lib/postgresql/data
    networks:
      - nafaplace-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Service de Paiement
  payment-api:
    build:
      context: .
      dockerfile: src/Services/Payment/NafaPlace.Payment.API/Dockerfile
    image: lamyas92/nafaplace-payment-api:latest
    container_name: nafaplace-payment-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - StripeSettings__PublishableKey=pk_test_51P9u3j04AJZAFmCsex3b11RJU4e2Z1NMbFcM1WWsDb3u5PAhEY1Ej1EX6bRMECbdYyQU92Qx49B7aMkAkoH6vNGC00rFKj6h67
      - StripeSettings__SecretKey=sk_test_51P9u3j04AJZAFmCsztfr3PLqzvosGKRrWEFIsL8vXM3Fb7kony2DHgR5BJQT24gUfTYJ8ClrIlxgtwhjtk0SoPWd00bWacMsb3
      - StripeSettings__WebhookSecret=whsec_test_webhook_secret
      - StripeSettings__DefaultCurrency=gnf
      - IdentityUrl=http://identity-api
    ports:
      - "5005:80"
    depends_on:
      - identity-api
    networks:
      - nafaplace-network

  # Service d'Avis et Évaluations
  reviews-api:
    build:
      context: .
      dockerfile: src/Services/Reviews/NafaPlace.Reviews.API/Dockerfile
    image: lamyas92/nafaplace-reviews-api:latest
    container_name: nafaplace-reviews-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Host=reviews-db;Database=NafaPlace.Reviews;Username=postgres;Password=*****************;
      - JwtSettings__SecretKey=YourSuperSecretKeyThatIsAtLeast32CharactersLong!
      - JwtSettings__Issuer=NafaPlace
      - JwtSettings__Audience=NafaPlaceApi
      - IdentityUrl=http://identity-api
    ports:
      - "5006:80"
    depends_on:
      - identity-api
      - reviews-db
    networks:
      - nafaplace-network

  # Base de données des avis (PostgreSQL)
  reviews-db:
    image: postgres:16
    container_name: nafaplace-reviews-db
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=*****************
      - POSTGRES_DB=NafaPlace.Reviews
    ports:
      - "5435:5432"
    volumes:
      - reviews-data:/var/lib/postgresql/data
    networks:
      - nafaplace-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Service de Recommandations IA (temporairement désactivé pour correction)
  # recommendation-api:
  #   build:
  #     context: .
  #     dockerfile: src/Services/Recommendation/NafaPlace.Recommendation.API/Dockerfile
  #   image: lamyas92/nafaplace-recommendation-api:latest
  #   container_name: nafaplace-recommendation-api
  #   environment:
  #     - ASPNETCORE_ENVIRONMENT=Development
  #     - ASPNETCORE_URLS=http://+:80
  #     - ConnectionStrings__DefaultConnection=Host=recommendation-db;Database=NafaPlace.Recommendation;Username=postgres;Password=*****************;
  #     - JwtSettings__SecretKey=YourSuperSecretKeyThatIsAtLeast32CharactersLong!
  #     - JwtSettings__Issuer=NafaPlace
  #     - JwtSettings__Audience=NafaPlaceApi
  #     - IdentityUrl=http://identity-api
  #     - CatalogUrl=http://catalog-api
  #     - OrderUrl=http://order-api
  #   ports:
  #     - "5015:80"
  #   depends_on:
  #     - identity-api
  #     - catalog-api
  #     - recommendation-db
  #   networks:
  #     - nafaplace-network

  # Base de données des recommandations (PostgreSQL) - temporairement désactivée
  # recommendation-db:
  #   image: postgres:16
  #   container_name: nafaplace-recommendation-db
  #   environment:
  #     - POSTGRES_USER=postgres
  #     - POSTGRES_PASSWORD=*****************
  #     - POSTGRES_DB=NafaPlace.Recommendation
  #   ports:
  #     - "5440:5432"
  #   volumes:
  #     - recommendation-data:/var/lib/postgresql/data
  #   networks:
  #     - nafaplace-network
  #   healthcheck:
  #     test: ["CMD-SHELL", "pg_isready -U postgres"]
  #     interval: 10s
  #     timeout: 5s
  #     retries: 5

  # Service de Notifications
  notifications-api:
    build:
      context: .
      dockerfile: src/Services/Notifications/NafaPlace.Notifications.API/Dockerfile
    image: lamyas92/nafaplace-notifications-api:latest
    container_name: nafaplace-notifications-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Host=notifications-db;Database=NafaPlace.Notifications;Username=postgres;Password=*****************;
      - JwtSettings__SecretKey=YourSuperSecretKeyThatIsAtLeast32CharactersLong!
      - JwtSettings__Issuer=NafaPlace
      - JwtSettings__Audience=NafaPlace-Users
      - IdentityUrl=http://identity-api
    ports:
      - "5007:80"
    depends_on:
      - identity-api
      - notifications-db
    networks:
      - nafaplace-network

  # Base de données des notifications (PostgreSQL)
  notifications-db:
    image: postgres:16
    container_name: nafaplace-notifications-db
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=*****************
      - POSTGRES_DB=NafaPlace.Notifications
    ports:
      - "5436:5432"
    volumes:
      - notifications-data:/var/lib/postgresql/data
    networks:
      - nafaplace-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Service de Wishlist
  wishlist-api:
    build:
      context: .
      dockerfile: src/Services/Wishlist/NafaPlace.Wishlist.API/Dockerfile
    image: lamyas92/nafaplace-wishlist-api:latest
    container_name: nafaplace-wishlist-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Host=wishlist-db;Database=NafaPlace.Wishlist;Username=postgres;Password=*****************;
      - IdentityUrl=http://identity-api
    ports:
      - "5008:80"
    depends_on:
      - identity-api
      - wishlist-db
    networks:
      - nafaplace-network

  # Base de données des wishlists (PostgreSQL)
  wishlist-db:
    image: postgres:16
    container_name: nafaplace-wishlist-db
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=*****************
      - POSTGRES_DB=NafaPlace.Wishlist
    ports:
      - "5437:5432"
    volumes:
      - wishlist-data:/var/lib/postgresql/data
    networks:
      - nafaplace-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Service d'Inventaire
  inventory-api:
    build:
      context: .
      dockerfile: src/Services/Inventory/NafaPlace.Inventory.API/Dockerfile
    image: lamyas92/nafaplace-inventory-api:latest
    container_name: nafaplace-inventory-api
    ports:
      - "5244:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Host=inventory-db;Database=NafaPlace.Inventory;Username=postgres;Password=*****************;
      - ApiSettings__NotificationApiUrl=http://notifications-api
      - ApiSettings__CatalogApiUrl=http://catalog-api
      - IdentityUrl=http://identity-api
    depends_on:
      - identity-api
      - catalog-api
      - notifications-api
      - inventory-db
    networks:
      - nafaplace-network

  # Service de Coupons
  coupon-api:
    build:
      context: .
      dockerfile: src/Services/Coupon/NafaPlace.Coupon.API/Dockerfile
    image: lamyas92/nafaplace-coupon-api:latest
    container_name: nafaplace-coupon-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Host=coupon-db;Database=NafaPlace.Coupon;Username=postgres;Password=*****************;
      - IdentityUrl=http://identity-api
      - JwtSettings__Secret=NafaPlaceSecretKey2025ForProductionEnvironment
      - JwtSettings__Issuer=NafaPlace
      - JwtSettings__Audience=NafaPlaceApi
      - JwtSettings__ExpiryInMinutes=60
    ports:
      - "5009:80"
    depends_on:
      - identity-api
      - coupon-db
    networks:
      - nafaplace-network

  # Service de Livraison
  delivery-api:
    build:
      context: .
      dockerfile: src/Services/Delivery/NafaPlace.Delivery.API/Dockerfile
    image: lamyas92/nafaplace-delivery-api:latest
    container_name: nafaplace-delivery-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Host=delivery-db;Database=NafaPlace.Delivery;Username=postgres;Password=*****************;
      - IdentityUrl=http://identity-api
    ports:
      - "5010:80"
    depends_on:
      - identity-api
      - delivery-db
    networks:
      - nafaplace-network

  # Base de données de l'inventaire (PostgreSQL)
  inventory-db:
    image: postgres:16
    container_name: nafaplace-inventory-db
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=*****************
      - POSTGRES_DB=NafaPlace.Inventory
    ports:
      - "5438:5432"
    volumes:
      - inventory-data:/var/lib/postgresql/data
    networks:
      - nafaplace-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Base de données des coupons (PostgreSQL)
  coupon-db:
    image: postgres:16
    container_name: nafaplace-coupon-db
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=*****************
      - POSTGRES_DB=NafaPlace.Coupon
    ports:
      - "5439:5432"
    volumes:
      - coupon-data:/var/lib/postgresql/data
    networks:
      - nafaplace-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Base de données de livraison (PostgreSQL)
  delivery-db:
    image: postgres:16
    container_name: nafaplace-delivery-db
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=*****************
      - POSTGRES_DB=NafaPlace.Delivery
    ports:
      - "5440:5432"
    volumes:
      - delivery-data:/var/lib/postgresql/data
    networks:
      - nafaplace-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Application Web
  web:
    build:
      context: .
      dockerfile: src/Web/Dockerfile
    image: lamyas92/nafaplace-web:latest
    container_name: nafaplace-web
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80;https://+:443
      - ApiEndpoints__IdentityApi=http://api-gateway/api/identity
      - ApiEndpoints__CatalogApi=http://api-gateway/api/catalog
      - ApiEndpoints__CartApi=http://api-gateway/api/cart
      - ApiEndpoints__OrderApi=http://api-gateway/api/orders
      - ApiEndpoints__PaymentApi=http://api-gateway/api/payments
      - ApiEndpoints__ReviewsApi=http://api-gateway/api/reviews
      - ApiEndpoints__NotificationsApi=http://api-gateway/api/notifications
      - ApiEndpoints__WishlistApi=http://api-gateway/api/wishlist
      - ASPNETCORE_HTTPS_PORT=59824
      - ASPNETCORE_Kestrel__Certificates__Default__Password=*****************
      - ASPNETCORE_Kestrel__Certificates__Default__Path=/https/aspnetapp.pfx
    ports:
      - "8080:80"
    depends_on:
      - catalog-api
      - identity-api
    networks:
      - nafaplace-network
    volumes:
      - ${APPDATA}/Microsoft/UserSecrets:/root/.microsoft/usersecrets:ro
      - ${APPDATA}/ASP.NET/Https:/https:ro

  # Portail Vendeur
  seller-portal:
    build:
      context: .
      dockerfile: Dockerfile.seller
    image: lamyas92/nafaplace-seller-portal:latest
    container_name: nafaplace-seller-portal
    environment:
      - CatalogApi=http://api-gateway/api/catalog
      - IdentityApi=http://api-gateway/api/identity
      - ApiSettings__BaseUrl=http://api-gateway
    ports:
      - "8082:80"
    depends_on:
      - api-gateway
      - catalog-api
      - identity-api
    networks:
      - nafaplace-network

  # Portail Administrateur
  admin-portal:
    build:
      context: .
      dockerfile: Dockerfile.admin
    image: lamyas92/nafaplace-admin-portal:latest
    container_name: nafaplace-admin-portal
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8080
      - CatalogApi=http://api-gateway/api/catalog
      - IdentityApi=http://api-gateway/api/identity
    ports:
      - "8081:80"
    depends_on:
      - catalog-api
      - identity-api
    networks:
      - nafaplace-network

  # Stockage local pour le développement
  azurite:
    image: mcr.microsoft.com/azure-storage/azurite
    container_name: nafaplace-azurite
    ports:
      - "10000:10000"
      - "10001:10001"
      - "10002:10002"
    volumes:
      - azurite-data:/data
    networks:
      - nafaplace-network

  # Interface d'administration PostgreSQL
  pgadmin:
    image: dpage/pgadmin4
    container_name: nafaplace-pgadmin
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=*****************
    ports:
      - "5050:80"
    depends_on:
      - catalog-db
      - identity-db
    networks:
      - nafaplace-network
    volumes:
      - pgadmin-data:/var/lib/pgadmin

  redis:
    image: redis:alpine
    container_name: nafaplace-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - nafaplace-network

volumes:
  catalog-data:
  identity-data:
  azurite-data:
  pgadmin-data:
  redis-data:
  order-data:
  reviews-data:
  notifications-data:
  wishlist-data:
  inventory-data:
  coupon-data:
  delivery-data:
  recommendation-data:

networks:
  nafaplace-network:
    name: nafaplace-network
    driver: bridge
