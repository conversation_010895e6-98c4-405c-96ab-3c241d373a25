using System;
using System.Collections.Generic;
using NafaPlace.Catalog.Application.DTOs.Product;

namespace NafaPlace.Catalog.Application.DTOs.Category
{
    public class CategoryDto
    {
        public int Id { get; set; }
        public required string Name { get; set; }
        public required string Description { get; set; }
        public int? ParentCategoryId { get; set; }
        public string? IconUrl { get; set; }
        public required string ImageUrl { get; set; }
        public bool IsActive { get; set; }
        public CategoryDto? ParentCategory { get; set; }
        public List<CategoryDto> SubCategories { get; set; } = new();
        public List<ProductDto> Products { get; set; } = new();
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public class CreateCategoryDto
    {
        public required string Name { get; set; }
        public required string Description { get; set; }
        public int? ParentId { get; set; }
    }

    public class UpdateCategoryDto
    {
        public required string Name { get; set; }
        public required string Description { get; set; }
    }
}
