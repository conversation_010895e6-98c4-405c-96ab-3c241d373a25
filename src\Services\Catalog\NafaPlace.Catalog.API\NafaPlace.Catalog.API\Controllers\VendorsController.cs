using Microsoft.AspNetCore.Mvc;
using NafaPlace.Catalog.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace NafaPlace.Catalog.API.Controllers
{
    [ApiController]
    [Route("api/v1/vendors")]
    public class VendorsController : ControllerBase
    {
        private readonly CatalogDbContext _context;
        private readonly ILogger<VendorsController> _logger;

        public VendorsController(CatalogDbContext context, ILogger<VendorsController> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// Récupère la liste de tous les vendeurs
        /// </summary>
        /// <returns>Liste des vendeurs</returns>
        [HttpGet]
        public async Task<IActionResult> GetAllVendors()
        {
            try
            {
                var sellers = await _context.Sellers
                    .Select(s => new
                    {
                        Id = s.Id,
                        Name = s.Name ?? "Vendeur sans nom",
                        Email = s.Email ?? "",
                        PhoneNumber = s.PhoneNumber ?? "",
                        Address = s.Address ?? "",
                        IsActive = s.IsActive,
                        IsVerified = true
                    })
                    .ToListAsync();

                return Ok(sellers);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération des vendeurs");
                return StatusCode(500, new { message = "Erreur lors de la récupération des vendeurs", error = ex.Message });
            }
        }
    }
}
