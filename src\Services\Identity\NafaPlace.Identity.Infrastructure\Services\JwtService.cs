using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using NafaPlace.Identity.Application.Common.Interfaces;
using NafaPlace.Identity.Domain.Models;

namespace NafaPlace.Identity.Infrastructure.Services;

public class JwtService : IJwtService
{
    private readonly IConfiguration _configuration;
    private readonly string _secret;
    private readonly string _issuer;
    private readonly string _audience;
    private readonly int _tokenLifetimeMinutes;

    public JwtService(IConfiguration configuration)
    {
        _configuration = configuration;
        _secret = _configuration["JwtSettings:Secret"] ?? throw new ArgumentNullException(nameof(_configuration), "JWT secret is not configured");
        _issuer = _configuration["JwtSettings:Issuer"] ?? throw new ArgumentNullException(nameof(_configuration), "JWT issuer is not configured");
        _audience = _configuration["JwtSettings:Audience"] ?? throw new ArgumentNullException(nameof(_configuration), "JWT audience is not configured");
        _tokenLifetimeMinutes = int.Parse(_configuration["JwtSettings:TokenLifetimeMinutes"] ?? "60");
    }

    public string GenerateAccessToken(int userId, string username, IEnumerable<string> roles)
    {
        return GenerateAccessToken(userId, username, roles, null);
    }

    public string GenerateAccessToken(int userId, string username, IEnumerable<string> roles, int? sellerId)
    {
        var securityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_secret));
        var credentials = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha256);

        var claims = new List<Claim>
        {
            new(JwtRegisteredClaimNames.Sub, userId.ToString()),
            new(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
            new(ClaimTypes.Name, username)
        };

        claims.AddRange(roles.Select(role => new Claim(ClaimTypes.Role, role)));

        // Ajouter le SellerId si l'utilisateur est un vendeur
        if (sellerId.HasValue)
        {
            claims.Add(new Claim("SellerId", sellerId.Value.ToString()));
        }

        var token = new JwtSecurityToken(
            issuer: _issuer,
            audience: _audience,
            claims: claims,
            expires: DateTime.UtcNow.AddMinutes(_tokenLifetimeMinutes),
            signingCredentials: credentials
        );

        return new JwtSecurityTokenHandler().WriteToken(token);
    }

    public string GenerateRefreshToken()
    {
        var randomNumber = new byte[64];
        using var rng = RandomNumberGenerator.Create();
        rng.GetBytes(randomNumber);
        return Convert.ToBase64String(randomNumber);
    }

    public ClaimsPrincipal GetPrincipalFromExpiredToken(string token)
    {
        var tokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = false, // Ne pas valider l'expiration
            ValidateIssuerSigningKey = true,
            ValidIssuer = _issuer,
            ValidAudience = _audience,
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_secret))
        };

        var tokenHandler = new JwtSecurityTokenHandler();
        var principal = tokenHandler.ValidateToken(token, tokenValidationParameters, out var securityToken);

        if (securityToken is not JwtSecurityToken jwtSecurityToken ||
            !jwtSecurityToken.Header.Alg.Equals(SecurityAlgorithms.HmacSha256, StringComparison.InvariantCultureIgnoreCase))
        {
            throw new SecurityTokenException("Invalid token");
        }

        return principal;
    }

    public DateTime GetTokenExpirationTime(string token)
    {
        var handler = new JwtSecurityTokenHandler();
        var jwtToken = handler.ReadJwtToken(token);
        var expiryClaim = jwtToken.Claims.FirstOrDefault(c => c.Type == JwtRegisteredClaimNames.Exp);

        if (expiryClaim == null)
        {
            throw new SecurityTokenException("Token does not have an expiry claim");
        }

        var expirySeconds = long.Parse(expiryClaim.Value);
        var expiryDate = DateTimeOffset.FromUnixTimeSeconds(expirySeconds).UtcDateTime;

        return expiryDate;
    }

    public IEnumerable<Claim> GetClaimsFromToken(string token)
    {
        var handler = new JwtSecurityTokenHandler();
        var jwtToken = handler.ReadJwtToken(token);
        return jwtToken.Claims;
    }
}
