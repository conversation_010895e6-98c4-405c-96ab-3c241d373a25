// Configuration SignalR pour le chat
window.chatSignalR = {
    connection: null,
    isConnected: false,
    
    // Initialiser la connexion SignalR
    async init(chatApiUrl, accessToken) {
        try {
            console.log('🔌 Initialisation de la connexion SignalR Chat...');
            
            // Créer la connexion
            this.connection = new signalR.HubConnectionBuilder()
                .withUrl(`${chatApiUrl}/chathub`, {
                    accessTokenFactory: () => accessToken,
                    transport: signalR.HttpTransportType.WebSockets | signalR.HttpTransportType.LongPolling
                })
                .withAutomaticReconnect([0, 2000, 10000, 30000])
                .configureLogging(signalR.LogLevel.Information)
                .build();

            // Gestionnaires d'événements de connexion
            this.connection.onreconnecting((error) => {
                console.warn('🔄 Reconnexion SignalR Chat en cours...', error);
                this.isConnected = false;
            });

            this.connection.onreconnected((connectionId) => {
                console.log('✅ Reconnexion SignalR Chat réussie:', connectionId);
                this.isConnected = true;
            });

            this.connection.onclose((error) => {
                console.error('❌ Connexion SignalR Chat fermée:', error);
                this.isConnected = false;
            });

            // Gestionnaires de messages
            this.setupMessageHandlers();

            // Démarrer la connexion
            await this.connection.start();
            this.isConnected = true;
            console.log('✅ Connexion SignalR Chat établie');
            
            return true;
        } catch (error) {
            console.error('❌ Erreur lors de l\'initialisation SignalR Chat:', error);
            this.isConnected = false;
            return false;
        }
    },

    // Configurer les gestionnaires de messages
    setupMessageHandlers() {
        if (!this.connection) return;

        // Réception d'un nouveau message
        this.connection.on('ReceiveMessage', (message) => {
            console.log('📨 Nouveau message reçu:', message);

            // Déclencher un événement personnalisé
            window.dispatchEvent(new CustomEvent('chatMessageReceived', {
                detail: message
            }));

            // Notifier Blazor si disponible
            if (window.blazorChatWidget) {
                window.blazorChatWidget.invokeMethodAsync('OnMessageReceived', message);
            }
        });

        // Indicateur de frappe
        this.connection.on('TypingIndicator', (data) => {
            console.log('⌨️ Indicateur de frappe:', data);
            
            window.dispatchEvent(new CustomEvent('chatTypingIndicator', {
                detail: data
            }));
        });

        // Statut de conversation mis à jour
        this.connection.on('ConversationStatusUpdated', (data) => {
            console.log('📋 Statut de conversation mis à jour:', data);
            
            window.dispatchEvent(new CustomEvent('chatConversationStatusUpdated', {
                detail: data
            }));
        });

        // Agent assigné
        this.connection.on('AgentAssigned', (data) => {
            console.log('👤 Agent assigné:', data);

            window.dispatchEvent(new CustomEvent('chatAgentAssigned', {
                detail: data
            }));

            // Notifier Blazor si disponible
            if (window.blazorChatWidget) {
                window.blazorChatWidget.invokeMethodAsync('OnAgentAssigned', data);
            }
        });

        // Notification de chat
        this.connection.on('ChatNotification', (notification) => {
            console.log('🔔 Notification de chat:', notification);
            
            window.dispatchEvent(new CustomEvent('chatNotification', {
                detail: notification
            }));
        });
    },

    // Rejoindre un groupe de conversation
    async joinGroup(conversationId) {
        if (!this.isConnected || !this.connection) {
            console.warn('⚠️ Connexion SignalR Chat non disponible pour rejoindre le groupe');
            return false;
        }

        try {
            await this.connection.invoke('JoinConversationGroup', conversationId);
            console.log(`✅ Rejoint le groupe de conversation: ${conversationId}`);
            return true;
        } catch (error) {
            console.error('❌ Erreur lors de la connexion au groupe:', error);
            return false;
        }
    },

    // Quitter un groupe de conversation
    async leaveGroup(conversationId) {
        if (!this.isConnected || !this.connection) {
            console.warn('⚠️ Connexion SignalR Chat non disponible pour quitter le groupe');
            return false;
        }

        try {
            await this.connection.invoke('LeaveConversationGroup', conversationId);
            console.log(`✅ Quitté le groupe de conversation: ${conversationId}`);
            return true;
        } catch (error) {
            console.error('❌ Erreur lors de la déconnexion du groupe:', error);
            return false;
        }
    },

    // Envoyer un indicateur de frappe
    async sendTypingIndicator(conversationId, isTyping) {
        if (!this.isConnected || !this.connection) {
            return false;
        }

        try {
            await this.connection.invoke('SendTypingIndicator', conversationId, isTyping);
            return true;
        } catch (error) {
            console.error('❌ Erreur lors de l\'envoi de l\'indicateur de frappe:', error);
            return false;
        }
    },

    // Envoyer un message via SignalR
    async sendMessage(conversationId, content, attachmentUrl = null) {
        if (!this.isConnected || !this.connection) {
            console.warn('⚠️ Connexion SignalR Chat non disponible pour envoyer le message');
            return false;
        }

        try {
            await this.connection.invoke('SendMessage', {
                conversationId: conversationId,
                content: content,
                attachmentUrl: attachmentUrl
            });
            console.log('✅ Message envoyé via SignalR');
            return true;
        } catch (error) {
            console.error('❌ Erreur lors de l\'envoi du message via SignalR:', error);
            return false;
        }
    },

    // Mettre à jour le statut de présence
    async updatePresence(isOnline) {
        if (!this.isConnected || !this.connection) {
            return false;
        }

        try {
            await this.connection.invoke('UpdatePresence', isOnline);
            return true;
        } catch (error) {
            console.error('❌ Erreur lors de la mise à jour de la présence:', error);
            return false;
        }
    },

    // Fermer la connexion
    async disconnect() {
        if (this.connection) {
            try {
                await this.connection.stop();
                console.log('🔌 Connexion SignalR Chat fermée');
            } catch (error) {
                console.error('❌ Erreur lors de la fermeture de la connexion:', error);
            }
            this.connection = null;
            this.isConnected = false;
        }
    },

    // Vérifier l'état de la connexion
    getConnectionState() {
        if (!this.connection) return 'Disconnected';
        
        const states = {
            0: 'Disconnected',
            1: 'Connecting',
            2: 'Connected',
            3: 'Disconnecting',
            4: 'Reconnecting'
        };
        
        return states[this.connection.state] || 'Unknown';
    },

    // Obtenir les statistiques de connexion
    getConnectionStats() {
        return {
            isConnected: this.isConnected,
            state: this.getConnectionState(),
            connectionId: this.connection?.connectionId || null
        };
    },

    // Enregistrer la référence Blazor pour les callbacks
    registerBlazorWidget(dotNetRef) {
        window.blazorChatWidget = dotNetRef;
        console.log('🔗 Widget Blazor enregistré pour les callbacks SignalR');
    }
};

// Initialisation automatique si SignalR est disponible
document.addEventListener('DOMContentLoaded', function() {
    if (typeof signalR !== 'undefined') {
        console.log('📡 SignalR disponible pour le chat');
    } else {
        console.warn('⚠️ SignalR non disponible - fonctionnalités temps réel limitées');
    }
});

// Gestion de la fermeture de la page
window.addEventListener('beforeunload', function() {
    if (window.chatSignalR && window.chatSignalR.isConnected) {
        window.chatSignalR.disconnect();
    }
});

// Gestion de la visibilité de la page
document.addEventListener('visibilitychange', function() {
    if (window.chatSignalR && window.chatSignalR.isConnected) {
        const isVisible = !document.hidden;
        window.chatSignalR.updatePresence(isVisible);
    }
});

console.log('🚀 Script SignalR Chat chargé');
