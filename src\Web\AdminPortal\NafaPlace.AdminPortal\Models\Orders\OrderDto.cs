namespace NafaPlace.AdminPortal.Models.Orders;

public class OrderDto
{
    public int Id { get; set; }
    public string OrderNumber { get; set; } = string.Empty;
    public int UserId { get; set; }
    public string UserEmail { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public DateTime OrderDate { get; set; }
    public string Status { get; set; } = string.Empty;
    public decimal TotalAmount { get; set; }
    public string Currency { get; set; } = "GNF";
    public string PaymentMethod { get; set; } = string.Empty;
    public string PaymentStatus { get; set; } = string.Empty;
    public ShippingAddressDto? ShippingAddress { get; set; }
    public List<OrderItemDto> Items { get; set; } = new();
    public DateTime? ShippedAt { get; set; }
    public DateTime? DeliveredAt { get; set; }
    public string? TrackingNumber { get; set; }
    public string? Notes { get; set; }
}

public class OrderItemDto
{
    public int Id { get; set; }
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public string ProductImage { get; set; } = string.Empty;
    public string ProductSku { get; set; } = string.Empty;
    public int Quantity { get; set; }
    public decimal UnitPrice { get; set; }
    public decimal TotalPrice { get; set; }
    public int? VariantId { get; set; }
    public string? VariantName { get; set; }
}

public class ShippingAddressDto
{
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string AddressLine1 { get; set; } = string.Empty;
    public string? AddressLine2 { get; set; }
    public string City { get; set; } = string.Empty;
    public string State { get; set; } = string.Empty;
    public string PostalCode { get; set; } = string.Empty;
    public string Country { get; set; } = string.Empty;
    public string PhoneNumber { get; set; } = string.Empty;
    public string FullName => $"{FirstName} {LastName}";
    public string FullAddress => $"{AddressLine1}, {City}, {State} {PostalCode}, {Country}";
}

public class OrderSearchRequest
{
    public string? SearchTerm { get; set; }
    public string? Status { get; set; }
    public string? PaymentStatus { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 20;
}

public class UpdateOrderStatusRequest
{
    public string Status { get; set; } = string.Empty;
    public string? TrackingNumber { get; set; }
    public string? Notes { get; set; }
}

public class OrderStatistics
{
    public int TotalOrders { get; set; }
    public int PendingOrders { get; set; }
    public int ProcessingOrders { get; set; }
    public int ShippedOrders { get; set; }
    public int DeliveredOrders { get; set; }
    public int CancelledOrders { get; set; }
    public decimal TotalRevenue { get; set; }
    public decimal MonthlyRevenue { get; set; }
    public decimal AverageOrderValue { get; set; }
}

public static class OrderStatus
{
    public const string Pending = "Pending";
    public const string Processing = "Processing";
    public const string Shipped = "Shipped";
    public const string Delivered = "Delivered";
    public const string Cancelled = "Cancelled";
    public const string Refunded = "Refunded";

    public static readonly List<string> All = new()
    {
        Pending, Processing, Shipped, Delivered, Cancelled, Refunded
    };

    public static string GetDisplayName(string status) => status switch
    {
        Pending => "En attente",
        Processing => "En cours de traitement",
        Shipped => "Expédiée",
        Delivered => "Livrée",
        Cancelled => "Annulée",
        Refunded => "Remboursée",
        _ => status
    };

    public static string GetBadgeClass(string status) => status switch
    {
        Pending => "badge bg-warning",
        Processing => "badge bg-info",
        Shipped => "badge bg-primary",
        Delivered => "badge bg-success",
        Cancelled => "badge bg-danger",
        Refunded => "badge bg-secondary",
        _ => "badge bg-light"
    };
}

public static class PaymentStatus
{
    public const string Pending = "Pending";
    public const string Paid = "Paid";
    public const string Failed = "Failed";
    public const string Refunded = "Refunded";

    public static readonly List<string> All = new()
    {
        Pending, Paid, Failed, Refunded
    };

    public static string GetDisplayName(string status) => status switch
    {
        Pending => "En attente",
        Paid => "Payé",
        Failed => "Échec",
        Refunded => "Remboursé",
        _ => status
    };

    public static string GetBadgeClass(string status) => status switch
    {
        Pending => "badge bg-warning",
        Paid => "badge bg-success",
        Failed => "badge bg-danger",
        Refunded => "badge bg-secondary",
        _ => "badge bg-light"
    };
}
