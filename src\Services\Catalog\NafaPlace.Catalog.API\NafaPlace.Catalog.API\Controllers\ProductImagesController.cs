using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using NafaPlace.Catalog.Application.Common.Interfaces;
using NafaPlace.Catalog.Application.DTOs.Product;
using System;
using System.Threading.Tasks;

namespace NafaPlace.Catalog.API.Controllers
{
    [ApiController]
    [Route("api/v1/products/{productId}/images")]
    public class ProductImagesController : ControllerBase
    {
        private readonly IProductImageService _productImageService;

        public ProductImagesController(IProductImageService productImageService)
        {
            _productImageService = productImageService;
        }

        [HttpPost]
        public async Task<IActionResult> AddProductImage(int productId, [FromBody] CreateProductImageRequest request)
        {
            var productImage = await _productImageService.AddProductImageAsync(productId, request);
            if (productImage == null)
            {
                return NotFound("Product not found");
            }
            return CreatedAtAction(nameof(GetProductImage), new { productId, imageId = productImage.Id }, productImage);
        }

        [HttpPost("bulk")]
        public async Task<IActionResult> AddBulkProductImages(int productId, [FromBody] List<CreateProductImageRequest> request)
        {
            var productImages = await _productImageService.AddBulkProductImagesAsync(productId, request);
            if (productImages == null)
            {
                return NotFound("Product not found");
            }
            return Ok(productImages);
        }

        [HttpDelete("{imageId}")]
        public async Task<IActionResult> DeleteProductImage(int productId, int imageId)
        {
            await _productImageService.DeleteProductImageAsync(imageId);
            return NoContent();
        }

        [HttpPut("{imageId}/main")]
        public async Task<IActionResult> SetMainImage(int productId, int imageId)
        {
            await _productImageService.SetMainImageAsync(productId, imageId);
            return NoContent();
        }

        [HttpGet("validate")]
        public async Task<IActionResult> ValidateImage([FromQuery] string imageUrl)
        {
            var isValid = await _productImageService.ValidateImageAsync(imageUrl);
            return Ok(new { isValid });
        }

        [HttpGet("{imageId}")]
        public async Task<IActionResult> GetProductImage(int productId, int imageId)
        {
            var productImage = await _productImageService.GetProductImageAsync(imageId);
            if (productImage == null)
                return NotFound();

            // Si l'image appartient à un autre produit, retourner une erreur 404
            if (productImage.ProductId != productId)
                return NotFound();

            // Si l'image n'a pas de données en base64, retourner le DTO
            return Ok(productImage);
        }
    }
}
