using System.Collections.Generic;

namespace NafaPlace.Cart.Domain
{
    public class ShoppingCart
    {
        public string UserId { get; set; }
        public List<CartItem> Items { get; set; } = new List<CartItem>();

        public ShoppingCart()
        {
            UserId = string.Empty;
        }

        public ShoppingCart(string userId)
        {
            UserId = userId;
        }
    }
}