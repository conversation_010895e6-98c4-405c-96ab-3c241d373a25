{"ConnectionStrings": {"DefaultConnection": "${DATABASE_URL}"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Cloudinary": {"CloudName": "${CLOUDINARY_CLOUD_NAME}", "ApiKey": "${CLOUDINARY_API_KEY}", "ApiSecret": "${CLOUDINARY_API_SECRET}"}, "Storage": {"ProductImagesContainer": "nafaplace-test-products", "CategoryImagesContainer": "nafaplace-test-categories"}}