using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using NafaPlace.Chat.Application.Services;

namespace NafaPlace.Chat.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class AgentsController : ControllerBase
{
    private readonly IChatService _chatService;
    private readonly IAgentService _agentService;
    private readonly ILogger<AgentsController> _logger;

    public AgentsController(
        IChatService chatService, 
        IAgentService agentService,
        ILogger<AgentsController> logger)
    {
        _chatService = chatService;
        _agentService = agentService;
        _logger = logger;
    }

    [HttpGet("available")]
    public async Task<IActionResult> GetAvailableAgents([FromQuery] string? departmentId = null)
    {
        try
        {
            var agents = await _chatService.GetAvailableAgentsAsync(departmentId);
            return Ok(agents);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des agents disponibles");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    [HttpGet]
    [Authorize(Roles = "Admin,Agent")]
    public async Task<IActionResult> GetAgents([FromQuery] string? departmentId = null)
    {
        try
        {
            var agents = await _chatService.GetAgentsAsync(departmentId);
            return Ok(agents);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des agents");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    [HttpGet("{id}")]
    [Authorize(Roles = "Admin,Agent")]
    public async Task<IActionResult> GetAgent(string id)
    {
        try
        {
            var agent = await _chatService.GetAgentAsync(id);
            
            if (agent == null)
            {
                return NotFound("Agent non trouvé");
            }

            return Ok(agent);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de l'agent {AgentId}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    [HttpPut("{id}/status")]
    [Authorize(Roles = "Admin,Agent")]
    public async Task<IActionResult> UpdateAgentStatus(string id, [FromBody] UpdateStatusRequest request)
    {
        try
        {
            var success = await _chatService.SetAgentStatusAsync(id, request.Status);
            
            if (success)
            {
                return Ok(new { message = "Statut mis à jour avec succès" });
            }

            return BadRequest("Impossible de mettre à jour le statut");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la mise à jour du statut de l'agent {AgentId}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    [HttpGet("{id}/conversations")]
    [Authorize(Roles = "Admin,Agent")]
    public async Task<IActionResult> GetAgentConversations(string id, [FromQuery] string? status = null)
    {
        try
        {
            var conversations = await _chatService.GetAgentConversationsAsync(id, 
                status != null ? Enum.Parse<ConversationStatus>(status) : null);

            return Ok(conversations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des conversations de l'agent {AgentId}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    [HttpGet("{id}/stats")]
    [Authorize(Roles = "Admin,Agent")]
    public async Task<IActionResult> GetAgentStats(string id, [FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var stats = await _agentService.GetAgentStatsAsync(id, 
                startDate ?? DateTime.UtcNow.AddDays(-30),
                endDate ?? DateTime.UtcNow);

            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des statistiques de l'agent {AgentId}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    [HttpPost("{id}/assign")]
    [Authorize(Roles = "Admin,Agent")]
    public async Task<IActionResult> AssignConversation(string id, [FromBody] AssignConversationRequest request)
    {
        try
        {
            var success = await _chatService.AssignConversationAsync(request.ConversationId, id);
            
            if (success)
            {
                return Ok(new { message = "Conversation assignée avec succès" });
            }

            return BadRequest("Impossible d'assigner la conversation");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'assignation de la conversation à l'agent {AgentId}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    [HttpPost("{id}/transfer")]
    [Authorize(Roles = "Admin,Agent")]
    public async Task<IActionResult> TransferConversation(string id, [FromBody] TransferConversationRequest request)
    {
        try
        {
            var success = await _chatService.TransferConversationAsync(
                request.ConversationId, 
                request.NewAgentId, 
                request.Reason);
            
            if (success)
            {
                return Ok(new { message = "Conversation transférée avec succès" });
            }

            return BadRequest("Impossible de transférer la conversation");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du transfert de la conversation");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }
}

// DTOs pour les requêtes
public class UpdateStatusRequest
{
    public AgentStatus Status { get; set; }
}

public class AssignConversationRequest
{
    public int ConversationId { get; set; }
}

public class TransferConversationRequest
{
    public int ConversationId { get; set; }
    public string NewAgentId { get; set; } = string.Empty;
    public string? Reason { get; set; }
}
