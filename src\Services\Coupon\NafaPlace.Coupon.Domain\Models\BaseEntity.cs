using System.ComponentModel.DataAnnotations;

namespace NafaPlace.Coupon.Domain.Models;

public abstract class BaseEntity
{
    [Key]
    public int Id { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    [MaxLength(50)]
    public string? CreatedBy { get; set; }
    
    [MaxLength(50)]
    public string? UpdatedBy { get; set; }
}
