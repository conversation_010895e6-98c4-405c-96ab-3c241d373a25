@using NafaPlace.AdminPortal.Models.Orders
@using NafaPlace.AdminPortal.Services
@inject IJSRuntime JSRuntime

<!-- Modal de mise à jour du statut -->
<div class="modal fade @(IsVisible ? "show" : "")" id="orderStatusModal" tabindex="-1" style="display: @(IsVisible ? "block" : "none")" aria-hidden="@(!IsVisible)">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>Modifier le statut de la commande
                </h5>
                <button type="button" class="btn-close" @onclick="Hide" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                @if (Order != null)
                {
                    <div class="mb-3">
                        <h6>Commande @Order.OrderNumber</h6>
                        <small class="text-muted">Client: @Order.UserName (@Order.UserEmail)</small>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Statut actuel</label>
                                <div>
                                    <span class="@OrderStatus.GetBadgeClass(Order.Status)">
                                        @OrderStatus.GetDisplayName(Order.Status)
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Paiement actuel</label>
                                <div>
                                    <span class="@PaymentStatus.GetBadgeClass(Order.PaymentStatus)">
                                        @PaymentStatus.GetDisplayName(Order.PaymentStatus)
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="newStatus" class="form-label">Nouveau statut *</label>
                        <select id="newStatus" class="form-select" @bind="updateRequest.Status">
                            <option value="">-- Sélectionner un statut --</option>
                            @foreach (var status in OrderStatus.All)
                            {
                                <option value="@status">@OrderStatus.GetDisplayName(status)</option>
                            }
                        </select>
                    </div>

                    @if (updateRequest.Status == OrderStatus.Shipped)
                    {
                        <div class="mb-3">
                            <label for="trackingNumber" class="form-label">Numéro de suivi</label>
                            <input type="text" id="trackingNumber" class="form-control" @bind="updateRequest.TrackingNumber" 
                                   placeholder="Entrez le numéro de suivi..." />
                        </div>
                    }

                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes (optionnel)</label>
                        <textarea id="notes" class="form-control" rows="3" @bind="updateRequest.Notes" 
                                  placeholder="Ajoutez des notes sur cette mise à jour..."></textarea>
                    </div>
                }
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" @onclick="Hide">Annuler</button>
                <button type="button" class="btn btn-primary" @onclick="UpdateStatus" disabled="@(isUpdating || string.IsNullOrEmpty(updateRequest.Status))">
                    @if (isUpdating)
                    {
                        <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                    }
                    Mettre à jour
                </button>
            </div>
        </div>
    </div>
</div>

@if (IsVisible)
{
    <div class="modal-backdrop fade show"></div>
}

@code {
    [Parameter] public OrderDto? Order { get; set; }
    [Parameter] public bool IsVisible { get; set; }
    [Parameter] public EventCallback OnStatusUpdated { get; set; }
    [Parameter] public EventCallback OnHide { get; set; }
    [Inject] public IOrderService OrderService { get; set; } = default!;

    private UpdateOrderStatusRequest updateRequest = new();
    private bool isUpdating = false;

    protected override void OnParametersSet()
    {
        if (Order != null && IsVisible)
        {
            // Réinitialiser le formulaire quand une nouvelle commande est sélectionnée
            updateRequest = new UpdateOrderStatusRequest();
        }
    }

    private async Task UpdateStatus()
    {
        if (Order == null || string.IsNullOrEmpty(updateRequest.Status))
            return;

        isUpdating = true;
        try
        {
            await OrderService.UpdateOrderStatusAsync(Order.OrderNumber, updateRequest);
            await JSRuntime.InvokeVoidAsync("alert", "Statut mis à jour avec succès!");
            await OnStatusUpdated.InvokeAsync();
            await Hide();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Erreur lors de la mise à jour: {ex.Message}");
        }
        finally
        {
            isUpdating = false;
        }
    }

    private async Task Hide()
    {
        await OnHide.InvokeAsync();
    }
}
