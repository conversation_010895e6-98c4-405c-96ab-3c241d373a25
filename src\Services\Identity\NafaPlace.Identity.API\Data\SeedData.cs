using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using NafaPlace.Identity.Domain.Models;
using NafaPlace.Identity.Infrastructure.Data;

namespace NafaPlace.Identity.API.Data;

public static class SeedData
{
    public static async Task Initialize(IServiceProvider serviceProvider)
    {
        using var scope = serviceProvider.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<IdentityDbContext>();
        var passwordHasher = scope.ServiceProvider.GetRequiredService<IPasswordHasher<User>>();
        
        // Assurez-vous que la base de données est créée et que les migrations sont appliquées
        await context.Database.MigrateAsync();
        
        // Vérifiez s'il y a déjà des utilisateurs dans la base de données
        if (await context.Users.AnyAsync())
        {
            return; // La base de données a déjà été initialisée
        }
        
        // Créer un utilisateur administrateur
        var adminUser = new User
        {
            Email = "<EMAIL>",
            Username = "admin",
            FirstName = "Admin",
            LastName = "User",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            IsActive = true,
            EmailConfirmed = true
        };
        
        // Hacher le mot de passe
        adminUser.PasswordHash = passwordHasher.HashPassword(adminUser, "Admin123!");
        
        // Ajouter l'utilisateur à la base de données
        await context.Users.AddAsync(adminUser);
        
        // Créer un rôle administrateur si nécessaire
        var adminRole = await context.Roles.FirstOrDefaultAsync(r => r.Name == "Admin");
        if (adminRole == null)
        {
            adminRole = new Role
            {
                Name = "Admin",
                Description = "Administrateur du système"
            };
            await context.Roles.AddAsync(adminRole);
            await context.SaveChangesAsync();
        }
        
        await context.SaveChangesAsync();
        // Attribuer le rôle à l'utilisateur
        var userRole = new UserRole
        {
            UserId = adminUser.Id,
            RoleId = adminRole.Id
        };
        
        await context.UserRoles.AddAsync(userRole);
        
        // Créer un utilisateur standard
        var standardUser = new User
        {
            Email = "<EMAIL>",
            Username = "user",
            FirstName = "Standard",
            LastName = "User",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            IsActive = true,
            EmailConfirmed = true
        };
        
        // Hacher le mot de passe
        standardUser.PasswordHash = passwordHasher.HashPassword(standardUser, "User123!");
        
        // Ajouter l'utilisateur à la base de données
        await context.Users.AddAsync(standardUser);
        await context.SaveChangesAsync();
    }
}
