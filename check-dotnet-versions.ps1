# Script pour vérifier les versions .NET dans tous les projets
Write-Host "🔍 Vérification des versions .NET dans NafaPlace" -ForegroundColor Green

# Fonction pour analyser un fichier .csproj
function Check-ProjectFile {
    param(
        [string]$FilePath
    )
    
    if (Test-Path $FilePath) {
        $content = Get-Content $FilePath -Raw
        
        # Extraire la version .NET
        if ($content -match '<TargetFramework>(.*?)</TargetFramework>') {
            $version = $matches[1]
            $relativePath = $FilePath.Replace((Get-Location).Path, "").TrimStart('\')
            
            if ($version -eq "net9.0") {
                Write-Host "✅ $relativePath - $version" -ForegroundColor Green
                return $true
            } else {
                Write-Host "❌ $relativePath - $version (devrait être net9.0)" -ForegroundColor Red
                return $false
            }
        } else {
            Write-Host "⚠️  $FilePath - Version non trouvée" -ForegroundColor Yellow
            return $false
        }
    }
    return $false
}

# Fonction pour analyser un Dockerfile
function Check-Dockerfile {
    param(
        [string]$FilePath
    )
    
    if (Test-Path $FilePath) {
        $content = Get-Content $FilePath -Raw
        $relativePath = $FilePath.Replace((Get-Location).Path, "").TrimStart('\')
        
        # Vérifier les images .NET
        $dotnetImages = @()
        if ($content -match 'FROM mcr\.microsoft\.com/dotnet/sdk:(\d+\.\d+)') {
            $dotnetImages += "SDK: $($matches[1])"
        }
        if ($content -match 'FROM mcr\.microsoft\.com/dotnet/aspnet:(\d+\.\d+)') {
            $dotnetImages += "ASP.NET: $($matches[1])"
        }
        
        if ($dotnetImages.Count -gt 0) {
            $allCorrect = $true
            foreach ($image in $dotnetImages) {
                if ($image -like "*9.0*") {
                    Write-Host "✅ $relativePath - $image" -ForegroundColor Green
                } else {
                    Write-Host "❌ $relativePath - $image (devrait être 9.0)" -ForegroundColor Red
                    $allCorrect = $false
                }
            }
            return $allCorrect
        } else {
            Write-Host "⚠️  $relativePath - Aucune image .NET trouvée" -ForegroundColor Yellow
            return $true
        }
    }
    return $false
}

Write-Host "`n📋 VÉRIFICATION DES FICHIERS .CSPROJ" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan

# Trouver tous les fichiers .csproj
$csprojFiles = Get-ChildItem -Path . -Recurse -Filter "*.csproj" | Where-Object { $_.FullName -notlike "*\bin\*" -and $_.FullName -notlike "*\obj\*" }

$correctProjects = 0
$totalProjects = $csprojFiles.Count

foreach ($file in $csprojFiles) {
    if (Check-ProjectFile -FilePath $file.FullName) {
        $correctProjects++
    }
}

Write-Host "`n📋 VÉRIFICATION DES DOCKERFILES" -ForegroundColor Cyan
Write-Host "==============================" -ForegroundColor Cyan

# Trouver tous les Dockerfiles
$dockerFiles = Get-ChildItem -Path . -Recurse -Filter "Dockerfile*" | Where-Object { $_.Name -match "^Dockerfile" }

$correctDockerfiles = 0
$totalDockerfiles = $dockerFiles.Count

foreach ($file in $dockerFiles) {
    if (Check-Dockerfile -FilePath $file.FullName) {
        $correctDockerfiles++
    }
}

# Résumé
Write-Host "`n📊 RÉSUMÉ" -ForegroundColor Magenta
Write-Host "=========" -ForegroundColor Magenta

$projectSuccessRate = [math]::Round(($correctProjects / $totalProjects) * 100, 1)
$dockerSuccessRate = [math]::Round(($correctDockerfiles / $totalDockerfiles) * 100, 1)

Write-Host "Projets .csproj: $correctProjects/$totalProjects ($projectSuccessRate%)" -ForegroundColor $(if($projectSuccessRate -eq 100){'Green'}else{'Red'})
Write-Host "Dockerfiles: $correctDockerfiles/$totalDockerfiles ($dockerSuccessRate%)" -ForegroundColor $(if($dockerSuccessRate -eq 100){'Green'}else{'Red'})

if ($projectSuccessRate -eq 100 -and $dockerSuccessRate -eq 100) {
    Write-Host "`n🎉 PARFAIT! Tous les projets utilisent .NET 9.0" -ForegroundColor Green
} elseif ($projectSuccessRate -eq 100) {
    Write-Host "`n✅ Tous les projets .csproj sont à jour" -ForegroundColor Green
    Write-Host "⚠️  Certains Dockerfiles nécessitent une mise à jour" -ForegroundColor Yellow
} else {
    Write-Host "`n❌ Certains projets nécessitent une mise à jour vers .NET 9.0" -ForegroundColor Red
}

# Recommandations
Write-Host "`n💡 RECOMMANDATIONS" -ForegroundColor Cyan
Write-Host "==================" -ForegroundColor Cyan

if ($projectSuccessRate -lt 100) {
    Write-Host "Mettre a jour les projets vers .NET 9.0:" -ForegroundColor Yellow
    Write-Host "   - Changer TargetFramework de net8.0 vers net9.0" -ForegroundColor White
    Write-Host "   - Mettre a jour les packages NuGet vers les versions 9.0.x" -ForegroundColor White
}

if ($dockerSuccessRate -lt 100) {
    Write-Host "Mettre a jour les Dockerfiles:" -ForegroundColor Yellow
    Write-Host "   - Changer mcr.microsoft.com/dotnet/sdk:8.0 vers mcr.microsoft.com/dotnet/sdk:9.0" -ForegroundColor White
    Write-Host "   - Changer mcr.microsoft.com/dotnet/aspnet:8.0 vers mcr.microsoft.com/dotnet/aspnet:9.0" -ForegroundColor White
}

Write-Host "`n🔄 COMMANDES UTILES" -ForegroundColor Cyan
Write-Host "==================" -ForegroundColor Cyan
Write-Host "Nettoyer et reconstruire:" -ForegroundColor White
Write-Host "  dotnet clean" -ForegroundColor Gray
Write-Host "  dotnet restore" -ForegroundColor Gray
Write-Host "  dotnet build" -ForegroundColor Gray
Write-Host ""
Write-Host "Reconstruire les conteneurs Docker:" -ForegroundColor White
Write-Host "  docker-compose down" -ForegroundColor Gray
Write-Host "  docker-compose build --no-cache" -ForegroundColor Gray
Write-Host "  docker-compose up -d" -ForegroundColor Gray

Write-Host "`nVerification terminee." -ForegroundColor Blue
