﻿using System.Net.Http.Json;
using NafaPlace.Web.Models.Catalog;
using NafaPlace.Web.Models.Common;
using Microsoft.Extensions.DependencyInjection;
using System.Net.Http;

namespace NafaPlace.Web.Services;

public class ProductService : IProductService
{
    private readonly HttpClient _httpClient;
    private readonly IReviewService _reviewService;

    public ProductService(HttpClient httpClient, IReviewService reviewService)
    {
        _httpClient = httpClient;
        _reviewService = reviewService;
    }

    public async Task<ProductSearchResponse> SearchProductsAsync(ProductSearchRequest request)
    {
        try
        {
            var queryParams = new List<string>();

            if (!string.IsNullOrEmpty(request.SearchTerm))
            {
                queryParams.Add($"searchTerm={Uri.EscapeDataString(request.SearchTerm)}");
            }

            if (request.CategoryIds != null && request.CategoryIds.Any())
            {
                foreach (var categoryId in request.CategoryIds)
                {
                    queryParams.Add($"categoryIds={categoryId}");
                }
            }

            queryParams.Add($"page={request.Page}");
            queryParams.Add($"pageSize={request.PageSize}");

            if (request.MinPrice.HasValue)
            {
                queryParams.Add($"minPrice={request.MinPrice.Value}");
            }

            if (request.MaxPrice.HasValue)
            {
                queryParams.Add($"maxPrice={request.MaxPrice.Value}");
            }

            if (request.InStockOnly)
            {
                queryParams.Add($"inStockOnly={request.InStockOnly}");
            }

            if (!string.IsNullOrEmpty(request.SortBy))
            {
                queryParams.Add($"sortBy={Uri.EscapeDataString(request.SortBy)}");
            }

            queryParams.Add($"sortDescending={request.SortDescending}");

            // Utiliser l'API existante /v1/products au lieu de /search
            var url = $"/api/v1/products?{string.Join("&", queryParams)}";
            Console.WriteLine($"Recherche de produits avec l'URL: {url}");
            var apiResponse = await _httpClient.GetFromJsonAsync<PagedResultDto<ProductDto>>(url);

            if (apiResponse != null && apiResponse.Items != null)
            {
                Console.WriteLine($"Produits trouvÃ©s avec dÃ©tails complets: {apiResponse.Items.Count()}");

                // Retourner immédiatement les produits sans attendre les reviews
                var response = new ProductSearchResponse
                {
                    Products = apiResponse.Items.ToList(),
                    TotalItems = apiResponse.TotalCount,
                    Page = request.Page,
                    PageSize = request.PageSize
                };

                // Enrichir les produits avec les reviews en arrière-plan (fire-and-forget)
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await EnrichProductsWithReviewsAsync(apiResponse.Items);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Erreur lors de l'enrichissement en arrière-plan: {ex.Message}");
                    }
                });

                Console.WriteLine($"Produits trouvÃ©s: {response.Products.Count()}");
                return response;
            }

            return new ProductSearchResponse
            {
                Products = new List<ProductDto>(),
                TotalItems = 0,
                Page = request.Page,
                PageSize = request.PageSize
            };
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la recherche de produits: {ex.Message}");
            return new ProductSearchResponse 
            { 
                Products = new List<ProductDto>(),
                TotalItems = 0,
                Page = request.Page,
                PageSize = request.PageSize
            };
        }
    }

    public async Task<ProductDto?> GetProductByIdAsync(int id)
    {
        try
        {
            Console.WriteLine($"RÃ©cupÃ©ration du produit {id}...");
            var product = await _httpClient.GetFromJsonAsync<ProductDto>($"/api/v1/products/{id}");
            if (product != null)
            {
                Console.WriteLine($"Produit {id} rÃ©cupÃ©rÃ© avec succÃ¨s");
                var enrichedProduct = await EnrichProductWithReviewsAsync(product);
                return enrichedProduct;
            }
            return null;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la rÃ©cupÃ©ration du produit {id}: {ex.Message}");
            return null;
        }
    }

    public async Task<IEnumerable<ProductDto>> GetRelatedProductsAsync(int productId, int count = 4)
    {
        try
        {
            Console.WriteLine($"RÃ©cupÃ©ration des produits liÃ©s Ã  {productId}...");

            // RÃ©cupÃ©rer d'abord le produit pour connaÃ®tre sa catÃ©gorie
            var product = await GetProductByIdAsync(productId);
            if (product?.Category?.Id != null)
            {
                // RÃ©cupÃ©rer des produits de la mÃªme catÃ©gorie
                var apiResponse = await _httpClient.GetFromJsonAsync<PagedResultDto<ProductDto>>($"/api/v1/products?page=1&pageSize={count + 1}");

                if (apiResponse?.Items != null)
                {
                    // Filtrer pour exclure le produit actuel et limiter le nombre
                    var relatedProducts = apiResponse.Items
                        .Where(p => p.Id != productId)
                        .Take(count)
                        .ToList();

                    Console.WriteLine($"Produits liÃ©s rÃ©cupÃ©rÃ©s: {relatedProducts.Count}");
                    return relatedProducts;
                }
            }

            return new List<ProductDto>();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la rÃ©cupÃ©ration des produits liÃ©s: {ex.Message}");
            return new List<ProductDto>();
        }
    }

    public async Task<IEnumerable<ProductDto>> GetFeaturedProductsAsync(int count = 8)
    {
        try
        {
            Console.WriteLine($"RÃ©cupÃ©ration des produits en vedette...");
            var productsResponse = await _httpClient.GetFromJsonAsync<IEnumerable<ProductDto>>($"/api/v1/products/featured?count={count}");

            if (productsResponse != null)
            {
                Console.WriteLine($"Produits en vedette rÃ©cupÃ©rÃ©s: {productsResponse.Count()}");

                // Enrichir les produits avec les reviews en arrière-plan
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await EnrichProductsWithReviewsAsync(productsResponse);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Erreur lors de l'enrichissement en arrière-plan: {ex.Message}");
                    }
                });

                return productsResponse;
            }

            return new List<ProductDto>();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la rÃ©cupÃ©ration des produits en vedette: {ex.Message}");
            return new List<ProductDto>();
        }
    }

    public async Task<IEnumerable<ProductDto>> GetNewProductsAsync(int count = 8)
    {
        try
        {
            Console.WriteLine($"RÃ©cupÃ©ration des nouveaux produits...");
            // Utiliser l'API existante avec tri par date de crÃ©ation
            var apiResponse = await _httpClient.GetFromJsonAsync<PagedResultDto<ProductDto>>($"/api/v1/products?page=1&pageSize={count}");

            if (apiResponse != null && apiResponse.Items != null)
            {
                Console.WriteLine($"Nouveaux produits rÃ©cupÃ©rÃ©s: {apiResponse.Items.Count()}");

                // Enrichir les produits avec les reviews en arrière-plan
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await EnrichProductsWithReviewsAsync(apiResponse.Items);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Erreur lors de l'enrichissement en arrière-plan: {ex.Message}");
                    }
                });

                return apiResponse.Items;
            }

            return new List<ProductDto>();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la rÃ©cupÃ©ration des nouveaux produits: {ex.Message}");
            return new List<ProductDto>();
        }
    }

    public async Task<IEnumerable<ProductDto>> GetSellerProductsAsync(int sellerId, int page, int pageSize)
    {
        try
        {
            Console.WriteLine($"RÃ©cupÃ©ration des produits du vendeur {sellerId}...");
            var apiResponse = await _httpClient.GetFromJsonAsync<PagedResultDto<ProductDto>>(
                $"/api/v1/products?sellerId={sellerId}&page={page}&pageSize={pageSize}");
            Console.WriteLine($"Produits du vendeur rÃ©cupÃ©rÃ©s: {apiResponse?.Items?.Count() ?? 0}");

            if (apiResponse != null && apiResponse.Items != null)
            {
                Console.WriteLine($"Produits du vendeur rÃ©cupÃ©rÃ©s avec dÃ©tails complets: {apiResponse.Items.Count()}");
                return apiResponse.Items;
            }

            return new List<ProductDto>();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la rÃ©cupÃ©ration des produits du vendeur: {ex.Message}");
            return new List<ProductDto>();
        }
    }

    public async Task<IEnumerable<ProductDto>> GetProductsByCategoryAsync(int categoryId, int page, int pageSize)
    {
        try
        {
            Console.WriteLine($"RÃ©cupÃ©ration des produits de la catÃ©gorie {categoryId}...");
            var productsResponse = await _httpClient.GetFromJsonAsync<IEnumerable<ProductDto>>($"/api/v1/products/category/{categoryId}?page={page}&pageSize={pageSize}");
            Console.WriteLine($"Produits de la catÃ©gorie rÃ©cupÃ©rÃ©s: {productsResponse?.Count() ?? 0}");
            
            if (productsResponse != null)
            {
                Console.WriteLine($"Produits de la catÃ©gorie rÃ©cupÃ©rÃ©s avec dÃ©tails complets: {productsResponse.Count()}");
                return productsResponse;
            }
            
            return new List<ProductDto>();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la rÃ©cupÃ©ration des produits de la catÃ©gorie: {ex.Message}");
            return new List<ProductDto>();
        }
    }

    public async Task<IEnumerable<ProductDto>> GetAllProductsAsync()
    {
        try
        {
            Console.WriteLine("RÃ©cupÃ©ration de tous les produits...");
            var apiResponse = await _httpClient.GetFromJsonAsync<PagedResultDto<ProductDto>>("/api/v1/products?page=1&pageSize=1000");
            Console.WriteLine($"Produits rÃ©cupÃ©rÃ©s: {apiResponse?.Items?.Count() ?? 0}");

            if (apiResponse != null && apiResponse.Items != null)
            {
                Console.WriteLine($"Produits rÃ©cupÃ©rÃ©s avec dÃ©tails complets: {apiResponse.Items.Count()}");

                // Enrichir les produits avec les reviews en arrière-plan
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await EnrichProductsWithReviewsAsync(apiResponse.Items);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Erreur lors de l'enrichissement en arrière-plan: {ex.Message}");
                    }
                });

                return apiResponse.Items;
            }

            return new List<ProductDto>();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la rÃ©cupÃ©ration de tous les produits: {ex.Message}");
            return new List<ProductDto>();
        }
    }

    public string GetImageUrl(ProductImageDto image, bool thumbnail = false)
    {
        if (image == null)
        {
            return "/images/no-image.png";
        }

        var url = thumbnail ? image.ThumbnailUrl : image.Url;

        if (string.IsNullOrEmpty(url))
        {
            // Fallback to the other url if one is empty
            url = thumbnail ? image.Url : image.ThumbnailUrl;
        }

        if (string.IsNullOrEmpty(url))
        {
            return "/images/no-image.png";
        }

        if (url.StartsWith("http://") || url.StartsWith("https://") || url.StartsWith("data:"))
        {
            return url;
        }

        return url.StartsWith("/") ? url : $"/{url}";
    }

    private async Task<ProductDto> EnrichProductWithReviewsAsync(ProductDto product)
    {
        try
        {
            // Utiliser un timeout court pour éviter de bloquer l'affichage
            using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(2));
            var reviewSummary = await _reviewService.GetReviewSummaryAsync(product.Id);
            product.Rating = reviewSummary.AverageRating;
            product.ReviewCount = reviewSummary.TotalReviews;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de l'enrichissement du produit {product.Id} avec les reviews: {ex.Message}");
            // En cas d'erreur, garder les valeurs par défaut (0)
            product.Rating = 0;
            product.ReviewCount = 0;
        }
        return product;
    }

    private async Task<IEnumerable<ProductDto>> EnrichProductsWithReviewsAsync(IEnumerable<ProductDto> products)
    {
        // Traitement en parallèle avec timeout global pour éviter de bloquer l'affichage
        var enrichmentTasks = products.Select(async product =>
        {
            try
            {
                // Timeout court par produit
                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(1));
                return await EnrichProductWithReviewsAsync(product);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors de l'enrichissement du produit {product.Id}: {ex.Message}");
                // Retourner le produit sans enrichissement en cas d'erreur
                product.Rating = 0;
                product.ReviewCount = 0;
                return product;
            }
        });

        try
        {
            // Timeout global pour tous les enrichissements
            using var globalCts = new CancellationTokenSource(TimeSpan.FromSeconds(5));
            var enrichedProducts = await Task.WhenAll(enrichmentTasks);
            return enrichedProducts;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Timeout global lors de l'enrichissement des produits: {ex.Message}");
            // Retourner les produits sans enrichissement
            return products.Select(p => { p.Rating = 0; p.ReviewCount = 0; return p; });
        }
    }
}

