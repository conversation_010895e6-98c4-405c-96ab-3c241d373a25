using NafaPlace.Inventory.Application.DTOs;
using NafaPlace.Inventory.Domain.Enums;

namespace NafaPlace.Inventory.Application.Interfaces;

public interface IProductStockRepository
{
    Task<bool> UpdateStockAsync(int productId, int newQuantity, string reason, string updatedBy, string? notes = null);
    Task<bool> AdjustStockAsync(int productId, int newQuantity, string reason, string updatedBy, string? notes = null);
    Task<StockValidationResult> ValidateStockAvailabilityAsync(int productId, int quantity);
    Task<int> GetAvailableStockAsync(int productId);
    Task<bool> RecordMovementAsync(int productId, MovementType movementType, int quantity, string reason, string performedBy, string? notes = null);
}
