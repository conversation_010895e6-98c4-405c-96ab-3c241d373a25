@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Routing
@inject NavigationManager NavigationManager

<div class="p-4">
    <h4 class="text-white mb-0">
        <i class="bi bi-shop"></i>
        NafaPlace Vendeur
    </h4>
</div>

<ul class="nav flex-column px-2">
        <li class="nav-item">
            <a class="nav-link @(GetActive(""))" href="">
                <i class="bi bi-house-door me-2"></i>
                Accueil
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link @(GetActive("dashboard"))" href="dashboard">
                <i class="bi bi-speedometer2 me-2"></i>
                Dashboard
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link @(GetActive("products"))" href="products">
                <i class="bi bi-box-seam me-2"></i>
                Produits
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link @(GetActive("inventory"))" href="inventory">
                <i class="bi bi-boxes me-2"></i>
                Inventaire
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link @(GetActive("orders"))" href="orders">
                <i class="bi bi-bag me-2"></i>
                Commandes
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link @(GetActive("reviews"))" href="reviews">
                <i class="bi bi-star me-2"></i>
                Avis
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link @(GetActive("coupons"))" href="coupons">
                <i class="bi bi-ticket-perforated me-2"></i>
                Coupons
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link @(GetActive("statistics"))" href="statistics">
                <i class="bi bi-bar-chart me-2"></i>
                Statistiques
            </a>
        </li>
</ul>

<div class="mt-auto p-4">
    <div class="d-flex align-items-center text-white">
        <img src="https://via.placeholder.com/40x40/667eea/ffffff?text=VD"
             class="rounded-circle me-3" alt="Avatar">
        <div>
            <div class="fw-bold">Vendeur</div>
            <small class="opacity-75"><EMAIL></small>
        </div>
    </div>
</div>

@code {
    private bool collapseNavMenu = true;

    private string? NavMenuCssClass => collapseNavMenu ? "collapse" : null;

    private void ToggleNavMenu()
    {
        collapseNavMenu = !collapseNavMenu;
    }
    
    private string GetActive(string href)
    {
        var uri = new Uri(NavigationManager.Uri);
        var path = uri.AbsolutePath;
        
        if (string.IsNullOrEmpty(href) && path == "/")
            return "active";
            
        if (!string.IsNullOrEmpty(href) && path.StartsWith($"/{href}"))
            return "active";
            
        return "";
    }
}
