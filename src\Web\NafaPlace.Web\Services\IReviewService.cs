using NafaPlace.Reviews.DTOs;

namespace NafaPlace.Web.Services;

public interface IReviewService
{
    // Basic Review operations
    Task<ReviewDto?> GetReviewByIdAsync(int id);
    Task<ReviewsPagedResult> GetReviewsByProductIdAsync(int productId, int page = 1, int pageSize = 10);
    Task<ReviewsPagedResult> GetUserReviewsAsync(string userId, int page = 1, int pageSize = 10);
    Task<ReviewDto> CreateReviewAsync(CreateReviewRequest request);
    Task<ReviewDto> UpdateReviewAsync(int id, UpdateReviewRequest request);
    Task DeleteReviewAsync(int id);
    Task<ReviewSummaryDto> GetReviewSummaryAsync(int productId);
    Task<bool> MarkReviewHelpfulAsync(int reviewId);
    Task<bool> UnmarkReviewHelpfulAsync(int reviewId);
    Task<bool> CanUserReviewProductAsync(int productId, string userId);
    
    // Reply operations
    Task<List<ReplyDto>> GetReviewRepliesAsync(int reviewId);
    Task<ReplyDto> CreateReplyAsync(CreateReplyRequest request);
    Task DeleteReplyAsync(int replyId);
}
