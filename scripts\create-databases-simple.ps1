# Simple script to create NafaPlace databases
# Author: AI Assistant
# Date: 2025-01-28

param(
    [string]$PostgreSQLPath = "C:\Program Files\PostgreSQL\17\bin",
    [string]$DbHost = "localhost",
    [string]$DbPort = "5432",
    [string]$AdminUser = "postgres",
    [string]$AppUser = "nafaplace",
    [string]$AppPassword = "NafaPlace2025@Dev",
    [switch]$Force
)

Write-Host "Creating NafaPlace databases..." -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green

# Database list
$databases = @(
    "NafaPlace.Catalog",
    "NafaPlace.Order", 
    "NafaPlace.Notification",
    "NafaPlace.Analytics",
    "NafaPlace.Chat",
    "NafaPlace.Recommendation",
    "NafaPlace.Loyalty",
    "NafaPlace.Localization"
)

# Function to execute SQL command
function Invoke-SQL {
    param(
        [string]$Command,
        [string]$Database = "postgres"
    )
    
    try {
        $psqlPath = Join-Path $PostgreSQLPath "psql.exe"
        
        if (-not (Test-Path $psqlPath)) {
            Write-Host "ERROR: psql.exe not found in: $PostgreSQLPath" -ForegroundColor Red
            return $false
        }
        
        # Create temp password file
        $pgpassFile = Join-Path $env:TEMP "pgpass_temp.txt"
        "$DbHost`:$DbPort`:$Database`:$AdminUser`:$AdminPassword" | Out-File -FilePath $pgpassFile -Encoding ASCII

        $env:PGPASSFILE = $pgpassFile

        # Execute command
        $result = & $psqlPath -h $DbHost -p $DbPort -U $AdminUser -d $Database -c $Command -t -A 2>&1
        
        # Cleanup
        Remove-Item $pgpassFile -ErrorAction SilentlyContinue
        Remove-Item Env:PGPASSFILE -ErrorAction SilentlyContinue
        
        if ($LASTEXITCODE -eq 0) {
            return $result
        } else {
            Write-Host "SQL Error: $result" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "Exception: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Get admin password
if ([string]::IsNullOrEmpty($AdminPassword)) {
    $securePassword = Read-Host "Password for postgres user" -AsSecureString
    $script:AdminPassword = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($securePassword))
}

# Test connection
Write-Host "Testing PostgreSQL connection..." -ForegroundColor Yellow
$testResult = Invoke-SQL "SELECT version();"

if (-not $testResult) {
    Write-Host "ERROR: Cannot connect to PostgreSQL" -ForegroundColor Red
    exit 1
}

Write-Host "SUCCESS: Connected to PostgreSQL" -ForegroundColor Green

# Create application user
Write-Host "Creating application user..." -ForegroundColor Yellow
$userExists = Invoke-SQL "SELECT 1 FROM pg_roles WHERE rolname='$AppUser';"

if ($userExists -and $userExists.Trim() -eq "1") {
    if ($Force) {
        Write-Host "Dropping existing user..." -ForegroundColor Yellow
        Invoke-SQL "DROP USER IF EXISTS $AppUser;" | Out-Null
    } else {
        Write-Host "INFO: User $AppUser already exists" -ForegroundColor Blue
    }
}

if (-not $userExists -or $Force) {
    $createUserResult = Invoke-SQL "CREATE USER $AppUser WITH PASSWORD '$AppPassword' CREATEDB;"
    if ($createUserResult -ne $false) {
        Write-Host "SUCCESS: User $AppUser created" -ForegroundColor Green
    } else {
        Write-Host "ERROR: Failed to create user $AppUser" -ForegroundColor Red
        exit 1
    }
}

# Create databases
Write-Host "Creating databases..." -ForegroundColor Yellow
$successCount = 0

foreach ($database in $databases) {
    Write-Host "Creating database: $database" -ForegroundColor Cyan
    
    # Check if database exists
    $dbExists = Invoke-SQL "SELECT 1 FROM pg_database WHERE datname='$database';"
    
    if ($dbExists -and $dbExists.Trim() -eq "1") {
        if ($Force) {
            Write-Host "Dropping existing database..." -ForegroundColor Yellow
            Invoke-SQL "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname='$database';" | Out-Null
            Invoke-SQL "DROP DATABASE IF EXISTS `"$database`";" | Out-Null
        } else {
            Write-Host "INFO: Database $database already exists" -ForegroundColor Blue
            $successCount++
            continue
        }
    }
    
    # Create database
    $createDbResult = Invoke-SQL "CREATE DATABASE `"$database`" OWNER $AppUser;"
    
    if ($createDbResult -ne $false) {
        Write-Host "SUCCESS: Database $database created" -ForegroundColor Green
        Invoke-SQL "GRANT ALL PRIVILEGES ON DATABASE `"$database`" TO $AppUser;" | Out-Null
        $successCount++
    } else {
        Write-Host "ERROR: Failed to create database $database" -ForegroundColor Red
    }
    
    Start-Sleep -Seconds 1
}

# Summary
Write-Host "`nSUMMARY" -ForegroundColor Magenta
Write-Host "=======" -ForegroundColor Magenta

$successRate = [math]::Round(($successCount / $databases.Count) * 100, 2)
Write-Host "Databases created: $successCount/$($databases.Count) ($successRate%)" -ForegroundColor Green

if ($successRate -eq 100) {
    Write-Host "SUCCESS: All databases created!" -ForegroundColor Green
    
    # Show created databases
    Write-Host "`nVerifying created databases..." -ForegroundColor Magenta
    $result = Invoke-SQL "SELECT datname FROM pg_database WHERE datname LIKE 'NafaPlace.%' ORDER BY datname;"
    
    if ($result) {
        Write-Host "NafaPlace databases found:" -ForegroundColor Green
        $result.Split("`n") | Where-Object { $_.Trim() -ne "" } | ForEach-Object {
            Write-Host "  - $_" -ForegroundColor White
        }
    }
    
    # Show connection strings
    Write-Host "`nConnection strings:" -ForegroundColor Magenta
    foreach ($db in $databases) {
        $connectionString = "Host=$DbHost;Port=$DbPort;Database=$db;Username=$AppUser;Password=$AppPassword"
        Write-Host "$db :" -ForegroundColor Cyan
        Write-Host "  $connectionString" -ForegroundColor White
    }
    
    Write-Host "`nNafaPlace databases are ready!" -ForegroundColor Green
    Write-Host "Next step: Run EF Core migrations" -ForegroundColor Cyan
    
} else {
    Write-Host "WARNING: Some databases could not be created" -ForegroundColor Yellow
}

Write-Host "`nScript completed!" -ForegroundColor Green
