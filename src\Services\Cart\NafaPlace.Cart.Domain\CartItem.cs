namespace NafaPlace.Cart.Domain
{
    public class CartItem
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public decimal UnitPrice { get; set; }
        public int Quantity { get; set; }
        public string ImageUrl { get; set; } = string.Empty;

        // Propriétés pour les coupons et la gestion des commandes
        public int? CategoryId { get; set; }
        public int? SellerId { get; set; }
    }
}