-- Script pour ajouter les nouvelles tables des fonctionnalités avancées
-- Exécuter ce script pour créer les tables Coupon, Inventory, Delivery et Notifications

-- =============================================
-- TABLES COUPONS
-- =============================================

-- Table des coupons
CREATE TABLE IF NOT EXISTS "Coupons" (
    "Id" SERIAL PRIMARY KEY,
    "Code" VARCHAR(50) NOT NULL UNIQUE,
    "Name" VARCHAR(200) NOT NULL,
    "Description" VARCHAR(500),
    "Type" INTEGER NOT NULL, -- 1=FixedAmount, 2=Percentage, 3=FreeShipping, 4=BuyXGetY
    "Value" DECIMAL(18,2) NOT NULL,
    "MinimumOrderAmount" DECIMAL(18,2),
    "MaximumDiscountAmount" DECIMAL(18,2),
    "StartDate" TIMESTAMP NOT NULL,
    "EndDate" TIMESTAMP NOT NULL,
    "UsageLimit" INTEGER,
    "UsageLimitPerUser" INTEGER,
    "UsageCount" INTEGER DEFAULT 0,
    "IsActive" BOOLEAN DEFAULT true,
    "Currency" VARCHAR(3) DEFAULT 'GNF',
    "ApplicableToAllProducts" BOOLEAN DEFAULT true,
    "ApplicableProductIds" TEXT, -- JSON array
    "ApplicableCategoryIds" TEXT, -- JSON array
    "ApplicableSellerIds" TEXT, -- JSON array
    "ExcludedProductIds" TEXT, -- JSON array
    "ExcludedCategoryIds" TEXT, -- JSON array
    "ExcludedSellerIds" TEXT, -- JSON array
    "CreatedBy" VARCHAR(50),
    "UpdatedBy" VARCHAR(50),
    "CreatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des utilisations de coupons
CREATE TABLE IF NOT EXISTS "CouponUsages" (
    "Id" SERIAL PRIMARY KEY,
    "CouponId" INTEGER NOT NULL REFERENCES "Coupons"("Id") ON DELETE CASCADE,
    "UserId" VARCHAR(50) NOT NULL,
    "OrderId" VARCHAR(50) NOT NULL,
    "DiscountAmount" DECIMAL(18,2) NOT NULL,
    "Currency" VARCHAR(3) DEFAULT 'GNF',
    "UsedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- TABLES INVENTORY (GESTION DES STOCKS)
-- =============================================

-- Table des réservations de stock
CREATE TABLE IF NOT EXISTS "StockReservations" (
    "Id" SERIAL PRIMARY KEY,
    "ProductId" INTEGER NOT NULL,
    "UserId" VARCHAR(50) NOT NULL,
    "SessionId" VARCHAR(50) NOT NULL,
    "Quantity" INTEGER NOT NULL,
    "Status" INTEGER NOT NULL DEFAULT 1, -- 1=Active, 2=Confirmed, 3=Released, 4=Expired
    "ReservedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "ExpiresAt" TIMESTAMP NOT NULL,
    "OrderId" VARCHAR(50),
    "Reason" VARCHAR(200),
    "ReleasedAt" TIMESTAMP,
    "CreatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des alertes de stock
CREATE TABLE IF NOT EXISTS "StockAlerts" (
    "Id" SERIAL PRIMARY KEY,
    "ProductId" INTEGER NOT NULL,
    "ProductName" VARCHAR(100) NOT NULL,
    "Type" INTEGER NOT NULL, -- 1=LowStock, 2=OutOfStock, 3=OverStock, 4=StockMovement, 5=ReservationExpiry
    "Severity" INTEGER NOT NULL, -- 1=Info, 2=Warning, 3=Critical, 4=Emergency
    "Message" VARCHAR(500) NOT NULL,
    "CurrentStock" INTEGER NOT NULL,
    "ThresholdValue" INTEGER,
    "IsActive" BOOLEAN DEFAULT true,
    "IsAcknowledged" BOOLEAN DEFAULT false,
    "AcknowledgedBy" VARCHAR(50),
    "AcknowledgedAt" TIMESTAMP,
    "SellerId" INTEGER NOT NULL,
    "SellerName" VARCHAR(100),
    "CreatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des mouvements de stock
CREATE TABLE IF NOT EXISTS "StockMovements" (
    "Id" SERIAL PRIMARY KEY,
    "ProductId" INTEGER NOT NULL,
    "ProductName" VARCHAR(100) NOT NULL,
    "Type" INTEGER NOT NULL, -- 1=Sale, 2=Return, 3=Adjustment, 4=Restock, 5=Reservation, 6=Release, 7=Damage, 8=Loss, 9=Transfer
    "Quantity" INTEGER NOT NULL,
    "PreviousStock" INTEGER NOT NULL,
    "NewStock" INTEGER NOT NULL,
    "Reason" VARCHAR(200) NOT NULL,
    "Reference" VARCHAR(50), -- Order ID, Adjustment ID, etc.
    "UserId" VARCHAR(50) NOT NULL,
    "UserName" VARCHAR(100),
    "SellerId" INTEGER NOT NULL,
    "Notes" VARCHAR(500),
    "CreatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- TABLES NOTIFICATIONS
-- =============================================

-- Table des templates de notifications
CREATE TABLE IF NOT EXISTS "NotificationTemplates" (
    "Id" SERIAL PRIMARY KEY,
    "Name" VARCHAR(100) NOT NULL,
    "Code" VARCHAR(50) NOT NULL UNIQUE,
    "Type" INTEGER NOT NULL, -- 1=Welcome, 2=OrderConfirmation, etc.
    "Channel" INTEGER NOT NULL, -- 1=Email, 2=SMS, 3=Push, 4=InApp
    "Subject" VARCHAR(200) NOT NULL,
    "Body" TEXT NOT NULL,
    "Description" VARCHAR(500),
    "IsActive" BOOLEAN DEFAULT true,
    "Language" VARCHAR(10) DEFAULT 'fr',
    "Variables" TEXT, -- JSON format
    "CreatedBy" VARCHAR(50),
    "UpdatedBy" VARCHAR(50),
    "CreatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des logs de notifications
CREATE TABLE IF NOT EXISTS "NotificationLogs" (
    "Id" SERIAL PRIMARY KEY,
    "UserId" VARCHAR(50) NOT NULL,
    "UserName" VARCHAR(100) NOT NULL,
    "Recipient" VARCHAR(100) NOT NULL,
    "Channel" INTEGER NOT NULL, -- 1=Email, 2=SMS, 3=Push, 4=InApp
    "Type" INTEGER NOT NULL,
    "Subject" VARCHAR(200) NOT NULL,
    "Content" TEXT NOT NULL,
    "Status" INTEGER DEFAULT 1, -- 1=Pending, 2=Sent, 3=Delivered, 4=Read, 5=Failed, 6=Cancelled, 7=Expired
    "Priority" INTEGER DEFAULT 2, -- 1=Low, 2=Normal, 3=High, 4=Critical
    "ScheduledAt" TIMESTAMP,
    "SentAt" TIMESTAMP,
    "DeliveredAt" TIMESTAMP,
    "ReadAt" TIMESTAMP,
    "ErrorMessage" VARCHAR(500),
    "RetryCount" INTEGER DEFAULT 0,
    "MaxRetries" INTEGER DEFAULT 3,
    "Reference" VARCHAR(50), -- Order ID, etc.
    "TemplateCode" VARCHAR(50),
    "Metadata" TEXT, -- JSON format
    "CreatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des préférences de notifications
CREATE TABLE IF NOT EXISTS "NotificationPreferences" (
    "Id" SERIAL PRIMARY KEY,
    "UserId" VARCHAR(50) NOT NULL,
    "Type" INTEGER NOT NULL,
    "Channel" INTEGER NOT NULL,
    "IsEnabled" BOOLEAN DEFAULT true,
    "QuietHoursStart" TIME,
    "QuietHoursEnd" TIME,
    "TimeZone" VARCHAR(10),
    "Language" VARCHAR(10) DEFAULT 'fr',
    "Frequency" INTEGER DEFAULT 1, -- 1=Immediate, 2=Hourly, 3=Daily, 4=Weekly, 5=Monthly, 6=Never
    "LastSent" TIMESTAMP,
    "CreatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE("UserId", "Type", "Channel")
);

-- =============================================
-- TABLES DELIVERY (LIVRAISON)
-- =============================================

-- Table des zones de livraison
CREATE TABLE IF NOT EXISTS "DeliveryZones" (
    "Id" SERIAL PRIMARY KEY,
    "Name" VARCHAR(100) NOT NULL,
    "Code" VARCHAR(50) NOT NULL UNIQUE,
    "Description" VARCHAR(500),
    "Type" INTEGER NOT NULL, -- 1=Country, 2=Region, 3=Prefecture, 4=City, 5=District, 6=Custom
    "ParentZoneCode" VARCHAR(100),
    "Latitude" DOUBLE PRECISION,
    "Longitude" DOUBLE PRECISION,
    "Radius" DOUBLE PRECISION, -- En kilomètres
    "Boundaries" TEXT, -- JSON format
    "IsActive" BOOLEAN DEFAULT true,
    "BaseDeliveryFee" DECIMAL(18,2) NOT NULL,
    "FreeDeliveryThreshold" DECIMAL(18,2),
    "EstimatedDeliveryDays" INTEGER DEFAULT 1,
    "MaxDeliveryDays" INTEGER DEFAULT 7,
    "SameDayDeliveryAvailable" BOOLEAN DEFAULT false,
    "SameDayDeliveryFee" DECIMAL(18,2),
    "ExpressDeliveryAvailable" BOOLEAN DEFAULT false,
    "ExpressDeliveryFee" DECIMAL(18,2),
    "Currency" VARCHAR(3) DEFAULT 'GNF',
    "MaxWeight" DECIMAL(10,2), -- En kg
    "MaxVolume" DECIMAL(15,2), -- En cm³
    "MaxOrderValue" DECIMAL(18,2),
    "MinOrderValue" DECIMAL(18,2),
    "DeliveryStartTime" TIME,
    "DeliveryEndTime" TIME,
    "DeliveryDays" TEXT, -- JSON format
    "CreatedBy" VARCHAR(50),
    "UpdatedBy" VARCHAR(50),
    "CreatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des transporteurs
CREATE TABLE IF NOT EXISTS "Carriers" (
    "Id" SERIAL PRIMARY KEY,
    "Name" VARCHAR(100) NOT NULL,
    "Code" VARCHAR(50) NOT NULL UNIQUE,
    "Description" VARCHAR(500),
    "LogoUrl" VARCHAR(200),
    "ContactEmail" VARCHAR(100),
    "ContactPhone" VARCHAR(20),
    "Address" VARCHAR(500),
    "Website" VARCHAR(200),
    "IsActive" BOOLEAN DEFAULT true,
    "Type" INTEGER NOT NULL, -- 1=Internal, 2=ThirdParty, 3=Marketplace, 4=Postal, 5=Express
    "ApiEndpoint" VARCHAR(200),
    "ApiKey" VARCHAR(100),
    "ApiSecret" VARCHAR(100),
    "Rating" DECIMAL(3,2) DEFAULT 0,
    "ReviewCount" INTEGER DEFAULT 0,
    "TotalDeliveries" INTEGER DEFAULT 0,
    "SuccessfulDeliveries" INTEGER DEFAULT 0,
    "CreatedBy" VARCHAR(50),
    "UpdatedBy" VARCHAR(50),
    "CreatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- INDEX POUR PERFORMANCE
-- =============================================

-- Index pour les coupons
CREATE INDEX IF NOT EXISTS "IX_Coupons_Code" ON "Coupons"("Code");
CREATE INDEX IF NOT EXISTS "IX_Coupons_IsActive" ON "Coupons"("IsActive");
CREATE INDEX IF NOT EXISTS "IX_Coupons_StartDate_EndDate" ON "Coupons"("StartDate", "EndDate");

-- Index pour les réservations de stock
CREATE INDEX IF NOT EXISTS "IX_StockReservations_ProductId" ON "StockReservations"("ProductId");
CREATE INDEX IF NOT EXISTS "IX_StockReservations_UserId" ON "StockReservations"("UserId");
CREATE INDEX IF NOT EXISTS "IX_StockReservations_Status" ON "StockReservations"("Status");
CREATE INDEX IF NOT EXISTS "IX_StockReservations_ExpiresAt" ON "StockReservations"("ExpiresAt");

-- Index pour les alertes de stock
CREATE INDEX IF NOT EXISTS "IX_StockAlerts_ProductId" ON "StockAlerts"("ProductId");
CREATE INDEX IF NOT EXISTS "IX_StockAlerts_SellerId" ON "StockAlerts"("SellerId");
CREATE INDEX IF NOT EXISTS "IX_StockAlerts_IsActive" ON "StockAlerts"("IsActive");

-- Index pour les mouvements de stock
CREATE INDEX IF NOT EXISTS "IX_StockMovements_ProductId" ON "StockMovements"("ProductId");
CREATE INDEX IF NOT EXISTS "IX_StockMovements_SellerId" ON "StockMovements"("SellerId");
CREATE INDEX IF NOT EXISTS "IX_StockMovements_CreatedAt" ON "StockMovements"("CreatedAt");

-- Index pour les notifications
CREATE INDEX IF NOT EXISTS "IX_NotificationLogs_UserId" ON "NotificationLogs"("UserId");
CREATE INDEX IF NOT EXISTS "IX_NotificationLogs_Status" ON "NotificationLogs"("Status");
CREATE INDEX IF NOT EXISTS "IX_NotificationLogs_CreatedAt" ON "NotificationLogs"("CreatedAt");

-- Index pour les zones de livraison
CREATE INDEX IF NOT EXISTS "IX_DeliveryZones_Code" ON "DeliveryZones"("Code");
CREATE INDEX IF NOT EXISTS "IX_DeliveryZones_IsActive" ON "DeliveryZones"("IsActive");

-- Index pour les transporteurs
CREATE INDEX IF NOT EXISTS "IX_Carriers_Code" ON "Carriers"("Code");
CREATE INDEX IF NOT EXISTS "IX_Carriers_IsActive" ON "Carriers"("IsActive");

-- =============================================
-- DONNÉES DE TEST
-- =============================================

-- Insérer des coupons de test
INSERT INTO "Coupons" ("Code", "Name", "Description", "Type", "Value", "MinimumOrderAmount", "StartDate", "EndDate", "UsageLimit", "UsageLimitPerUser", "IsActive", "Currency", "ApplicableToAllProducts", "CreatedBy") VALUES
('WELCOME10', 'Bienvenue - 10% de réduction', '10% de réduction pour les nouveaux clients', 2, 10, 50000, CURRENT_TIMESTAMP - INTERVAL '30 days', CURRENT_TIMESTAMP + INTERVAL '365 days', 1000, 1, true, 'GNF', true, 'System'),
('FREESHIP', 'Livraison gratuite', 'Livraison gratuite pour les commandes de plus de 100,000 GNF', 3, 0, 100000, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP + INTERVAL '30 days', 500, 3, true, 'GNF', true, 'System'),
('SAVE50K', 'Économisez 50,000 GNF', '50,000 GNF de réduction sur votre commande', 1, 50000, 200000, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP + INTERVAL '14 days', 100, 1, true, 'GNF', true, 'System')
ON CONFLICT ("Code") DO NOTHING;

-- Insérer des templates de notifications
INSERT INTO "NotificationTemplates" ("Name", "Code", "Type", "Channel", "Subject", "Body", "Description", "IsActive", "Language", "Variables", "CreatedBy") VALUES
('Bienvenue sur NafaPlace', 'WELCOME_EMAIL', 1, 1, 'Bienvenue sur NafaPlace, {{CustomerName}} ! 🎉', '<h1>Bienvenue sur NafaPlace !</h1><p>Bonjour {{CustomerName}},</p><p>Nous sommes ravis de vous accueillir sur NafaPlace !</p>', 'Email de bienvenue pour les nouveaux utilisateurs', true, 'fr', 'CustomerName,WebsiteUrl', 'System'),
('Confirmation de commande', 'ORDER_CONFIRMATION_EMAIL', 2, 1, 'Commande confirmée #{{OrderNumber}} - {{OrderTotal}} {{Currency}}', '<h1>Votre commande est confirmée ! ✅</h1><p>Bonjour {{CustomerName}},</p><p>Nous avons bien reçu votre commande #{{OrderNumber}}.</p>', 'Email de confirmation de commande', true, 'fr', 'CustomerName,OrderNumber,OrderTotal,Currency', 'System'),
('SMS Confirmation commande', 'ORDER_CONFIRMATION_SMS', 2, 2, 'Commande confirmée', 'NafaPlace: Votre commande #{{OrderNumber}} de {{OrderTotal}} {{Currency}} est confirmée.', 'SMS de confirmation de commande', true, 'fr', 'OrderNumber,OrderTotal,Currency', 'System')
ON CONFLICT ("Code") DO NOTHING;

-- Insérer des zones de livraison pour la Guinée
INSERT INTO "DeliveryZones" ("Name", "Code", "Description", "Type", "Latitude", "Longitude", "Radius", "IsActive", "BaseDeliveryFee", "FreeDeliveryThreshold", "EstimatedDeliveryDays", "MaxDeliveryDays", "SameDayDeliveryAvailable", "SameDayDeliveryFee", "ExpressDeliveryAvailable", "ExpressDeliveryFee", "Currency", "CreatedBy") VALUES
('Conakry Centre', 'CKY_CENTER', 'Centre-ville de Conakry - Livraison rapide', 4, 9.5370, -13.6785, 5, true, 15000, 100000, 1, 2, true, 25000, true, 35000, 'GNF', 'System'),
('Kaloum', 'CKY_KALOUM', 'Commune de Kaloum - Zone administrative', 5, 9.5092, -13.7122, 3, true, 12000, 80000, 1, 1, true, 20000, true, 30000, 'GNF', 'System'),
('Kankan', 'KANKAN', 'Ville de Kankan - Haute Guinée', 4, 10.3853, -9.3064, 10, true, 50000, 300000, 3, 5, false, null, false, null, 'GNF', 'System')
ON CONFLICT ("Code") DO NOTHING;

-- Insérer des transporteurs
INSERT INTO "Carriers" ("Name", "Code", "Description", "ContactEmail", "ContactPhone", "IsActive", "Type", "Rating", "TotalDeliveries", "SuccessfulDeliveries", "CreatedBy") VALUES
('NafaPlace Express', 'NAFA_EXPRESS', 'Service de livraison interne NafaPlace - Rapide et fiable', '<EMAIL>', '+224 123 456 789', true, 1, 4.5, 500, 475, 'System'),
('Guinea Post', 'GUINEA_POST', 'Service postal national de Guinée', '<EMAIL>', '+224 987 654 321', true, 4, 3.8, 200, 180, 'System'),
('Express Guinée', 'EXPRESS_GUINEA', 'Service de livraison express en Guinée', '<EMAIL>', '+224 555 123 456', true, 5, 4.2, 150, 140, 'System')
ON CONFLICT ("Code") DO NOTHING;

PRINT 'Tables des nouvelles fonctionnalités créées avec succès !';
