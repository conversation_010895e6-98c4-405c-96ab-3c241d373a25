using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using NafaPlace.Catalog.Application.Common.Interfaces;
using NafaPlace.Catalog.Application.Interfaces;
using NafaPlace.Catalog.Infrastructure.Persistence;
using NafaPlace.Catalog.Infrastructure.Services;

namespace NafaPlace.Catalog.Infrastructure
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddInfrastructure(
            this IServiceCollection services,
            IConfiguration configuration)
        {
            // Utiliser DATABASE_URL de Fly.io si disponible, sinon utiliser DefaultConnection
            var connectionString = Environment.GetEnvironmentVariable("DATABASE_URL")
                ?? configuration.GetConnectionString("DefaultConnection");

            services.AddDbContext<CatalogDbContext>(options =>
                options.UseNpgsql(connectionString));

            services.AddScoped<ICatalogDbContext>(provider => 
                provider.GetRequiredService<CatalogDbContext>());

            services.AddScoped<IProductImageService, ProductImageService>();
            services.AddScoped<ICategoryImageService, CategoryImageService>();
            services.AddScoped<ICloudinaryService, CloudinaryService>();

            return services;
        }
    }
}
