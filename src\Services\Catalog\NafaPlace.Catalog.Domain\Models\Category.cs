using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using NafaPlace.Catalog.Domain.Common;

namespace NafaPlace.Catalog.Domain.Models
{
    public class Category : BaseEntity
    {
        [Required]
        [MaxLength(100)]
        public required string Name { get; set; }

        [Required]
        [MaxLength(500)]
        public required string Description { get; set; }

        [MaxLength(500)]
        public string? IconUrl { get; set; }

        [Required]
        [MaxLength(500)]
        public required string ImageUrl { get; set; }

        public int? ParentCategoryId { get; set; }
        public Category? ParentCategory { get; set; }

        public bool IsActive { get; set; } = true;

        public List<Category> SubCategories { get; set; } = new();
        public List<Product> Products { get; set; } = new();
    }
}
