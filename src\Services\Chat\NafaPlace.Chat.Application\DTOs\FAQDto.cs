using NafaPlace.Chat.Domain.Enums;

namespace NafaPlace.Chat.Application.DTOs;

public class FAQItemDto
{
    public int Id { get; set; }
    public string Question { get; set; } = string.Empty;
    public string Answer { get; set; } = string.Empty;
    public FAQCategory Category { get; set; }
    public List<string> Tags { get; set; } = new();
    public List<string> Keywords { get; set; } = new();
    public int ViewCount { get; set; }
    public int HelpfulCount { get; set; }
    public int NotHelpfulCount { get; set; }
    public double HelpfulnessRatio { get; set; }
    public bool IsActive { get; set; } = true;
    public bool IsFeatured { get; set; }
    public int Priority { get; set; }
    public string Language { get; set; } = "fr";
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public List<FAQRelatedItemDto> RelatedItems { get; set; } = new();
    public List<FAQAttachmentDto> Attachments { get; set; } = new();
}

public class FAQRelatedItemDto
{
    public int Id { get; set; }
    public string Question { get; set; } = string.Empty;
    public double RelevanceScore { get; set; }
}

public class FAQAttachmentDto
{
    public int Id { get; set; }
    public string FileName { get; set; } = string.Empty;
    public string FileUrl { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public DateTime UploadedAt { get; set; }
}

public class CreateFAQItemDto
{
    public string Question { get; set; } = string.Empty;
    public string Answer { get; set; } = string.Empty;
    public FAQCategory Category { get; set; }
    public List<string> Tags { get; set; } = new();
    public List<string> Keywords { get; set; } = new();
    public bool IsActive { get; set; } = true;
    public bool IsFeatured { get; set; }
    public int Priority { get; set; }
    public string Language { get; set; } = "fr";
    public string CreatedBy { get; set; } = string.Empty;
    public List<FAQAttachmentDto> Attachments { get; set; } = new();
}

public class UpdateFAQItemDto
{
    public int Id { get; set; }
    public string? Question { get; set; }
    public string? Answer { get; set; }
    public FAQCategory? Category { get; set; }
    public List<string>? Tags { get; set; }
    public List<string>? Keywords { get; set; }
    public bool? IsActive { get; set; }
    public bool? IsFeatured { get; set; }
    public int? Priority { get; set; }
    public string UpdatedBy { get; set; } = string.Empty;
}

public class FAQSearchDto
{
    public string Query { get; set; } = string.Empty;
    public FAQCategory? Category { get; set; }
    public List<string>? Tags { get; set; }
    public string? Language { get; set; }
    public bool? IsActive { get; set; }
    public bool? IsFeatured { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 20;
    public string? SortBy { get; set; } = "Relevance";
    public bool SortDescending { get; set; } = true;
}

public class FAQSearchResultDto
{
    public List<FAQItemDto> Items { get; set; } = new();
    public int TotalCount { get; set; }
    public int Page { get; set; }
    public int PageSize { get; set; }
    public string Query { get; set; } = string.Empty;
    public double SearchTime { get; set; }
    public List<string> SuggestedQueries { get; set; } = new();
    public Dictionary<FAQCategory, int> CategoryCounts { get; set; } = new();
}

public class ChatbotDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Avatar { get; set; } = string.Empty;
    public string WelcomeMessage { get; set; } = string.Empty;
    public string FallbackMessage { get; set; } = string.Empty;
    public List<string> SupportedLanguages { get; set; } = new();
    public ChatbotSettings Settings { get; set; } = new();
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public ChatbotStats Stats { get; set; } = new();
}

public class ChatbotSettings
{
    public double ConfidenceThreshold { get; set; } = 0.7;
    public int MaxSuggestions { get; set; } = 3;
    public bool EnableSmallTalk { get; set; } = true;
    public bool EnableHandoff { get; set; } = true;
    public double HandoffThreshold { get; set; } = 0.3;
    public bool EnableLearning { get; set; } = true;
    public bool EnableAnalytics { get; set; } = true;
    public Dictionary<string, object> CustomSettings { get; set; } = new();
}

public class ChatbotStats
{
    public int TotalInteractions { get; set; }
    public int SuccessfulResponses { get; set; }
    public int HandoffsToAgent { get; set; }
    public double SuccessRate { get; set; }
    public double AverageConfidence { get; set; }
    public Dictionary<string, int> TopIntents { get; set; } = new();
    public Dictionary<string, int> TopQuestions { get; set; } = new();
}

public class ChatbotInteractionDto
{
    public int Id { get; set; }
    public string SessionId { get; set; } = string.Empty;
    public string UserId { get; set; } = string.Empty;
    public string UserMessage { get; set; } = string.Empty;
    public string BotResponse { get; set; } = string.Empty;
    public string Intent { get; set; } = string.Empty;
    public double Confidence { get; set; }
    public List<string> Entities { get; set; } = new();
    public bool WasHelpful { get; set; }
    public bool RequiredHandoff { get; set; }
    public DateTime Timestamp { get; set; }
    public Dictionary<string, object> Context { get; set; } = new();
}

public class ChatbotTrainingDataDto
{
    public int Id { get; set; }
    public string Intent { get; set; } = string.Empty;
    public List<string> Examples { get; set; } = new();
    public List<string> Responses { get; set; } = new();
    public List<EntityDefinitionDto> Entities { get; set; } = new();
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class EntityDefinitionDto
{
    public string Name { get; set; } = string.Empty;
    public EntityType Type { get; set; }
    public List<string> Values { get; set; } = new();
    public List<string> Synonyms { get; set; } = new();
}

public class ChatbotResponseDto
{
    public string Message { get; set; } = string.Empty;
    public string Intent { get; set; } = string.Empty;
    public double Confidence { get; set; }
    public List<string> Suggestions { get; set; } = new();
    public List<FAQItemDto> RelatedFAQs { get; set; } = new();
    public bool RequiresHandoff { get; set; }
    public Dictionary<string, object> Context { get; set; } = new();
    public List<QuickActionDto> QuickActions { get; set; } = new();
}

public class QuickActionDto
{
    public string Id { get; set; } = string.Empty;
    public string Label { get; set; } = string.Empty;
    public string Action { get; set; } = string.Empty;
    public Dictionary<string, object> Parameters { get; set; } = new();
}

public class KnowledgeBaseDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<KnowledgeArticleDto> Articles { get; set; } = new();
    public List<FAQItemDto> FAQs { get; set; } = new();
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public KnowledgeBaseStats Stats { get; set; } = new();
}

public class KnowledgeArticleDto
{
    public int Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public string Summary { get; set; } = string.Empty;
    public ArticleCategory Category { get; set; }
    public List<string> Tags { get; set; } = new();
    public int ViewCount { get; set; }
    public int HelpfulCount { get; set; }
    public int NotHelpfulCount { get; set; }
    public bool IsPublished { get; set; } = true;
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public string Author { get; set; } = string.Empty;
}

public class KnowledgeBaseStats
{
    public int TotalArticles { get; set; }
    public int TotalFAQs { get; set; }
    public int TotalViews { get; set; }
    public int TotalSearches { get; set; }
    public double AverageHelpfulness { get; set; }
    public Dictionary<string, int> PopularTopics { get; set; } = new();
}

public class SmartSuggestionDto
{
    public string Text { get; set; } = string.Empty;
    public SuggestionType Type { get; set; }
    public double Confidence { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class ConversationAnalysisDto
{
    public int ConversationId { get; set; }
    public string Summary { get; set; } = string.Empty;
    public List<string> KeyTopics { get; set; } = new();
    public ConversationSentiment Sentiment { get; set; }
    public List<string> SuggestedFAQs { get; set; } = new();
    public List<string> SuggestedActions { get; set; } = new();
    public double UrgencyScore { get; set; }
    public List<string> DetectedIssues { get; set; } = new();
}

public class AutoResponseDto
{
    public string Response { get; set; } = string.Empty;
    public double Confidence { get; set; }
    public string Source { get; set; } = string.Empty; // FAQ, Knowledge Base, AI
    public List<string> References { get; set; } = new();
    public bool RequiresApproval { get; set; }
}

// Enums
public enum FAQCategory
{
    General,
    Account,
    Orders,
    Payments,
    Shipping,
    Returns,
    Products,
    Technical,
    Billing,
    Security,
    Privacy,
    Policies
}

public enum EntityType
{
    Text,
    Number,
    Date,
    Email,
    Phone,
    URL,
    Custom
}

public enum ArticleCategory
{
    GettingStarted,
    UserGuide,
    Troubleshooting,
    BestPractices,
    API,
    Integration,
    Security,
    Policies
}

public enum SuggestionType
{
    FAQ,
    Article,
    QuickReply,
    Action,
    Escalation
}

public enum ConversationSentiment
{
    Positive,
    Neutral,
    Negative,
    Frustrated,
    Satisfied,
    Confused
}
