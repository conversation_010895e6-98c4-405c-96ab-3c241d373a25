#!/bin/bash

# Script de déploiement Docker pour NafaPlace
# Utilisation: ./scripts/deploy-docker.sh

echo "🚀 Déploiement de NafaPlace avec Docker"
echo "======================================="

# Vérifier que Docker est en cours d'exécution
echo "🔍 Vérification de Docker..."
if ! command -v docker &> /dev/null || ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker ou Docker Compose n'est pas installé"
    exit 1
fi

if ! docker info &> /dev/null; then
    echo "❌ Docker n'est pas en cours d'exécution"
    exit 1
fi

echo "✅ Docker et Docker Compose sont prêts"

# Nettoyer les conteneurs et volumes existants
echo "🧹 Nettoyage des conteneurs existants..."
docker-compose down -v --remove-orphans

# Supprimer les images existantes pour forcer la reconstruction
echo "🗑️ Suppression des images existantes..."
docker rmi $(docker images "lamyas92/nafaplace-*" -q) 2>/dev/null || true

# Construire et démarrer tous les services
echo "🔨 Construction et démarrage des services..."
docker-compose up --build -d

# Attendre que les services soient prêts
echo "⏳ Attente du démarrage des services..."
sleep 30

# Vérifier l'état des services
echo "🔍 Vérification de l'état des services..."
docker-compose ps

# Tester les endpoints de santé
echo "🏥 Test des endpoints de santé..."

services=(
    "API Gateway:http://localhost:5000/health"
    "Identity API:http://localhost:5155/health"
    "Catalog API:http://localhost:5243/health"
    "Cart API:http://localhost:5003/health"
    "Order API:http://localhost:5004/health"
    "Payment API:http://localhost:5005/health"
    "Reviews API:http://localhost:5006/health"
    "Notifications API:http://localhost:5007/health"
)

for service in "${services[@]}"; do
    IFS=':' read -r name url <<< "$service"
    if curl -s --max-time 10 "$url" > /dev/null; then
        echo "✅ $name - OK"
    else
        echo "❌ $name - Non accessible"
    fi
done

# Afficher les URLs importantes
echo ""
echo "🌐 URLs importantes:"
echo "API Gateway: http://localhost:5000"
echo "API Gateway Swagger: http://localhost:5000/swagger"
echo "Web App: http://localhost:8080"
echo "Admin Portal: http://localhost:8081"
echo "Seller Portal: http://localhost:8082"
echo "PgAdmin: http://localhost:5050 (<EMAIL> / admin)"

# Afficher les informations de base de données
echo ""
echo "💾 Bases de données:"
echo "PostgreSQL Catalog: localhost:5432"
echo "PostgreSQL Identity: localhost:5433"
echo "PostgreSQL Order: localhost:5434"
echo "PostgreSQL Reviews: localhost:5435"
echo "PostgreSQL Notifications: localhost:5436"
echo "Redis: localhost:6379"

# Afficher les logs en cas de problème
echo ""
echo "📋 Pour voir les logs:"
echo "docker-compose logs -f [service-name]"
echo "Exemple: docker-compose logs -f api-gateway"

echo ""
echo "🎉 Déploiement terminé!"
echo "La plateforme NafaPlace est maintenant accessible avec toutes les fonctionnalités en GNF"
