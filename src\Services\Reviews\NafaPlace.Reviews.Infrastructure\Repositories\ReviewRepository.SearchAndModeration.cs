using Microsoft.EntityFrameworkCore;
using NafaPlace.Reviews.Application.Interfaces;
using NafaPlace.Reviews.Domain.Models;
using NafaPlace.Reviews.Infrastructure.Data;

namespace NafaPlace.Reviews.Infrastructure.Repositories;

public partial class ReviewRepository : IReviewRepository
{
    // Les méthodes pour les fonctionnalités de recherche et filtrage
    
    public async Task<IEnumerable<Review>> SearchReviewsAsync(string searchTerm, int? productId = null, int? minRating = null, int? maxRating = null, bool? isVerifiedOnly = null, int page = 1, int pageSize = 10)
    {
        var query = _context.Reviews.Include(r => r.ReviewHelpfuls).Include(r => r.Replies).AsQueryable();
        
        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            query = query.Where(r => 
                r.Title.Contains(searchTerm) || 
                r.Comment.Contains(searchTerm) ||
                r.UserName.Contains(searchTerm));
        }
        
        if (productId.HasValue)
        {
            query = query.Where(r => r.ProductId == productId.Value);
        }
        
        if (minRating.HasValue)
        {
            query = query.Where(r => r.Rating >= minRating.Value);
        }
        
        if (maxRating.HasValue)
        {
            query = query.Where(r => r.Rating <= maxRating.Value);
        }
        
        if (isVerifiedOnly.HasValue && isVerifiedOnly.Value)
        {
            query = query.Where(r => r.IsVerifiedPurchase);
        }
        
        // Only show approved reviews in search
        query = query.Where(r => r.IsApproved);
        
        return await query
            .OrderByDescending(r => r.CreatedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();
    }
    
    public async Task<int> CountSearchResultsAsync(string searchTerm, int? productId = null, int? minRating = null, int? maxRating = null, bool? isVerifiedOnly = null)
    {
        var query = _context.Reviews.AsQueryable();
        
        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            query = query.Where(r => 
                r.Title.Contains(searchTerm) || 
                r.Comment.Contains(searchTerm) ||
                r.UserName.Contains(searchTerm));
        }
        
        if (productId.HasValue)
        {
            query = query.Where(r => r.ProductId == productId.Value);
        }
        
        if (minRating.HasValue)
        {
            query = query.Where(r => r.Rating >= minRating.Value);
        }
        
        if (maxRating.HasValue)
        {
            query = query.Where(r => r.Rating <= maxRating.Value);
        }
        
        if (isVerifiedOnly.HasValue && isVerifiedOnly.Value)
        {
            query = query.Where(r => r.IsVerifiedPurchase);
        }
        
        // Only count approved reviews in search
        query = query.Where(r => r.IsApproved);
        
        return await query.CountAsync();
    }
    
    // Les méthodes pour les fonctionnalités de modération
    
    public async Task<IEnumerable<Review>> GetReviewsByStatusAsync(ReviewStatus status, int page = 1, int pageSize = 10)
    {
        return await _context.Reviews
            .Include(r => r.ReviewHelpfuls)
            .Include(r => r.Replies)
            .Include(r => r.Reports)
            .Where(r => r.Status == status)
            .OrderByDescending(r => r.CreatedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();
    }
    
    public async Task<int> CountReviewsByStatusAsync(ReviewStatus status)
    {
        return await _context.Reviews
            .CountAsync(r => r.Status == status);
    }
    
    public async Task<Review> UpdateReviewStatusAsync(int id, ReviewStatus status)
    {
        var review = await _context.Reviews.FindAsync(id);
        if (review != null)
        {
            review.Status = status;
            review.UpdatedAt = DateTime.UtcNow;
            
            // If status is published, set IsApproved to true
            if (status == ReviewStatus.Published)
            {
                review.IsApproved = true;
            }
            // If status is rejected, set IsApproved to false
            else if (status == ReviewStatus.Rejected)
            {
                review.IsApproved = false;
            }
            
            _context.Reviews.Update(review);
            await _context.SaveChangesAsync();
        }
        
        return review;
    }
}
