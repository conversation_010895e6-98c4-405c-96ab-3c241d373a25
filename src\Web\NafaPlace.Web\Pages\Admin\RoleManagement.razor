@page "/admin/roles"
@using Microsoft.AspNetCore.Components.Authorization
@using NafaPlace.Web.Models.Auth
@using NafaPlace.Web.Services
@using Microsoft.AspNetCore.Authorization
@inject IAuthService AuthService
@inject NavigationManager NavigationManager
@attribute [Authorize(Roles = "Admin")]

<PageTitle>NafaPlace - Gestion des Rôles</PageTitle>

<div class="container-fluid py-4">
    <h2 class="mb-4">Gestion des Rôles</h2>

    @if (isLoading)
    {
        <div class="d-flex justify-content-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Chargement...</span>
            </div>
        </div>
    }
    else
    {
        @if (!string.IsNullOrEmpty(errorMessage))
        {
            <div class="alert alert-danger" role="alert">
                @errorMessage
            </div>
        }

        @if (!string.IsNullOrEmpty(successMessage))
        {
            <div class="alert alert-success" role="alert">
                @successMessage
            </div>
        }

        <div class="row">
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Liste des Rôles</h5>
                    </div>
                    <div class="card-body">
                        @if (roles.Count == 0)
                        {
                            <p class="text-muted">Aucun rôle trouvé.</p>
                        }
                        else
                        {
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Nom</th>
                                            <th>Description</th>
                                            <th>Date de création</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var role in roles)
                                        {
                                            <tr>
                                                <td>@role.Name</td>
                                                <td>@role.Description</td>
                                                <td>@role.CreatedAt.ToShortDateString()</td>
                                                <td>
                                                    <button class="btn btn-sm btn-primary me-1" @onclick="() => EditRole(role)">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-danger" @onclick="() => DeleteRoleConfirmation(role)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        }
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">@(isEditing ? "Modifier le Rôle" : "Créer un Nouveau Rôle")</h5>
                    </div>
                    <div class="card-body">
                        <EditForm Model="@currentRole" OnValidSubmit="SaveRole">
                            <DataAnnotationsValidator />
                            <ValidationSummary />

                            <div class="mb-3">
                                <label for="roleName" class="form-label">Nom du Rôle</label>
                                <InputText id="roleName" @bind-Value="currentRole.Name" class="form-control" />
                                <ValidationMessage For="@(() => currentRole.Name)" />
                            </div>

                            <div class="mb-3">
                                <label for="roleDescription" class="form-label">Description</label>
                                <InputTextArea id="roleDescription" @bind-Value="currentRole.Description" class="form-control" rows="3" />
                            </div>

                            <div class="d-flex justify-content-between">
                                <button type="submit" class="btn btn-primary">
                                    @(isEditing ? "Mettre à jour" : "Créer")
                                </button>
                                @if (isEditing)
                                {
                                    <button type="button" class="btn btn-secondary" @onclick="CancelEdit">
                                        Annuler
                                    </button>
                                }
                            </div>
                        </EditForm>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Assigner un Rôle à un Utilisateur</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="userEmail" class="form-label">Email de l'Utilisateur</label>
                            <input type="email" id="userEmail" class="form-control" @bind="userEmail" placeholder="<EMAIL>" />
                        </div>

                        <div class="mb-3">
                            <label for="roleSelect" class="form-label">Rôle à Assigner</label>
                            <select id="roleSelect" class="form-select" @bind="selectedRoleId">
                                <option value="">Sélectionnez un rôle</option>
                                @foreach (var role in roles)
                                {
                                    <option value="@role.Id">@role.Name</option>
                                }
                            </select>
                        </div>

                        <button type="button" class="btn btn-success" @onclick="AssignRoleToUser" disabled="@(string.IsNullOrEmpty(userEmail) || selectedRoleId == 0)">
                            Assigner le Rôle
                        </button>
                    </div>
                </div>
            </div>
        </div>
    }

    @if (showDeleteConfirmation)
    {
        <div class="modal fade show" style="display: block; background-color: rgba(0,0,0,0.5);" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Confirmation de suppression</h5>
                        <button type="button" class="btn-close" @onclick="() => showDeleteConfirmation = false"></button>
                    </div>
                    <div class="modal-body">
                        <p>Êtes-vous sûr de vouloir supprimer le rôle "@roleToDelete?.Name" ?</p>
                        <p class="text-danger">Cette action est irréversible.</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" @onclick="() => showDeleteConfirmation = false">Annuler</button>
                        <button type="button" class="btn btn-danger" @onclick="DeleteRole">Supprimer</button>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@code {
    private List<RoleDto> roles = new List<RoleDto>();
    private RoleDto currentRole = new RoleDto();
    private bool isLoading = true;
    private bool isEditing = false;
    private string errorMessage = string.Empty;
    private string successMessage = string.Empty;
    private bool showDeleteConfirmation = false;
    private RoleDto? roleToDelete;
    private string userEmail = string.Empty;
    private int selectedRoleId = 0;

    protected override async Task OnInitializedAsync()
    {
        await LoadRoles();
    }

    private async Task LoadRoles()
    {
        try
        {
            isLoading = true;
            errorMessage = string.Empty;
            successMessage = string.Empty;

            roles = await AuthService.GetAllRolesAsync();
        }
        catch (Exception ex)
        {
            errorMessage = $"Erreur lors du chargement des rôles: {ex.Message}";
            Console.WriteLine(errorMessage);
        }
        finally
        {
            isLoading = false;
        }
    }

    private void EditRole(RoleDto role)
    {
        currentRole = new RoleDto
        {
            Id = role.Id,
            Name = role.Name,
            Description = role.Description,
            CreatedAt = role.CreatedAt,
            UpdatedAt = role.UpdatedAt
        };
        isEditing = true;
    }

    private void CancelEdit()
    {
        currentRole = new RoleDto();
        isEditing = false;
    }

    private void SaveRole()
    {
        try
        {
            isLoading = true;
            errorMessage = string.Empty;
            successMessage = string.Empty;

            // Pour l'instant, nous allons simplement simuler la création/mise à jour d'un rôle
            // Dans une implémentation réelle, vous appelleriez l'API pour créer ou mettre à jour le rôle
            if (isEditing)
            {
                // Simuler la mise à jour d'un rôle existant
                var existingRole = roles.FirstOrDefault(r => r.Id == currentRole.Id);
                if (existingRole != null)
                {
                    existingRole.Name = currentRole.Name;
                    existingRole.Description = currentRole.Description;
                    existingRole.UpdatedAt = DateTime.Now;
                    successMessage = $"Le rôle '{currentRole.Name}' a été mis à jour avec succès.";
                }
            }
            else
            {
                // Simuler la création d'un nouveau rôle
                var newRole = new RoleDto
                {
                    Id = roles.Count > 0 ? roles.Max(r => r.Id) + 1 : 1,
                    Name = currentRole.Name,
                    Description = currentRole.Description,
                    CreatedAt = DateTime.Now
                };
                roles.Add(newRole);
                successMessage = $"Le rôle '{currentRole.Name}' a été créé avec succès.";
            }

            // Réinitialiser le formulaire
            currentRole = new RoleDto();
            isEditing = false;
        }
        catch (Exception ex)
        {
            errorMessage = $"Erreur lors de l'enregistrement du rôle: {ex.Message}";
            Console.WriteLine(errorMessage);
        }
        finally
        {
            isLoading = false;
        }
    }

    private void DeleteRoleConfirmation(RoleDto role)
    {
        roleToDelete = role;
        showDeleteConfirmation = true;
    }

    private void DeleteRole()
    {
        try
        {
            if (roleToDelete != null)
            {
                isLoading = true;
                errorMessage = string.Empty;
                successMessage = string.Empty;

                // Simuler la suppression d'un rôle
                roles.Remove(roleToDelete);
                successMessage = $"Le rôle '{roleToDelete.Name}' a été supprimé avec succès.";
                roleToDelete = null;
                showDeleteConfirmation = false;
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Erreur lors de la suppression du rôle: {ex.Message}";
            Console.WriteLine(errorMessage);
        }
        finally
        {
            isLoading = false;
        }
    }

    private void AssignRoleToUser()
    {
        try
        {
            isLoading = true;
            errorMessage = string.Empty;
            successMessage = string.Empty;

            if (string.IsNullOrEmpty(userEmail))
            {
                errorMessage = "Veuillez saisir l'email de l'utilisateur.";
                return;
            }

            if (selectedRoleId == 0)
            {
                errorMessage = "Veuillez sélectionner un rôle à assigner.";
                return;
            }

            // Simuler l'assignation d'un rôle à un utilisateur
            var roleName = roles.FirstOrDefault(r => r.Id == selectedRoleId)?.Name;
            successMessage = $"Le rôle '{roleName}' a été assigné à l'utilisateur '{userEmail}' avec succès.";

            // Réinitialiser le formulaire
            userEmail = string.Empty;
            selectedRoleId = 0;
        }
        catch (Exception ex)
        {
            errorMessage = $"Erreur lors de l'assignation du rôle: {ex.Message}";
            Console.WriteLine(errorMessage);
        }
        finally
        {
            isLoading = false;
        }
    }
}
