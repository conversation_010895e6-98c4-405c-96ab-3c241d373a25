namespace NafaPlace.Web.Models.Catalog;

public class ProductDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string ShortDescription { get; set; } = string.Empty;
    public decimal Price { get; set; }
    public decimal? OldPrice { get; set; }
    public int DiscountPercentage { get; set; }
    public string Currency { get; set; } = "GNF";
    public int Stock { get; set; }
    public double Rating { get; set; }
    public int ReviewCount { get; set; }
    public string Brand { get; set; } = string.Empty;
    public int CategoryId { get; set; }
    public CategoryDto? Category { get; set; }
    public List<ProductImageDto> Images { get; set; } = new List<ProductImageDto>();
    public string MainImageUrl => Images.FirstOrDefault(i => i.IsMain)?.Url ?? Images.FirstOrDefault()?.Url ?? string.Empty;
    public List<ProductAttributeDto> Attributes { get; set; } = new List<ProductAttributeDto>();
    public List<ProductVariantDto> Variants { get; set; } = new List<ProductVariantDto>();
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }

    // Propriétés calculées
    public bool HasDiscount => OldPrice.HasValue && OldPrice > Price;
    public string CategoryName => Category?.Name ?? string.Empty;
    public int CurrentStock => Stock;
}
