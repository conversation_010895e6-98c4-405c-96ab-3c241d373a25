FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

COPY ["src/Web/AdminPortal/NafaPlace.AdminPortal/NafaPlace.AdminPortal.csproj", "src/Web/AdminPortal/NafaPlace.AdminPortal/"]
RUN dotnet restore "src/Web/AdminPortal/NafaPlace.AdminPortal/NafaPlace.AdminPortal.csproj"

COPY . .
WORKDIR "/src/src/Web/AdminPortal/NafaPlace.AdminPortal"
RUN dotnet build "NafaPlace.AdminPortal.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "NafaPlace.AdminPortal.csproj" -c Release -o /app/publish

FROM nginx:alpine AS final
WORKDIR /usr/share/nginx/html
COPY --from=publish /app/publish/wwwroot .
COPY src/Web/AdminPortal/nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
EXPOSE 443