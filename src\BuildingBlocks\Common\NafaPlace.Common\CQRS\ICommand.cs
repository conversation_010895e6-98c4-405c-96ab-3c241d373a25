using MediatR;

namespace NafaPlace.Common.CQRS;

/// <summary>
/// Interface de base pour toutes les commandes
/// </summary>
public interface ICommand : IRequest
{
    /// <summary>
    /// Identifiant unique de la commande pour le tracking
    /// </summary>
    int CommandId { get; }
    
    /// <summary>
    /// Timestamp de création de la commande
    /// </summary>
    DateTime CreatedAt { get; }
    
    /// <summary>
    /// Identifiant de l'utilisateur qui exécute la commande
    /// </summary>
    string? UserId { get; }
    
    /// <summary>
    /// Métadonnées additionnelles pour l'audit
    /// </summary>
    Dictionary<string, object>? Metadata { get; }
}

/// <summary>
/// Interface de base pour toutes les commandes avec résultat
/// </summary>
/// <typeparam name="TResult">Type du résultat</typeparam>
public interface ICommand<TResult> : IRequest<TResult>
{
    /// <summary>
    /// Identifiant unique de la commande pour le tracking
    /// </summary>
    int CommandId { get; }
    
    /// <summary>
    /// Timestamp de création de la commande
    /// </summary>
    DateTime CreatedAt { get; }
    
    /// <summary>
    /// Identifiant de l'utilisateur qui exécute la commande
    /// </summary>
    string? UserId { get; }
    
    /// <summary>
    /// Métadonnées additionnelles pour l'audit
    /// </summary>
    Dictionary<string, object>? Metadata { get; }
}

/// <summary>
/// Classe de base pour les commandes
/// </summary>
public abstract class BaseCommand : ICommand
{
    protected BaseCommand()
    {
        CommandId = Random.Shared.Next(1, int.MaxValue);
        CreatedAt = DateTime.UtcNow;
        Metadata = new Dictionary<string, object>();
    }

    public int CommandId { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public string? UserId { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}

/// <summary>
/// Classe de base pour les commandes avec résultat
/// </summary>
/// <typeparam name="TResult">Type du résultat</typeparam>
public abstract class BaseCommand<TResult> : ICommand<TResult>
{
    protected BaseCommand()
    {
        CommandId = Random.Shared.Next(1, int.MaxValue);
        CreatedAt = DateTime.UtcNow;
        Metadata = new Dictionary<string, object>();
    }

    public int CommandId { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public string? UserId { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}
