@using NafaPlace.Web.Services
@using NafaPlace.Web.Models.Catalog
@inject IAdvancedSearchService AdvancedSearchService
@inject ICategoryService CategoryService

<div class="advanced-filters">
    <div class="filters-header d-flex justify-content-between align-items-center mb-3">
        <h6 class="mb-0">
            <i class="bi bi-funnel me-2"></i>Filtres avancés
        </h6>
        <button class="btn btn-sm btn-outline-secondary" @onclick="ClearAllFilters">
            <i class="bi bi-x-circle me-1"></i>Effacer tout
        </button>
    </div>

    <!-- Filtres de prix -->
    <div class="filter-section mb-4">
        <h6 class="filter-title">Prix (GNF)</h6>
        <div class="row g-2">
            <div class="col-6">
                <input type="number" class="form-control form-control-sm" 
                       placeholder="Min" @bind="MinPrice" @bind:event="onchange" />
            </div>
            <div class="col-6">
                <input type="number" class="form-control form-control-sm" 
                       placeholder="Max" @bind="MaxPrice" @bind:event="onchange" />
            </div>
        </div>
        
        <!-- Ranges de prix prédéfinis -->
        <div class="price-ranges mt-2">
            @foreach (var range in priceRanges)
            {
                <button class="btn btn-sm btn-outline-primary me-1 mb-1" 
                        @onclick="() => SetPriceRange(range.Min, range.Max)">
                    @range.Label
                </button>
            }
        </div>
    </div>

    <!-- Filtres de catégories -->
    <div class="filter-section mb-4">
        <h6 class="filter-title">Catégories</h6>
        <div class="category-filters">
            @if (categories.Any())
            {
                @foreach (var category in categories.Take(showAllCategories ? categories.Count : 5))
                {
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" 
                               id="<EMAIL>"
                               checked="@(SelectedCategoryIds?.Contains(category.Id) == true)"
                               @onchange="(e) => ToggleCategory(category.Id, (bool)e.Value!)" />
                        <label class="form-check-label" for="<EMAIL>">
                            @category.Name
                        </label>
                    </div>
                }
                
                @if (categories.Count > 5)
                {
                    <button class="btn btn-sm btn-link p-0 mt-1" @onclick="() => showAllCategories = !showAllCategories">
                        @(showAllCategories ? "Voir moins" : $"Voir {categories.Count - 5} de plus")
                    </button>
                }
            }
        </div>
    </div>

    <!-- Filtres de marques -->
    <div class="filter-section mb-4">
        <h6 class="filter-title">Marques</h6>
        <div class="brand-search mb-2">
            <input type="text" class="form-control form-control-sm" 
                   placeholder="Rechercher une marque..." 
                   @bind="brandSearchQuery" @bind:event="oninput" />
        </div>
        <div class="brand-filters" style="max-height: 200px; overflow-y: auto;">
            @foreach (var brand in filteredBrands.Take(showAllBrands ? filteredBrands.Count : 8))
            {
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" 
                           id="<EMAIL>(" ", "")"
                           checked="@(SelectedBrands?.Contains(brand) == true)"
                           @onchange="(e) => ToggleBrand(brand, (bool)e.Value!)" />
                    <label class="form-check-label" for="<EMAIL>(" ", "")">
                        @brand
                    </label>
                </div>
            }
            
            @if (filteredBrands.Count > 8)
            {
                <button class="btn btn-sm btn-link p-0 mt-1" @onclick="() => showAllBrands = !showAllBrands">
                    @(showAllBrands ? "Voir moins" : $"Voir {filteredBrands.Count - 8} de plus")
                </button>
            }
        </div>
    </div>

    <!-- Filtres de notation -->
    <div class="filter-section mb-4">
        <h6 class="filter-title">Note minimum</h6>
        <div class="rating-filters">
            @for (int i = 5; i >= 1; i--)
            {
                var rating = i;
                <div class="form-check">
                    <input class="form-check-input" type="radio" 
                           name="rating" id="rating-@rating"
                           checked="@(MinRating == rating)"
                           @onchange="() => SetMinRating(rating)" />
                    <label class="form-check-label d-flex align-items-center" for="rating-@rating">
                        @for (int j = 1; j <= 5; j++)
                        {
                            <i class="bi @(j <= rating ? "bi-star-fill" : "bi-star") text-warning me-1"></i>
                        }
                        <span class="ms-1">et plus</span>
                    </label>
                </div>
            }
        </div>
    </div>

    <!-- Autres filtres -->
    <div class="filter-section mb-4">
        <h6 class="filter-title">Autres options</h6>
        
        <div class="form-check">
            <input class="form-check-input" type="checkbox" id="inStock"
                   @bind="InStockOnly" @bind:event="onchange" />
            <label class="form-check-label" for="inStock">
                En stock uniquement
            </label>
        </div>
        
        <div class="form-check">
            <input class="form-check-input" type="checkbox" id="hasDiscount"
                   @bind="HasDiscount" @bind:event="onchange" />
            <label class="form-check-label" for="hasDiscount">
                En promotion
            </label>
        </div>
        
        <div class="form-check">
            <input class="form-check-input" type="checkbox" id="freeShipping"
                   @bind="HasFreeShipping" @bind:event="onchange" />
            <label class="form-check-label" for="freeShipping">
                Livraison gratuite
            </label>
        </div>
        
        <div class="form-check">
            <input class="form-check-input" type="checkbox" id="newArrival"
                   @bind="IsNewArrival" @bind:event="onchange" />
            <label class="form-check-label" for="newArrival">
                Nouveautés (30 derniers jours)
            </label>
        </div>
    </div>

    <!-- Boutons d'action -->
    <div class="filter-actions">
        <button class="btn btn-primary w-100 mb-2" @onclick="ApplyFilters">
            <i class="bi bi-check2 me-1"></i>Appliquer les filtres
        </button>
        <div class="text-center">
            <small class="text-muted">@GetActiveFiltersCount() filtre(s) actif(s)</small>
        </div>
    </div>
</div>

<style>
    .advanced-filters {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
    }

    .filter-title {
        color: #495057;
        font-size: 0.9rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        border-bottom: 1px solid #dee2e6;
        padding-bottom: 0.25rem;
    }

    .filter-section {
        border-bottom: 1px solid #e9ecef;
        padding-bottom: 1rem;
    }

    .filter-section:last-child {
        border-bottom: none;
        padding-bottom: 0;
    }

    .form-check {
        margin-bottom: 0.25rem;
    }

    .form-check-label {
        font-size: 0.875rem;
        cursor: pointer;
    }

    .price-ranges .btn {
        font-size: 0.75rem;
    }

    .brand-search input {
        border-radius: 4px;
    }
</style>

@code {
    [Parameter] public decimal? MinPrice { get; set; }
    [Parameter] public decimal? MaxPrice { get; set; }
    [Parameter] public List<int>? SelectedCategoryIds { get; set; }
    [Parameter] public List<string>? SelectedBrands { get; set; }
    [Parameter] public decimal? MinRating { get; set; }
    [Parameter] public bool InStockOnly { get; set; }
    [Parameter] public bool? HasDiscount { get; set; }
    [Parameter] public bool? HasFreeShipping { get; set; }
    [Parameter] public bool? IsNewArrival { get; set; }
    
    [Parameter] public EventCallback OnFiltersChanged { get; set; }

    private List<CategoryDto> categories = new();
    private List<string> availableBrands = new();
    private List<string> filteredBrands = new();
    private bool showAllCategories = false;
    private bool showAllBrands = false;

    private readonly List<PriceRange> priceRanges = new()
    {
        new("< 50K", null, 50000),
        new("50K - 100K", 50000, 100000),
        new("100K - 200K", 100000, 200000),
        new("200K - 500K", 200000, 500000),
        new("> 500K", 500000, null)
    };

    protected override async Task OnInitializedAsync()
    {
        await LoadCategories();
        await LoadAvailableBrands();
        UpdateFilteredBrands();
    }

    private async Task LoadCategories()
    {
        try
        {
            categories = (await CategoryService.GetAllCategoriesAsync()).ToList();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des catégories: {ex.Message}");
        }
    }

    private async Task LoadAvailableBrands()
    {
        try
        {
            var filters = await AdvancedSearchService.GetAvailableFiltersAsync();
            if (filters.ContainsKey("brands"))
            {
                availableBrands = filters["brands"];
                UpdateFilteredBrands();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des marques: {ex.Message}");
        }
    }

    private void UpdateFilteredBrands()
    {
        filteredBrands = string.IsNullOrEmpty(brandSearchQuery)
            ? availableBrands
            : availableBrands.Where(b => b.Contains(brandSearchQuery, StringComparison.OrdinalIgnoreCase)).ToList();

        StateHasChanged();
    }

    // Déclencher la mise à jour des marques filtrées quand la recherche change
    private string _brandSearchQuery = "";
    private string brandSearchQuery
    {
        get => _brandSearchQuery;
        set
        {
            if (_brandSearchQuery != value)
            {
                _brandSearchQuery = value;
                UpdateFilteredBrands();
            }
        }
    }

    private void SetPriceRange(decimal? min, decimal? max)
    {
        MinPrice = min;
        MaxPrice = max;
        _ = OnFiltersChanged.InvokeAsync();
    }

    private void ToggleCategory(int categoryId, bool isSelected)
    {
        SelectedCategoryIds ??= new List<int>();
        
        if (isSelected)
        {
            if (!SelectedCategoryIds.Contains(categoryId))
                SelectedCategoryIds.Add(categoryId);
        }
        else
        {
            SelectedCategoryIds.Remove(categoryId);
        }
        
        _ = OnFiltersChanged.InvokeAsync();
    }

    private void ToggleBrand(string brand, bool isSelected)
    {
        SelectedBrands ??= new List<string>();
        
        if (isSelected)
        {
            if (!SelectedBrands.Contains(brand))
                SelectedBrands.Add(brand);
        }
        else
        {
            SelectedBrands.Remove(brand);
        }
        
        _ = OnFiltersChanged.InvokeAsync();
    }

    private void SetMinRating(decimal rating)
    {
        MinRating = MinRating == rating ? null : rating;
        _ = OnFiltersChanged.InvokeAsync();
    }

    private async Task ApplyFilters()
    {
        await OnFiltersChanged.InvokeAsync();
    }

    private void ClearAllFilters()
    {
        MinPrice = null;
        MaxPrice = null;
        SelectedCategoryIds?.Clear();
        SelectedBrands?.Clear();
        MinRating = null;
        InStockOnly = false;
        HasDiscount = null;
        HasFreeShipping = null;
        IsNewArrival = null;
        
        _ = OnFiltersChanged.InvokeAsync();
    }

    private int GetActiveFiltersCount()
    {
        int count = 0;
        
        if (MinPrice.HasValue || MaxPrice.HasValue) count++;
        if (SelectedCategoryIds?.Any() == true) count++;
        if (SelectedBrands?.Any() == true) count++;
        if (MinRating.HasValue) count++;
        if (InStockOnly) count++;
        if (HasDiscount == true) count++;
        if (HasFreeShipping == true) count++;
        if (IsNewArrival == true) count++;
        
        return count;
    }

    private record PriceRange(string Label, decimal? Min, decimal? Max);
}
