events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Compression générale - exclure les fichiers WASM pour éviter les erreurs SRI
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        # Health check endpoint
        location = /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        # Configuration pour les fichiers du framework Blazor
        location /_framework/ {
            # Désactiver la compression dynamique pour éviter les conflits SRI
            gzip off;

            # Configuration spécifique pour les fichiers WASM - servir uniquement les versions non compressées
            location ~* \.wasm$ {
                add_header Content-Type application/wasm;
                add_header Access-Control-Allow-Origin "*" always;
                add_header Cache-Control "public, max-age=31536000, immutable";
                # Servir uniquement le fichier WASM original, pas les versions compressées
                try_files $uri =404;
            }

            # Configuration pour les autres fichiers du framework
            add_header Cache-Control "public, max-age=31536000, immutable";
            add_header Access-Control-Allow-Origin "*" always;
            try_files $uri =404;
        }

        # Configuration générale pour tous les autres fichiers
        location / {
            try_files $uri $uri/ /index.html;

            # CORS headers
            add_header Access-Control-Allow-Origin "*" always;
            add_header Access-Control-Allow-Methods "GET, POST, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Content-Type, Range, If-Range" always;
        }
    }
}
