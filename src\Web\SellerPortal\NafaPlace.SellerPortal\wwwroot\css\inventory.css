/* Styles CSS pour la page Inventory */

/* Cartes de statistiques */
.inventory-stats-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    border: none;
    border-radius: 12px;
    overflow: hidden;
}

.inventory-stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.inventory-stats-card .card-body {
    padding: 1.5rem;
}

.inventory-stats-card .card-footer {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    padding: 0.75rem 1.5rem;
}

.inventory-stats-card .card-footer a {
    text-decoration: none;
    font-weight: 500;
}

/* Badges de statut de stock */
.stock-badge {
    font-size: 0.875rem;
    font-weight: 600;
    padding: 0.375rem 0.75rem;
    border-radius: 6px;
}

.stock-badge.stock-normal {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.stock-badge.stock-low {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.stock-badge.stock-out {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Alertes */
.alert-row {
    transition: background-color 0.2s ease-in-out;
}

.alert-row:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.alert-badge {
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Mouvements de stock */
.movement-badge {
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.movement-quantity-positive {
    color: #28a745;
    font-weight: 700;
}

.movement-quantity-negative {
    color: #dc3545;
    font-weight: 700;
}

/* Modal de mise à jour en lot */
.bulk-update-modal .modal-dialog {
    max-width: 95vw;
}

.bulk-update-modal .table-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 8px;
}

.bulk-update-modal .table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.bulk-update-modal .table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

.bulk-update-modal .table tbody tr.table-primary {
    background-color: rgba(0, 123, 255, 0.1);
    border-left: 4px solid #007bff;
}

.bulk-update-modal .form-control-sm {
    font-size: 0.875rem;
}

/* Filtres et recherche */
.filter-section {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid #e9ecef;
}

.filter-section .form-label {
    font-weight: 600;
    font-size: 0.875rem;
    color: #495057;
    margin-bottom: 0.5rem;
}

/* Actions rapides */
.quick-actions .btn {
    transition: all 0.2s ease-in-out;
    border-radius: 8px;
    font-weight: 500;
}

.quick-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.quick-actions .btn i {
    margin-right: 0.5rem;
}

/* Pagination */
.pagination .page-link {
    border-radius: 6px;
    margin: 0 2px;
    border: 1px solid #dee2e6;
    color: #495057;
    font-weight: 500;
}

.pagination .page-item.active .page-link {
    background-color: #007bff;
    border-color: #007bff;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.25);
}

.pagination .page-link:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
}

/* Statistiques de sélection */
.selection-stats {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.selection-stats i {
    margin-right: 0.5rem;
}

/* Modal d'historique */
.movements-modal .modal-dialog {
    max-width: 95vw;
}

.movements-modal .table-container {
    max-height: 500px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 8px;
}

.movements-modal .sticky-top {
    background-color: #f8f9fa;
    z-index: 10;
}

/* Modal d'export */
.export-modal .form-check {
    padding: 0.75rem;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    margin-bottom: 0.5rem;
    transition: background-color 0.2s ease-in-out;
}

.export-modal .form-check:hover {
    background-color: #f8f9fa;
}

.export-modal .form-check-input:checked + .form-check-label {
    color: #007bff;
    font-weight: 600;
}

/* Responsive */
@media (max-width: 768px) {
    .bulk-update-modal .modal-dialog,
    .movements-modal .modal-dialog {
        max-width: 98vw;
        margin: 0.5rem;
    }
    
    .inventory-stats-card .card-body {
        padding: 1rem;
    }
    
    .filter-section {
        padding: 0.75rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .quick-actions .btn {
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 576px) {
    .bulk-update-modal .table-container,
    .movements-modal .table-container {
        max-height: 300px;
    }
    
    .pagination .page-link {
        padding: 0.375rem 0.5rem;
        font-size: 0.875rem;
    }
    
    .selection-stats {
        padding: 0.75rem;
        font-size: 0.875rem;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* Indicateurs de statut */
.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 0.5rem;
}

.status-indicator.status-success {
    background-color: #28a745;
}

.status-indicator.status-warning {
    background-color: #ffc107;
}

.status-indicator.status-danger {
    background-color: #dc3545;
}

.status-indicator.status-info {
    background-color: #17a2b8;
}

/* Tooltips personnalisés */
.custom-tooltip {
    position: relative;
    cursor: help;
}

.custom-tooltip:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #333;
    color: white;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    white-space: nowrap;
    z-index: 1000;
}

/* Chargement */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    width: 2rem;
    height: 2rem;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
