using NafaPlace.Localization.Domain.Enums;

namespace NafaPlace.Localization.Application.DTOs;

public class LanguageDto
{
    public string Code { get; set; } = string.Empty; // fr, en, ar, etc.
    public string Name { get; set; } = string.Empty; // Fran<PERSON>, English, العربية
    public string NativeName { get; set; } = string.Empty; // Français, English, العربية
    public string Region { get; set; } = string.Empty; // FR, US, SA
    public bool IsRightToLeft { get; set; }
    public bool IsActive { get; set; } = true;
    public bool IsDefault { get; set; }
    public int Priority { get; set; }
    public string? FlagIcon { get; set; }
    public CurrencyDto Currency { get; set; } = new();
    public NumberFormatDto NumberFormat { get; set; } = new();
    public DateFormatDto DateFormat { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class CurrencyDto
{
    public string Code { get; set; } = string.Empty; // GNF, EUR, USD
    public string Symbol { get; set; } = string.Empty; // GNF, €, $
    public string Name { get; set; } = string.Empty; // Franc Guinéen, Euro, Dollar
    public int DecimalPlaces { get; set; } = 0;
    public string DecimalSeparator { get; set; } = ",";
    public string ThousandsSeparator { get; set; } = " ";
    public bool SymbolBefore { get; set; } = false;
    public double ExchangeRate { get; set; } = 1.0;
    public DateTime LastUpdated { get; set; }
}

public class NumberFormatDto
{
    public string DecimalSeparator { get; set; } = ",";
    public string ThousandsSeparator { get; set; } = " ";
    public int DefaultDecimalPlaces { get; set; } = 2;
    public string PercentageFormat { get; set; } = "#,##0.00%";
    public string NegativeFormat { get; set; } = "-#,##0.00";
}

public class DateFormatDto
{
    public string ShortDateFormat { get; set; } = "dd/MM/yyyy";
    public string LongDateFormat { get; set; } = "dddd, dd MMMM yyyy";
    public string TimeFormat { get; set; } = "HH:mm";
    public string DateTimeFormat { get; set; } = "dd/MM/yyyy HH:mm";
    public string TimeZone { get; set; } = "UTC";
    public int WeekStartDay { get; set; } = 1; // 1 = Monday
}

public class TranslationDto
{
    public int Id { get; set; }
    public string Key { get; set; } = string.Empty;
    public string LanguageCode { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string? Context { get; set; }
    public TranslationStatus Status { get; set; }
    public TranslationSource Source { get; set; }
    public string? Namespace { get; set; }
    public List<string> Tags { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public string? CreatedBy { get; set; }
    public string? UpdatedBy { get; set; }
    public int Version { get; set; }
    public bool IsPlural { get; set; }
    public Dictionary<string, string>? PluralForms { get; set; }
}

public class TranslationRequestDto
{
    public string Key { get; set; } = string.Empty;
    public string SourceLanguage { get; set; } = "fr";
    public List<string> TargetLanguages { get; set; } = new();
    public string SourceText { get; set; } = string.Empty;
    public string? Context { get; set; }
    public string? Namespace { get; set; }
    public List<string> Tags { get; set; } = new();
    public TranslationPriority Priority { get; set; } = TranslationPriority.Normal;
    public bool UseAutoTranslation { get; set; } = true;
    public bool RequiresReview { get; set; } = true;
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class TranslationBatchDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string SourceLanguage { get; set; } = string.Empty;
    public List<string> TargetLanguages { get; set; } = new();
    public BatchStatus Status { get; set; }
    public int TotalKeys { get; set; }
    public int CompletedKeys { get; set; }
    public int PendingKeys { get; set; }
    public int FailedKeys { get; set; }
    public double Progress { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public List<TranslationDto> Translations { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class LocalizationResourceDto
{
    public string Namespace { get; set; } = string.Empty;
    public string LanguageCode { get; set; } = string.Empty;
    public Dictionary<string, string> Resources { get; set; } = new();
    public DateTime LastUpdated { get; set; }
    public string Version { get; set; } = string.Empty;
    public int TotalKeys { get; set; }
    public int TranslatedKeys { get; set; }
    public double CompletionPercentage { get; set; }
}

public class AutoTranslationDto
{
    public int Id { get; set; }
    public string SourceText { get; set; } = string.Empty;
    public string SourceLanguage { get; set; } = string.Empty;
    public string TargetLanguage { get; set; } = string.Empty;
    public string TranslatedText { get; set; } = string.Empty;
    public AutoTranslationProvider Provider { get; set; }
    public double ConfidenceScore { get; set; }
    public bool IsReviewed { get; set; }
    public bool IsApproved { get; set; }
    public string? ReviewedBy { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? ReviewedAt { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class TranslationMemoryDto
{
    public int Id { get; set; }
    public string SourceText { get; set; } = string.Empty;
    public string TargetText { get; set; } = string.Empty;
    public string SourceLanguage { get; set; } = string.Empty;
    public string TargetLanguage { get; set; } = string.Empty;
    public double SimilarityScore { get; set; }
    public int UsageCount { get; set; }
    public DateTime LastUsed { get; set; }
    public string? Context { get; set; }
    public List<string> Tags { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class LocalizationAnalyticsDto
{
    public string Period { get; set; } = string.Empty;
    public Dictionary<string, int> TranslationsByLanguage { get; set; } = new();
    public Dictionary<string, double> CompletionByLanguage { get; set; } = new();
    public Dictionary<string, int> RequestsByLanguage { get; set; } = new();
    public Dictionary<AutoTranslationProvider, int> AutoTranslationUsage { get; set; } = new();
    public int TotalTranslations { get; set; }
    public int TotalKeys { get; set; }
    public int ActiveLanguages { get; set; }
    public double AverageCompletionRate { get; set; }
    public Dictionary<string, object> QualityMetrics { get; set; } = new();
    public List<PopularTranslationDto> PopularTranslations { get; set; } = new();
    public Dictionary<string, object> AdditionalMetrics { get; set; } = new();
}

public class PopularTranslationDto
{
    public string Key { get; set; } = string.Empty;
    public string SourceText { get; set; } = string.Empty;
    public int RequestCount { get; set; }
    public List<string> Languages { get; set; } = new();
    public DateTime LastRequested { get; set; }
}

public class TranslationQualityDto
{
    public int TranslationId { get; set; }
    public string Key { get; set; } = string.Empty;
    public string LanguageCode { get; set; } = string.Empty;
    public QualityScore OverallScore { get; set; }
    public QualityScore AccuracyScore { get; set; }
    public QualityScore FluencyScore { get; set; }
    public QualityScore ConsistencyScore { get; set; }
    public List<QualityIssueDto> Issues { get; set; } = new();
    public List<string> Suggestions { get; set; } = new();
    public DateTime AssessedAt { get; set; }
    public string? AssessedBy { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class QualityIssueDto
{
    public QualityIssueType Type { get; set; }
    public string Description { get; set; } = string.Empty;
    public QualitySeverity Severity { get; set; }
    public string? Suggestion { get; set; }
    public int Position { get; set; }
    public int Length { get; set; }
}

public class LocalizationWorkflowDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<WorkflowStepDto> Steps { get; set; } = new();
    public List<string> Languages { get; set; } = new();
    public WorkflowStatus Status { get; set; }
    public bool IsDefault { get; set; }
    public Dictionary<string, object> Settings { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class WorkflowStepDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public WorkflowStepType Type { get; set; }
    public int Order { get; set; }
    public bool IsRequired { get; set; } = true;
    public List<string> AssignedRoles { get; set; } = new();
    public List<string> AssignedUsers { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
    public TimeSpan? EstimatedDuration { get; set; }
    public bool IsParallel { get; set; }
}

public class TranslationProjectDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string SourceLanguage { get; set; } = string.Empty;
    public List<string> TargetLanguages { get; set; } = new();
    public ProjectStatus Status { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public DateTime? Deadline { get; set; }
    public string ProjectManager { get; set; } = string.Empty;
    public List<string> Translators { get; set; } = new();
    public List<string> Reviewers { get; set; } = new();
    public int TotalKeys { get; set; }
    public Dictionary<string, int> ProgressByLanguage { get; set; } = new();
    public Dictionary<string, object> Settings { get; set; } = new();
    public List<TranslationBatchDto> Batches { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class LocalizationConfigDto
{
    public string DefaultLanguage { get; set; } = "fr";
    public List<string> SupportedLanguages { get; set; } = new();
    public bool EnableAutoTranslation { get; set; } = true;
    public AutoTranslationProvider DefaultAutoTranslationProvider { get; set; } = AutoTranslationProvider.Google;
    public bool EnableTranslationMemory { get; set; } = true;
    public bool EnableQualityAssurance { get; set; } = true;
    public bool EnableVersioning { get; set; } = true;
    public int MaxTranslationLength { get; set; } = 5000;
    public Dictionary<string, object> ProviderSettings { get; set; } = new();
    public Dictionary<string, object> CacheSettings { get; set; } = new();
    public Dictionary<string, object> WorkflowSettings { get; set; } = new();
}

public class TranslationExportDto
{
    public string Format { get; set; } = string.Empty; // json, xml, csv, po, xliff
    public List<string> Languages { get; set; } = new();
    public List<string> Namespaces { get; set; } = new();
    public bool IncludeMetadata { get; set; } = true;
    public bool IncludeEmptyTranslations { get; set; } = false;
    public TranslationStatus? StatusFilter { get; set; }
    public Dictionary<string, object> FormatOptions { get; set; } = new();
}

public class TranslationImportDto
{
    public string Format { get; set; } = string.Empty;
    public string LanguageCode { get; set; } = string.Empty;
    public string? Namespace { get; set; }
    public bool OverwriteExisting { get; set; } = false;
    public bool ValidateBeforeImport { get; set; } = true;
    public TranslationSource Source { get; set; } = TranslationSource.Import;
    public Dictionary<string, object> ImportOptions { get; set; } = new();
}

// Enums
public enum TranslationStatus
{
    Draft,
    PendingTranslation,
    Translated,
    PendingReview,
    Reviewed,
    Approved,
    Published,
    Rejected,
    Outdated
}

public enum TranslationSource
{
    Manual,
    AutoTranslation,
    Import,
    API,
    Bulk,
    Migration
}

public enum TranslationPriority
{
    Low,
    Normal,
    High,
    Critical
}

public enum BatchStatus
{
    Created,
    InProgress,
    Completed,
    Failed,
    Cancelled
}

public enum AutoTranslationProvider
{
    Google,
    Microsoft,
    Amazon,
    DeepL,
    OpenAI,
    Custom
}

public enum QualityScore
{
    Poor = 1,
    Fair = 2,
    Good = 3,
    VeryGood = 4,
    Excellent = 5
}

public enum QualityIssueType
{
    Grammar,
    Spelling,
    Terminology,
    Consistency,
    Fluency,
    Accuracy,
    Formatting,
    Cultural
}

public enum QualitySeverity
{
    Info,
    Warning,
    Error,
    Critical
}

public enum WorkflowStatus
{
    Active,
    Inactive,
    Draft,
    Archived
}

public enum WorkflowStepType
{
    Translation,
    Review,
    Approval,
    QualityAssurance,
    AutoTranslation,
    Validation,
    Publishing
}

public enum ProjectStatus
{
    Planning,
    InProgress,
    Review,
    Completed,
    OnHold,
    Cancelled
}
