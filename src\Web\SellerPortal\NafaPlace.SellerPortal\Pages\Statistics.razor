@page "/statistics"
@using NafaPlace.SellerPortal.Models.Statistics
@using NafaPlace.SellerPortal.Services
@inject IStatisticsService StatisticsService
@inject IJSRuntime JSRuntime
@inject IAuthService AuthService
@inject ProductService ProductService

<h1 class="visually-hidden">Statistiques de Vente - NafaPlace</h1>

<div class="container-fluid px-4">
    <h1 class="mt-4">Statistiques de Vente</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="/dashboard">Dashboard</a></li>
        <li class="breadcrumb-item active">Statistiques</li>
    </ol>

    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-uppercase">Ventes Totales</h6>
                            <h3>@TotalSales.ToString("N0") GNF</h3>
                        </div>
                        <i class="fas fa-chart-line fa-2x"></i>
                    </div>
                    <div class="mt-2 small">
                        <span class="@(SalesGrowth >= 0 ? "text-success" : "text-danger")">
                            <i class="fas @(SalesGrowth >= 0 ? "fa-arrow-up" : "fa-arrow-down")"></i>
                            @Math.Abs(SalesGrowth).ToString("N1")%
                        </span>
                        <span class="ms-2">depuis le mois dernier</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-uppercase">Commandes</h6>
                            <h3>@TotalOrders</h3>
                        </div>
                        <i class="fas fa-shopping-cart fa-2x"></i>
                    </div>
                    <div class="mt-2 small">
                        <span class="@(OrdersGrowth >= 0 ? "text-light" : "text-danger")">
                            <i class="fas @(OrdersGrowth >= 0 ? "fa-arrow-up" : "fa-arrow-down")"></i>
                            @Math.Abs(OrdersGrowth).ToString("N1")%
                        </span>
                        <span class="ms-2">depuis le mois dernier</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-uppercase">Panier Moyen</h6>
                            <h3>@AverageOrderValue.ToString("N0") GNF</h3>
                        </div>
                        <i class="fas fa-coins fa-2x"></i>
                    </div>
                    <div class="mt-2 small">
                        <span class="@(AovGrowth >= 0 ? "text-light" : "text-danger")">
                            <i class="fas @(AovGrowth >= 0 ? "fa-arrow-up" : "fa-arrow-down")"></i>
                            @Math.Abs(AovGrowth).ToString("N1")%
                        </span>
                        <span class="ms-2">depuis le mois dernier</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-uppercase">Taux de Conversion</h6>
                            <h3>@ConversionRate.ToString("N1")%</h3>
                        </div>
                        <i class="fas fa-percentage fa-2x"></i>
                    </div>
                    <div class="mt-2 small">
                        <span class="@(ConversionGrowth >= 0 ? "text-light" : "text-danger")">
                            <i class="fas @(ConversionGrowth >= 0 ? "fa-arrow-up" : "fa-arrow-down")"></i>
                            @Math.Abs(ConversionGrowth).ToString("N1")%
                        </span>
                        <span class="ms-2">depuis le mois dernier</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-chart-area me-1"></i>
                        Évolution des Ventes
                    </div>
                    <div>
                        <select class="form-select form-select-sm" @bind="_salesChartPeriod">
                            <option value="7">7 derniers jours</option>
                            <option value="30">30 derniers jours</option>
                            <option value="90">90 derniers jours</option>
                            <option value="365">12 derniers mois</option>
                        </select>
                    </div>
                </div>
                <div class="card-body">
                    <div style="height: 300px; background-color: #f8f9fa; display: flex; align-items: center; justify-content: center;">
                        <div class="text-center">
                            <p class="text-muted mb-2">Graphique des ventes sur la période sélectionnée</p>
                            <div class="chart-placeholder" style="height: 200px; width: 100%; position: relative;">
                                <!-- Simulation d'un graphique avec des barres -->
                                @for (int i = 0; i < 12; i++)
                                {
                                    var height = 20 + (new Random(i * 10).Next(0, 150));
                                    <div style="position: absolute; bottom: 0; left: @(i * 8)%; width: 6%; height: @(height)px; background-color: #0d6efd; opacity: 0.7; border-radius: 3px 3px 0 0;"></div>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-chart-pie me-1"></i>
                    Répartition des Ventes par Catégorie
                </div>
                <div class="card-body">
                    <div style="height: 300px; background-color: #f8f9fa; display: flex; align-items: center; justify-content: center;">
                        <div class="text-center">
                            <p class="text-muted mb-3">Répartition par catégorie</p>
                            <div class="chart-placeholder" style="height: 180px; width: 180px; margin: 0 auto; position: relative; border-radius: 50%; overflow: hidden;">
                                <!-- Simulation d'un graphique en camembert -->
                                <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; clip-path: polygon(50% 50%, 100% 0, 100% 100%, 0 100%, 0 0); background-color: #0d6efd;"></div>
                                <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; clip-path: polygon(50% 50%, 0 0, 100% 0); background-color: #fd7e14;"></div>
                                <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; clip-path: polygon(50% 50%, 100% 0, 100% 50%); background-color: #198754;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-trophy me-1"></i>
                    Produits les Plus Vendus
                </div>
                <div class="card-body">
                    <table class="table table-bordered table-hover">
                        <thead>
                            <tr>
                                <th>Produit</th>
                                <th>Unités Vendues</th>
                                <th>Chiffre d'Affaires</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var product in _topProducts)
                            {
                                <tr>
                                    <td>@product.Name</td>
                                    <td>@product.UnitsSold</td>
                                    <td>@product.Revenue.ToString("N0") GNF</td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-map-marker-alt me-1"></i>
                    Ventes par Région
                </div>
                <div class="card-body">
                    <table class="table table-bordered table-hover">
                        <thead>
                            <tr>
                                <th>Région</th>
                                <th>Commandes</th>
                                <th>Chiffre d'Affaires</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var region in _salesByRegion)
                            {
                                <tr>
                                    <td>@region.Name</td>
                                    <td>@region.OrderCount</td>
                                    <td>@region.Revenue.ToString("N0") GNF</td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <i class="fas fa-table me-1"></i>
                Rapport de Ventes Détaillé
            </div>
            <div>
                <button class="btn btn-sm btn-outline-primary me-2" @onclick="ExportSalesReport">
                    <i class="fas fa-file-export me-1"></i> Exporter
                </button>
                <button class="btn btn-sm btn-outline-secondary" @onclick="RefreshData">
                    <i class="fas fa-sync me-1"></i> Actualiser
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-4">
                    <div class="input-group">
                        <span class="input-group-text">Du</span>
                        <input type="date" class="form-control" @bind="_reportStartDate">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="input-group">
                        <span class="input-group-text">Au</span>
                        <input type="date" class="form-control" @bind="_reportEndDate">
                    </div>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-primary w-100" @onclick="LoadStatisticsData">
                        <i class="fas fa-search me-1"></i> Générer le Rapport
                    </button>
                </div>
            </div>

            <table class="table table-bordered table-hover">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Commandes</th>
                        <th>Produits Vendus</th>
                        <th>Chiffre d'Affaires</th>
                        <th>Panier Moyen</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var report in _salesReports)
                    {
                        <tr>
                            <td>@report.Date.ToString("dd/MM/yyyy")</td>
                            <td>@report.OrderCount</td>
                            <td>@report.ProductsSold</td>
                            <td>@report.Revenue.ToString("N0") GNF</td>
                            <td>@report.AverageOrderValue.ToString("N0") GNF</td>
                        </tr>
                    }
                </tbody>
                <tfoot>
                    <tr class="table-primary">
                        <td><strong>Total</strong></td>
                        <td><strong>@_salesReports.Sum(r => r.OrderCount)</strong></td>
                        <td><strong>@_salesReports.Sum(r => r.ProductsSold)</strong></td>
                        <td><strong>@_salesReports.Sum(r => r.Revenue).ToString("N0") GNF</strong></td>
                        <td><strong>@(_salesReports.Any() ? (_salesReports.Sum(r => r.Revenue) / _salesReports.Sum(r => r.OrderCount)).ToString("N0") : "0") GNF</strong></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</div>

@code {
    private DateTime _reportStartDate = DateTime.Now.AddDays(-7);
    private DateTime _reportEndDate = DateTime.Now;
    private string _salesChartPeriod = "30";
    private bool _isLoading = false;
    private int _sellerId = 0; // ID du vendeur connecté

    // Données dynamiques
    private SalesStatistics? _salesStats;
    private List<TopProduct> _topProducts = new List<TopProduct>();
    private List<RegionSales> _salesByRegion = new List<RegionSales>();
    private List<SalesReport> _salesReports = new List<SalesReport>();
    private List<SalesChartData> _chartData = new List<SalesChartData>();

    protected override async Task OnInitializedAsync()
    {
        // Récupérer l'ID du vendeur connecté
        await LoadSellerInfo();

        if (_sellerId > 0)
        {
            await LoadAllStatisticsAsync();
        }
    }

    private async Task LoadStatisticsData()
    {
        await LoadAllStatisticsAsync();
    }

    private async Task LoadAllStatisticsAsync()
    {
        try
        {
            _isLoading = true;
            StateHasChanged();

            var request = new StatisticsRequest
            {
                StartDate = _reportStartDate,
                EndDate = _reportEndDate,
                Period = _salesChartPeriod,
                SellerId = _sellerId // Filtrer par vendeur
            };

            // Charger toutes les statistiques en parallèle
            var salesStatsTask = StatisticsService.GetSalesStatisticsAsync(request);
            var topProductsTask = StatisticsService.GetTopProductsAsync(request, 5);
            var salesByRegionTask = StatisticsService.GetSalesByRegionAsync(request);
            var salesReportTask = StatisticsService.GetSalesReportAsync(request);
            var chartDataTask = StatisticsService.GetSalesChartDataAsync(request);

            await Task.WhenAll(salesStatsTask, topProductsTask, salesByRegionTask, salesReportTask, chartDataTask);

            _salesStats = await salesStatsTask;
            _topProducts = await topProductsTask;
            _salesByRegion = await salesByRegionTask;
            _salesReports = await salesReportTask;
            _chartData = await chartDataTask;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading statistics: {ex.Message}");
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    private async Task ExportSalesReport()
    {
        try
        {
            var request = new ExportRequest
            {
                ReportType = "SalesReport",
                Format = "Excel",
                StartDate = _reportStartDate,
                EndDate = _reportEndDate
            };

            var fileData = await StatisticsService.ExportStatisticsAsync(request);
            if (fileData != null && fileData.Length > 0)
            {
                await JSRuntime.InvokeVoidAsync("downloadFile", "rapport-ventes.xlsx", Convert.ToBase64String(fileData));
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error exporting sales report: {ex.Message}");
        }
    }

    private async Task RefreshData()
    {
        await LoadAllStatisticsAsync();
    }

    // Propriétés pour l'affichage des statistiques avec valeurs par défaut
    private decimal TotalSales => _salesStats?.TotalSales ?? 0;
    private double SalesGrowth => _salesStats?.SalesGrowth ?? 0;
    private int TotalOrders => _salesStats?.TotalOrders ?? 0;
    private double OrdersGrowth => _salesStats?.OrdersGrowth ?? 0;
    private decimal AverageOrderValue => _salesStats?.AverageOrderValue ?? 0;
    private double AovGrowth => _salesStats?.AovGrowth ?? 0;
    private double ConversionRate => _salesStats?.ConversionRate ?? 0;
    private double ConversionGrowth => _salesStats?.ConversionGrowth ?? 0;

    private async Task LoadSellerInfo()
    {
        try
        {
            var currentUser = await AuthService.GetCurrentUserAsync();
            if (currentUser != null)
            {
                // Récupérer l'ID du vendeur basé sur l'ID utilisateur
                var sellerInfo = await ProductService.GetSellerByUserIdAsync(currentUser.Id);
                if (sellerInfo != null)
                {
                    _sellerId = sellerInfo.Id;
                    Console.WriteLine($"Vendeur connecté: ID={_sellerId}, Nom={sellerInfo.Name}");
                }
                else
                {
                    Console.WriteLine($"Aucun vendeur trouvé pour l'utilisateur {currentUser.Id}");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des informations du vendeur: {ex.Message}");
        }
    }
}
