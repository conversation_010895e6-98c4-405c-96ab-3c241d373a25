using NafaPlace.Notifications.Application.DTOs;
using NafaPlace.Notifications.Domain.Models;

namespace NafaPlace.Notifications.Application.Interfaces;

public interface INotificationRepository
{
    // Notification Management
    Task<NotificationLog> CreateNotificationAsync(NotificationLog notification);
    Task<bool> UpdateNotificationAsync(NotificationLog notification);
    Task<NotificationLog?> GetNotificationAsync(int id);
    Task<List<NotificationLog>> GetUserNotificationsAsync(string userId, int page = 1, int pageSize = 20, bool? isRead = null);
    Task<int> GetUserNotificationsCountAsync(string userId, bool? isRead = null);
    Task<bool> MarkAllAsReadAsync(string userId);
    Task<bool> DeleteNotificationAsync(int id);

    // Template Management
    Task<NotificationTemplate> CreateTemplateAsync(NotificationTemplate template);
    Task<NotificationTemplate> UpdateTemplateAsync(NotificationTemplate template);
    Task<bool> DeleteTemplateAsync(int id);
    Task<NotificationTemplate?> GetTemplateAsync(int id);
    Task<NotificationTemplate?> GetTemplateByCodeAsync(string code);
    Task<List<NotificationTemplate>> GetTemplatesAsync(NotificationType? type = null, NotificationChannel? channel = null);

    // Preference Management
    Task<NotificationPreference> CreatePreferenceAsync(NotificationPreference preference);
    Task<NotificationPreference> UpdatePreferenceAsync(NotificationPreference preference);
    Task<List<NotificationPreference>> GetUserPreferencesAsync(string userId);
    Task<NotificationPreference?> GetUserPreferenceAsync(string userId, NotificationType type, NotificationChannel channel);

    // Queue Management
    Task<List<NotificationLog>> GetPendingNotificationsAsync();
    Task<List<NotificationLog>> GetFailedNotificationsAsync();
    Task<int> GetPendingNotificationsCountAsync();

    // Statistics
    Task<NotificationStatsDto> GetNotificationStatsAsync(string? userId = null, DateTime? startDate = null, DateTime? endDate = null);

    // Maintenance
    Task<int> CleanupOldNotificationsAsync(int daysToKeep = 30);
    Task<int> CleanupExpiredNotificationsAsync();
}
