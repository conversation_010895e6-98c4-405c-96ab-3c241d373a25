@page "/blog"
@inject NavigationManager NavigationManager

<PageTitle>Blog - NafaPlace</PageTitle>

<div class="container py-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="bi bi-journal-text text-info me-3"></i>
                    Blog NafaPlace
                </h1>
                <p class="page-subtitle">Actualités, conseils et tendances du e-commerce en Guinée</p>
            </div>
        </div>
    </div>

    <!-- Featured Article Professionnel -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="professional-featured-article">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <img src="/images/blog/featured-article.jpg" class="img-fluid rounded" alt="Article en vedette"
                             onerror="this.src='/images/placeholder-blog.png'">
                    </div>
                    <div class="col-md-6">
                        <div class="article-content">
                            <span class="article-badge">À la une</span>
                            <h2 class="article-title">Bienvenue sur NafaPlace : La nouvelle marketplace guinéenne</h2>
                            <p class="article-excerpt">
                                Découvrez comment NafaPlace révolutionne le commerce électronique en Guinée en connectant
                                vendeurs et acheteurs sur une plateforme moderne et sécurisée.
                            </p>
                            <div class="article-meta">
                                <small>
                                    <i class="bi bi-calendar me-1"></i>15 janvier 2024
                                    <i class="bi bi-person ms-3 me-1"></i>Équipe NafaPlace
                                    <i class="bi bi-eye ms-3 me-1"></i>1,234 vues
                                </small>
                            </div>
                            <a href="/blog/bienvenue-nafaplace" class="btn btn-primary mt-3">
                                Lire l'article <i class="bi bi-arrow-right ms-1"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Categories Filter -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="blog-categories">
                <h5 class="mb-3">Catégories</h5>
                <div class="category-tags">
                    <button class="btn btn-outline-primary active me-2 mb-2" @onclick="() => FilterByCategory(null)">
                        Tous les articles
                    </button>
                    <button class="btn btn-outline-primary me-2 mb-2" @onclick='() => FilterByCategory("actualites")'>
                        <i class="bi bi-newspaper me-1"></i>Actualités
                    </button>
                    <button class="btn btn-outline-primary me-2 mb-2" @onclick='() => FilterByCategory("conseils")'>
                        <i class="bi bi-lightbulb me-1"></i>Conseils
                    </button>
                    <button class="btn btn-outline-primary me-2 mb-2" @onclick='() => FilterByCategory("tendances")'>
                        <i class="bi bi-graph-up me-1"></i>Tendances
                    </button>
                    <button class="btn btn-outline-primary me-2 mb-2" @onclick='() => FilterByCategory("vendeurs")'>
                        <i class="bi bi-shop me-1"></i>Guide Vendeurs
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Blog Articles Grid -->
    <div class="row">
        @foreach (var article in filteredArticles)
        {
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card blog-card h-100">
                    <img src="@article.ImageUrl" class="card-img-top" alt="@article.Title"
                         onerror="this.src='/images/placeholder-blog.png'">
                    <div class="card-body d-flex flex-column">
                        <div class="article-category mb-2">
                            <span class="badge bg-@GetCategoryColor(article.Category)">@GetCategoryName(article.Category)</span>
                        </div>
                        <h5 class="card-title">@article.Title</h5>
                        <p class="card-text">@article.Excerpt</p>
                        <div class="article-meta mt-auto">
                            <small class="text-muted">
                                <i class="bi bi-calendar me-1"></i>@article.PublishedDate.ToString("dd MMM yyyy")
                                <i class="bi bi-person ms-3 me-1"></i>@article.Author
                            </small>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent">
                        <a href="/blog/@article.Slug" class="btn btn-outline-primary btn-sm">
                            Lire la suite <i class="bi bi-arrow-right ms-1"></i>
                        </a>
                        <div class="float-end">
                            <small class="text-muted">
                                <i class="bi bi-eye me-1"></i>@article.Views
                                <i class="bi bi-chat ms-2 me-1"></i>@article.Comments
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        }
    </div>

    <!-- Empty State -->
    @if (!filteredArticles.Any())
    {
        <div class="row">
            <div class="col-12">
                <div class="empty-state text-center py-5">
                    <i class="bi bi-journal-x display-1 text-muted"></i>
                    <h3 class="mt-3">Aucun article trouvé</h3>
                    <p class="text-muted">Aucun article ne correspond à cette catégorie pour le moment.</p>
                    <button class="btn btn-primary" @onclick="() => FilterByCategory(null)">
                        <i class="bi bi-arrow-left me-2"></i>Voir tous les articles
                    </button>
                </div>
            </div>
        </div>
    }

    <!-- Newsletter Subscription Professionnelle -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="professional-newsletter">
                <div class="newsletter-content">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h4 class="newsletter-title">Restez informé</h4>
                            <p class="newsletter-description">Abonnez-vous à notre newsletter pour recevoir les dernières actualités et conseils.</p>
                        </div>
                        <div class="col-md-4">
                            <div class="newsletter-form">
                                <div class="input-group">
                                    <input type="email" class="form-control" placeholder="Votre email" @bind="newsletterEmail">
                                    <button class="btn btn-newsletter" @onclick="SubscribeNewsletter">
                                        <i class="bi bi-envelope me-1"></i>S'abonner
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private string? selectedCategory = null;
    private string newsletterEmail = "";
    
    private List<BlogArticle> articles = new()
    {
        new BlogArticle
        {
            Id = 1,
            Title = "Comment réussir sur NafaPlace : Guide du vendeur débutant",
            Excerpt = "Découvrez les meilleures pratiques pour lancer votre boutique en ligne et maximiser vos ventes sur NafaPlace.",
            Category = "vendeurs",
            Author = "Marie Camara",
            PublishedDate = DateTime.Now.AddDays(-5),
            ImageUrl = "/images/blog/guide-vendeur.jpg",
            Slug = "guide-vendeur-debutant",
            Views = 856,
            Comments = 12
        },
        new BlogArticle
        {
            Id = 2,
            Title = "Les tendances e-commerce 2024 en Afrique de l'Ouest",
            Excerpt = "Analyse des principales tendances qui façonnent le commerce électronique dans notre région.",
            Category = "tendances",
            Author = "Amadou Diallo",
            PublishedDate = DateTime.Now.AddDays(-10),
            ImageUrl = "/images/blog/tendances-2024.jpg",
            Slug = "tendances-ecommerce-2024",
            Views = 1203,
            Comments = 8
        },
        new BlogArticle
        {
            Id = 3,
            Title = "Sécurité des paiements en ligne : Ce qu'il faut savoir",
            Excerpt = "Tout ce que vous devez savoir sur la sécurité des transactions en ligne sur NafaPlace.",
            Category = "conseils",
            Author = "Fatoumata Bah",
            PublishedDate = DateTime.Now.AddDays(-15),
            ImageUrl = "/images/blog/securite-paiements.jpg",
            Slug = "securite-paiements-ligne",
            Views = 642,
            Comments = 15
        },
        new BlogArticle
        {
            Id = 4,
            Title = "NafaPlace lance sa nouvelle application mobile",
            Excerpt = "Découvrez les nouvelles fonctionnalités de notre application mobile pour une expérience d'achat optimisée.",
            Category = "actualites",
            Author = "Équipe NafaPlace",
            PublishedDate = DateTime.Now.AddDays(-3),
            ImageUrl = "/images/blog/app-mobile.jpg",
            Slug = "nouvelle-application-mobile",
            Views = 2156,
            Comments = 24
        },
        new BlogArticle
        {
            Id = 5,
            Title = "Optimiser ses photos produits pour augmenter les ventes",
            Excerpt = "Conseils pratiques pour prendre des photos attractives qui convertissent vos visiteurs en clients.",
            Category = "conseils",
            Author = "Ibrahim Touré",
            PublishedDate = DateTime.Now.AddDays(-20),
            ImageUrl = "/images/blog/photos-produits.jpg",
            Slug = "optimiser-photos-produits",
            Views = 934,
            Comments = 18
        },
        new BlogArticle
        {
            Id = 6,
            Title = "L'impact du mobile commerce en Guinée",
            Excerpt = "Comment les smartphones transforment les habitudes d'achat des consommateurs guinéens.",
            Category = "tendances",
            Author = "Aissatou Diop",
            PublishedDate = DateTime.Now.AddDays(-25),
            ImageUrl = "/images/blog/mobile-commerce.jpg",
            Slug = "impact-mobile-commerce-guinee",
            Views = 1567,
            Comments = 31
        }
    };

    private IEnumerable<BlogArticle> filteredArticles => 
        selectedCategory == null 
            ? articles.OrderByDescending(a => a.PublishedDate)
            : articles.Where(a => a.Category == selectedCategory).OrderByDescending(a => a.PublishedDate);

    private void FilterByCategory(string? category)
    {
        selectedCategory = category;
    }

    private string GetCategoryName(string category) => category switch
    {
        "actualites" => "Actualités",
        "conseils" => "Conseils",
        "tendances" => "Tendances",
        "vendeurs" => "Guide Vendeurs",
        _ => "Général"
    };

    private string GetCategoryColor(string category) => category switch
    {
        "actualites" => "primary",
        "conseils" => "success",
        "tendances" => "warning",
        "vendeurs" => "info",
        _ => "secondary"
    };

    private async Task SubscribeNewsletter()
    {
        if (!string.IsNullOrWhiteSpace(newsletterEmail))
        {
            // TODO: Implémenter l'abonnement à la newsletter
            Console.WriteLine($"Newsletter subscription for: {newsletterEmail}");
            newsletterEmail = "";
            // Afficher un message de succès
        }
    }

    public class BlogArticle
    {
        public int Id { get; set; }
        public string Title { get; set; } = "";
        public string Excerpt { get; set; } = "";
        public string Category { get; set; } = "";
        public string Author { get; set; } = "";
        public DateTime PublishedDate { get; set; }
        public string ImageUrl { get; set; } = "";
        public string Slug { get; set; } = "";
        public int Views { get; set; }
        public int Comments { get; set; }
    }
}
