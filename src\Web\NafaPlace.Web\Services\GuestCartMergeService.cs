using Microsoft.JSInterop;
using NafaPlace.Web.Models.Cart;

namespace NafaPlace.Web.Services
{
    public interface IGuestCartMergeService
    {
        Task<bool> MergeGuestCartOnLoginAsync(string authenticatedUserId);
        Task<bool> SaveUserCartToGuestOnLogoutAsync(string authenticatedUserId);
        Task ClearGuestCartAsync();
    }

    public class GuestCartMergeService : IGuestCartMergeService
    {
        private readonly IJSRuntime _jsRuntime;
        private readonly ICartService _cartService;

        public GuestCartMergeService(IJSRuntime jsRuntime, ICartService cartService)
        {
            _jsRuntime = jsRuntime;
            _cartService = cartService;
        }

        public async Task<bool> MergeGuestCartOnLoginAsync(string authenticatedUserId)
        {
            try
            {
                // Récupérer l'ID invité du localStorage
                var guestId = await _jsRuntime.InvokeAsync<string>("localStorage.getItem", "guestUserId");
                
                if (string.IsNullOrEmpty(guestId))
                {
                    return false; // Pas de panier invité à fusionner
                }

                // Récupérer le panier invité
                var guestCart = await _cartService.GetCartAsync(guestId);
                
                if (guestCart?.Items?.Any() != true)
                {
                    // Pas d'articles dans le panier invité
                    await ClearGuestCartAsync();
                    return false;
                }

                // Récupérer le panier de l'utilisateur authentifié
                var userCart = await _cartService.GetCartAsync(authenticatedUserId);

                // Fusionner les articles du panier invité dans le panier utilisateur
                foreach (var guestItem in guestCart.Items)
                {
                    // Vérifier si l'article existe déjà dans le panier utilisateur
                    var existingItem = userCart?.Items?.FirstOrDefault(ui =>
                        ui.ProductId == guestItem.ProductId &&
                        ui.VariantId == guestItem.VariantId);

                    if (existingItem != null)
                    {
                        // Si l'article existe, mettre à jour la quantité
                        var updateItem = new CartItemDto
                        {
                            ProductId = existingItem.ProductId,
                            ProductName = existingItem.ProductName,
                            UnitPrice = existingItem.UnitPrice,
                            Quantity = existingItem.Quantity + guestItem.Quantity, // Additionner les quantités
                            VariantId = existingItem.VariantId,
                            VariantName = existingItem.VariantName
                        };

                        await _cartService.UpdateItemInCartAsync(authenticatedUserId, updateItem);
                    }
                    else
                    {
                        // Si l'article n'existe pas, l'ajouter
                        var cartItem = new CartItemCreateDto
                        {
                            ProductId = guestItem.ProductId,
                            ProductName = guestItem.ProductName,
                            Price = guestItem.UnitPrice,
                            Quantity = guestItem.Quantity,
                            VariantId = guestItem.VariantId,
                            VariantName = guestItem.VariantName
                        };

                        await _cartService.AddItemToCartAsync(authenticatedUserId, cartItem);
                    }
                }

                // Nettoyer le panier invité
                await _cartService.ClearCartAsync(guestId);
                await ClearGuestCartAsync();

                return true; // Fusion réussie
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors de la fusion des paniers: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> SaveUserCartToGuestOnLogoutAsync(string authenticatedUserId)
        {
            try
            {
                // Récupérer le panier de l'utilisateur connecté
                var userCart = await _cartService.GetCartAsync(authenticatedUserId);

                if (userCart?.Items?.Any() != true)
                {
                    return false; // Pas d'articles à sauvegarder
                }

                // Créer un nouvel ID invité
                var newGuestId = $"guest_{Random.Shared.Next(1, int.MaxValue)}";

                // Sauvegarder les articles du panier utilisateur dans le panier invité
                foreach (var userItem in userCart.Items)
                {
                    var cartItem = new CartItemCreateDto
                    {
                        ProductId = userItem.ProductId,
                        ProductName = userItem.ProductName,
                        Price = userItem.UnitPrice,
                        Quantity = userItem.Quantity,
                        VariantId = userItem.VariantId,
                        VariantName = userItem.VariantName
                    };

                    await _cartService.AddItemToCartAsync(newGuestId, cartItem);
                }

                // Stocker le nouvel ID invité dans le localStorage
                await _jsRuntime.InvokeVoidAsync("localStorage.setItem", "guestUserId", newGuestId);

                Console.WriteLine($"✅ Panier utilisateur sauvegardé vers l'invité: {newGuestId}");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors de la sauvegarde du panier utilisateur vers invité: {ex.Message}");
                return false;
            }
        }

        public async Task ClearGuestCartAsync()
        {
            try
            {
                await _jsRuntime.InvokeVoidAsync("localStorage.removeItem", "guestUserId");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors du nettoyage du panier invité: {ex.Message}");
            }
        }
    }
}
