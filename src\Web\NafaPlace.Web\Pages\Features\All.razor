@page "/features/all"
@using Microsoft.AspNetCore.Components
@inject NavigationManager NavigationManager

<PageTitle>Toutes les Fonctionnalités - NafaPlace</PageTitle>

<div class="container py-4">
    <!-- Header -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="text-center">
                <h1 class="display-4 fw-bold mb-3">
                    ✨ Nouvelles Fonctionnalités NafaPlace
                </h1>
                <p class="lead text-muted">
                    Découvrez toutes les innovations que nous avons ajoutées pour améliorer votre expérience d'achat
                </p>
            </div>
        </div>
    </div>

    <!-- Features Grid -->
    <div class="row g-4 mb-5">
        @if (features != null && features.Any())
        {
            @foreach (var feature in features)
            {
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 border-0 shadow-sm feature-card" @onclick="() => NavigateToFeature(feature.Url)">
                        <div class="card-body p-4">
                            <div class="feature-header mb-3">
                                <div class="feature-icon @feature.ColorClass">
                                    <i class="@feature.Icon"></i>
                                </div>
                                @if (feature.IsNew)
                                {
                                    <span class="badge bg-success position-absolute top-0 end-0 m-2">Nouveau</span>
                                }
                            </div>
                            <h5 class="card-title mb-2">@feature.Title</h5>
                            <p class="card-text text-muted mb-3">@feature.Description</p>
                            <div class="feature-stats mb-3">
                                <small class="text-muted">
                                    <i class="bi bi-clock me-1"></i>
                                    Ajouté le @feature.ReleaseDate.ToString("dd/MM/yyyy")
                                </small>
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="feature-status">
                                    <span class="badge @GetStatusBadgeClass(feature.Status)">
                                        @GetStatusText(feature.Status)
                                    </span>
                                </div>
                                <button class="btn btn-outline-primary btn-sm">
                                    Découvrir <i class="bi bi-arrow-right ms-1"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            }
        }
        else
        {
            <div class="col-12">
                <div class="text-center py-5">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Chargement des fonctionnalités...</span>
                    </div>
                    <p class="text-muted">Chargement des fonctionnalités en cours...</p>
                </div>
            </div>
        }
    </div>

    <!-- Coming Soon Section -->
    <div class="row mb-5">
        <div class="col-12">
            <h3 class="text-center mb-4">🚀 Bientôt Disponible</h3>
            <div class="row g-4">
                @if (upcomingFeatures != null)
                {
                    @foreach (var upcoming in upcomingFeatures)
                    {
                    <div class="col-lg-6">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body">
                                <div class="d-flex align-items-start">
                                    <div class="flex-shrink-0">
                                        <div class="upcoming-icon text-muted">
                                            <i class="@upcoming.Icon fs-2"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h6 class="card-title">@upcoming.Title</h6>
                                        <p class="card-text text-muted small">@upcoming.Description</p>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted">
                                                <i class="bi bi-calendar me-1"></i>
                                                Prévu pour @upcoming.ExpectedDate
                                            </small>
                                            <span class="badge bg-warning">Bientôt</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    }
                }
            </div>
        </div>
    </div>

    <!-- Feature Categories -->
    <div class="row mb-5">
        <div class="col-12">
            <h3 class="text-center mb-4">📂 Par Catégorie</h3>
            <div class="row g-4">
                @if (featureCategories != null)
                {
                    @foreach (var category in featureCategories)
                    {
                    <div class="col-lg-3 col-md-6">
                        <div class="card h-100 border-0 shadow-sm text-center category-card">
                            <div class="card-body">
                                <i class="@category.Icon text-primary fs-1 mb-3"></i>
                                <h6 class="card-title">@category.Name</h6>
                                <p class="card-text text-muted small">@category.Description</p>
                                <span class="badge bg-primary">@category.FeatureCount fonctionnalités</span>
                            </div>
                        </div>
                    </div>
                    }
                }
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm bg-light">
                <div class="card-body text-center p-4">
                    <h5 class="card-title mb-4">📊 Statistiques des Fonctionnalités</h5>
                    <div class="row g-4">
                        <div class="col-md-3">
                            <div class="stat-item">
                                <h3 class="text-primary mb-1">@(features?.Count ?? 0)</h3>
                                <small class="text-muted">Fonctionnalités Actives</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item">
                                <h3 class="text-success mb-1">@(features?.Count(f => f.IsNew) ?? 0)</h3>
                                <small class="text-muted">Nouvelles ce Mois</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item">
                                <h3 class="text-info mb-1">@(upcomingFeatures?.Count ?? 0)</h3>
                                <small class="text-muted">En Développement</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item">
                                <h3 class="text-warning mb-1">95%</h3>
                                <small class="text-muted">Satisfaction Utilisateur</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    /* Fix text visibility */
    .container h1, .container h3, .container h5, .container h6 {
        color: #212529 !important;
    }

    .text-muted {
        color: #6c757d !important;
    }

    .card-title {
        color: #212529 !important;
    }

    .card-text {
        color: #495057 !important;
    }

    small.text-muted {
        color: #6c757d !important;
    }

    .lead {
        color: #6c757d !important;
    }

    .feature-card {
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        background-color: white !important;
    }

    .feature-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    }

    .feature-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }

    .feature-icon.bg-primary { background: linear-gradient(135deg, #007bff, #0056b3); color: white; }
    .feature-icon.bg-success { background: linear-gradient(135deg, #28a745, #1e7e34); color: white; }
    .feature-icon.bg-info { background: linear-gradient(135deg, #17a2b8, #117a8b); color: white; }
    .feature-icon.bg-warning { background: linear-gradient(135deg, #ffc107, #e0a800); color: white; }
    .feature-icon.bg-danger { background: linear-gradient(135deg, #dc3545, #c82333); color: white; }
    .feature-icon.bg-purple { background: linear-gradient(135deg, #6f42c1, #5a32a3); color: white; }

    .category-card {
        transition: all 0.3s ease;
        background-color: white !important;
    }

    .category-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    }

    .stat-item {
        padding: 1rem;
    }

    .card {
        background-color: white !important;
        color: #212529 !important;
    }

    .card-body {
        color: #212529 !important;
    }

    .stat-item h3 {
        color: inherit !important;
    }
</style>

@code {
    private List<Feature>? features;
    private List<UpcomingFeature>? upcomingFeatures;
    private List<FeatureCategory>? featureCategories;

    protected override void OnInitialized()
    {
        LoadFeatures();
    }

    private void LoadFeatures()
    {
        features = new List<Feature>
        {
        new Feature
        {
            Title = "Recherche Avancée",
            Description = "Recherche intelligente avec filtres avancés, suggestions automatiques et recherche par image",
            Icon = "bi bi-search",
            ColorClass = "bg-primary",
            Url = "/search/advanced",
            Status = FeatureStatus.Active,
            IsNew = true,
            ReleaseDate = DateTime.Now.AddDays(-5)
        },
        new Feature
        {
            Title = "Analytics & KPIs",
            Description = "Tableau de bord complet avec métriques en temps réel et analyses prédictives",
            Icon = "bi bi-graph-up",
            ColorClass = "bg-success",
            Url = "/analytics/dashboard",
            Status = FeatureStatus.Active,
            IsNew = true,
            ReleaseDate = DateTime.Now.AddDays(-3)
        },
        new Feature
        {
            Title = "Chat Support",
            Description = "Support client en temps réel avec IA et agents humains disponibles 24/7",
            Icon = "bi bi-chat-dots",
            ColorClass = "bg-info",
            Url = "/chat/support",
            Status = FeatureStatus.Active,
            IsNew = true,
            ReleaseDate = DateTime.Now.AddDays(-7)
        },
        new Feature
        {
            Title = "Recommandations IA",
            Description = "Suggestions personnalisées basées sur l'intelligence artificielle et le machine learning",
            Icon = "bi bi-robot",
            ColorClass = "bg-warning",
            Url = "/recommendations",
            Status = FeatureStatus.Active,
            IsNew = true,
            ReleaseDate = DateTime.Now.AddDays(-2)
        },
        new Feature
        {
            Title = "Programme Fidélité",
            Description = "Système de points, niveaux de fidélité et récompenses exclusives",
            Icon = "bi bi-gift",
            ColorClass = "bg-danger",
            Url = "/loyalty/program",
            Status = FeatureStatus.Active,
            IsNew = true,
            ReleaseDate = DateTime.Now.AddDays(-10)
        },
        new Feature
        {
            Title = "Essayage Virtuel",
            Description = "Technologie AR pour essayer virtuellement vêtements et accessoires",
            Icon = "bi bi-camera",
            ColorClass = "bg-purple",
            Url = "/virtual-tryon",
            Status = FeatureStatus.Beta,
            IsNew = false,
            ReleaseDate = DateTime.Now.AddDays(-15)
        }
        };

        upcomingFeatures = new List<UpcomingFeature>
        {
            new UpcomingFeature
        {
            Title = "Paiement Crypto",
            Description = "Acceptation des cryptomonnaies comme moyen de paiement",
            Icon = "bi bi-currency-bitcoin",
            ExpectedDate = "Q2 2024"
        },
        new UpcomingFeature
        {
            Title = "Réalité Augmentée",
            Description = "Visualisation 3D des produits dans votre environnement",
            Icon = "bi bi-eye",
            ExpectedDate = "Q3 2024"
        },
        new UpcomingFeature
        {
            Title = "Marketplace B2B",
            Description = "Plateforme dédiée aux transactions entre entreprises",
            Icon = "bi bi-building",
            ExpectedDate = "Q4 2024"
        },
        new UpcomingFeature
        {
            Title = "Assistant Vocal",
            Description = "Commandes vocales et recherche par la voix",
            Icon = "bi bi-mic",
            ExpectedDate = "Q1 2025"
        }
        };

        featureCategories = new List<FeatureCategory>
        {
            new FeatureCategory { Name = "Intelligence Artificielle", Icon = "bi bi-robot", Description = "Fonctionnalités basées sur l'IA", FeatureCount = 3 },
            new FeatureCategory { Name = "Expérience Utilisateur", Icon = "bi bi-person-heart", Description = "Amélioration de l'UX", FeatureCount = 4 },
            new FeatureCategory { Name = "Analytics", Icon = "bi bi-graph-up", Description = "Données et analyses", FeatureCount = 2 },
            new FeatureCategory { Name = "Support Client", Icon = "bi bi-headset", Description = "Service et assistance", FeatureCount = 2 }
        };
    }

    private void NavigateToFeature(string url)
    {
        NavigationManager.NavigateTo(url);
    }

    private string GetStatusBadgeClass(FeatureStatus status)
    {
        return status switch
        {
            FeatureStatus.Active => "bg-success",
            FeatureStatus.Beta => "bg-warning",
            FeatureStatus.Development => "bg-info",
            FeatureStatus.Deprecated => "bg-secondary",
            _ => "bg-primary"
        };
    }

    private string GetStatusText(FeatureStatus status)
    {
        return status switch
        {
            FeatureStatus.Active => "Actif",
            FeatureStatus.Beta => "Bêta",
            FeatureStatus.Development => "Développement",
            FeatureStatus.Deprecated => "Déprécié",
            _ => "Inconnu"
        };
    }

    public class Feature
    {
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public string Icon { get; set; } = "";
        public string ColorClass { get; set; } = "";
        public string Url { get; set; } = "";
        public FeatureStatus Status { get; set; }
        public bool IsNew { get; set; }
        public DateTime ReleaseDate { get; set; }
    }

    public class UpcomingFeature
    {
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public string Icon { get; set; } = "";
        public string ExpectedDate { get; set; } = "";
    }

    public class FeatureCategory
    {
        public string Name { get; set; } = "";
        public string Icon { get; set; } = "";
        public string Description { get; set; } = "";
        public int FeatureCount { get; set; }
    }

    public enum FeatureStatus
    {
        Active,
        Beta,
        Development,
        Deprecated
    }
}
