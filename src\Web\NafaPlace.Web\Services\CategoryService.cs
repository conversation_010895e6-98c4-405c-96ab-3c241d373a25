﻿using System.Net.Http.Json;
using NafaPlace.Web.Models.Catalog;

namespace NafaPlace.Web.Services;

public class CategoryService : ICategoryService
{
    private readonly HttpClient _httpClient;
    private readonly string _apiBaseUrl;
    private readonly List<CategoryDto> _mockCategories;

    public CategoryService(HttpClient httpClient)
    {
        _httpClient = httpClient;
        _apiBaseUrl = httpClient.BaseAddress?.ToString().TrimEnd('/') ?? "http://localhost:5243";
        
        // DonnÃ©es simulÃ©es pour les tests ou en cas d'indisponibilitÃ© de l'API
        _mockCategories = new List<CategoryDto>
        {
            new CategoryDto
            {
                Id = 1,
                Name = "Ã‰lectronique",
                Description = "Produits Ã©lectroniques et gadgets",
                ImageUrl = "/images/categories/electronics.jpg",
                ParentId = null
            },
            new CategoryDto
            {
                Id = 2,
                Name = "Smartphones",
                Description = "TÃ©lÃ©phones mobiles et accessoires",
                ImageUrl = "/images/categories/smartphones.jpg",
                ParentId = 1
            },
            new CategoryDto
            {
                Id = 3,
                Name = "Ordinateurs",
                Description = "Ordinateurs portables et de bureau",
                ImageUrl = "/images/categories/computers.jpg",
                ParentId = 1
            },
            new CategoryDto
            {
                Id = 4,
                Name = "Mode",
                Description = "VÃªtements, chaussures et accessoires",
                ImageUrl = "/images/categories/fashion.jpg",
                ParentId = null
            },
            new CategoryDto
            {
                Id = 5,
                Name = "Maison",
                Description = "Meubles et dÃ©coration",
                ImageUrl = "/images/categories/home.jpg",
                ParentId = null
            }
        };
    }

    public async Task<IEnumerable<CategoryDto>> GetAllCategoriesAsync()
    {
        try
        {
            // Essayer d'abord de rÃ©cupÃ©rer depuis l'API
            Console.WriteLine("Tentative de rÃ©cupÃ©ration des catÃ©gories depuis l'API...");
            var categories = await _httpClient.GetFromJsonAsync<IEnumerable<CategoryDto>>("/api/v1/categories");
            Console.WriteLine($"CatÃ©gories rÃ©cupÃ©rÃ©es avec succÃ¨s: {categories?.Count() ?? 0} catÃ©gories");
            return categories ?? _mockCategories;
        }
        catch (Exception ex)
        {
            // En cas d'erreur, retourner les donnÃ©es simulÃ©es
            Console.WriteLine($"Erreur lors de la rÃ©cupÃ©ration des catÃ©gories: {ex.Message}");
            return _mockCategories;
        }
    }

    public async Task<CategoryDto> GetCategoryByIdAsync(int id)
    {
        try
        {
            // Essayer d'abord de rÃ©cupÃ©rer depuis l'API
            Console.WriteLine($"Tentative de rÃ©cupÃ©ration de la catÃ©gorie {id} depuis l'API...");
            var category = await _httpClient.GetFromJsonAsync<CategoryDto>($"/api/v1/categories/{id}");
            Console.WriteLine($"CatÃ©gorie {id} rÃ©cupÃ©rÃ©e avec succÃ¨s");
            return category ?? new CategoryDto { Id = id, Name = "CatÃ©gorie inconnue" };
        }
        catch (Exception ex)
        {
            // En cas d'erreur, retourner la catÃ©gorie simulÃ©e correspondante
            Console.WriteLine($"Erreur lors de la rÃ©cupÃ©ration de la catÃ©gorie {id}: {ex.Message}");
            var category = _mockCategories.FirstOrDefault(c => c.Id == id);
            return category ?? new CategoryDto { Id = id, Name = "CatÃ©gorie inconnue" };
        }
    }

    public async Task<IEnumerable<CategoryDto>> GetChildCategoriesAsync(int parentId)
    {
        try
        {
            // Essayer d'abord de rÃ©cupÃ©rer depuis l'API
            Console.WriteLine($"Tentative de rÃ©cupÃ©ration des sous-catÃ©gories de {parentId} depuis l'API...");
            var categories = await _httpClient.GetFromJsonAsync<IEnumerable<CategoryDto>>($"/api/v1/categories/{parentId}/subcategories");
            Console.WriteLine($"Sous-catÃ©gories de {parentId} rÃ©cupÃ©rÃ©es avec succÃ¨s: {categories?.Count() ?? 0} catÃ©gories");
            return categories ?? _mockCategories.Where(c => c.ParentId == parentId).ToList();
        }
        catch (Exception ex)
        {
            // En cas d'erreur, retourner les catÃ©gories enfants simulÃ©es
            Console.WriteLine($"Erreur lors de la rÃ©cupÃ©ration des sous-catÃ©gories de {parentId}: {ex.Message}");
            return _mockCategories.Where(c => c.ParentId == parentId).ToList();
        }
    }

    public string GetImageUrl(CategoryDto category, bool thumbnail = false)
    {
        try
        {
            if (category == null)
            {
                return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNk+A8AAQUBAScY42YAAAAASUVORK5CYII=";
            }

            var url = category.ImageUrl;

            if (string.IsNullOrEmpty(url))
            {
                return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNk+A8AAQUBAScY42YAAAAASUVORK5CYII=";
            }

            // Si l'URL est dÃ©jÃ  complÃ¨te (http/https) ou est une data URL, la retourner telle quelle
            if (url.StartsWith("http://") || url.StartsWith("https://") || url.StartsWith("data:"))
            {
                return url;
            }

            // Sinon, construire l'URL complÃ¨te avec l'API base URL
            return _apiBaseUrl + (url.StartsWith("/") ? url : $"/{url}");
        }
        catch (Exception)
        {
            return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNk+A8AAQUBAScY42YAAAAASUVORK5CYII=";
        }
    }
}

