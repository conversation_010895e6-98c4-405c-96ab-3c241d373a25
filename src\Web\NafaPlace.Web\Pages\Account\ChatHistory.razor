@page "/account/chat-history"
@using Microsoft.AspNetCore.Authorization
@using NafaPlace.Web.Services
@attribute [Authorize]
@inject IChatService ChatService
@inject IJSRuntime JSRuntime
@inject ILogger<ChatHistory> Logger

<PageTitle>Historique des Conversations - NafaPlace</PageTitle>

<div class="container">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="bi bi-clock-history"></i> Historique des Conversations</h2>
                <button class="btn btn-outline-primary" @onclick="RefreshConversations">
                    <i class="bi bi-arrow-clockwise"></i> Actualiser
                </button>
            </div>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Chargement...</span>
            </div>
            <p class="mt-2 text-muted">Chargement de vos conversations...</p>
        </div>
    }
    else if (conversations?.Any() == true)
    {
        <div class="row">
            <div class="col-md-4">
                <!-- Liste des conversations -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Vos Conversations</h6>
                    </div>
                    <div class="card-body p-0">
                        <div class="list-group list-group-flush">
                            @foreach (var conversation in conversations)
                            {
                                <div class="list-group-item list-group-item-action @(selectedConversationId == conversation.Id ? "active" : "")" 
                                     @onclick="() => SelectConversation(conversation.Id)">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">@conversation.Subject</h6>
                                        <small>@conversation.CreatedAt.ToString("dd/MM")</small>
                                    </div>
                                    <p class="mb-1 text-truncate">
                                        @if (!string.IsNullOrEmpty(conversation.AssignedAgentName))
                                        {
                                            <small><i class="bi bi-person"></i> @conversation.AssignedAgentName</small>
                                        }
                                        else
                                        {
                                            <small><i class="bi bi-robot"></i> Assistant virtuel</small>
                                        }
                                    </p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="badge bg-@GetStatusColor(conversation.Status)">
                                            @GetStatusText(conversation.Status)
                                        </span>
                                        @if (conversation.UnreadCount > 0)
                                        {
                                            <span class="badge bg-danger rounded-pill">@conversation.UnreadCount</span>
                                        }
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-8">
                @if (selectedConversationId.HasValue)
                {
                    <!-- Messages de la conversation sélectionnée -->
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">
                                    @selectedConversation?.Subject
                                </h6>
                                <div class="btn-group btn-group-sm">
                                    @if (selectedConversation?.Status == "Open" || selectedConversation?.Status == "InProgress")
                                    {
                                        <button class="btn btn-outline-primary" @onclick="ContinueConversation">
                                            <i class="bi bi-chat-dots"></i> Continuer
                                        </button>
                                    }
                                    <button class="btn btn-outline-secondary" @onclick="ExportConversation">
                                        <i class="bi bi-download"></i> Exporter
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body chat-history-body">
                            @if (selectedMessages?.Any() == true)
                            {
                                <div class="chat-messages-history">
                                    @foreach (var message in selectedMessages)
                                    {
                                        <div class="message-history @(message.SenderType == "Customer" ? "user" : "support")">
                                            @if (message.SenderType != "Customer")
                                            {
                                                <div class="message-avatar">
                                                    <i class="bi bi-person-circle"></i>
                                                </div>
                                            }
                                            <div class="message-content">
                                                <div class="message-bubble">
                                                    @message.Content
                                                </div>
                                                <div class="message-meta">
                                                    <small class="text-muted">
                                                        @message.SenderName • @message.Timestamp.ToString("dd/MM/yyyy HH:mm")
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                </div>
                            }
                            else
                            {
                                <div class="text-center py-4">
                                    <i class="bi bi-chat-square-text fs-1 text-muted"></i>
                                    <p class="text-muted">Aucun message dans cette conversation</p>
                                </div>
                            }
                        </div>
                        @if (selectedConversation != null)
                        {
                            <div class="card-footer">
                                <div class="row">
                                    <div class="col-md-6">
                                        <small class="text-muted">
                                            <i class="bi bi-calendar"></i> Créée le @selectedConversation.CreatedAt.ToString("dd/MM/yyyy à HH:mm")
                                        </small>
                                    </div>
                                    <div class="col-md-6 text-end">
                                        @if (selectedConversation.LastMessageAt.HasValue)
                                        {
                                            <small class="text-muted">
                                                <i class="bi bi-clock"></i> Dernière activité : @selectedConversation.LastMessageAt.Value.ToString("dd/MM/yyyy à HH:mm")
                                            </small>
                                        }
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                }
                else
                {
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="bi bi-chat-square-dots fs-1 text-muted"></i>
                            <h5 class="mt-3">Sélectionnez une conversation</h5>
                            <p class="text-muted">Choisissez une conversation dans la liste pour voir les messages</p>
                        </div>
                    </div>
                }
            </div>
        </div>
    }
    else
    {
        <div class="text-center py-5">
            <i class="bi bi-chat-square fs-1 text-muted"></i>
            <h4 class="mt-3">Aucune conversation</h4>
            <p class="text-muted">Vous n'avez pas encore de conversations. Commencez en utilisant le chat de support.</p>
            <button class="btn btn-primary" @onclick="StartNewChat">
                <i class="bi bi-chat-dots"></i> Démarrer une conversation
            </button>
        </div>
    }
</div>

<style>
    .chat-history-body {
        max-height: 500px;
        overflow-y: auto;
    }

    .chat-messages-history {
        padding: 10px 0;
    }

    .message-history {
        display: flex;
        margin-bottom: 20px;
        align-items: flex-start;
    }

    .message-history.user {
        justify-content: flex-end;
    }

    .message-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: #6c757d;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 10px;
        font-size: 14px;
        flex-shrink: 0;
    }

    .message-content {
        max-width: 70%;
    }

    .message-bubble {
        padding: 12px 16px;
        border-radius: 18px;
        font-size: 14px;
        line-height: 1.4;
        word-wrap: break-word;
    }

    .message-history.support .message-bubble {
        background: #f8f9fa;
        color: #333;
        border-bottom-left-radius: 5px;
    }

    .message-history.user .message-bubble {
        background: linear-gradient(135deg, #E73C30, #F96302);
        color: white;
        border-bottom-right-radius: 5px;
    }

    .message-meta {
        margin-top: 5px;
        text-align: left;
    }

    .message-history.user .message-meta {
        text-align: right;
    }

    .list-group-item.active {
        background-color: #E73C30;
        border-color: #E73C30;
    }
</style>

@code {
    private bool isLoading = true;
    private List<ChatConversationDto>? conversations;
    private int? selectedConversationId;
    private ChatConversationDto? selectedConversation;
    private List<ChatMessageDto>? selectedMessages;

    protected override async Task OnInitializedAsync()
    {
        await LoadConversations();
    }

    private async Task LoadConversations()
    {
        try
        {
            isLoading = true;
            conversations = await ChatService.GetUserConversationsAsync();
            
            // Sélectionner la première conversation par défaut
            if (conversations?.Any() == true)
            {
                await SelectConversation(conversations.First().Id);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors du chargement des conversations");
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task SelectConversation(int conversationId)
    {
        try
        {
            selectedConversationId = conversationId;
            selectedConversation = conversations?.FirstOrDefault(c => c.Id == conversationId);
            
            if (selectedConversation != null)
            {
                selectedMessages = await ChatService.GetConversationMessagesAsync(conversationId);
                
                // Marquer la conversation comme lue
                await ChatService.MarkConversationAsReadAsync(conversationId);
                
                // Mettre à jour le compteur de messages non lus
                selectedConversation.UnreadCount = 0;
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de la sélection de la conversation");
        }
    }

    private async Task RefreshConversations()
    {
        await LoadConversations();
    }

    private async Task ContinueConversation()
    {
        // Ouvrir le chat flottant ou rediriger vers la page de chat
        await JSRuntime.InvokeVoidAsync("window.location.assign", "/chat/support");
    }

    private async Task ExportConversation()
    {
        if (selectedConversation == null) return;
        
        try
        {
            // TODO: Implémenter l'export de conversation
            await JSRuntime.InvokeVoidAsync("alert", "Fonctionnalité d'export en cours de développement");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de l'export de la conversation");
        }
    }

    private async Task StartNewChat()
    {
        await JSRuntime.InvokeVoidAsync("window.location.assign", "/chat/support");
    }

    private string GetStatusColor(string status)
    {
        return status switch
        {
            "Open" => "success",
            "InProgress" => "primary",
            "Waiting" => "warning",
            "Closed" => "secondary",
            "Archived" => "dark",
            _ => "secondary"
        };
    }

    private string GetStatusText(string status)
    {
        return status switch
        {
            "Open" => "Ouvert",
            "InProgress" => "En cours",
            "Waiting" => "En attente",
            "Closed" => "Fermé",
            "Archived" => "Archivé",
            _ => status
        };
    }
}
