{"ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=NafaPlace.Catalog;Username=postgres;Password=*****************", "AzureStorage": "UseDevelopmentStorage=true"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Storage": {"ProductImagesContainer": "product-images", "CategoryImagesContainer": "category-images"}, "JwtSettings": {"Secret": "NafaPlaceSecretKey2025ForProductionEnvironment", "Issuer": "NafaPlace", "Audience": "NafaPlaceApi", "ExpirationInMinutes": 60}}