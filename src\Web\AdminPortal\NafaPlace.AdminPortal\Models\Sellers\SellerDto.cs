using System.ComponentModel.DataAnnotations;

namespace NafaPlace.AdminPortal.Models.Sellers
{
    public class SellerDto
    {
        public int Id { get; set; }
        
        [Required(ErrorMessage = "Le nom du vendeur est obligatoire")]
        [StringLength(100, ErrorMessage = "Le nom du vendeur ne doit pas dépasser 100 caractères")]
        public string Name { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "L'email du vendeur est obligatoire")]
        [EmailAddress(ErrorMessage = "Format d'email invalide")]
        public string Email { get; set; } = string.Empty;
        
        public string PhoneNumber { get; set; } = string.Empty;
        
        public string Address { get; set; } = string.Empty;
        
        public bool IsActive { get; set; } = true;
        
        public string? UserId { get; set; }
        
        // Champs pour la création d'utilisateur (uniquement lors de la création)
        [StringLength(50, ErrorMessage = "Le nom d'utilisateur ne doit pas dépasser 50 caractères")]
        public string Username { get; set; } = string.Empty;
        
        [StringLength(100, ErrorMessage = "Le mot de passe doit contenir au moins 6 caractères", MinimumLength = 6)]
        public string Password { get; set; } = string.Empty;
        
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }
}
