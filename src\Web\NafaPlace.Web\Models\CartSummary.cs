using NafaPlace.Web.Models.Cart;

namespace NafaPlace.Web.Models;

public class CartSummary
{
    public decimal TotalAmount { get; set; }
    public decimal SubTotal { get; set; }
    public int ItemCount { get; set; }
    public List<CartItemSummary> Items { get; set; } = new();
    public string Currency { get; set; } = "GNF";

    public static CartSummary FromCartDto(CartDto cart)
    {
        return new CartSummary
        {
            TotalAmount = cart.SubTotal, // Utiliser le sous-total avant réductions
            SubTotal = cart.SubTotal,
            ItemCount = cart.Items.Sum(i => i.Quantity),
            Currency = cart.Currency,
            Items = cart.Items.Select(item => new CartItemSummary
            {
                ProductId = item.ProductId,
                ProductName = item.ProductName,
                Quantity = item.Quantity,
                UnitPrice = item.UnitPrice,
                CategoryId = item.CategoryId,
                SellerId = item.SellerId
            }).ToList()
        };
    }
}

public class CartItemSummary
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public int Quantity { get; set; }
    public decimal UnitPrice { get; set; }
    public int? CategoryId { get; set; }
    public int? SellerId { get; set; }
}
