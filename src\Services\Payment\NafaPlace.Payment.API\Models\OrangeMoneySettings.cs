namespace NafaPlace.Payment.API.Models
{
    public class OrangeMoneySettings
    {
        public string ApiUrl { get; set; } = string.Empty;
        public string MerchantId { get; set; } = string.Empty;
        public string ApiKey { get; set; } = string.Empty;
        public string SecretKey { get; set; } = string.Empty;
        public string DefaultCurrency { get; set; } = "GNF";
        public decimal MinimumAmount { get; set; } = 1000;
        public decimal MaximumAmount { get; set; } = 50000000;
        public string CallbackUrl { get; set; } = string.Empty;
        public string ReturnUrl { get; set; } = string.Empty;
        public string CancelUrl { get; set; } = string.Empty;
    }
}
