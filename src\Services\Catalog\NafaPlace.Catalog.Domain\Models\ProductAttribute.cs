using System;
using System.ComponentModel.DataAnnotations;
using NafaPlace.Catalog.Domain.Common;

namespace NafaPlace.Catalog.Domain.Models;

public class ProductAttribute : BaseEntity
{
    [Required]
    [MaxLength(100)]
    public required string Name { get; set; }

    [Required]
    [MaxLength(500)]
    public required string Value { get; set; }

    public int ProductId { get; set; }
    public Product? Product { get; set; }

    public bool IsFilterable { get; set; }
    public bool IsSearchable { get; set; }
    public int DisplayOrder { get; set; }
}
