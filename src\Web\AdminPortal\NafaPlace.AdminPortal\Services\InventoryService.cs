using System.Text;
using System.Text.Json;
using NafaPlace.AdminPortal.Models.Inventory;

namespace NafaPlace.AdminPortal.Services;

public class InventoryService
{
    private readonly HttpClient _httpClient;
    private readonly JsonSerializerOptions _jsonOptions;

    public InventoryService(HttpClient httpClient)
    {
        _httpClient = httpClient;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            PropertyNameCaseInsensitive = true
        };
    }

    // Dashboard
    public async Task<InventoryDashboardDto> GetInventoryDashboardAsync(int? sellerId = null)
    {
        try
        {
            var url = sellerId.HasValue ? $"api/v1/inventory/dashboard?sellerId={sellerId}" : "api/v1/inventory/dashboard";
            var response = await _httpClient.GetAsync(url);
            
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<InventoryDashboardDto>(json, _jsonOptions) ?? new InventoryDashboardDto();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération du dashboard inventaire: {ex.Message}");
        }

        return new InventoryDashboardDto();
    }

    // Stock Alerts
    public async Task<List<StockAlertDto>> GetActiveAlertsAsync(int? sellerId = null)
    {
        try
        {
            var url = sellerId.HasValue ? $"api/v1/inventory/alerts?sellerId={sellerId}" : "api/v1/inventory/alerts";
            var response = await _httpClient.GetAsync(url);
            
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<List<StockAlertDto>>(json, _jsonOptions) ?? new List<StockAlertDto>();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des alertes: {ex.Message}");
        }

        return new List<StockAlertDto>();
    }

    public async Task<bool> AcknowledgeAlertAsync(int alertId)
    {
        try
        {
            var response = await _httpClient.PutAsync($"api/v1/inventory/alerts/{alertId}/acknowledge", null);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de l'acquittement de l'alerte: {ex.Message}");
            return false;
        }
    }

    // Stock Movements
    public async Task<List<StockMovementDto>> GetProductMovementsAsync(int productId, int page = 1, int pageSize = 20)
    {
        try
        {
            var response = await _httpClient.GetAsync($"api/v1/inventory/products/{productId}/movements?page={page}&pageSize={pageSize}");
            
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<List<StockMovementDto>>(json, _jsonOptions) ?? new List<StockMovementDto>();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des mouvements: {ex.Message}");
        }

        return new List<StockMovementDto>();
    }

    public async Task<List<StockMovementDto>> GetSellerMovementsAsync(int sellerId, int page = 1, int pageSize = 20)
    {
        try
        {
            var response = await _httpClient.GetAsync($"api/v1/inventory/sellers/{sellerId}/movements?page={page}&pageSize={pageSize}");
            
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<List<StockMovementDto>>(json, _jsonOptions) ?? new List<StockMovementDto>();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des mouvements du vendeur: {ex.Message}");
        }

        return new List<StockMovementDto>();
    }

    // Stock Management
    public async Task<bool> UpdateStockAsync(int productId, UpdateStockRequest request)
    {
        try
        {
            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            var response = await _httpClient.PutAsync($"api/v1/inventory/products/{productId}/stock", content);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la mise à jour du stock: {ex.Message}");
            return false;
        }
    }

    public async Task<bool> AdjustStockAsync(int productId, StockAdjustmentRequest request)
    {
        try
        {
            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            var response = await _httpClient.PostAsync($"api/v1/inventory/products/{productId}/adjust", content);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de l'ajustement du stock: {ex.Message}");
            return false;
        }
    }

    // Stock Reservations
    public async Task<List<StockReservationDto>> GetActiveReservationsAsync(int? sellerId = null)
    {
        try
        {
            var url = sellerId.HasValue ? $"api/v1/inventory/reservations?sellerId={sellerId}" : "api/v1/inventory/reservations";
            var response = await _httpClient.GetAsync(url);
            
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<List<StockReservationDto>>(json, _jsonOptions) ?? new List<StockReservationDto>();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des réservations: {ex.Message}");
        }

        return new List<StockReservationDto>();
    }

    public async Task<bool> ReleaseReservationAsync(int reservationId, string reason)
    {
        try
        {
            var request = new { reason };
            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            var response = await _httpClient.PostAsync($"api/v1/inventory/reservations/{reservationId}/release", content);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la libération de la réservation: {ex.Message}");
            return false;
        }
    }

    // Stock Validation
    public async Task<StockValidationResult> ValidateStockAvailabilityAsync(int productId, int quantity)
    {
        try
        {
            var response = await _httpClient.GetAsync($"api/v1/inventory/products/{productId}/validate?quantity={quantity}");
            
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<StockValidationResult>(json, _jsonOptions) ?? new StockValidationResult();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la validation du stock: {ex.Message}");
        }

        return new StockValidationResult { IsValid = false, ErrorMessage = "Erreur de validation" };
    }

    public async Task<int> GetAvailableStockAsync(int productId)
    {
        try
        {
            var response = await _httpClient.GetAsync($"api/v1/inventory/products/{productId}/available-stock");
            
            if (response.IsSuccessStatusCode)
            {
                var stockText = await response.Content.ReadAsStringAsync();
                if (int.TryParse(stockText, out var stock))
                {
                    return stock;
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération du stock disponible: {ex.Message}");
        }

        return 0;
    }

    // Analytics
    public async Task<List<TopProductDto>> GetTopSellingProductsAsync(int? sellerId = null, int count = 10)
    {
        try
        {
            var url = sellerId.HasValue
                ? $"api/v1/inventory/analytics/top-selling?sellerId={sellerId}&count={count}"
                : $"api/v1/inventory/analytics/top-selling?count={count}";
            
            var response = await _httpClient.GetAsync(url);
            
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<List<TopProductDto>>(json, _jsonOptions) ?? new List<TopProductDto>();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des produits les plus vendus: {ex.Message}");
        }

        return new List<TopProductDto>();
    }

    public async Task<List<TopProductDto>> GetLowStockProductsAsync(int? sellerId = null, int threshold = 10)
    {
        try
        {
            var url = sellerId.HasValue
                ? $"api/v1/inventory/analytics/low-stock?sellerId={sellerId}&threshold={threshold}"
                : $"api/v1/inventory/analytics/low-stock?threshold={threshold}";
            
            var response = await _httpClient.GetAsync(url);
            
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<List<TopProductDto>>(json, _jsonOptions) ?? new List<TopProductDto>();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des produits en stock faible: {ex.Message}");
        }

        return new List<TopProductDto>();
    }

    // Maintenance
    public async Task<bool> RecalculateStockLevelsAsync()
    {
        try
        {
            var response = await _httpClient.PostAsync("api/v1/inventory/maintenance/recalculate-stock", null);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du recalcul des stocks: {ex.Message}");
            return false;
        }
    }

    public async Task<int> CleanupExpiredReservationsAsync()
    {
        try
        {
            var response = await _httpClient.PostAsync("api/v1/inventory/maintenance/cleanup-reservations", null);
            
            if (response.IsSuccessStatusCode)
            {
                var countText = await response.Content.ReadAsStringAsync();
                if (int.TryParse(countText, out var count))
                {
                    return count;
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du nettoyage des réservations: {ex.Message}");
        }

        return 0;
    }
}
