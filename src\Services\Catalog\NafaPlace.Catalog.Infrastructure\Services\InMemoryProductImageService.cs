using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using NafaPlace.Catalog.Application.Common.Interfaces;
using NafaPlace.Catalog.Application.DTOs.Product;
using NafaPlace.Catalog.Domain.Models;
using Microsoft.Extensions.Logging;

using Microsoft.EntityFrameworkCore;

namespace NafaPlace.Catalog.Infrastructure.Services
{
    public class InMemoryProductImageService : IProductImageService
    {
        private readonly ILogger<InMemoryProductImageService> _logger;
        private readonly ICatalogDbContext _context;

        public InMemoryProductImageService(ILogger<InMemoryProductImageService> logger, ICatalogDbContext context)
        {
            _logger = logger;
            _context = context;
        }

        public async Task<ProductImageDto> AddProductImageAsync(int productId, CreateProductImageRequest request)
        {
            _logger.LogInformation("Adding image for product {ProductId}", productId);
            var product = await _context.Products.FindAsync(productId);
            if (product == null)
            {
                _logger.LogError("Product with id {ProductId} not found", productId);
                throw new Exception("Product not found");
            }

            var image = new ProductImage
            {
                ProductId = productId,
                ImageUrl = request.Image,
                IsMain = request.IsMain,
                ThumbnailUrl = request.Image
            };

            if (request.IsMain)
            {
                var existingMainImages = await _context.ProductImages
                    .Where(pi => pi.ProductId == productId && pi.IsMain)
                    .ToListAsync();

                foreach (var img in existingMainImages)
                {
                    img.IsMain = false;
                }
            }

            _context.ProductImages.Add(image);
            await _context.SaveChangesAsync();

            return new ProductImageDto
            {
                Id = image.Id,
                ProductId = image.ProductId,
                Url = image.ImageUrl,
                IsMain = image.IsMain,
                ThumbnailUrl = image.ThumbnailUrl
            };
        }

        public async Task DeleteProductImageAsync(int imageId)
        {
            _logger.LogInformation("Deleting image {ImageId}", imageId);
            var image = await _context.ProductImages.FindAsync(imageId);
            if (image != null)
            {
                _context.ProductImages.Remove(image);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<ProductImageDto> GetProductImageAsync(int imageId)
        {
            var image = await _context.ProductImages.FindAsync(imageId);
            if (image == null)
            {
                return null;
            }
            return new ProductImageDto
            {
                Id = image.Id,
                ProductId = image.ProductId,
                Url = image.ImageUrl,
                IsMain = image.IsMain,
                ThumbnailUrl = image.ThumbnailUrl
            };
        }

        public async Task<IEnumerable<ProductImageDto>> GetProductImagesAsync(int productId)
        {
            return await _context.ProductImages
                .Where(i => i.ProductId == productId)
                .Select(i => new ProductImageDto
                {
                    Id = i.Id,
                    ProductId = i.ProductId,
                    Url = i.ImageUrl,
                    IsMain = i.IsMain,
                    ThumbnailUrl = i.ThumbnailUrl
                })
                .ToListAsync();
        }

        public async Task SetMainImageAsync(int productId, int imageId)
        {
            var productImages = await _context.ProductImages
                .Where(i => i.ProductId == productId)
                .ToListAsync();

            foreach (var image in productImages)
            {
                image.IsMain = image.Id == imageId;
            }
            await _context.SaveChangesAsync();
        }

        public Task<bool> UpdateImageOrderAsync(int imageId, int displayOrder)
        {
            return Task.FromResult(true); // Not implemented for in-memory
        }

        public async Task<bool> UpdateProductImageAsync(int imageId, UpdateProductImageRequest request)
        {
            var image = await _context.ProductImages.FindAsync(imageId);
            if (image != null)
            {
                image.ImageUrl = request.Image;
                image.IsMain = request.IsMain;
                await _context.SaveChangesAsync();
                return true;
            }
            return false;
        }

        public Task<bool> ValidateImageAsync(string imageUrl)
        {
            return Task.FromResult(true); // Assume valid for in-memory
        }

        public Task<string> GenerateThumbnailAsync(string imageUrl)
        {
            return Task.FromResult(imageUrl); // Return original for in-memory
        }

        public Task<string> UploadImageAsync(string base64Image)
        {
            return Task.FromResult(base64Image); // Return original for in-memory
        }

        public Task<IEnumerable<ProductImageDto>> AddBulkProductImagesAsync(int productId, IEnumerable<CreateProductImageRequest> request)
        {
            var product = _context.Products.Find(productId);
            if (product == null)
            {
                _logger.LogError("Product with id {ProductId} not found", productId);
                throw new Exception("Product not found");
            }

            var newImages = new List<ProductImageDto>();

            foreach (var imageRequest in request)
            {
                var image = new ProductImage
                {
                    ProductId = productId,
                    ImageUrl = imageRequest.Image,
                    IsMain = imageRequest.IsMain,
                    ThumbnailUrl = imageRequest.Image
                };

                if (imageRequest.IsMain)
                {
                    var existingMainImages = _context.ProductImages
                        .Where(pi => pi.ProductId == productId && pi.IsMain)
                        .ToList();

                    foreach (var img in existingMainImages)
                    {
                        img.IsMain = false;
                    }
                }

                _context.ProductImages.Add(image);
                newImages.Add(new ProductImageDto
                {
                    Id = image.Id,
                    ProductId = image.ProductId,
                    Url = image.ImageUrl,
                    IsMain = image.IsMain,
                    ThumbnailUrl = image.ThumbnailUrl
                });
            }

            _context.SaveChangesAsync(default).GetAwaiter().GetResult();
            return Task.FromResult<IEnumerable<ProductImageDto>>(newImages);
        }

        public Task DeleteProductImageAsync(int productId, int imageId)
        {
            var image = _context.ProductImages.Find(imageId);
            if (image != null)
            {
                _context.ProductImages.Remove(image);
                _context.SaveChangesAsync(default).GetAwaiter().GetResult();
            }
            return Task.CompletedTask;
        }
    }
}