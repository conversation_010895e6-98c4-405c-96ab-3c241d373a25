<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>NafaPlace Admin Portal</title>
    <base href="/" />
    <link rel="stylesheet" href="css/bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="css/app.css" />
    <link rel="stylesheet" href="css/badges.css" />
    <link rel="stylesheet" href="css/modern-dashboard.css" />
    <link rel="stylesheet" href="NafaPlace.AdminPortal.styles.css" />
    <link rel="icon" type="image/png" href="favicon.png" />
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <!-- Toastr CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" />
</head>

<body>
    <div id="app">
        <div class="d-flex justify-content-center align-items-center vh-100">
            <div class="text-center">
                <h1>NafaPlace Admin Portal</h1>
                <div class="spinner-border mt-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-3">Chargement en cours...</p>
            </div>
        </div>
    </div>

    <div id="blazor-error-ui" class="d-none">
        Une erreur s'est produite.
        <a href="" class="reload">Recharger</a>
        <a class="dismiss">🗙</a>
    </div>
    <script src="_framework/blazor.webassembly.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-ka7Sk0Gln4gmtz2MlQnikT1wXgYsOg+OMhuP+IlRH9sENBO0LRn5q+8nbTov4+1p" crossorigin="anonymous"></script>
    <!-- jQuery (requis pour Toastr) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Toastr JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <!-- Notre fichier JavaScript personnalisé -->
    <script src="js/site.js"></script>
</body>

</html>
