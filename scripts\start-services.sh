#!/bin/bash

# Script de démarrage pour tous les services NafaPlace sur fly.io

echo "🚀 Démarrage des services NafaPlace..."

# Fonction pour démarrer un service en arrière-plan
start_service() {
    local service_name=$1
    local service_path=$2
    local service_dll=$3
    local port=$4
    
    echo "📦 Démarrage du service $service_name sur le port $port..."
    cd /app/$service_path
    ASPNETCORE_URLS="http://+:$port" dotnet $service_dll &
    echo "✅ Service $service_name démarré (PID: $!)"
}

# Démarrer les services en arrière-plan
start_service "Identity API" "identity" "NafaPlace.Identity.API.dll" "5155"
start_service "Catalog API" "catalog" "NafaPlace.Catalog.API.dll" "5243"
start_service "Cart API" "cart" "NafaPlace.Cart.API.dll" "5003"
start_service "Order API" "order" "NafaPlace.Order.API.dll" "5004"
start_service "Payment API" "payment" "NafaPlace.Payment.API.dll" "5005"
start_service "Reviews API" "reviews" "NafaPlace.Reviews.API.dll" "5006"
start_service "Notifications API" "notifications" "NafaPlace.Notifications.API.dll" "5007"
start_service "Wishlist API" "wishlist" "NafaPlace.Wishlist.API.dll" "5008"
start_service "API Gateway" "gateway" "NafaPlace.ApiGateway.dll" "5000"

# Attendre que tous les services soient prêts
echo "⏳ Attente du démarrage des services..."
sleep 10

# Démarrer l'application web principale (au premier plan)
echo "🌐 Démarrage de l'application web principale..."
cd /app/web
exec dotnet NafaPlace.Web.dll
