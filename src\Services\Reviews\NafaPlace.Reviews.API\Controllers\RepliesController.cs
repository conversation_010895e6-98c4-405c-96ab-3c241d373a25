using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NafaPlace.Reviews.Application.DTOs;
using NafaPlace.Reviews.Application.Services;
using NafaPlace.Reviews.DTOs;
using System.Security.Claims;

namespace NafaPlace.Reviews.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class RepliesController : ControllerBase
{
    private readonly IReviewService _reviewService;

    public RepliesController(IReviewService reviewService)
    {
        _reviewService = reviewService;
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<ReplyDto>> GetReply(int id)
    {
        var reply = await _reviewService.GetReplyByIdAsync(id);
        if (reply == null)
            return NotFound();

        return Ok(reply);
    }

    [HttpGet("review/{reviewId}")]
    public async Task<ActionResult<List<ReplyDto>>> GetRepliesByReview(int reviewId)
    {
        var replies = await _reviewService.GetRepliesByReviewIdAsync(reviewId);
        return Ok(replies);
    }

    [HttpPost]
    [Authorize]
    public async Task<ActionResult<ReplyDto>> CreateReply([FromBody] CreateReplyRequest request)
    {
        try
        {
            var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var currentUserName = User.FindFirst(ClaimTypes.Name)?.Value;

            if (string.IsNullOrEmpty(currentUserId) || string.IsNullOrEmpty(currentUserName))
                return Unauthorized();

            var reply = await _reviewService.CreateReplyAsync(request, currentUserId, currentUserName);
            return CreatedAtAction(nameof(GetReply), new { id = reply.Id }, reply);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal server error: {ex.Message}");
        }
    }

    [HttpPut("{id}")]
    [Authorize]
    public async Task<ActionResult<ReplyDto>> UpdateReply(int id, [FromBody] UpdateReplyRequest request)
    {
        try
        {
            var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(currentUserId))
                return Unauthorized();

            var reply = await _reviewService.UpdateReplyAsync(id, request, currentUserId);
            return Ok(reply);
        }
        catch (ArgumentException ex)
        {
            return NotFound(ex.Message);
        }
        catch (UnauthorizedAccessException ex)
        {
            return Forbid(ex.Message);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal server error: {ex.Message}");
        }
    }

    [HttpDelete("{id}")]
    [Authorize]
    public async Task<IActionResult> DeleteReply(int id)
    {
        try
        {
            var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(currentUserId))
                return Unauthorized();

            await _reviewService.DeleteReplyAsync(id, currentUserId);
            return NoContent();
        }
        catch (ArgumentException ex)
        {
            return NotFound(ex.Message);
        }
        catch (UnauthorizedAccessException ex)
        {
            return Forbid(ex.Message);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal server error: {ex.Message}");
        }
    }
}
