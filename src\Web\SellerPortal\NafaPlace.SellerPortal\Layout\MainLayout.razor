@inherits LayoutComponentBase
@inject IAuthService AuthService
@inject NavigationManager NavigationManager
@implements IDisposable

<div class="page">
    <AuthorizeView>
        <Authorized>
            <div class="sidebar">
                <NavMenu />
            </div>
        </Authorized>
    </AuthorizeView>

    <main>
        <div class="top-row px-4 justify-content-end">
            <AuthorizeView>
                <Authorized>
                    <div class="dropdown">
                        <button class="btn btn-link dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-person-circle me-1"></i>
                            @context.User.Identity?.Name
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                            <li><a class="dropdown-item" href="/profile"><i class="bi bi-person me-2"></i>Mon profil</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout"><i class="bi bi-box-arrow-right me-2"></i>Déconnexion</a></li>
                        </ul>
                    </div>
                </Authorized>
                <NotAuthorized>
                    <a href="/login" class="btn btn-outline-primary me-2">Connexion</a>
                    <a href="/register" class="btn btn-primary">Inscription</a>
                </NotAuthorized>
            </AuthorizeView>
        </div>

        <article class="content px-4">
            @Body
        </article>
    </main>
</div>

<NotificationComponent />

@code {
    protected override void OnInitialized()
    {
        // S'abonner aux changements d'état d'authentification
        AuthService.AuthenticationStateChanged += StateHasChanged;
    }

    public void Dispose()
    {
        // Se désabonner des changements d'état d'authentification
        AuthService.AuthenticationStateChanged -= StateHasChanged;
    }
}
