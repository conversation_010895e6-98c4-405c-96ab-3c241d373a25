@inherits LayoutComponentBase
@inject IAuthService AuthService
@inject NavigationManager NavigationManager
@implements IDisposable

<AuthorizeView>
    <Authorized>
        <!-- Sidebar -->
        <nav class="sidebar">
            <NavMenu />
        </nav>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Header -->
            <header class="header d-flex align-items-center px-4">
                <div class="d-flex align-items-center flex-grow-1">
                    <h5 class="mb-0 me-4">Tableau de bord</h5>
                    <div class="input-group" style="max-width: 300px;">
                        <span class="input-group-text bg-light border-0">
                            <i class="bi bi-search"></i>
                        </span>
                        <input type="text" class="form-control border-0 bg-light"
                               placeholder="Rechercher...">
                    </div>
                </div>

                <div class="d-flex align-items-center">
                    <div class="position-relative me-3">
                        <i class="bi bi-bell fs-5 text-muted"></i>
                        <span class="notification-dot"></span>
                    </div>
                    <div class="position-relative me-3">
                        <i class="bi bi-envelope fs-5 text-muted"></i>
                        <span class="notification-dot"></span>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-link dropdown-toggle text-decoration-none" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-person-circle me-1 fs-4"></i>
                            @context.User.Identity?.Name
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="userDropdown">
                            <li><a class="dropdown-item" href="/profile"><i class="bi bi-person me-2"></i>Mon profil</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout"><i class="bi bi-box-arrow-right me-2"></i>Déconnexion</a></li>
                        </ul>
                    </div>
                </div>
            </header>

            <!-- Dashboard Content -->
            <div class="p-4">
                @Body
            </div>
        </div>
    </Authorized>
    <NotAuthorized>
        <div class="d-flex justify-content-center align-items-center vh-100">
            <div class="text-center">
                <h3>Accès non autorisé</h3>
                <p>Veuillez vous connecter pour accéder au portail vendeur.</p>
                <a href="/login" class="btn btn-primary me-2">Connexion</a>
                <a href="/register" class="btn btn-outline-primary">Inscription</a>
            </div>
        </div>
    </NotAuthorized>
</AuthorizeView>

<NotificationComponent />

@code {
    protected override void OnInitialized()
    {
        // S'abonner aux changements d'état d'authentification
        AuthService.AuthenticationStateChanged += StateHasChanged;
    }

    public void Dispose()
    {
        // Se désabonner des changements d'état d'authentification
        AuthService.AuthenticationStateChanged -= StateHasChanged;
    }
}
