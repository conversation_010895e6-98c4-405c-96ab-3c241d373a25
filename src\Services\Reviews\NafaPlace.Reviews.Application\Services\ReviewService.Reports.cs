using NafaPlace.Reviews.Application.DTOs;
using NafaPlace.Reviews.Application.Interfaces;
using NafaPlace.Reviews.Application.Services;
using NafaPlace.Reviews.Domain.Models;
using NafaPlace.Reviews.DTOs;

namespace NafaPlace.Reviews.Application.Services;

public partial class ReviewService : IReviewService
{
    // Report operations
    
    public async Task<ReviewReportDto?> GetReportByIdAsync(int id)
    {
        var report = await _reviewRepository.GetReportByIdAsync(id);
        return report == null ? null : MapToReportDto(report);
    }
    
    public async Task<List<ReviewReportDto>> GetReportsByReviewIdAsync(int reviewId)
    {
        var reports = await _reviewRepository.GetReportsByReviewIdAsync(reviewId);
        return reports.Select(MapToReportDto).ToList();
    }
    
    public async Task<ReviewReportDto> CreateReportAsync(CreateReviewReportRequest request, string userId)
    {
        // Check if user already reported this review
        var hasReported = await _reviewRepository.HasUserReportedReviewAsync(request.ReviewId, userId);
        if (hasReported)
        {
            throw new InvalidOperationException("User has already reported this review");
        }
        
        var report = new ReviewReport
        {
            ReviewId = request.ReviewId,
            UserId = userId,
            Reason = request.Reason,
            AdditionalComments = request.AdditionalComments,
            CreatedAt = DateTime.UtcNow,
            Status = ReportStatus.Pending
        };
        
        var createdReport = await _reviewRepository.CreateReportAsync(report);
        return MapToReportDto(createdReport);
    }
    
    public async Task<ReviewReportDto> UpdateReportStatusAsync(int id, UpdateReviewReportRequest request, string moderatorId)
    {
        var report = await _reviewRepository.GetReportByIdAsync(id);
        if (report == null)
        {
            throw new ArgumentException("Report not found");
        }
        
        // Parse the status
        if (!Enum.TryParse<ReportStatus>(request.Status, out var reportStatus))
        {
            throw new ArgumentException($"Invalid report status: {request.Status}");
        }
        
        report.Status = reportStatus;
        report.ResolutionNotes = request.ResolutionNotes;
        report.ResolvedBy = moderatorId;
        report.ResolvedAt = DateTime.UtcNow;
        
        var updatedReport = await _reviewRepository.UpdateReportAsync(report);
        return MapToReportDto(updatedReport);
    }
    
    public async Task<List<ReviewReportDto>> GetReportsByStatusAsync(ReportStatus status, int page = 1, int pageSize = 10)
    {
        var reports = await _reviewRepository.GetReportsByStatusAsync(status, page, pageSize);
        return reports.Select(MapToReportDto).ToList();
    }
    
    public async Task<ReviewReportSummaryDto> GetReportSummaryAsync()
    {
        var pendingCount = await _reviewRepository.CountReportsByStatusAsync(ReportStatus.Pending);
        var totalCount = pendingCount +
                         await _reviewRepository.CountReportsByStatusAsync(ReportStatus.Investigating) +
                         await _reviewRepository.CountReportsByStatusAsync(ReportStatus.Resolved) +
                         await _reviewRepository.CountReportsByStatusAsync(ReportStatus.Dismissed);
        
        var mostReportedReviews = await GetMostReportedReviewsAsync(5);
        
        return new ReviewReportSummaryDto
        {
            TotalReportsCount = totalCount,
            PendingReportsCount = pendingCount,
            TopReportedReviews = mostReportedReviews
        };
    }
    
    public async Task<List<ReportedReviewDto>> GetMostReportedReviewsAsync(int count = 10)
    {
        var reviews = await _reviewRepository.GetMostReportedReviewsAsync(count);
        var result = new List<ReportedReviewDto>();
        
        foreach (var review in reviews)
        {
            var reportReasons = review.Reports
                .Select(r => r.Reason)
                .GroupBy(r => r)
                .Select(g => g.Key)
                .ToList();
            
            result.Add(new ReportedReviewDto
            {
                Review = MapToDto(review),
                ReportCount = review.ReportCount,
                ReportReasons = reportReasons
            });
        }
        
        return result;
    }
    
    private static ReviewReportDto MapToReportDto(ReviewReport report)
    {
        return new ReviewReportDto
        {
            Id = report.Id,
            ReviewId = report.ReviewId,
            UserId = report.UserId,
            Reason = report.Reason,
            AdditionalComments = report.AdditionalComments,
            CreatedAt = report.CreatedAt,
            Status = report.Status.ToString(),
            ResolvedAt = report.ResolvedAt,
            ResolvedBy = report.ResolvedBy,
            ResolutionNotes = report.ResolutionNotes
        };
    }
}
