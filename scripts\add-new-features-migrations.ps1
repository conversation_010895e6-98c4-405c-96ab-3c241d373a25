# Script pour ajouter les nouvelles fonctionnalités aux bases existantes
# Auteur: Assistant IA
# Date: 2025-01-28

param(
    [switch]$Force,
    [switch]$OnlyGenerate,
    [string]$Service = "all"
)

Write-Host "🆕 Ajout des nouvelles fonctionnalités NafaPlace" -ForegroundColor Green
Write-Host "===============================================" -ForegroundColor Green

# Configuration des nouvelles fonctionnalités à ajouter
$newFeatures = @{
    "Analytics" = @{
        "ProjectPath" = "src/Services/Analytics/NafaPlace.Analytics.Infrastructure"
        "Context" = "AnalyticsDbContext"
        "MigrationName" = "AddAnalyticsFeatures"
        "Description" = "Analytics, KPIs, Reports, Events"
    }
    "Chat" = @{
        "ProjectPath" = "src/Services/Chat/NafaPlace.Chat.Infrastructure"
        "Context" = "ChatDbContext"
        "MigrationName" = "AddChatSupportFeatures"
        "Description" = "Chat, Tickets, FAQ, Agents"
    }
    "Recommendation" = @{
        "ProjectPath" = "src/Services/Recommendation/NafaPlace.Recommendation.Infrastructure"
        "Context" = "RecommendationDbContext"
        "MigrationName" = "AddRecommendationFeatures"
        "Description" = "Recommandations IA, ML, Interactions"
    }
    "Loyalty" = @{
        "ProjectPath" = "src/Services/Loyalty/NafaPlace.Loyalty.Infrastructure"
        "Context" = "LoyaltyDbContext"
        "MigrationName" = "AddLoyaltyProgramFeatures"
        "Description" = "Points, Badges, Challenges, Rewards"
    }
    "Localization" = @{
        "ProjectPath" = "src/Services/Localization/NafaPlace.Localization.Infrastructure"
        "Context" = "LocalizationDbContext"
        "MigrationName" = "AddMultiLanguageFeatures"
        "Description" = "Traductions, Langues, Localisation"
    }
    "Search" = @{
        "ProjectPath" = "src/Services/Search/NafaPlace.Search.Infrastructure"
        "Context" = "SearchDbContext"
        "MigrationName" = "AddAdvancedSearchFeatures"
        "Description" = "Recherche avancée, Index, Suggestions"
    }
    "Inventory" = @{
        "ProjectPath" = "src/Services/Inventory/NafaPlace.Inventory.Infrastructure"
        "Context" = "InventoryDbContext"
        "MigrationName" = "AddInventoryManagementFeatures"
        "Description" = "Stock, Alertes, Prévisions"
    }
}

# Fonction pour vérifier les prérequis
function Test-Prerequisites {
    Write-Host "🔍 Vérification des prérequis..." -ForegroundColor Yellow
    
    # Vérifier dotnet
    try {
        $dotnetVersion = dotnet --version
        if ($dotnetVersion -like "8.*") {
            Write-Host "✅ .NET 8 SDK trouvé : $dotnetVersion" -ForegroundColor Green
        } else {
            Write-Host "⚠️ .NET version : $dotnetVersion (8.x recommandé)" -ForegroundColor Yellow
        }
    }
    catch {
        Write-Host "❌ .NET SDK non trouvé" -ForegroundColor Red
        return $false
    }
    
    # Vérifier dotnet-ef
    try {
        $efVersion = dotnet tool list -g | Select-String "dotnet-ef"
        if ($efVersion) {
            Write-Host "✅ Entity Framework Core tools trouvés" -ForegroundColor Green
        } else {
            Write-Host "📦 Installation de dotnet-ef..." -ForegroundColor Cyan
            dotnet tool install --global dotnet-ef --version 8.0.0
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ dotnet-ef installé avec succès" -ForegroundColor Green
            } else {
                Write-Host "❌ Erreur lors de l'installation de dotnet-ef" -ForegroundColor Red
                return $false
            }
        }
    }
    catch {
        Write-Host "❌ Erreur lors de la vérification des outils EF" -ForegroundColor Red
        return $false
    }
    
    return $true
}

# Fonction pour créer une migration pour un service
function Add-ServiceMigration {
    param($serviceName, $serviceConfig)
    
    Write-Host "`n🔧 Ajout de la fonctionnalité : $serviceName" -ForegroundColor Magenta
    Write-Host "Description : $($serviceConfig.Description)" -ForegroundColor Cyan
    Write-Host "----------------------------------------" -ForegroundColor Gray
    
    $projectPath = $serviceConfig.ProjectPath
    $contextName = $serviceConfig.Context
    $migrationName = $serviceConfig.MigrationName
    
    # Vérifier si le projet existe
    if (-not (Test-Path $projectPath)) {
        Write-Host "❌ Projet non trouvé : $projectPath" -ForegroundColor Red
        return $false
    }
    
    try {
        Push-Location $projectPath
        
        # Vérifier s'il y a déjà des migrations
        $migrationsPath = Join-Path $projectPath "Migrations"
        $hasMigrations = Test-Path $migrationsPath
        
        if ($hasMigrations) {
            $migrationFiles = Get-ChildItem $migrationsPath -Filter "*.cs" | Where-Object { $_.Name -like "*$migrationName*" }
            if ($migrationFiles -and -not $Force) {
                Write-Host "ℹ️ Migration $migrationName déjà présente pour $serviceName" -ForegroundColor Blue
                Pop-Location
                return $true
            }
        }
        
        # Créer la migration
        Write-Host "📝 Création de la migration : $migrationName" -ForegroundColor Cyan
        
        dotnet ef migrations add $migrationName --context $contextName --verbose
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Migration $migrationName créée pour $serviceName" -ForegroundColor Green
            
            if (-not $OnlyGenerate) {
                # Appliquer la migration
                Write-Host "🚀 Application de la migration..." -ForegroundColor Cyan
                dotnet ef database update --context $contextName --verbose
                
                if ($LASTEXITCODE -eq 0) {
                    Write-Host "✅ Migration appliquée avec succès pour $serviceName" -ForegroundColor Green
                } else {
                    Write-Host "❌ Erreur lors de l'application de la migration pour $serviceName" -ForegroundColor Red
                    Pop-Location
                    return $false
                }
            }
            
            Pop-Location
            return $true
        } else {
            Write-Host "❌ Erreur lors de la création de la migration pour $serviceName" -ForegroundColor Red
            Pop-Location
            return $false
        }
    }
    catch {
        Write-Host "❌ Exception lors du traitement de $serviceName : $($_.Exception.Message)" -ForegroundColor Red
        Pop-Location
        return $false
    }
}

# Fonction pour traiter tous les services
function Add-AllNewFeatures {
    Write-Host "🚀 Ajout de toutes les nouvelles fonctionnalités..." -ForegroundColor Green
    
    $successCount = 0
    $totalCount = $newFeatures.Count
    
    foreach ($feature in $newFeatures.GetEnumerator()) {
        $success = Add-ServiceMigration $feature.Key $feature.Value
        if ($success) {
            $successCount++
        }
        
        # Pause entre les migrations
        Start-Sleep -Seconds 2
    }
    
    # Résumé
    Write-Host "`n📊 RÉSUMÉ DES NOUVELLES FONCTIONNALITÉS" -ForegroundColor Magenta
    Write-Host "=======================================" -ForegroundColor Magenta
    
    $successRate = [math]::Round(($successCount / $totalCount) * 100, 2)
    Write-Host "✅ Fonctionnalités ajoutées : $successCount/$totalCount ($successRate%)" -ForegroundColor Green
    
    if ($successRate -eq 100) {
        Write-Host "🎉 Toutes les nouvelles fonctionnalités ont été ajoutées !" -ForegroundColor Green
        
        Write-Host "`n🆕 Nouvelles fonctionnalités disponibles :" -ForegroundColor Cyan
        foreach ($feature in $newFeatures.GetEnumerator()) {
            Write-Host "   • $($feature.Key) : $($feature.Value.Description)" -ForegroundColor White
        }
        
        Write-Host "`n🚀 NafaPlace est maintenant une plateforme e-commerce complète !" -ForegroundColor Green
        
    } elseif ($successRate -ge 80) {
        Write-Host "⚠️ La plupart des fonctionnalités ont été ajoutées ($successRate%)" -ForegroundColor Yellow
        Write-Host "🔍 Vérifiez les erreurs ci-dessus pour les services en échec" -ForegroundColor Yellow
    } else {
        Write-Host "🚨 Plusieurs fonctionnalités ont échoué ($successRate%)" -ForegroundColor Red
        Write-Host "🔧 Vérifiez la configuration et les projets" -ForegroundColor Red
    }
}

# Fonction pour traiter un service spécifique
function Add-SpecificFeature {
    param($serviceName)
    
    if ($newFeatures.ContainsKey($serviceName)) {
        $success = Add-ServiceMigration $serviceName $newFeatures[$serviceName]
        if ($success) {
            Write-Host "🎉 Fonctionnalité $serviceName ajoutée avec succès !" -ForegroundColor Green
        } else {
            Write-Host "❌ Échec de l'ajout de la fonctionnalité $serviceName" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ Service '$serviceName' non reconnu" -ForegroundColor Red
        Write-Host "Services disponibles : $($newFeatures.Keys -join ', ')" -ForegroundColor Yellow
    }
}

# Fonction pour afficher l'aide
function Show-Help {
    Write-Host "📖 Aide - Ajout des nouvelles fonctionnalités" -ForegroundColor Cyan
    Write-Host "=============================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Ce script ajoute les nouvelles fonctionnalités aux bases existantes" -ForegroundColor White
    Write-Host ""
    Write-Host "Syntaxe :" -ForegroundColor Yellow
    Write-Host "  .\add-new-features-migrations.ps1 [options]" -ForegroundColor White
    Write-Host ""
    Write-Host "Options :" -ForegroundColor Yellow
    Write-Host "  -Service <nom>    Ajouter une fonctionnalité spécifique" -ForegroundColor White
    Write-Host "  -Force            Recréer les migrations existantes" -ForegroundColor White
    Write-Host "  -OnlyGenerate     Générer seulement, ne pas appliquer" -ForegroundColor White
    Write-Host ""
    Write-Host "Nouvelles fonctionnalités disponibles :" -ForegroundColor Yellow
    foreach ($feature in $newFeatures.GetEnumerator()) {
        Write-Host "  • $($feature.Key) : $($feature.Value.Description)" -ForegroundColor White
    }
    Write-Host ""
    Write-Host "Exemples :" -ForegroundColor Yellow
    Write-Host "  .\add-new-features-migrations.ps1                    # Ajouter toutes les fonctionnalités" -ForegroundColor White
    Write-Host "  .\add-new-features-migrations.ps1 -Service Analytics # Ajouter seulement Analytics" -ForegroundColor White
    Write-Host "  .\add-new-features-migrations.ps1 -Force             # Forcer la recréation" -ForegroundColor White
    Write-Host "  .\add-new-features-migrations.ps1 -OnlyGenerate      # Générer sans appliquer" -ForegroundColor White
}

# Fonction principale
function Start-NewFeaturesAddition {
    # Vérifier les prérequis
    if (-not (Test-Prerequisites)) {
        Write-Host "❌ Prérequis manquants. Impossible de continuer." -ForegroundColor Red
        return
    }
    
    if ($Service -eq "all") {
        Add-AllNewFeatures
    } elseif ($Service -eq "help") {
        Show-Help
    } else {
        Add-SpecificFeature $Service
    }
}

# Afficher la configuration
Write-Host "⚙️ Configuration :" -ForegroundColor Yellow
Write-Host "   • Service : $Service" -ForegroundColor White
Write-Host "   • Force : $Force" -ForegroundColor White
Write-Host "   • Générer seulement : $OnlyGenerate" -ForegroundColor White

# Vérifier les paramètres d'aide
if ($args -contains "-help" -or $args -contains "--help" -or $args -contains "/?") {
    Show-Help
    return
}

# Exécuter l'ajout des fonctionnalités
Start-NewFeaturesAddition

Write-Host "`n🏁 Script d'ajout des nouvelles fonctionnalités terminé !" -ForegroundColor Green
