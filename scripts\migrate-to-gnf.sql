-- Script de migration des données de XOF vers GNF
-- Taux de conversion: 1 XOF = 15 GNF (approximatif)

-- =============================================
-- MIGRATION CATALOG DATABASE
-- =============================================

-- Mise à jour des prix des produits (XOF vers GNF)
UPDATE "Products" 
SET "Price" = "Price" * 15,
    "Currency" = 'GNF'
WHERE "Currency" = 'XOF' OR "Currency" IS NULL;

-- Mise à jour des prix de comparaison
UPDATE "Products" 
SET "CompareAtPrice" = "CompareAtPrice" * 15
WHERE "CompareAtPrice" IS NOT NULL AND ("Currency" = 'GNF' OR "Currency" IS NULL);

-- Mise à jour des seuils de livraison gratuite dans les configurations
-- (Si vous avez une table de configuration)
-- UPDATE "Settings" 
-- SET "Value" = CAST((CAST("Value" AS DECIMAL) * 15) AS VARCHAR)
-- WHERE "Key" = 'FreeShippingThreshold';

-- =============================================
-- MIGRATION ORDER DATABASE
-- =============================================

-- Mise à jour des montants des commandes
UPDATE "Orders" 
SET "SubTotal" = "SubTotal" * 15,
    "ShippingFee" = "ShippingFee" * 15,
    "Tax" = "Tax" * 15,
    "Total" = "Total" * 15,
    "Currency" = 'GNF'
WHERE "Currency" = 'XOF' OR "Currency" IS NULL;

-- Mise à jour des éléments de commande
UPDATE "OrderItems" 
SET "UnitPrice" = "UnitPrice" * 15,
    "LineTotal" = "LineTotal" * 15
WHERE EXISTS (
    SELECT 1 FROM "Orders" o 
    WHERE o."Id" = "OrderItems"."OrderId" 
    AND o."Currency" = 'GNF'
);

-- =============================================
-- DONNÉES DE TEST EN GNF
-- =============================================

-- Insertion de produits de test avec prix en GNF
INSERT INTO "Products" ("Name", "Description", "Price", "Currency", "Stock", "CategoryId", "SellerId", "IsActive", "CreatedAt", "UpdatedAt")
VALUES 
    ('Smartphone Samsung Galaxy A54', 'Smartphone Android avec écran 6.4 pouces, 128GB', 2500000, 'GNF', 25, 1, 1, true, NOW(), NOW()),
    ('Ordinateur Portable HP Pavilion', 'PC portable 15.6 pouces, Intel Core i5, 8GB RAM', 8500000, 'GNF', 10, 2, 1, true, NOW(), NOW()),
    ('Chaussures Nike Air Max', 'Baskets de sport confortables, taille 42', 850000, 'GNF', 50, 3, 2, true, NOW(), NOW()),
    ('Réfrigérateur LG 300L', 'Réfrigérateur double porte, classe énergétique A+', 4200000, 'GNF', 8, 4, 2, true, NOW(), NOW()),
    ('Livre Histoire de la Guinée', 'Ouvrage de référence sur l''histoire guinéenne', 75000, 'GNF', 100, 5, 3, true, NOW(), NOW()),
    ('Montre Casio G-Shock', 'Montre sport étanche, résistante aux chocs', 1200000, 'GNF', 30, 6, 3, true, NOW(), NOW()),
    ('Sac à dos Eastpak', 'Sac à dos résistant, idéal pour l''école', 450000, 'GNF', 75, 7, 1, true, NOW(), NOW()),
    ('Télévision Samsung 55 pouces 4K', 'Smart TV 4K UHD, HDR, WiFi intégré', 6800000, 'GNF', 5, 1, 2, true, NOW(), NOW())
ON CONFLICT ("Name") DO UPDATE SET
    "Price" = EXCLUDED."Price",
    "Currency" = EXCLUDED."Currency",
    "UpdatedAt" = NOW();

-- =============================================
-- MISE À JOUR DES CONFIGURATIONS SYSTÈME
-- =============================================

-- Mise à jour des templates de notifications avec GNF
UPDATE "NotificationTemplates" 
SET "MessageTemplate" = REPLACE("MessageTemplate", 'XOF', 'GNF')
WHERE "MessageTemplate" LIKE '%XOF%';

-- Exemple de mise à jour spécifique pour le template de paiement
UPDATE "NotificationTemplates" 
SET "MessageTemplate" = 'Votre paiement de {Amount} GNF pour la commande #{OrderId} a été confirmé avec succès.'
WHERE "Name" = 'PaymentConfirmation';

-- =============================================
-- VÉRIFICATIONS POST-MIGRATION
-- =============================================

-- Vérifier les produits convertis
SELECT 
    "Name",
    "Price",
    "Currency",
    CASE 
        WHEN "Price" < 100000 THEN 'Budget (< 100,000 GNF)'
        WHEN "Price" < 1000000 THEN 'Moyen (100,000 - 1,000,000 GNF)'
        ELSE 'Premium (> 1,000,000 GNF)'
    END as "PriceRange"
FROM "Products" 
WHERE "Currency" = 'GNF'
ORDER BY "Price";

-- Vérifier les commandes converties
SELECT 
    "Id",
    "Total",
    "Currency",
    "CreatedAt"
FROM "Orders" 
WHERE "Currency" = 'GNF'
ORDER BY "CreatedAt" DESC
LIMIT 10;

-- Statistiques de migration
SELECT 
    'Products' as "Table",
    COUNT(*) as "Total",
    COUNT(CASE WHEN "Currency" = 'GNF' THEN 1 END) as "GNF_Count",
    AVG("Price") as "Average_Price_GNF"
FROM "Products"
UNION ALL
SELECT 
    'Orders' as "Table",
    COUNT(*) as "Total",
    COUNT(CASE WHEN "Currency" = 'GNF' THEN 1 END) as "GNF_Count",
    AVG("Total") as "Average_Total_GNF"
FROM "Orders";

-- =============================================
-- NOTES DE MIGRATION
-- =============================================

/*
NOTES IMPORTANTES:

1. Taux de conversion utilisé: 1 XOF = 15 GNF
2. Tous les montants ont été multipliés par 15
3. La devise par défaut est maintenant GNF
4. Les seuils de livraison gratuite: 500,000 GNF
5. Frais de livraison standard: 25,000 GNF
6. TVA: 18% (inchangée)

VÉRIFICATIONS À EFFECTUER:
- Tester les calculs de panier
- Vérifier les paiements Stripe
- Contrôler les notifications
- Valider l'affichage des prix

ROLLBACK (si nécessaire):
Pour revenir en arrière, diviser tous les montants par 15
et remettre la devise à 'XOF'
*/
