using Microsoft.AspNetCore.Mvc;

namespace NafaPlace.Catalog.API.Controllers
{
    [ApiController]
    [Route("api/v1/data")]
    public class DataController : ControllerBase
    {
        /// <summary>
        /// Récupère la liste de tous les vendeurs
        /// </summary>
        /// <returns>Liste des vendeurs</returns>
        [HttpGet("sellers")]
        public async Task<IActionResult> GetSellers()
        {
            try
            {
                // Retourner des vendeurs de test pour l'instant
                var sellers = new[]
                {
                    new { Id = 1, Name = "Électronique Mali", Email = "<EMAIL>", PhoneNumber = "+22379123456", Address = "123 Rue Bamako, Mali", IsActive = true, IsVerified = true },
                    new { Id = 2, Name = "Mode Africaine", Email = "<EMAIL>", PhoneNumber = "+22378456789", Address = "45 Avenue Dakar, Sénégal", IsActive = true, IsVerified = true },
                    new { Id = 3, Name = "Tech Guinée", Email = "<EMAIL>", PhoneNumber = "+22470123456", Address = "789 Boulevard Conakry, Guinée", IsActive = true, IsVerified = true }
                };

                return Ok(sellers);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Erreur lors de la récupération des vendeurs", error = ex.Message });
            }
        }
    }
}
