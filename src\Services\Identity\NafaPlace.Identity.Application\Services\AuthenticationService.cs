using System.Security.Cryptography;
using Microsoft.AspNetCore.Identity;
using NafaPlace.Identity.Domain.Models;
using NafaPlace.Identity.Domain.Repositories;
using NafaPlace.Identity.Domain.Services;

namespace NafaPlace.Identity.Application.Services;

public class AuthenticationService : IAuthenticationService
{
    private readonly IUserRepository _userRepository;
    private readonly ITokenService _tokenService;
    private readonly IPasswordHasher<User> _passwordHasher;

    public AuthenticationService(
        IUserRepository userRepository,
        ITokenService tokenService,
        IPasswordHasher<User> passwordHasher)
    {
        _userRepository = userRepository;
        _tokenService = tokenService;
        _passwordHasher = passwordHasher;
    }

    public async Task<AuthenticationResult> RegisterAsync(string email, string username, string password)
    {
        var existingUser = await _userRepository.GetByEmailOrUsernameAsync(email, username);
        if (existingUser != null)
        {
            return AuthenticationResult.Failure(new[] { "User with this email or username already exists" });
        }

        var user = new User
        {
            Email = email,
            Username = username,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            IsActive = true
        };

        user.PasswordHash = _passwordHasher.HashPassword(user, password);
        await _userRepository.CreateAsync(user);

        var accessToken = _tokenService.GenerateAccessToken(user);
        var refreshToken = _tokenService.GenerateRefreshToken();

        user.RefreshToken = refreshToken;
        user.RefreshTokenExpiryTime = DateTime.UtcNow.AddDays(7);
        await _userRepository.UpdateAsync(user);

        return AuthenticationResult.Success(accessToken, refreshToken, user);
    }

    public async Task<AuthenticationResult> LoginAsync(string emailOrUsername, string password)
    {
        var user = await _userRepository.GetByEmailOrUsernameAsync(emailOrUsername, emailOrUsername);
        if (user == null)
        {
            return AuthenticationResult.Failure(new[] { "Invalid credentials" });
        }

        var result = _passwordHasher.VerifyHashedPassword(user, user.PasswordHash, password);
        if (result == PasswordVerificationResult.Failed)
        {
            return AuthenticationResult.Failure(new[] { "Invalid credentials" });
        }

        var accessToken = _tokenService.GenerateAccessToken(user);
        var refreshToken = _tokenService.GenerateRefreshToken();

        user.RefreshToken = refreshToken;
        user.RefreshTokenExpiryTime = DateTime.UtcNow.AddDays(7);
        user.LastLoginAt = DateTime.UtcNow;
        await _userRepository.UpdateAsync(user);

        return AuthenticationResult.Success(accessToken, refreshToken, user);
    }

    public async Task<AuthenticationResult> RefreshTokenAsync(string accessToken, string refreshToken)
    {
        int? userId = _tokenService.ValidateRefreshToken(refreshToken);
        if (!userId.HasValue)
        {
            return AuthenticationResult.Failure(new[] { "Invalid refresh token" });
        }

        var user = await _userRepository.GetByIdAsync(userId.Value);
        if (user == null || user.RefreshToken != refreshToken || user.RefreshTokenExpiryTime <= DateTime.UtcNow)
        {
            return AuthenticationResult.Failure(new[] { "Invalid refresh token" });
        }

        var newAccessToken = _tokenService.GenerateAccessToken(user);
        var newRefreshToken = _tokenService.GenerateRefreshToken();

        user.RefreshToken = newRefreshToken;
        user.RefreshTokenExpiryTime = DateTime.UtcNow.AddDays(7);
        await _userRepository.UpdateAsync(user);

        return AuthenticationResult.Success(newAccessToken, newRefreshToken, user);
    }

    public async Task<bool> RevokeTokenAsync(string refreshToken)
    {
        int? userId = _tokenService.ValidateRefreshToken(refreshToken);
        if (!userId.HasValue)
        {
            return false;
        }

        var user = await _userRepository.GetByIdAsync(userId.Value);
        if (user == null || user.RefreshToken != refreshToken)
        {
            return false;
        }

        user.RefreshToken = null;
        user.RefreshTokenExpiryTime = null;
        await _userRepository.UpdateAsync(user);

        return true;
    }
}
