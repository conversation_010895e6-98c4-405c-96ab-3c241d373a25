# NafaPlace Wishlist Service

Service de gestion des listes de souhaits pour la plateforme NafaPlace.

## 🎯 Fonctionnalités

### ✅ Implémentées
- ✅ Création et gestion des listes de souhaits
- ✅ Ajout/suppression de produits dans la wishlist
- ✅ Support des utilisateurs authentifiés et invités
- ✅ API RESTful complète
- ✅ Intégration avec l'API Gateway
- ✅ Base de données PostgreSQL
- ✅ Migrations Entity Framework
- ✅ Interface utilisateur Blazor
- ✅ Composant WishlistButton réutilisable
- ✅ Indicateur de compteur dans le header
- ✅ Page dédiée à la wishlist
- ✅ Gestion des erreurs et notifications

### 🔄 En cours
- 🔄 Tests unitaires
- 🔄 Intégration avec le service Cart (move to cart)
- 🔄 Notifications en temps réel

### 📋 À venir
- 📋 Wishlist publiques/partagées
- 📋 Recommandations basées sur la wishlist
- 📋 Export de la wishlist
- 📋 Alertes de prix

## 🏗️ Architecture

```
NafaPlace.Wishlist/
├── Domain/           # Entités et modèles métier
├── Application/      # Services et DTOs
├── Infrastructure/   # Accès aux données et repositories
└── API/             # Contrôleurs et endpoints
```

## 🚀 Endpoints API

### Wishlist Management
- `GET /api/wishlist` - Obtenir la wishlist de l'utilisateur
- `GET /api/wishlist/summary` - Résumé de la wishlist
- `GET /api/wishlist/count` - Nombre d'articles dans la wishlist
- `DELETE /api/wishlist/clear` - Vider la wishlist

### Wishlist Items
- `GET /api/wishlist/items` - Obtenir les articles de la wishlist
- `POST /api/wishlist/items` - Ajouter un article à la wishlist
- `DELETE /api/wishlist/items/{productId}` - Retirer un article
- `GET /api/wishlist/items/{productId}/exists` - Vérifier si un produit est dans la wishlist
- `POST /api/wishlist/items/{productId}/move-to-cart` - Déplacer vers le panier

### Guest Support
- `GET /api/wishlist/guest/{guestId}` - Wishlist pour utilisateur invité
- `POST /api/wishlist/guest/{guestId}/items` - Ajouter à la wishlist invité

## 🗄️ Base de données

### Tables
- **Wishlists** - Listes de souhaits des utilisateurs
- **WishlistItems** - Articles dans les listes de souhaits

### Indexes
- Index sur `UserId` pour les performances
- Index unique sur `(UserId, ProductId)` pour éviter les doublons
- Index sur `AddedAt` pour les requêtes chronologiques

## 🔧 Configuration

### Variables d'environnement
```bash
ConnectionStrings__DefaultConnection=Host=localhost;Database=NafaPlace.Wishlist;Username=postgres;Password=postgres
IdentityUrl=http://localhost:5155
```

### Ports
- **API**: 5008
- **Database**: 5437 (PostgreSQL)

## 🧪 Tests

```bash
# Tester l'API
curl http://localhost:5008/api/wishlist/count

# Ajouter un produit (nécessite authentification)
curl -X POST http://localhost:5008/api/wishlist/items \
  -H "Content-Type: application/json" \
  -d '{"productId": 1, "productName": "Test Product", "productPrice": 25000, "currency": "GNF"}'
```

## 🐳 Docker

Le service est inclus dans le docker-compose principal :

```yaml
wishlist-api:
  image: lamyas92/nafaplace-wishlist-api:latest
  ports:
    - "5008:80"
  depends_on:
    - wishlist-db
```

## 🔗 Intégrations

- **Identity Service** - Authentification des utilisateurs
- **Catalog Service** - Informations produits
- **Cart Service** - Déplacement vers le panier
- **API Gateway** - Routage des requêtes
- **Web App** - Interface utilisateur

## 💡 Utilisation

### Dans les composants Blazor
```razor
<NafaPlace.Web.Shared.Components.WishlistButton Product="product" ShowText="true" />
```

### Service injection
```csharp
@inject IWishlistService WishlistService

// Ajouter à la wishlist
await WishlistService.AddToWishlistAsync(userId, request);

// Vérifier si dans la wishlist
var isInWishlist = await WishlistService.IsProductInWishlistAsync(userId, productId);
```

## 🎨 Interface utilisateur

- **Bouton Wishlist** - Composant réutilisable avec état visuel
- **Page Wishlist** - Vue complète avec gestion des articles
- **Indicateur Header** - Compteur en temps réel
- **Notifications** - Feedback utilisateur avec toasts

## 🔒 Sécurité

- Authentification JWT via Identity Service
- Validation des données d'entrée
- Protection contre les doublons
- Isolation des données utilisateur

## 📊 Monitoring

- Logs structurés avec Serilog
- Health checks pour la base de données
- Métriques de performance
- Gestion des erreurs centralisée
