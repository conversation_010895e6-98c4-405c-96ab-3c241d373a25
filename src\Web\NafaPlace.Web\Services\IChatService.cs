using NafaPlace.Web.Models;

namespace NafaPlace.Web.Services;

public interface IChatService
{
    // Gestion des conversations
    Task<int> StartConversationAsync(string subject, string initialMessage);
    Task<List<ChatConversationDto>> GetUserConversationsAsync();
    Task<ChatConversationDto?> GetConversationAsync(int conversationId);
    Task<bool> CloseConversationAsync(int conversationId);
    
    // Gestion des messages
    Task<int> SendMessageAsync(int conversationId, string content, string? attachmentUrl = null);
    Task<List<ChatMessageDto>> GetConversationMessagesAsync(int conversationId, int page = 1, int pageSize = 50);
    Task<bool> MarkMessageAsReadAsync(int messageId);
    Task<bool> MarkConversationAsReadAsync(int conversationId);
    
    // Support et FAQ
    Task<List<FAQItemDto>> GetFAQAsync(string? category = null);
    Task<List<string>> GetFAQCategoriesAsync();
    Task<ChatbotResponseDto> SendChatbotMessageAsync(string message, string sessionId);
    
    // Statut et présence
    Task<bool> UpdateUserPresenceAsync(bool isOnline);
    Task<List<ChatAgentDto>> GetAvailableAgentsAsync();
    Task<bool> RequestHumanAgentAsync(int conversationId, string reason);
    
    // Notifications temps réel
    Task<bool> JoinChatGroupAsync(int conversationId);
    Task<bool> LeaveChatGroupAsync(int conversationId);
    Task SendTypingIndicatorAsync(int conversationId, bool isTyping);
    
    // Satisfaction
    Task<bool> SubmitSatisfactionRatingAsync(int conversationId, int rating, string? comment = null);
    
    // Configuration
    Task<Dictionary<string, object>> GetChatConfigAsync();

    // Authentification
    Task<string?> GetAuthTokenAsync();
}

public class ChatConversationDto
{
    public int Id { get; set; }
    public string Subject { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty;
    public string? AssignedAgentId { get; set; }
    public string? AssignedAgentName { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? LastMessageAt { get; set; }
    public int UnreadCount { get; set; }
    public List<string> Tags { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class ChatMessageDto
{
    public int Id { get; set; }
    public int ConversationId { get; set; }
    public string Content { get; set; } = string.Empty;
    public string SenderId { get; set; } = string.Empty;
    public string SenderName { get; set; } = string.Empty;
    public string SenderType { get; set; } = string.Empty; // Customer, Agent, System
    public DateTime Timestamp { get; set; }
    public bool IsRead { get; set; }
    public string? AttachmentUrl { get; set; }
    public string? AttachmentType { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class FAQItemDto
{
    public int Id { get; set; }
    public string Question { get; set; } = string.Empty;
    public string Answer { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public List<string> Tags { get; set; } = new();
    public int ViewCount { get; set; }
    public bool IsHelpful { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class ChatbotResponseDto
{
    public string Message { get; set; } = string.Empty;
    public string Intent { get; set; } = string.Empty;
    public double Confidence { get; set; }
    public List<string> Suggestions { get; set; } = new();
    public List<FAQItemDto> RelatedFAQs { get; set; } = new();
    public bool RequiresHumanAgent { get; set; }
    public Dictionary<string, object> Context { get; set; } = new();
}

public class ChatAgentDto
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string? Avatar { get; set; }
    public string Status { get; set; } = string.Empty;
    public List<string> Departments { get; set; } = new();
    public List<string> Skills { get; set; } = new();
    public List<string> Languages { get; set; } = new();
    public int MaxConcurrentChats { get; set; } = 5;
    public int CurrentChatCount { get; set; }
    public DateTime? LastActiveAt { get; set; }
}
