using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace NafaPlace.Catalog.Application.DTOs.Product
{
    public class UpdateProductDto
    {
        [Required]
        [MaxLength(100)]
        public required string Name { get; set; }

        [Required]
        [MaxLength(500)]
        public required string Description { get; set; }

        [Required]
        [Range(0, double.MaxValue)]
        public decimal Price { get; set; }

        public int CategoryId { get; set; }

        [Required]
        [Range(0, int.MaxValue)]
        public int StockQuantity { get; set; }

        [Required]
        [MaxLength(3)]
        public required string Currency { get; set; }

        [MaxLength(50)]
        public string? Brand { get; set; }

        [MaxLength(50)]
        public string? Model { get; set; }

        [Range(0, double.MaxValue)]
        public decimal Weight { get; set; }

        [MaxLength(50)]
        public string? Dimensions { get; set; }

        public bool IsActive { get; set; }
        public bool IsFeatured { get; set; }

        [Required]
        public int SellerId { get; set; }

        public List<CreateProductImageRequest>? Images { get; set; }
        public List<CreateProductVariantRequest>? Variants { get; set; }
        public List<CreateProductAttributeRequest>? Attributes { get; set; }
    }
}
