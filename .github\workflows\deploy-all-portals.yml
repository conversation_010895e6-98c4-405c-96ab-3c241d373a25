name: Deploy All Portals to Fly.io Test

on:
  workflow_dispatch:
    inputs:
      deploy_web:
        description: 'Deploy Web Portal'
        required: false
        default: true
        type: boolean
      deploy_seller:
        description: 'Deploy Seller Portal'
        required: false
        default: true
        type: boolean
      deploy_admin:
        description: 'Deploy Admin Portal'
        required: false
        default: true
        type: boolean

jobs:
  deploy-web:
    if: ${{ github.event.inputs.deploy_web == 'true' }}
    name: Deploy Web Portal
    runs-on: ubuntu-latest
    environment: staging

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '9.0.x'

    - name: Restore dependencies
      run: dotnet restore

    - name: Build
      run: dotnet build --no-restore --configuration Release

    - name: Setup Fly CLI
      uses: superfly/flyctl-actions/setup-flyctl@master

    - name: Create Fly.io app if it doesn't exist
      run: |
        if ! flyctl apps list | grep -q "nafaplace-web-test"; then
          echo "Creating nafaplace-web-test app..."
          flyctl apps create nafaplace-web-test --org personal
        else
          echo "App nafaplace-web-test already exists"
        fi
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

    - name: Deploy Web Portal to Fly.io
      run: flyctl deploy --config fly-web.toml --dockerfile Dockerfile.web
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

  deploy-seller:
    if: ${{ github.event.inputs.deploy_seller == 'true' }}
    name: Deploy Seller Portal
    runs-on: ubuntu-latest
    environment: staging

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '9.0.x'

    - name: Restore dependencies
      run: dotnet restore

    - name: Build
      run: dotnet build --no-restore --configuration Release

    - name: Setup Fly CLI
      uses: superfly/flyctl-actions/setup-flyctl@master

    - name: Create Fly.io app if it doesn't exist
      run: |
        if ! flyctl apps list | grep -q "nafaplace-seller-test"; then
          echo "Creating nafaplace-seller-test app..."
          flyctl apps create nafaplace-seller-test --org personal
        else
          echo "App nafaplace-seller-test already exists"
        fi
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

    - name: Deploy Seller Portal to Fly.io
      run: flyctl deploy --config fly-seller.toml --dockerfile Dockerfile.seller
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

  deploy-admin:
    if: ${{ github.event.inputs.deploy_admin == 'true' }}
    name: Deploy Admin Portal
    runs-on: ubuntu-latest
    environment: staging

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '9.0.x'

    - name: Restore dependencies
      run: dotnet restore

    - name: Build
      run: dotnet build --no-restore --configuration Release

    - name: Setup Fly CLI
      uses: superfly/flyctl-actions/setup-flyctl@master

    - name: Create Fly.io app if it doesn't exist
      run: |
        if ! flyctl apps list | grep -q "nafaplace-admin-test"; then
          echo "Creating nafaplace-admin-test app..."
          flyctl apps create nafaplace-admin-test --org personal
        else
          echo "App nafaplace-admin-test already exists"
        fi
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

    - name: Deploy Admin Portal to Fly.io
      run: flyctl deploy --config fly-admin.toml --dockerfile Dockerfile.admin
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}
