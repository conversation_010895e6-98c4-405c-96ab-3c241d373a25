using NafaPlace.Reviews.Application.DTOs;
using NafaPlace.Reviews.Application.Interfaces;
using NafaPlace.Reviews.Application.Services;
using NafaPlace.Reviews.Domain.Models;
using NafaPlace.Reviews.DTOs;

namespace NafaPlace.Reviews.Application.Services;

public partial class ReviewService : IReviewService
{
    // Search and Filter operations
    
    public async Task<ReviewsPagedResult> SearchReviewsAsync(string searchTerm, int? productId = null, int? minRating = null, int? maxRating = null, bool? isVerifiedOnly = null, int page = 1, int pageSize = 10)
    {
        var reviews = await _reviewRepository.SearchReviewsAsync(searchTerm, productId, minRating, maxRating, isVerifiedOnly, page, pageSize);
        var totalCount = await _reviewRepository.CountSearchResultsAsync(searchTerm, productId, minRating, maxRating, isVerifiedOnly);
        
        return new ReviewsPagedResult
        {
            Reviews = reviews.Select(MapToDto).ToList(),
            TotalCount = totalCount,
            PageNumber = page,
            PageSize = pageSize,
            TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize)
        };
    }
    
    // Moderation operations
    
    public async Task<ReviewsPagedResult> GetReviewsByStatusAsync(ReviewStatus status, int page = 1, int pageSize = 10)
    {
        var reviews = await _reviewRepository.GetReviewsByStatusAsync(status, page, pageSize);
        var totalCount = await _reviewRepository.CountReviewsByStatusAsync(status);
        
        return new ReviewsPagedResult
        {
            Reviews = reviews.Select(MapToDto).ToList(),
            TotalCount = totalCount,
            PageNumber = page,
            PageSize = pageSize,
            TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize)
        };
    }
    
    public async Task<ReviewDto> UpdateReviewStatusAsync(int id, ReviewStatus status, string moderatorId, string? notes = null)
    {
        var review = await _reviewRepository.GetByIdAsync(id);
        if (review == null)
        {
            throw new ArgumentException("Review not found");
        }
        
        var previousStatus = review.Status;
        review = await _reviewRepository.UpdateReviewStatusAsync(id, status);
        
        // Send notifications if status changed to Published or Rejected
        if (previousStatus != ReviewStatus.Published && status == ReviewStatus.Published)
        {
            await _notificationService.SendReviewApprovedNotificationAsync(id, review.UserId);
        }
        else if (previousStatus != ReviewStatus.Rejected && status == ReviewStatus.Rejected)
        {
            await _notificationService.SendReviewRejectedNotificationAsync(id, review.UserId, notes);
        }
        
        return MapToDto(review);
    }
}
