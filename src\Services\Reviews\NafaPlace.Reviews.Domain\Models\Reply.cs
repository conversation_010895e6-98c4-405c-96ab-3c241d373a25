using System.ComponentModel.DataAnnotations;

namespace NafaPlace.Reviews.Domain.Models;

public class Reply
{
    public int Id { get; set; }
    
    [Required]
    public int ReviewId { get; set; }
    
    [Required]
    public string UserId { get; set; } = string.Empty;
    
    [Required]
    public string UserName { get; set; } = string.Empty;
    
    [Required]
    [StringLength(1000)]
    public string Content { get; set; } = string.Empty;
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    public bool IsFromSeller { get; set; }
    
    // Navigation property
    public virtual Review Review { get; set; } = null!;
}
