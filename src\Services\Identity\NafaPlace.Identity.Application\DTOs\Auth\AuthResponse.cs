namespace NafaPlace.Identity.Application.DTOs.Auth;

public record AuthResponse
{
    public bool Success { get; init; } = true;
    public string Message { get; init; } = string.Empty;
    public string AccessToken { get; init; } = string.Empty;
    public string RefreshToken { get; init; } = string.Empty;
    public DateTime ExpiresAt { get; init; }
    public string TokenType { get; init; } = "Bearer";
    public UserDto User { get; init; } = null!;
    public string? Token { get; init; } // Pour compatibilité avec le client
}
