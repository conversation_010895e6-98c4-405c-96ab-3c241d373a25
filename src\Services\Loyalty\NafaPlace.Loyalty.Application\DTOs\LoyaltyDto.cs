using NafaPlace.Loyalty.Domain.Enums;

namespace NafaPlace.Loyalty.Application.DTOs;

public class LoyaltyAccountDto
{
    public string UserId { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public int TotalPoints { get; set; }
    public int AvailablePoints { get; set; }
    public int PendingPoints { get; set; }
    public int LifetimePoints { get; set; }
    public LoyaltyTier CurrentTier { get; set; }
    public int PointsToNextTier { get; set; }
    public DateTime TierExpiryDate { get; set; }
    public decimal TotalSpent { get; set; }
    public int TotalOrders { get; set; }
    public DateTime JoinedAt { get; set; }
    public DateTime LastActivity { get; set; }
    public bool IsActive { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
    public List<LoyaltyBadgeDto> Badges { get; set; } = new();
    public LoyaltyStatsDto Stats { get; set; } = new();
}

public class LoyaltyPointTransactionDto
{
    public int Id { get; set; }
    public string UserId { get; set; } = string.Empty;
    public TransactionType Type { get; set; }
    public int Points { get; set; }
    public string Description { get; set; } = string.Empty;
    public string? ReferenceId { get; set; }
    public string? ReferenceType { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public TransactionStatus Status { get; set; }
    public string? ReasonCode { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class RewardDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string? ImageUrl { get; set; }
    public RewardType Type { get; set; }
    public RewardCategory Category { get; set; }
    public int PointsCost { get; set; }
    public decimal? MonetaryValue { get; set; }
    public int? DiscountPercentage { get; set; }
    public decimal? DiscountAmount { get; set; }
    public int? FreeShippingThreshold { get; set; }
    public List<LoyaltyTier> EligibleTiers { get; set; } = new();
    public int StockQuantity { get; set; }
    public int MaxRedemptionsPerUser { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public bool IsActive { get; set; }
    public bool IsFeatured { get; set; }
    public int Priority { get; set; }
    public List<string> Terms { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
    public RewardStatsDto Stats { get; set; } = new();
}

public class RewardRedemptionDto
{
    public int Id { get; set; }
    public string UserId { get; set; } = string.Empty;
    public int RewardId { get; set; }
    public string RewardName { get; set; } = string.Empty;
    public int PointsUsed { get; set; }
    public RedemptionStatus Status { get; set; }
    public DateTime RedeemedAt { get; set; }
    public DateTime? UsedAt { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public string? CouponCode { get; set; }
    public string? QRCode { get; set; }
    public int? OrderId { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class LoyaltyTierDto
{
    public LoyaltyTier Tier { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string? Color { get; set; }
    public string? Icon { get; set; }
    public int MinPoints { get; set; }
    public decimal MinSpent { get; set; }
    public int MinOrders { get; set; }
    public List<TierBenefitDto> Benefits { get; set; } = new();
    public double PointsMultiplier { get; set; } = 1.0;
    public int BonusPointsOnBirthday { get; set; }
    public bool FreeShipping { get; set; }
    public int EarlyAccessHours { get; set; }
    public bool PrioritySupport { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class TierBenefitDto
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public BenefitType Type { get; set; }
    public string Value { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
}

public class LoyaltyBadgeDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string? ImageUrl { get; set; }
    public BadgeCategory Category { get; set; }
    public BadgeRarity Rarity { get; set; }
    public int PointsReward { get; set; }
    public List<BadgeRequirementDto> Requirements { get; set; } = new();
    public DateTime? EarnedAt { get; set; }
    public bool IsActive { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class BadgeRequirementDto
{
    public string Type { get; set; } = string.Empty;
    public string Condition { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty;
    public bool IsMet { get; set; }
}

public class LoyaltyChallengeDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string? ImageUrl { get; set; }
    public ChallengeType Type { get; set; }
    public ChallengeDifficulty Difficulty { get; set; }
    public int PointsReward { get; set; }
    public List<ChallengeRequirementDto> Requirements { get; set; } = new();
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public int MaxParticipants { get; set; }
    public int CurrentParticipants { get; set; }
    public bool IsActive { get; set; }
    public ChallengeProgressDto? UserProgress { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class ChallengeRequirementDto
{
    public string Type { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int TargetValue { get; set; }
    public int CurrentValue { get; set; }
    public bool IsCompleted { get; set; }
}

public class ChallengeProgressDto
{
    public int ChallengeId { get; set; }
    public string UserId { get; set; } = string.Empty;
    public DateTime StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public int Progress { get; set; }
    public bool IsCompleted { get; set; }
    public Dictionary<string, int> RequirementProgress { get; set; } = new();
}

public class ReferralProgramDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int ReferrerPoints { get; set; }
    public int RefereePoints { get; set; }
    public decimal? ReferrerCashback { get; set; }
    public decimal? RefereeCashback { get; set; }
    public int MaxReferrals { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public bool IsActive { get; set; }
    public List<string> Terms { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class ReferralDto
{
    public int Id { get; set; }
    public string ReferrerId { get; set; } = string.Empty;
    public string ReferrerName { get; set; } = string.Empty;
    public string RefereeId { get; set; } = string.Empty;
    public string RefereeName { get; set; } = string.Empty;
    public string ReferralCode { get; set; } = string.Empty;
    public ReferralStatus Status { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public int PointsEarned { get; set; }
    public decimal CashbackEarned { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class LoyaltyStatsDto
{
    public int TotalPointsEarned { get; set; }
    public int TotalPointsRedeemed { get; set; }
    public int TotalRedemptions { get; set; }
    public int BadgesEarned { get; set; }
    public int ChallengesCompleted { get; set; }
    public int ReferralsMade { get; set; }
    public decimal TotalSavings { get; set; }
    public int DaysActive { get; set; }
    public DateTime LastEarnedPoints { get; set; }
    public DateTime LastRedeemedPoints { get; set; }
}

public class RewardStatsDto
{
    public int TotalRedemptions { get; set; }
    public int ActiveRedemptions { get; set; }
    public int ExpiredRedemptions { get; set; }
    public double RedemptionRate { get; set; }
    public int AveragePointsPerRedemption { get; set; }
    public DateTime LastRedeemed { get; set; }
}

public class LoyaltyAnalyticsDto
{
    public string Period { get; set; } = string.Empty;
    public int TotalMembers { get; set; }
    public int ActiveMembers { get; set; }
    public int NewMembers { get; set; }
    public Dictionary<LoyaltyTier, int> MembersByTier { get; set; } = new();
    public int TotalPointsIssued { get; set; }
    public int TotalPointsRedeemed { get; set; }
    public double RedemptionRate { get; set; }
    public decimal TotalSavingsProvided { get; set; }
    public Dictionary<string, int> TopRewards { get; set; } = new();
    public Dictionary<string, int> PointsSourceBreakdown { get; set; } = new();
    public double AveragePointsPerMember { get; set; }
    public double MemberRetentionRate { get; set; }
    public Dictionary<string, object> AdditionalMetrics { get; set; } = new();
}

public class LoyaltyEventDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public EventType Type { get; set; }
    public double PointsMultiplier { get; set; } = 1.0;
    public int BonusPoints { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public List<string> EligibleCategories { get; set; } = new();
    public List<LoyaltyTier> EligibleTiers { get; set; } = new();
    public bool IsActive { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class PointsEarnRuleDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public EarnRuleType Type { get; set; }
    public int PointsPerUnit { get; set; }
    public decimal? MinimumAmount { get; set; }
    public decimal? MaximumAmount { get; set; }
    public List<string> EligibleCategories { get; set; } = new();
    public List<LoyaltyTier> EligibleTiers { get; set; } = new();
    public int MaxPointsPerDay { get; set; }
    public int MaxPointsPerMonth { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public bool IsActive { get; set; }
    public int Priority { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class LoyaltyNotificationDto
{
    public int Id { get; set; }
    public string UserId { get; set; } = string.Empty;
    public NotificationType Type { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public Dictionary<string, object> Data { get; set; } = new();
    public bool IsRead { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? ReadAt { get; set; }
}

// Enums
public enum LoyaltyTier
{
    Bronze = 1,
    Silver = 2,
    Gold = 3,
    Platinum = 4,
    Diamond = 5
}

public enum TransactionType
{
    Earned,
    Redeemed,
    Expired,
    Adjusted,
    Bonus,
    Refunded,
    Transferred
}

public enum TransactionStatus
{
    Pending,
    Completed,
    Cancelled,
    Expired,
    Failed
}

public enum RewardType
{
    Discount,
    FreeShipping,
    Product,
    Cashback,
    ExperienceGift,
    ServiceUpgrade,
    EarlyAccess,
    ExclusiveContent
}

public enum RewardCategory
{
    Shopping,
    Shipping,
    Experience,
    Digital,
    Physical,
    Service,
    Exclusive
}

public enum RedemptionStatus
{
    Active,
    Used,
    Expired,
    Cancelled,
    Pending
}

public enum BenefitType
{
    PointsMultiplier,
    FreeShipping,
    EarlyAccess,
    PrioritySupport,
    ExclusiveOffers,
    BirthdayBonus,
    CustomService
}

public enum BadgeCategory
{
    Purchase,
    Engagement,
    Social,
    Milestone,
    Seasonal,
    Special,
    Achievement
}

public enum BadgeRarity
{
    Common,
    Uncommon,
    Rare,
    Epic,
    Legendary
}

public enum ChallengeType
{
    Purchase,
    Engagement,
    Social,
    Streak,
    Milestone,
    Seasonal,
    Community
}

public enum ChallengeDifficulty
{
    Easy,
    Medium,
    Hard,
    Expert
}

public enum ReferralStatus
{
    Pending,
    Completed,
    Expired,
    Cancelled
}

public enum EventType
{
    DoublePoints,
    BonusPoints,
    SpecialRewards,
    TierUpgrade,
    Flash,
    Seasonal,
    Anniversary
}

public enum EarnRuleType
{
    Purchase,
    Registration,
    Review,
    Referral,
    SocialShare,
    Newsletter,
    Birthday,
    Login,
    Survey,
    Custom
}

public enum NotificationType
{
    PointsEarned,
    PointsExpiring,
    RewardAvailable,
    TierUpgrade,
    BadgeEarned,
    ChallengeCompleted,
    ReferralCompleted,
    SpecialOffer
}
