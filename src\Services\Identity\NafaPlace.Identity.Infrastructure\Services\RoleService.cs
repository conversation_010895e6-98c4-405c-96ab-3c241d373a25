using Microsoft.EntityFrameworkCore;
using NafaPlace.Identity.Application.Common.Exceptions;
using NafaPlace.Identity.Application.Common.Interfaces;
using NafaPlace.Identity.Application.DTOs.Role;
using NafaPlace.Identity.Domain.Models;
using NafaPlace.Identity.Infrastructure.Data;

namespace NafaPlace.Identity.Infrastructure.Services;

public class RoleService : IRoleService
{
    private readonly IdentityDbContext _context;

    public RoleService(IdentityDbContext context)
    {
        _context = context;
    }

    public async Task<RoleDto> CreateRoleAsync(CreateRoleRequest request)
    {
        var existingRole = await _context.Roles.FirstOrDefaultAsync(r => r.Name == request.Name);
        if (existingRole != null)
        {
            throw new ValidationException($"Role with name {request.Name} already exists");
        }

        var role = new Role
        {
            Name = request.Name,
            Description = request.Description,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        _context.Roles.Add(role);
        await _context.SaveChangesAsync();

        return new RoleDto
        {
            Id = role.Id,
            Name = role.Name,
            Description = role.Description
        };
    }

    public async Task<RoleDto> UpdateRoleAsync(int id, UpdateRoleRequest request)
    {
        var role = await _context.Roles.FindAsync(id);
        if (role == null)
        {
            throw new NotFoundException("Role", id);
        }

        role.Name = request.Name;
        role.Description = request.Description;
        role.UpdatedAt = DateTime.UtcNow;

        _context.Roles.Update(role);
        await _context.SaveChangesAsync();

        return new RoleDto
        {
            Id = role.Id,
            Name = role.Name,
            Description = role.Description
        };
    }

    public async Task DeleteRoleAsync(int id)
    {
        var role = await _context.Roles.FindAsync(id);
        if (role == null)
        {
            throw new NotFoundException("Role", id);
        }

        // Vérifier si le rôle est assigné à des utilisateurs
        var userRolesCount = await _context.Users
            .Where(u => u.Roles.Contains(role.Name))
            .CountAsync();

        if (userRolesCount > 0)
        {
            throw new ValidationException("Cannot delete role that is assigned to users");
        }

        _context.Roles.Remove(role);
        await _context.SaveChangesAsync();
    }

    public async Task<RoleDto> GetRoleByIdAsync(int id)
    {
        var role = await _context.Roles.FindAsync(id);
        if (role == null)
        {
            throw new NotFoundException("Role", id);
        }

        return new RoleDto
        {
            Id = role.Id,
            Name = role.Name,
            Description = role.Description
        };
    }

    public async Task<IEnumerable<RoleDto>> GetAllRolesAsync()
    {
        var roles = await _context.Roles.ToListAsync();
        return roles.Select(r => new RoleDto
        {
            Id = r.Id,
            Name = r.Name,
            Description = r.Description
        });
    }

    public async Task AssignRoleToUserAsync(int userId, int roleId)
    {
        var user = await _context.Users.FindAsync(userId);
        if (user == null)
        {
            throw new NotFoundException("User", userId);
        }

        var role = await _context.Roles.FindAsync(roleId);
        if (role == null)
        {
            throw new NotFoundException("Role", roleId);
        }

        // Vérifier si l'utilisateur a déjà ce rôle
        var userRoles = user.Roles.Split(',', StringSplitOptions.RemoveEmptyEntries);
        if (userRoles.Contains(role.Name))
        {
            // L'utilisateur a déjà ce rôle
            return;
        }

        // Mettre à jour la propriété Roles de l'utilisateur
        var rolesList = userRoles.ToList();
        rolesList.Add(role.Name);
        user.Roles = string.Join(",", rolesList);
        
        _context.Users.Update(user);
        await _context.SaveChangesAsync();
    }

    public async Task RemoveRoleFromUserAsync(int userId, int roleId)
    {
        var user = await _context.Users.FindAsync(userId);
        if (user == null)
        {
            throw new NotFoundException("User", userId);
        }

        var role = await _context.Roles.FindAsync(roleId);
        if (role == null)
        {
            throw new NotFoundException("Role", roleId);
        }

        // Vérifier si l'utilisateur a ce rôle
        var userRoles = user.Roles.Split(',', StringSplitOptions.RemoveEmptyEntries);
        if (!userRoles.Contains(role.Name))
        {
            return;
        }

        // Mettre à jour la propriété Roles de l'utilisateur
        var rolesList = userRoles.ToList();
        rolesList.Remove(role.Name);
        user.Roles = string.Join(",", rolesList);
        
        _context.Users.Update(user);
        await _context.SaveChangesAsync();
    }

    public async Task<IEnumerable<RoleDto>> GetUserRolesAsync(int userId)
    {
        var user = await _context.Users.FindAsync(userId);
        if (user == null)
        {
            throw new NotFoundException("User", userId);
        }

        var roleNames = user.Roles.Split(',', StringSplitOptions.RemoveEmptyEntries);
        var roles = await _context.Roles
            .Where(r => roleNames.Contains(r.Name))
            .ToListAsync();

        return roles.Select(r => new RoleDto
        {
            Id = r.Id,
            Name = r.Name,
            Description = r.Description
        });
    }
}
