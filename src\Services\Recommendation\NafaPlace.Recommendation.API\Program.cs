using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using NafaPlace.Recommendation.Infrastructure.Data;
using NafaPlace.Recommendation.Application.Services;
using NafaPlace.Recommendation.Application.Interfaces;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Database
var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");
builder.Services.AddDbContext<RecommendationDbContext>(options =>
    options.UseNpgsql(connectionString));

// Redis Cache
var redisConnectionString = builder.Configuration.GetConnectionString("Redis");
builder.Services.AddStackExchangeRedisCache(options =>
{
    options.Configuration = redisConnectionString;
});

// JWT Authentication
var jwtSettings = builder.Configuration.GetSection("JwtSettings");
var secretKey = jwtSettings["SecretKey"];

builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = jwtSettings["Issuer"],
            ValidAudience = jwtSettings["Audience"],
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey))
        };
    });

builder.Services.AddAuthorization();

// CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

// Application Services
builder.Services.AddScoped<IRecommendationService, RecommendationService>();
builder.Services.AddScoped<NafaPlace.Recommendation.Application.Interfaces.IRecommendationRepository, NafaPlace.Recommendation.Infrastructure.Repositories.RecommendationRepository>();
builder.Services.AddScoped<NafaPlace.Recommendation.Application.Interfaces.ICollaborativeFilteringService, NafaPlace.Recommendation.Infrastructure.Services.CollaborativeFilteringService>();
builder.Services.AddScoped<NafaPlace.Recommendation.Application.Interfaces.IContentBasedService, NafaPlace.Recommendation.Infrastructure.Services.ContentBasedService>();
builder.Services.AddScoped<NafaPlace.Recommendation.Application.Interfaces.IMLModelService, NafaPlace.Recommendation.Infrastructure.Services.MLModelService>();

// Background Services
builder.Services.AddHostedService<NafaPlace.Recommendation.Infrastructure.BackgroundServices.RecommendationProcessingService>();
builder.Services.AddHostedService<NafaPlace.Recommendation.Infrastructure.BackgroundServices.ModelTrainingService>();

// HTTP Clients for external services
builder.Services.AddHttpClient("CatalogAPI", client =>
{
    client.BaseAddress = new Uri(builder.Configuration["Services:CatalogAPI"] ?? "http://localhost:5243");
});

builder.Services.AddHttpClient("OrderAPI", client =>
{
    client.BaseAddress = new Uri(builder.Configuration["Services:OrderAPI"] ?? "http://localhost:5004");
});

// Logging
builder.Logging.ClearProviders();
builder.Logging.AddConsole();
builder.Logging.AddDebug();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseCors("AllowAll");
app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

// Ensure database is created
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<RecommendationDbContext>();
    context.Database.EnsureCreated();
}

app.Run();
