namespace NafaPlace.Coupon.Application.DTOs;

public class UsageStatsDto
{
    public int CouponId { get; set; }
    public string CouponCode { get; set; } = string.Empty;
    public int TotalUsages { get; set; }
    public decimal TotalDiscountAmount { get; set; }
    public DateTime FirstUsage { get; set; }
    public DateTime LastUsage { get; set; }
    public List<UsageByDateDto> UsagesByDate { get; set; } = new();
    public List<TopUserDto> TopUsers { get; set; } = new();
}

public class UsageByDateDto
{
    public DateTime Date { get; set; }
    public int Count { get; set; }
    public decimal TotalDiscount { get; set; }
}

public class TopUserDto
{
    public string UserId { get; set; } = string.Empty;
    public int UsageCount { get; set; }
    public decimal TotalDiscount { get; set; }
}
