using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NafaPlace.Reviews.Application.DTOs;
using NafaPlace.Reviews.Application.Services;
using NafaPlace.Reviews.Domain.Models;
using System.Security.Claims;

namespace NafaPlace.Reviews.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class ReportsController : ControllerBase
{
    private readonly IReviewService _reviewService;

    public ReportsController(IReviewService reviewService)
    {
        _reviewService = reviewService;
    }

    [HttpGet("{id}")]
    [Authorize(Roles = "Admin,Moderator")]
    public async Task<ActionResult<ReviewReportDto>> GetReport(int id)
    {
        var report = await _reviewService.GetReportByIdAsync(id);
        if (report == null)
            return NotFound();

        return Ok(report);
    }

    [HttpGet("review/{reviewId}")]
    [Authorize(Roles = "Admin,Moderator")]
    public async Task<ActionResult<List<ReviewReportDto>>> GetReportsByReview(int reviewId)
    {
        var reports = await _reviewService.GetReportsByReviewIdAsync(reviewId);
        return Ok(reports);
    }

    [HttpGet("status/{status}")]
    [Authorize(Roles = "Admin,Moderator")]
    public async Task<ActionResult<List<ReviewReportDto>>> GetReportsByStatus(
        string status, 
        [FromQuery] int page = 1, 
        [FromQuery] int pageSize = 10)
    {
        if (!Enum.TryParse<ReportStatus>(status, out var reportStatus))
        {
            return BadRequest($"Invalid report status: {status}");
        }
        
        var reports = await _reviewService.GetReportsByStatusAsync(reportStatus, page, pageSize);
        return Ok(reports);
    }

    [HttpGet("summary")]
    [Authorize(Roles = "Admin,Moderator")]
    public async Task<ActionResult<ReviewReportSummaryDto>> GetReportSummary()
    {
        var summary = await _reviewService.GetReportSummaryAsync();
        return Ok(summary);
    }

    [HttpGet("mostReported")]
    [Authorize(Roles = "Admin,Moderator")]
    public async Task<ActionResult<List<ReportedReviewDto>>> GetMostReportedReviews([FromQuery] int count = 10)
    {
        var reviews = await _reviewService.GetMostReportedReviewsAsync(count);
        return Ok(reviews);
    }

    [HttpPost]
    [Authorize]
    public async Task<ActionResult<ReviewReportDto>> CreateReport([FromBody] CreateReviewReportRequest request)
    {
        try
        {
            var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

            if (string.IsNullOrEmpty(currentUserId))
                return Unauthorized();

            var report = await _reviewService.CreateReportAsync(request, currentUserId);
            return CreatedAtAction(nameof(GetReport), new { id = report.Id }, report);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal server error: {ex.Message}");
        }
    }

    [HttpPut("{id}")]
    [Authorize(Roles = "Admin,Moderator")]
    public async Task<ActionResult<ReviewReportDto>> UpdateReportStatus(int id, [FromBody] UpdateReviewReportRequest request)
    {
        try
        {
            var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(currentUserId))
                return Unauthorized();

            var report = await _reviewService.UpdateReportStatusAsync(id, request, currentUserId);
            return Ok(report);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal server error: {ex.Message}");
        }
    }
}
