# Script pour tester les services avec .NET 9.0
Write-Host "Test des services NafaPlace avec .NET 9.0" -ForegroundColor Green

# Configuration des endpoints
$services = @(
    @{ Name = "Identity API"; Url = "http://localhost:5001/health"; Port = 5001 },
    @{ Name = "Catalog API"; Url = "http://localhost:5002/health"; Port = 5002 },
    @{ Name = "Cart API"; Url = "http://localhost:5003/health"; Port = 5003 },
    @{ Name = "Order API"; Url = "http://localhost:5004/health"; Port = 5004 },
    @{ Name = "Payment API"; Url = "http://localhost:5005/health"; Port = 5005 }
)

Write-Host "`nTest des services:" -ForegroundColor Cyan

$successCount = 0
$totalCount = $services.Count

foreach ($service in $services) {
    try {
        Write-Host "Testing $($service.Name)..." -ForegroundColor Yellow -NoNewline
        
        # Test de connectivité sur le port
        $tcpClient = New-Object System.Net.Sockets.TcpClient
        $connect = $tcpClient.BeginConnect("localhost", $service.Port, $null, $null)
        $wait = $connect.AsyncWaitHandle.WaitOne(3000, $false)
        
        if ($wait) {
            $tcpClient.EndConnect($connect)
            $tcpClient.Close()
            
            # Test HTTP si le port est ouvert
            try {
                $response = Invoke-WebRequest -Uri $service.Url -Method GET -TimeoutSec 5 -ErrorAction SilentlyContinue
                if ($response.StatusCode -eq 200) {
                    Write-Host " OK (HTTP 200)" -ForegroundColor Green
                    $successCount++
                } else {
                    Write-Host " WARN (HTTP $($response.StatusCode))" -ForegroundColor Yellow
                    $successCount++
                }
            }
            catch {
                # Essayer sans /health
                try {
                    $baseUrl = $service.Url -replace "/health", ""
                    $response = Invoke-WebRequest -Uri $baseUrl -Method GET -TimeoutSec 5 -ErrorAction SilentlyContinue
                    Write-Host " OK (Port ouvert)" -ForegroundColor Green
                    $successCount++
                }
                catch {
                    Write-Host " WARN (Port ouvert, pas de reponse HTTP)" -ForegroundColor Yellow
                    $successCount++
                }
            }
        } else {
            $tcpClient.Close()
            Write-Host " FAIL (Port ferme)" -ForegroundColor Red
        }
    }
    catch {
        Write-Host " ERROR ($($_.Exception.Message))" -ForegroundColor Red
    }
}

Write-Host "`nTest des bases de donnees:" -ForegroundColor Cyan

# Test PostgreSQL
try {
    Write-Host "Testing PostgreSQL..." -ForegroundColor Yellow -NoNewline
    $tcpClient = New-Object System.Net.Sockets.TcpClient
    $connect = $tcpClient.BeginConnect("localhost", 5441, $null, $null)
    $wait = $connect.AsyncWaitHandle.WaitOne(3000, $false)
    
    if ($wait) {
        $tcpClient.EndConnect($connect)
        $tcpClient.Close()
        Write-Host " OK (Port 5441 ouvert)" -ForegroundColor Green
    } else {
        $tcpClient.Close()
        Write-Host " FAIL (Port 5441 ferme)" -ForegroundColor Red
    }
}
catch {
    Write-Host " ERROR" -ForegroundColor Red
}

# Test Redis
try {
    Write-Host "Testing Redis..." -ForegroundColor Yellow -NoNewline
    $tcpClient = New-Object System.Net.Sockets.TcpClient
    $connect = $tcpClient.BeginConnect("localhost", 6379, $null, $null)
    $wait = $connect.AsyncWaitHandle.WaitOne(3000, $false)
    
    if ($wait) {
        $tcpClient.EndConnect($connect)
        $tcpClient.Close()
        Write-Host " OK (Port 6379 ouvert)" -ForegroundColor Green
    } else {
        $tcpClient.Close()
        Write-Host " FAIL (Port 6379 ferme)" -ForegroundColor Red
    }
}
catch {
    Write-Host " ERROR" -ForegroundColor Red
}

# Résumé
Write-Host "`nResume:" -ForegroundColor Magenta
$successRate = [math]::Round(($successCount / $totalCount) * 100, 1)
Write-Host "Services fonctionnels: $successCount/$totalCount ($successRate%)" -ForegroundColor $(if($successRate -ge 80){'Green'}elseif($successRate -ge 60){'Yellow'}else{'Red'})

if ($successRate -ge 80) {
    Write-Host "`nExcellent! Les services .NET 9.0 fonctionnent correctement." -ForegroundColor Green
} elseif ($successRate -ge 60) {
    Write-Host "`nBien! La plupart des services fonctionnent." -ForegroundColor Yellow
} else {
    Write-Host "`nAttention! Plusieurs services ont des problemes." -ForegroundColor Red
}

Write-Host "`nCommandes utiles:" -ForegroundColor Cyan
Write-Host "  docker-compose -f docker-compose.minimal.yml logs [service]" -ForegroundColor White
Write-Host "  docker-compose -f docker-compose.minimal.yml restart [service]" -ForegroundColor White
Write-Host "  docker-compose -f docker-compose.minimal.yml ps" -ForegroundColor White

Write-Host "`nTest termine." -ForegroundColor Blue
