using Microsoft.EntityFrameworkCore;
using NafaPlace.Catalog.Domain.Models;

namespace NafaPlace.Catalog.Application.Common.Interfaces
{
    public interface ICatalogDbContext
    {
        DbSet<Product> Products { get; set; }
        DbSet<Category> Categories { get; set; }
        DbSet<ProductImage> ProductImages { get; set; }
        DbSet<ProductVariant> ProductVariants { get; set; }
        DbSet<ProductAttribute> ProductAttributes { get; set; }
        DbSet<ProductVariantAttribute> ProductVariantAttributes { get; set; }
        DbSet<Seller> Sellers { get; set; }

        Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
    }
}
