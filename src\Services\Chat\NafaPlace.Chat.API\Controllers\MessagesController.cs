using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using NafaPlace.Chat.Application.Services;
using NafaPlace.Chat.Application.DTOs;
using System.Security.Claims;

namespace NafaPlace.Chat.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class MessagesController : ControllerBase
{
    private readonly IChatService _chatService;
    private readonly ILogger<MessagesController> _logger;

    public MessagesController(IChatService chatService, ILogger<MessagesController> logger)
    {
        _chatService = chatService;
        _logger = logger;
    }

    [HttpPost]
    [Authorize]
    public async Task<IActionResult> SendMessage([FromBody] SendMessageRequest request)
    {
        try
        {
            var userId = GetUserId();
            var userName = GetUserName();

            var messageDto = new SendMessageDto
            {
                ConversationId = request.ConversationId,
                Content = request.Content,
                MessageType = MessageType.Text,
                SenderType = SenderType.Customer,
                SenderId = userId,
                SenderName = userName,
                Attachments = request.AttachmentUrl != null ? 
                    new List<MessageAttachmentDto> 
                    { 
                        new() { Url = request.AttachmentUrl, Type = "file" } 
                    } : new List<MessageAttachmentDto>(),
                Metadata = request.Metadata ?? new Dictionary<string, object>()
            };

            var messageId = await _chatService.SendMessageAsync(messageDto, userId);

            if (messageId > 0)
            {
                return Ok(new { messageId });
            }

            return BadRequest("Impossible d'envoyer le message");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi du message");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    [HttpPut("{id}/read")]
    [Authorize]
    public async Task<IActionResult> MarkMessageAsRead(int id)
    {
        try
        {
            var success = await _chatService.MarkMessageAsReadAsync(id);
            
            if (success)
            {
                return Ok(new { message = "Message marqué comme lu" });
            }

            return BadRequest("Impossible de marquer le message comme lu");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du marquage du message comme lu {MessageId}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    [HttpPut("{id}")]
    [Authorize]
    public async Task<IActionResult> EditMessage(int id, [FromBody] EditMessageRequest request)
    {
        try
        {
            var success = await _chatService.EditMessageAsync(id, request.Content);
            
            if (success)
            {
                return Ok(new { message = "Message modifié avec succès" });
            }

            return BadRequest("Impossible de modifier le message");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la modification du message {MessageId}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    [HttpDelete("{id}")]
    [Authorize]
    public async Task<IActionResult> DeleteMessage(int id)
    {
        try
        {
            var success = await _chatService.DeleteMessageAsync(id);
            
            if (success)
            {
                return Ok(new { message = "Message supprimé avec succès" });
            }

            return BadRequest("Impossible de supprimer le message");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression du message {MessageId}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    [HttpPost("upload")]
    [Authorize]
    public async Task<IActionResult> UploadFile([FromForm] IFormFile file, [FromForm] int conversationId)
    {
        try
        {
            if (file == null || file.Length == 0)
            {
                return BadRequest("Aucun fichier fourni");
            }

            // Vérifier la taille du fichier (max 10MB)
            if (file.Length > 10 * 1024 * 1024)
            {
                return BadRequest("Le fichier est trop volumineux (max 10MB)");
            }

            // Vérifier le type de fichier
            var allowedTypes = new[] { "image/jpeg", "image/png", "image/gif", "application/pdf", "text/plain" };
            if (!allowedTypes.Contains(file.ContentType))
            {
                return BadRequest("Type de fichier non autorisé");
            }

            using var stream = file.OpenReadStream();
            var fileUrl = await _chatService.UploadFileAsync(stream, file.FileName, file.ContentType, conversationId);

            if (!string.IsNullOrEmpty(fileUrl))
            {
                return Ok(new { fileUrl });
            }

            return BadRequest("Impossible d'uploader le fichier");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'upload du fichier");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    [HttpGet("download")]
    [Authorize]
    public async Task<IActionResult> DownloadFile([FromQuery] string fileUrl)
    {
        try
        {
            var fileStream = await _chatService.DownloadFileAsync(fileUrl);
            
            if (fileStream != null)
            {
                var fileName = Path.GetFileName(fileUrl);
                return File(fileStream, "application/octet-stream", fileName);
            }

            return NotFound("Fichier non trouvé");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du téléchargement du fichier");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    private string GetUserId()
    {
        return User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? 
               User.FindFirst("sub")?.Value ?? 
               "anonymous";
    }

    private string GetUserName()
    {
        return User.FindFirst(ClaimTypes.Name)?.Value ?? 
               User.FindFirst("name")?.Value ?? 
               "Utilisateur";
    }
}

// DTOs pour les requêtes
public class SendMessageRequest
{
    public int ConversationId { get; set; }
    public string Content { get; set; } = string.Empty;
    public string? AttachmentUrl { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}

public class EditMessageRequest
{
    public string Content { get; set; } = string.Empty;
}
