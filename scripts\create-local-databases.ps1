# Script de création des bases de données locales NafaPlace
# Auteur: Assistant IA
# Date: 2025-01-28

param(
    [string]$PostgreSQLPath = "C:\Program Files\PostgreSQL\17\bin",
    [string]$Host = "localhost",
    [string]$Port = "5432",
    [string]$AdminUser = "postgres",
    [string]$AdminPassword = "",
    [string]$AppUser = "nafaplace",
    [string]$AppPassword = "NafaPlace2025@Dev",
    [switch]$Force
)

Write-Host "🗄️ Création des bases de données locales NafaPlace" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green

# Configuration des bases de données
$databases = @(
    "NafaPlace.Catalog",
    "NafaPlace.Order", 
    "NafaPlace.Notification",
    "NafaPlace.Analytics",
    "NafaPlace.Chat",
    "NafaPlace.Recommendation",
    "NafaPlace.Loyalty",
    "NafaPlace.Localization",
    "NafaPlace.Inventory",
    "NafaPlace.Search"
)

# Fonction pour exécuter une commande SQL
function Invoke-PostgreSQLCommand {
    param(
        [string]$Command,
        [string]$Database = "postgres",
        [string]$User = $AdminUser,
        [string]$Password = $AdminPassword
    )
    
    try {
        $psqlPath = Join-Path $PostgreSQLPath "psql.exe"
        
        if (-not (Test-Path $psqlPath)) {
            Write-Host "❌ psql.exe non trouvé dans : $PostgreSQLPath" -ForegroundColor Red
            return $false
        }
        
        # Créer un fichier temporaire pour le mot de passe
        $pgpassFile = Join-Path $env:TEMP "pgpass_temp.txt"
        "$Host`:$Port`:$Database`:$User`:$Password" | Out-File -FilePath $pgpassFile -Encoding ASCII
        
        # Définir la variable d'environnement
        $env:PGPASSFILE = $pgpassFile
        
        # Exécuter la commande
        $result = & $psqlPath -h $Host -p $Port -U $User -d $Database -c $Command -t -A 2>&1
        
        # Nettoyer
        Remove-Item $pgpassFile -ErrorAction SilentlyContinue
        Remove-Item Env:PGPASSFILE -ErrorAction SilentlyContinue
        
        if ($LASTEXITCODE -eq 0) {
            return $result
        } else {
            Write-Host "❌ Erreur SQL : $result" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ Exception : $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Fonction pour tester la connexion PostgreSQL
function Test-PostgreSQLConnection {
    Write-Host "🔍 Test de connexion PostgreSQL..." -ForegroundColor Yellow
    
    # Si pas de mot de passe fourni, demander
    if ([string]::IsNullOrEmpty($AdminPassword)) {
        $securePassword = Read-Host "Mot de passe pour l'utilisateur postgres" -AsSecureString
        $AdminPassword = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($securePassword))
    }
    
    $result = Invoke-PostgreSQLCommand "SELECT version();" "postgres" $AdminUser $AdminPassword
    
    if ($result) {
        Write-Host "✅ Connexion PostgreSQL réussie" -ForegroundColor Green
        Write-Host "ℹ️ Version : $($result.Split(',')[0])" -ForegroundColor Blue
        return $true
    } else {
        Write-Host "❌ Impossible de se connecter à PostgreSQL" -ForegroundColor Red
        return $false
    }
}

# Fonction pour créer l'utilisateur application
function New-ApplicationUser {
    Write-Host "👤 Création de l'utilisateur application..." -ForegroundColor Yellow
    
    # Vérifier si l'utilisateur existe
    $userExists = Invoke-PostgreSQLCommand "SELECT 1 FROM pg_roles WHERE rolname='$AppUser';" "postgres" $AdminUser $AdminPassword
    
    if ($userExists -and $userExists.Trim() -eq "1") {
        if ($Force) {
            Write-Host "🗑️ Suppression de l'utilisateur existant..." -ForegroundColor Yellow
            Invoke-PostgreSQLCommand "DROP USER IF EXISTS $AppUser;" "postgres" $AdminUser $AdminPassword | Out-Null
        } else {
            Write-Host "ℹ️ Utilisateur $AppUser existe déjà" -ForegroundColor Blue
            return $true
        }
    }
    
    # Créer l'utilisateur
    $createUserResult = Invoke-PostgreSQLCommand "CREATE USER $AppUser WITH PASSWORD '$AppPassword' CREATEDB;" "postgres" $AdminUser $AdminPassword
    
    if ($createUserResult -ne $false) {
        Write-Host "✅ Utilisateur $AppUser créé avec succès" -ForegroundColor Green
        return $true
    } else {
        Write-Host "❌ Erreur lors de la création de l'utilisateur $AppUser" -ForegroundColor Red
        return $false
    }
}

# Fonction pour créer une base de données
function New-Database {
    param([string]$DatabaseName)
    
    Write-Host "📊 Création de la base : $DatabaseName" -ForegroundColor Cyan
    
    # Vérifier si la base existe
    $dbExists = Invoke-PostgreSQLCommand "SELECT 1 FROM pg_database WHERE datname='$DatabaseName';" "postgres" $AdminUser $AdminPassword
    
    if ($dbExists -and $dbExists.Trim() -eq "1") {
        if ($Force) {
            Write-Host "🗑️ Suppression de la base existante..." -ForegroundColor Yellow
            
            # Terminer les connexions actives
            Invoke-PostgreSQLCommand "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname='$DatabaseName';" "postgres" $AdminUser $AdminPassword | Out-Null
            
            # Supprimer la base
            Invoke-PostgreSQLCommand "DROP DATABASE IF EXISTS `"$DatabaseName`";" "postgres" $AdminUser $AdminPassword | Out-Null
        } else {
            Write-Host "ℹ️ Base de données $DatabaseName existe déjà" -ForegroundColor Blue
            return $true
        }
    }
    
    # Créer la base de données
    $createDbResult = Invoke-PostgreSQLCommand "CREATE DATABASE `"$DatabaseName`" OWNER $AppUser;" "postgres" $AdminUser $AdminPassword
    
    if ($createDbResult -ne $false) {
        Write-Host "✅ Base de données $DatabaseName créée" -ForegroundColor Green
        
        # Accorder les privilèges
        Invoke-PostgreSQLCommand "GRANT ALL PRIVILEGES ON DATABASE `"$DatabaseName`" TO $AppUser;" "postgres" $AdminUser $AdminPassword | Out-Null
        
        return $true
    } else {
        Write-Host "❌ Erreur lors de la création de $DatabaseName" -ForegroundColor Red
        return $false
    }
}

# Fonction pour lister les bases créées
function Show-CreatedDatabases {
    Write-Host "`n📋 Vérification des bases créées..." -ForegroundColor Magenta
    
    $query = "SELECT datname FROM pg_database WHERE datname LIKE 'NafaPlace.%' ORDER BY datname;"
    $result = Invoke-PostgreSQLCommand $query "postgres" $AdminUser $AdminPassword
    
    if ($result) {
        Write-Host "✅ Bases de données NafaPlace trouvées :" -ForegroundColor Green
        $result.Split("`n") | Where-Object { $_.Trim() -ne "" } | ForEach-Object {
            Write-Host "   • $_" -ForegroundColor White
        }
    } else {
        Write-Host "⚠️ Aucune base de données NafaPlace trouvée" -ForegroundColor Yellow
    }
}

# Fonction pour créer les chaînes de connexion
function Show-ConnectionStrings {
    Write-Host "`n🔗 Chaînes de connexion pour les services :" -ForegroundColor Magenta
    
    foreach ($db in $databases) {
        $connectionString = "Host=$Host;Port=$Port;Database=$db;Username=$AppUser;Password=$AppPassword"
        Write-Host "📊 $db :" -ForegroundColor Cyan
        Write-Host "   $connectionString" -ForegroundColor White
    }
}

# Fonction pour mettre à jour les fichiers appsettings
function Update-AppSettings {
    Write-Host "`n⚙️ Mise à jour des fichiers appsettings..." -ForegroundColor Magenta
    
    $services = @{
        "Catalog" = "NafaPlace.Catalog"
        "Order" = "NafaPlace.Order"
        "Notification" = "NafaPlace.Notification"
        "Analytics" = "NafaPlace.Analytics"
        "Chat" = "NafaPlace.Chat"
        "Recommendation" = "NafaPlace.Recommendation"
        "Loyalty" = "NafaPlace.Loyalty"
        "Localization" = "NafaPlace.Localization"
    }
    
    foreach ($service in $services.GetEnumerator()) {
        $serviceName = $service.Key
        $databaseName = $service.Value
        $appsettingsPath = "src/Services/$serviceName/NafaPlace.$serviceName.API/appsettings.json"
        
        if (Test-Path $appsettingsPath) {
            try {
                $content = Get-Content $appsettingsPath -Raw | ConvertFrom-Json
                $newConnectionString = "Host=$Host;Port=$Port;Database=$databaseName;Username=$AppUser;Password=$AppPassword"
                $content.ConnectionStrings.DefaultConnection = $newConnectionString
                
                $content | ConvertTo-Json -Depth 10 | Set-Content $appsettingsPath -Encoding UTF8
                Write-Host "✅ Mis à jour : $appsettingsPath" -ForegroundColor Green
            }
            catch {
                Write-Host "⚠️ Erreur mise à jour : $appsettingsPath" -ForegroundColor Yellow
            }
        }
    }
}

# Fonction principale
function Start-DatabaseCreation {
    Write-Host "🚀 Démarrage de la création des bases de données..." -ForegroundColor Green
    
    # Test de connexion
    if (-not (Test-PostgreSQLConnection)) {
        Write-Host "❌ Impossible de continuer sans connexion PostgreSQL" -ForegroundColor Red
        return $false
    }
    
    # Créer l'utilisateur application
    if (-not (New-ApplicationUser)) {
        Write-Host "❌ Impossible de créer l'utilisateur application" -ForegroundColor Red
        return $false
    }
    
    # Créer les bases de données
    $successCount = 0
    foreach ($database in $databases) {
        if (New-Database $database) {
            $successCount++
        }
        Start-Sleep -Seconds 1
    }
    
    # Résumé
    Write-Host "`n📊 RÉSUMÉ DE LA CRÉATION" -ForegroundColor Magenta
    Write-Host "========================" -ForegroundColor Magenta
    
    $successRate = [math]::Round(($successCount / $databases.Count) * 100, 2)
    Write-Host "✅ Bases créées : $successCount/$($databases.Count) ($successRate%)" -ForegroundColor Green
    
    if ($successRate -eq 100) {
        Write-Host "🎉 Toutes les bases de données ont été créées avec succès !" -ForegroundColor Green
        
        # Afficher les bases créées
        Show-CreatedDatabases
        
        # Afficher les chaînes de connexion
        Show-ConnectionStrings
        
        # Mettre à jour les appsettings
        Update-AppSettings
        
        Write-Host "`n🚀 NafaPlace est prêt pour les migrations !" -ForegroundColor Green
        Write-Host "Prochaine étape : Exécuter les migrations EF Core" -ForegroundColor Cyan
        
        return $true
    } else {
        Write-Host "⚠️ Certaines bases n'ont pas pu être créées" -ForegroundColor Yellow
        return $false
    }
}

# Afficher les paramètres
Write-Host "⚙️ Configuration :" -ForegroundColor Yellow
Write-Host "   • Host PostgreSQL : $Host:$Port" -ForegroundColor White
Write-Host "   • Utilisateur admin : $AdminUser" -ForegroundColor White
Write-Host "   • Utilisateur app : $AppUser" -ForegroundColor White
Write-Host "   • Force recréation : $Force" -ForegroundColor White
Write-Host "   • Chemin PostgreSQL : $PostgreSQLPath" -ForegroundColor White

# Exécuter la création
$result = Start-DatabaseCreation

Write-Host "`n🏁 Script terminé !" -ForegroundColor Green

if ($result) {
    exit 0
} else {
    exit 1
}
