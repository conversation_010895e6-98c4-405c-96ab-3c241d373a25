using System.Collections.Generic;

namespace NafaPlace.Catalog.Application.DTOs.Product;

public class ProductSearchRequest
{
    public required string SearchTerm { get; set; } = string.Empty;
    public int? CategoryId { get; set; }
    public int? SellerId { get; set; }
    public decimal? MinPrice { get; set; }
    public decimal? MaxPrice { get; set; }
    public required string Currency { get; set; } = "GNF";
    public required string Brand { get; set; } = string.Empty;
    public bool? IsActive { get; set; }
    public bool? IsFeatured { get; set; }
    public decimal? MinRating { get; set; }
    public required Dictionary<string, List<string>> Attributes { get; set; } = new();
    public required string SortBy { get; set; } = "relevance";
    public bool SortDescending { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 10;
}
