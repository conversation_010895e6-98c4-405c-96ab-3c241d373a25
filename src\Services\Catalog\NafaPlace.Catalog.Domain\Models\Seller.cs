using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using NafaPlace.Catalog.Domain.Common;

namespace NafaPlace.Catalog.Domain.Models
{
    public class Seller : BaseEntity
    {
        [Required]
        [MaxLength(100)]
        public required string Name { get; set; }
        
        [Required]
        [MaxLength(100)]
        [EmailAddress]
        public required string Email { get; set; }
        
        [Required]
        [MaxLength(20)]
        public required string PhoneNumber { get; set; }
        
        [Required]
        [MaxLength(200)]
        public required string Address { get; set; }
        
        public bool IsActive { get; set; } = true;
        

        public string? UserId { get; set; }
        public string? ProfilePictureUrl { get; set; }
        
        // Navigation property
        public virtual ICollection<Product> Products { get; set; } = new List<Product>();
    }
}
