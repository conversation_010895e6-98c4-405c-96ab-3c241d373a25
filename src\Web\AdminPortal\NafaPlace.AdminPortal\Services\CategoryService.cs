﻿using NafaPlace.AdminPortal.Models;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;

namespace NafaPlace.AdminPortal.Services
{
    public class CategoryService
    {
        private readonly HttpClient _httpClient;
        private readonly string _apiBaseUrl;
        private readonly JsonSerializerOptions _jsonOptions = new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        };

        public CategoryService(HttpClient httpClient)
        {
            _httpClient = httpClient;
            _apiBaseUrl = httpClient.BaseAddress?.ToString().TrimEnd('/') ?? "http://localhost:5243";
        }

        public async Task<IEnumerable<CategoryDto>> GetAllCategoriesAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync("/api/v1/categories");
                
                
                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    throw new HttpRequestException($"Erreur lors de la rÃ©cupÃ©ration des catÃ©gories: {response.StatusCode}, {errorContent}");
                }
                
                var categories = await response.Content.ReadFromJsonAsync<IEnumerable<CategoryDto>>(_jsonOptions);
               
                return categories ?? Enumerable.Empty<CategoryDto>();
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"GetAllCategoriesAsync: InnerException - {ex.InnerException.Message}");
                }
                throw;
            }
        }

        public async Task<CategoryDto> GetCategoryByIdAsync(int id)
        {
            try
            {
                var response = await _httpClient.GetAsync($"/api/v1/categories/{id}");
                response.EnsureSuccessStatusCode();
                var result = await response.Content.ReadFromJsonAsync<CategoryDto>(_jsonOptions);
                return result ?? new CategoryDto();
            }
            catch (Exception)
            {
                throw;
            }
        }

        public async Task<CategoryDto> CreateCategoryAsync(CreateCategoryRequest request)
        {
            try
            {
                
                var content = new StringContent(JsonSerializer.Serialize(request, _jsonOptions), Encoding.UTF8, "application/json");
                var response = await _httpClient.PostAsync("/api/v1/categories", content);
                
                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    throw new Exception($"Erreur lors de la crÃ©ation de la catÃ©gorie: {response.StatusCode}");
                }
                
                var responseContent = await response.Content.ReadAsStringAsync();
                
                var category = JsonSerializer.Deserialize<CategoryDto>(responseContent, _jsonOptions);
                
                return category ?? new CategoryDto();
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"CreateCategoryAsync: InnerException - {ex.InnerException.Message}");
                }
                throw;
            }
        }

        public async Task<CategoryDto> UpdateCategoryAsync(int id, UpdateCategoryRequest request)
        {
            try
            {
                var response = await _httpClient.PutAsJsonAsync($"/api/v1/categories/{id}", request, _jsonOptions);
                
                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"UpdateCategoryAsync: Error - {response.StatusCode}, Content: {errorContent}");
                }
                
                response.EnsureSuccessStatusCode();
                var result = await response.Content.ReadFromJsonAsync<CategoryDto>(_jsonOptions);
                return result ?? new CategoryDto();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"UpdateCategoryAsync Error: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }
                throw;
            }
        }

        public async Task<bool> DeleteCategoryAsync(int id)
        {
            try
            {
                var response = await _httpClient.DeleteAsync($"/api/v1/categories/{id}");
                
                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();

                }
                
                response.EnsureSuccessStatusCode();
                return true;
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"DeleteCategoryAsync: InnerException - {ex.InnerException.Message}");
                }
                throw;
            }
        }

        public async Task<IEnumerable<CategoryDto>> GetMainCategoriesAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync("/api/v1/categories/main");
                response.EnsureSuccessStatusCode();
                var result = await response.Content.ReadFromJsonAsync<IEnumerable<CategoryDto>>(_jsonOptions);
                return result ?? Array.Empty<CategoryDto>();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"GetMainCategoriesAsync Error: {ex.Message}");
                throw;
            }
        }

        public async Task<IEnumerable<CategoryDto>> GetSubcategoriesAsync(int parentId)
        {
            try
            {
                var response = await _httpClient.GetAsync($"/api/v1/categories/{parentId}/subcategories");
                response.EnsureSuccessStatusCode();
                var result = await response.Content.ReadFromJsonAsync<IEnumerable<CategoryDto>>(_jsonOptions);
                return result ?? Array.Empty<CategoryDto>();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"GetSubcategoriesAsync Error: {ex.Message}");
                throw;
            }
        }

        public string GetImageUrl(CategoryDto category, bool thumbnail = false)
        {
            try
            {
                if (category == null)
                {
                    return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNk+A8AAQUBAScY42YAAAAASUVORK5CYII=";
                }

                var url = category.ImageUrl;

                if (string.IsNullOrEmpty(url))
                {
                    return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNk+A8AAQUBAScY42YAAAAASUVORK5CYII=";
                }

                // Si l'URL est dÃ©jÃ  complÃ¨te (http/https) ou est une data URL, la retourner telle quelle
                if (url.StartsWith("http://") || url.StartsWith("https://") || url.StartsWith("data:"))
                {
                    return url;
                }

                // Sinon, construire l'URL complÃ¨te avec l'API base URL
                return _apiBaseUrl + (url.StartsWith("/") ? url : $"/{url}");
            }
            catch (Exception)
            {
                return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNk+A8AAQUBAScY42YAAAAASUVORK5CYII=";
            }
        }
    }
}

