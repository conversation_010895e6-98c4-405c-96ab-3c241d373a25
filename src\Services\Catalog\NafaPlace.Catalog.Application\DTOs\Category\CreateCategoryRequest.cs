using System;
using System.Text.Json.Serialization;

namespace NafaPlace.Catalog.Application.DTOs.Category
{
    public class CreateCategoryRequest
    {
        public required string Name { get; set; }
        public required string Description { get; set; }
        public string? IconUrl { get; set; }
        public string? ImageUrl { get; set; }
        
        [JsonPropertyName("parentId")]
        public int? ParentId { get; set; }
    }
}
