@page "/payment/orange-money/{OrderId:int}"
@using NafaPlace.Web.Models.Order
@inject NavigationManager NavigationManager
@inject IOrderService OrderService

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header bg-warning text-white text-center">
                    <h4><i class="fas fa-mobile-alt me-2"></i>Paiement Orange Money</h4>
                </div>
                <div class="card-body">
                    @if (_loading)
                    {
                        <div class="text-center">
                            <div class="spinner-border text-warning" role="status">
                                <span class="visually-hidden">Chargement...</span>
                            </div>
                            <p class="mt-2">Initialisation du paiement...</p>
                        </div>
                    }
                    else if (_order != null)
                    {
                        <div class="text-center mb-4">
                            <h5>Commande #@_order.Id.ToString().PadLeft(8, '0')</h5>
                            <p class="text-muted">Montant à payer: <strong>@_order.TotalAmount.ToString("N0") GNF</strong></p>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Instructions de paiement:</strong>
                            <ol class="mt-2 mb-0">
                                <li>Composez <strong>#144#</strong> sur votre téléphone</li>
                                <li>Sélectionnez <strong>Paiement marchand</strong></li>
                                <li>Entrez le code marchand: <strong>NAFA001</strong></li>
                                <li>Entrez le montant: <strong>@_order.TotalAmount.ToString("N0")</strong></li>
                                <li>Confirmez avec votre code PIN</li>
                            </ol>
                        </div>

                        <div class="text-center">
                            <button class="btn btn-warning btn-lg me-2" @onclick="SimulatePayment">
                                <i class="fas fa-check me-2"></i>Simuler le paiement
                            </button>
                            <button class="btn btn-outline-secondary" @onclick="CancelPayment">
                                <i class="fas fa-times me-2"></i>Annuler
                            </button>
                        </div>

                        <div class="mt-3 text-center">
                            <small class="text-muted">
                                <i class="fas fa-shield-alt me-1"></i>
                                Paiement sécurisé par Orange Money
                            </small>
                        </div>
                    }
                    else
                    {
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Erreur lors du chargement de la commande.
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    [Parameter] public int OrderId { get; set; }
    
    private OrderDto? _order;
    private bool _loading = true;

    protected override async Task OnInitializedAsync()
    {
        try
        {
            _order = await OrderService.GetOrderByIdAsync(OrderId);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement de la commande: {ex.Message}");
        }
        finally
        {
            _loading = false;
        }
    }

    private async Task SimulatePayment()
    {
        try
        {
            // En mode développement, simuler le paiement réussi
            // TODO: Intégrer avec l'API Orange Money réelle
            
            // Rediriger vers la confirmation
            NavigationManager.NavigateTo($"/order-confirmation/{OrderId}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la simulation du paiement: {ex.Message}");
        }
    }

    private void CancelPayment()
    {
        NavigationManager.NavigateTo("/cart");
    }
}
