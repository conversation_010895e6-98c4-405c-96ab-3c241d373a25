@page "/categories/add"
@using NafaPlace.AdminPortal.Models
@using NafaPlace.AdminPortal.Services
@inject CategoryService CategoryService
@inject NavigationManager NavigationManager

<h3>Ajouter une catégorie</h3>

<EditForm Model="@category" OnValidSubmit="@HandleValidSubmit">
    <DataAnnotationsValidator />
    <ValidationSummary />

    <div class="form-group">
        <label for="name">Nom</label>
        <InputText id="name" class="form-control" @bind-Value="category.Name" />
    </div>

    <div class="form-group">
        <label for="description">Description</label>
        <InputText id="description" class="form-control" @bind-Value="category.Description" />
    </div>

    <button type="submit" class="btn btn-primary">Ajouter</button>
</EditForm>

@code {
    private CreateCategoryRequest category = new CreateCategoryRequest();

    private async Task HandleValidSubmit()
    {
        await CategoryService.CreateCategoryAsync(category);
        NavigationManager.NavigateTo("/categories");
    }
}