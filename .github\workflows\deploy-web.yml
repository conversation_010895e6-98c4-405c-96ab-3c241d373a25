name: Deploy Web Portal to Fly.io Test

on:
  push:
    branches: [ main ]
    paths:
      - 'src/Web/NafaPlace.Web/**'
      - 'Dockerfile.web'
      - 'fly-web.toml'
      - '.github/workflows/deploy-web.yml'
  workflow_dispatch:

jobs:
  deploy:
    name: Deploy Web Portal
    runs-on: ubuntu-latest
    environment: staging

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '9.0.x'

    - name: Restore dependencies
      run: dotnet restore src/Web/NafaPlace.Web/NafaPlace.Web.csproj

    - name: Build
      run: dotnet build src/Web/NafaPlace.Web/NafaPlace.Web.csproj --no-restore --configuration Release

    - name: Setup Fly CLI
      uses: superfly/flyctl-actions/setup-flyctl@master

    - name: Create Fly.io app if it doesn't exist
      run: |
        if ! flyctl apps list | grep -q "nafaplace-web-test"; then
          echo "Creating nafaplace-web-test app..."
          flyctl apps create nafaplace-web-test --org personal
        else
          echo "App nafaplace-web-test already exists"
        fi
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

    - name: Deploy to Fly.io
      run: flyctl deploy --config fly-web.toml --dockerfile Dockerfile.web
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}
