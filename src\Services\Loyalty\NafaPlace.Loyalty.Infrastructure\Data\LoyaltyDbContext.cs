using Microsoft.EntityFrameworkCore;
using NafaPlace.Loyalty.Domain.Entities;

namespace NafaPlace.Loyalty.Infrastructure.Data;

public class LoyaltyDbContext : DbContext
{
    public LoyaltyDbContext(DbContextOptions<LoyaltyDbContext> options) : base(options)
    {
    }

    public DbSet<LoyaltyAccount> LoyaltyAccounts { get; set; }
    public DbSet<PointTransaction> PointTransactions { get; set; }
    public DbSet<Reward> Rewards { get; set; }
    public DbSet<RewardRedemption> RewardRedemptions { get; set; }
    public DbSet<Badge> Badges { get; set; }
    public DbSet<UserBadge> UserBadges { get; set; }
    public DbSet<Challenge> Challenges { get; set; }
    public DbSet<ChallengeProgress> ChallengeProgress { get; set; }
    public DbSet<ReferralProgram> ReferralPrograms { get; set; }
    public DbSet<Referral> Referrals { get; set; }
    public DbSet<LoyaltyEvent> LoyaltyEvents { get; set; }
    public DbSet<PointsEarnRule> PointsEarnRules { get; set; }
    public DbSet<LoyaltyNotification> LoyaltyNotifications { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configuration LoyaltyAccounts
        modelBuilder.Entity<LoyaltyAccount>(entity =>
        {
            entity.HasKey(e => e.UserId);
            entity.Property(e => e.UserId).HasMaxLength(100);
            entity.Property(e => e.UserName).HasMaxLength(100).IsRequired();
            entity.Property(e => e.Email).HasMaxLength(200).IsRequired();
            entity.Property(e => e.TotalPoints).HasDefaultValue(0);
            entity.Property(e => e.AvailablePoints).HasDefaultValue(0);
            entity.Property(e => e.PendingPoints).HasDefaultValue(0);
            entity.Property(e => e.LifetimePoints).HasDefaultValue(0);
            entity.Property(e => e.CurrentTier).HasDefaultValue(LoyaltyTier.Bronze);
            entity.Property(e => e.TotalSpent).HasColumnType("decimal(18,2)").HasDefaultValue(0);
            entity.Property(e => e.TotalOrders).HasDefaultValue(0);
            entity.Property(e => e.JoinedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
            entity.Property(e => e.LastActivity).HasDefaultValueSql("CURRENT_TIMESTAMP");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.Metadata).HasColumnType("jsonb").HasDefaultValue("{}");

            entity.HasIndex(e => e.CurrentTier);
            entity.HasIndex(e => e.IsActive);
        });

        // Configuration PointTransactions
        modelBuilder.Entity<PointTransaction>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.UserId).HasMaxLength(100).IsRequired();
            entity.Property(e => e.Description).HasMaxLength(500).IsRequired();
            entity.Property(e => e.ReferenceId).HasMaxLength(100);
            entity.Property(e => e.ReferenceType).HasMaxLength(50);
            entity.Property(e => e.ReasonCode).HasMaxLength(50);
            entity.Property(e => e.Metadata).HasColumnType("jsonb").HasDefaultValue("{}");
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

            entity.HasIndex(e => new { e.UserId, e.CreatedAt });
            entity.HasIndex(e => new { e.Type, e.Status });
            entity.HasIndex(e => e.ReferenceId);

            entity.HasOne<LoyaltyAccount>()
                .WithMany()
                .HasForeignKey(e => e.UserId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        // Configuration Rewards
        modelBuilder.Entity<Reward>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).HasMaxLength(200).IsRequired();
            entity.Property(e => e.Description).IsRequired();
            entity.Property(e => e.ImageUrl).HasMaxLength(500);
            entity.Property(e => e.MonetaryValue).HasColumnType("decimal(18,2)");
            entity.Property(e => e.DiscountAmount).HasColumnType("decimal(18,2)");
            entity.Property(e => e.EligibleTiers).HasColumnType("jsonb").HasDefaultValue("[]");
            entity.Property(e => e.StockQuantity).HasDefaultValue(-1);
            entity.Property(e => e.MaxRedemptionsPerUser).HasDefaultValue(-1);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsFeatured).HasDefaultValue(false);
            entity.Property(e => e.Priority).HasDefaultValue(1);
            entity.Property(e => e.Terms).HasColumnType("jsonb").HasDefaultValue("[]");
            entity.Property(e => e.Metadata).HasColumnType("jsonb").HasDefaultValue("{}");
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

            entity.HasIndex(e => new { e.Type, e.Category, e.IsActive });
            entity.HasIndex(e => e.IsFeatured);
        });

        // Configuration RewardRedemptions
        modelBuilder.Entity<RewardRedemption>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.UserId).HasMaxLength(100).IsRequired();
            entity.Property(e => e.RewardName).HasMaxLength(200).IsRequired();
            entity.Property(e => e.CouponCode).HasMaxLength(50);
            entity.Property(e => e.QRCode).HasMaxLength(200);
            entity.Property(e => e.RedeemedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
            entity.Property(e => e.Metadata).HasColumnType("jsonb").HasDefaultValue("{}");

            entity.HasIndex(e => e.UserId);
            entity.HasIndex(e => e.RewardId);
            entity.HasIndex(e => e.Status);
            entity.HasIndex(e => e.CouponCode).IsUnique();

            entity.HasOne<LoyaltyAccount>()
                .WithMany()
                .HasForeignKey(e => e.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne<Reward>()
                .WithMany()
                .HasForeignKey(e => e.RewardId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        // Configuration Badges
        modelBuilder.Entity<Badge>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).HasMaxLength(100).IsRequired();
            entity.Property(e => e.Description).IsRequired();
            entity.Property(e => e.ImageUrl).HasMaxLength(500);
            entity.Property(e => e.PointsReward).HasDefaultValue(0);
            entity.Property(e => e.Requirements).HasColumnType("jsonb").HasDefaultValue("[]");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.Metadata).HasColumnType("jsonb").HasDefaultValue("{}");
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

            entity.HasIndex(e => new { e.Category, e.Rarity });
        });

        // Configuration UserBadges
        modelBuilder.Entity<UserBadge>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.UserId).HasMaxLength(100).IsRequired();
            entity.Property(e => e.Reason).HasMaxLength(200);
            entity.Property(e => e.EarnedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

            entity.HasIndex(e => e.UserId);
            entity.HasIndex(e => e.BadgeId);
            entity.HasIndex(e => new { e.UserId, e.BadgeId }).IsUnique();

            entity.HasOne<LoyaltyAccount>()
                .WithMany()
                .HasForeignKey(e => e.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne<Badge>()
                .WithMany()
                .HasForeignKey(e => e.BadgeId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        // Configuration Challenges
        modelBuilder.Entity<Challenge>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).HasMaxLength(200).IsRequired();
            entity.Property(e => e.Description).IsRequired();
            entity.Property(e => e.ImageUrl).HasMaxLength(500);
            entity.Property(e => e.Requirements).HasColumnType("jsonb").HasDefaultValue("[]");
            entity.Property(e => e.MaxParticipants).HasDefaultValue(-1);
            entity.Property(e => e.CurrentParticipants).HasDefaultValue(0);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.Metadata).HasColumnType("jsonb").HasDefaultValue("{}");
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

            entity.HasIndex(e => new { e.Type, e.IsActive });
            entity.HasIndex(e => new { e.StartDate, e.EndDate });
        });

        // Configuration ChallengeProgress
        modelBuilder.Entity<ChallengeProgress>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.UserId).HasMaxLength(100).IsRequired();
            entity.Property(e => e.Progress).HasDefaultValue(0);
            entity.Property(e => e.IsCompleted).HasDefaultValue(false);
            entity.Property(e => e.RequirementProgress).HasColumnType("jsonb").HasDefaultValue("{}");
            entity.Property(e => e.StartedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

            entity.HasIndex(e => e.ChallengeId);
            entity.HasIndex(e => e.UserId);
            entity.HasIndex(e => new { e.UserId, e.ChallengeId }).IsUnique();

            entity.HasOne<LoyaltyAccount>()
                .WithMany()
                .HasForeignKey(e => e.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne<Challenge>()
                .WithMany()
                .HasForeignKey(e => e.ChallengeId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        // Configuration Referrals
        modelBuilder.Entity<Referral>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.ReferrerId).HasMaxLength(100).IsRequired();
            entity.Property(e => e.RefereeId).HasMaxLength(100).IsRequired();
            entity.Property(e => e.ReferralCode).HasMaxLength(50).IsRequired();
            entity.Property(e => e.PointsEarned).HasDefaultValue(0);
            entity.Property(e => e.CashbackEarned).HasColumnType("decimal(18,2)").HasDefaultValue(0);
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
            entity.Property(e => e.Metadata).HasColumnType("jsonb").HasDefaultValue("{}");

            entity.HasIndex(e => e.ReferrerId);
            entity.HasIndex(e => e.RefereeId);
            entity.HasIndex(e => e.ReferralCode).IsUnique();

            entity.HasOne<LoyaltyAccount>()
                .WithMany()
                .HasForeignKey(e => e.ReferrerId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne<LoyaltyAccount>()
                .WithMany()
                .HasForeignKey(e => e.RefereeId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        // Configuration LoyaltyNotifications
        modelBuilder.Entity<LoyaltyNotification>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.UserId).HasMaxLength(100).IsRequired();
            entity.Property(e => e.Title).HasMaxLength(200).IsRequired();
            entity.Property(e => e.Message).IsRequired();
            entity.Property(e => e.Data).HasColumnType("jsonb").HasDefaultValue("{}");
            entity.Property(e => e.IsRead).HasDefaultValue(false);
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

            entity.HasIndex(e => new { e.UserId, e.IsRead });
            entity.HasIndex(e => new { e.Type, e.CreatedAt });

            entity.HasOne<LoyaltyAccount>()
                .WithMany()
                .HasForeignKey(e => e.UserId)
                .OnDelete(DeleteBehavior.Cascade);
        });
    }
}
