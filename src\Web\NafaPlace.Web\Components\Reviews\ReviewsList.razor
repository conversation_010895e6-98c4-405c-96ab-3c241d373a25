@using NafaPlace.Reviews.DTOs
@using NafaPlace.Web.Components.Reviews
@namespace NafaPlace.Web.Components.Reviews

<div class="reviews-list">
    @if (Reviews == null || !Reviews.Any())
    {
        <div class="text-center py-4">
            <i class="fas fa-comments fa-3x text-muted mb-3"></i>
            <p class="text-muted">Aucun avis pour le moment. Soyez le premier à donner votre avis !</p>
        </div>
    }
    else
    {
        @foreach (var review in Reviews)
        {
            <div class="review-item mb-4">
                <div class="review-header d-flex justify-content-between align-items-start mb-2">
                    <div class="reviewer-info">
                        <div class="d-flex align-items-center mb-1">
                            <div class="reviewer-avatar me-2">
                                <i class="fas fa-user-circle fa-2x text-muted"></i>
                            </div>
                            <div>
                                <h6 class="mb-0">@review.UserName</h6>
                                <small class="text-muted">@review.CreatedAt.ToString("dd/MM/yyyy")</small>
                                @if (review.IsVerifiedPurchase)
                                {
                                    <span class="badge bg-success ms-2">
                                        <i class="fas fa-check-circle me-1"></i>Achat vérifié
                                    </span>
                                }
                            </div>
                        </div>
                    </div>
                    <div class="review-actions">
                        @if (ShowActions && OnEditReview.HasDelegate && CanEditReview(review))
                        {
                            <button class="btn btn-sm btn-outline-primary me-1" @onclick="() => OnEditReview.InvokeAsync(review)">
                                <i class="fas fa-edit"></i>
                            </button>
                        }
                        @if (ShowActions && OnDeleteReview.HasDelegate && CanDeleteReview(review))
                        {
                            <button class="btn btn-sm btn-outline-danger" @onclick="() => OnDeleteReview.InvokeAsync(review)">
                                <i class="fas fa-trash"></i>
                            </button>
                        }
                    </div>
                </div>

                <div class="review-rating mb-2">
                    <StarRating Rating="review.Rating" ShowRatingText="false" />
                </div>

                <div class="review-content">
                    <h6 class="review-title">@review.Title</h6>
                    <p class="review-comment">@review.Comment</p>
                </div>

                <div class="review-footer d-flex justify-content-between align-items-center">
                    <div class="review-helpful">
                        <button class="btn btn-sm btn-outline-secondary" @onclick="() => ToggleHelpful(review)">
                            <i class="fas fa-thumbs-up me-1"></i>
                            Utile (@review.HelpfulCount)
                        </button>
                    </div>
                    
                    @if (review.Replies.Any())
                    {
                        <button class="btn btn-sm btn-link" @onclick="() => ToggleReplies(review.Id)">
                            <i class="fas fa-comments me-1"></i>
                            @review.Replies.Count réponse(s)
                        </button>
                    }
                </div>

                @if (_expandedReplies.Contains(review.Id) && review.Replies.Any())
                {
                    <div class="review-replies mt-3 ms-4">
                        @foreach (var reply in review.Replies)
                        {
                            <div class="reply-item border-start border-3 border-primary ps-3 mb-2">
                                <div class="reply-header d-flex justify-content-between align-items-center mb-1">
                                    <div>
                                        <strong>@reply.UserName</strong>
                                        @if (reply.IsFromSeller)
                                        {
                                            <span class="badge bg-primary ms-1">Vendeur</span>
                                        }
                                        <small class="text-muted ms-2">@reply.CreatedAt.ToString("dd/MM/yyyy")</small>
                                    </div>
                                </div>
                                <p class="reply-content mb-0">@reply.Content</p>
                            </div>
                        }
                    </div>
                }
            </div>
        }

        @if (ShowPagination && TotalPages > 1)
        {
            <nav aria-label="Navigation des avis">
                <ul class="pagination justify-content-center">
                    <li class="page-item @(CurrentPage <= 1 ? "disabled" : "")">
                        <button class="page-link" @onclick="() => OnPageChanged.InvokeAsync(CurrentPage - 1)" disabled="@(CurrentPage <= 1)">
                            Précédent
                        </button>
                    </li>
                    
                    @for (int i = Math.Max(1, CurrentPage - 2); i <= Math.Min(TotalPages, CurrentPage + 2); i++)
                    {
                        var pageNumber = i;
                        <li class="page-item @(CurrentPage == pageNumber ? "active" : "")">
                            <button class="page-link" @onclick="() => OnPageChanged.InvokeAsync(pageNumber)">
                                @pageNumber
                            </button>
                        </li>
                    }
                    
                    <li class="page-item @(CurrentPage >= TotalPages ? "disabled" : "")">
                        <button class="page-link" @onclick="() => OnPageChanged.InvokeAsync(CurrentPage + 1)" disabled="@(CurrentPage >= TotalPages)">
                            Suivant
                        </button>
                    </li>
                </ul>
            </nav>
        }
    }
</div>

@code {
    [Parameter] public List<ReviewDto> Reviews { get; set; } = new();
    [Parameter] public bool ShowActions { get; set; } = false;
    [Parameter] public bool ShowPagination { get; set; } = true;
    [Parameter] public int CurrentPage { get; set; } = 1;
    [Parameter] public int TotalPages { get; set; } = 1;
    [Parameter] public string? CurrentUserId { get; set; }
    [Parameter] public EventCallback<ReviewDto> OnEditReview { get; set; }
    [Parameter] public EventCallback<ReviewDto> OnDeleteReview { get; set; }
    [Parameter] public EventCallback<int> OnPageChanged { get; set; }
    [Parameter] public EventCallback<ReviewDto> OnToggleHelpful { get; set; }

    private HashSet<int> _expandedReplies = new();

    private void ToggleReplies(int reviewId)
    {
        if (_expandedReplies.Contains(reviewId))
        {
            _expandedReplies.Remove(reviewId);
        }
        else
        {
            _expandedReplies.Add(reviewId);
        }
        StateHasChanged();
    }

    private async Task ToggleHelpful(ReviewDto review)
    {
        if (OnToggleHelpful.HasDelegate)
        {
            await OnToggleHelpful.InvokeAsync(review);
        }
    }

    private bool CanEditReview(ReviewDto review)
    {
        return !string.IsNullOrEmpty(CurrentUserId) && review.UserId == CurrentUserId;
    }

    private bool CanDeleteReview(ReviewDto review)
    {
        return !string.IsNullOrEmpty(CurrentUserId) && review.UserId == CurrentUserId;
    }
}

<style>
    .reviews-list {
        max-width: 100%;
    }

    .review-item {
        background: #fff;
        border: 1px solid #dee2e6;
        border-radius: 0.5rem;
        padding: 1.5rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }

    .reviewer-avatar {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .review-title {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.5rem;
    }

    .review-comment {
        color: #6c757d;
        line-height: 1.6;
        margin-bottom: 0;
    }

    .review-helpful button {
        border: none;
        background: none;
        color: #6c757d;
        transition: color 0.2s ease;
    }

    .review-helpful button:hover {
        color: #0d6efd;
    }

    .reply-item {
        background: #f8f9fa;
        border-radius: 0.25rem;
        padding: 0.75rem;
    }

    .reply-content {
        color: #6c757d;
        font-size: 0.9rem;
    }
</style>
