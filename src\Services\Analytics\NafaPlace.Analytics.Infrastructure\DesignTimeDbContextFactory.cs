using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using NafaPlace.Analytics.Infrastructure.Data;

namespace NafaPlace.Analytics.Infrastructure
{
    public class DesignTimeDbContextFactory : IDesignTimeDbContextFactory<AnalyticsDbContext>
    {
        public AnalyticsDbContext CreateDbContext(string[] args)
        {
            var connectionString = "Host=localhost;Port=5432;Database=NafaPlace.Analytics;Username=postgres;Password=*****************";
            var optionsBuilder = new DbContextOptionsBuilder<AnalyticsDbContext>();
            optionsBuilder.UseNpgsql(connectionString);

            return new AnalyticsDbContext(optionsBuilder.Options);
        }
    }
}
