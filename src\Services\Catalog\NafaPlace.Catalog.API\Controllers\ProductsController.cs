using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NafaPlace.Catalog.Application.Common.Interfaces;
using NafaPlace.Catalog.Application.DTOs.Product;
using NafaPlace.Catalog.Application.DTOs;

namespace NafaPlace.Catalog.API.Controllers;
    [ApiController]
    [Route("api/v1/[controller]")]
    public class ProductsController : ControllerBase
    {
        private readonly IProductService _productService;
        private readonly IProductImageService _productImageService;

        public ProductsController(
            IProductService productService,
            IProductImageService productImageService)
        {
            _productService = productService;
            _productImageService = productImageService;
        }



        [HttpGet]
        [AllowAnonymous]
        public async Task<ActionResult<PagedResultDto<ProductDto>>> GetProducts(
            [FromQuery] int? sellerId,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10,
            [FromQuery] string? searchTerm = null,
            [FromQuery] int[]? categoryIds = null,
            [FromQuery] decimal? minPrice = null,
            [FromQuery] decimal? maxPrice = null,
            [FromQuery] bool inStockOnly = false,
            [FromQuery] string sortBy = "CreatedAt",
            [FromQuery] bool sortDescending = true)
        {
            var searchDto = new ProductSearchDto
            {
                SellerId = sellerId,
                Page = page,
                PageSize = pageSize,
                SearchTerm = searchTerm,
                CategoryIds = categoryIds?.ToList(),
                MinPrice = minPrice,
                MaxPrice = maxPrice,
                InStockOnly = inStockOnly,
                SortBy = sortBy,
                SortDescending = sortDescending
            };
            var products = await _productService.SearchProductsAsync(searchDto);
            return Ok(products);
        }

        [HttpGet("featured")]
        [AllowAnonymous]
        public async Task<ActionResult<IEnumerable<ProductDto>>> GetFeaturedProducts([FromQuery] int count = 8)
        {
            try
            {
                var products = await _productService.GetFeaturedProductsAsync(count);
                return Ok(products);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpGet("new")]
        [AllowAnonymous]
        public async Task<ActionResult<IEnumerable<ProductDto>>> GetNewProducts([FromQuery] int count = 8)
        {
            try
            {
                var products = await _productService.GetNewProductsAsync(count);
                return Ok(products);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpGet("{id:int}")]
        [AllowAnonymous]
        public async Task<ActionResult<ProductDto>> GetProduct(int id)
        {
            try
            {
                var product = await _productService.GetProductByIdAsync(id);
                return Ok(product);
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
        }

        [HttpGet("{id:int}/related")]
        [AllowAnonymous]
        public async Task<ActionResult<IEnumerable<ProductDto>>> GetRelatedProducts(int id, [FromQuery] int count = 4)
        {
            try
            {
                var products = await _productService.GetRelatedProductsAsync(id, count);
                return Ok(products);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpGet("category/{categoryId:int}")]
        [AllowAnonymous]
        public async Task<ActionResult<IEnumerable<ProductDto>>> GetProductsByCategory(
            int categoryId,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            try
            {
                var products = await _productService.GetProductsByCategoryAsync(categoryId, page, pageSize);
                return Ok(products);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpPost]
        public async Task<ActionResult<ProductDto>> CreateProduct([FromBody] CreateProductRequest request)
        {
            try
            {
                var product = await _productService.CreateProductAsync(request);

                return CreatedAtAction(nameof(GetProduct), new { id = product.Id }, product);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpPut("{id:int}")]
        public async Task<ActionResult<ProductDto>> UpdateProduct(int id, UpdateProductRequest request)
        {
            try
            {
                var product = await _productService.UpdateProductAsync(id, request);
                return Ok(product);
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpDelete("{id:int}")]
        public async Task<ActionResult> DeleteProduct(int id)
        {
            try
            {
                await _productService.DeleteProductAsync(id);
                return NoContent();
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
        }

        [HttpPost("{productId:int}/images")]
        public async Task<ActionResult<ProductDto>> AddProductImage(int productId, [FromBody] AddProductImageRequest request)
        {
            try
            {
                var product = await _productService.AddProductImageAsync(productId, new CreateProductImageRequest
                {
                    ProductId = productId,
                    Image = request.Image,
                    IsMain = request.IsMain
                });

                return Ok(product);
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpPost("{productId:int}/images/bulk")]
        public async Task<ActionResult<IEnumerable<ProductImageDto>>> AddBulkProductImages(int productId, [FromBody] IEnumerable<CreateProductImageRequest> request)
        {
            try
            {
                var images = await _productImageService.AddBulkProductImagesAsync(productId, request);
                return Ok(images);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpDelete("{productId:int}/images/{imageId:int}")]
        public async Task<ActionResult> DeleteProductImage(int productId, int imageId)
        {
            try
            {
                await _productImageService.DeleteProductImageAsync(imageId);
                return NoContent();
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpPost("{id:int}/variants")]
        public async Task<ActionResult<ProductDto>> AddProductVariant(int id, CreateProductVariantRequest request)
        {
            try
            {
                var product = await _productService.AddProductVariantAsync(id, request);
                return Ok(product);
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpPut("{productId:int}/variants/{variantId:int}")]
        public async Task<ActionResult<ProductDto>> UpdateProductVariant(
            int productId,
            int variantId,
            UpdateProductVariantRequest request)
        {
            try
            {
                var product = await _productService.UpdateProductVariantAsync(productId, variantId, request);
                return Ok(product);
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpDelete("{productId:int}/variants/{variantId:int}")]
        public async Task<ActionResult<ProductDto>> DeleteProductVariant(int productId, int variantId)
        {
            try
            {
                var product = await _productService.DeleteProductVariantAsync(productId, variantId);
                return Ok(product);
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpGet("search")]
        [AllowAnonymous]
        public async Task<ActionResult<IEnumerable<ProductDto>>> SearchProducts(
            [FromQuery] string? searchTerm,
            [FromQuery] int? categoryId,
            [FromQuery] List<int>? categoryIds,
            [FromQuery] decimal? minPrice,
            [FromQuery] decimal? maxPrice,
            [FromQuery] string? brand,
            [FromQuery] bool? inStockOnly,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10,
            [FromQuery] string? sortBy = "newest",
            [FromQuery] bool sortDescending = true)
        {
            try
            {
                var searchDto = new ProductSearchDto
                {
                    SearchTerm = searchTerm,
                    CategoryId = categoryId,
                    CategoryIds = categoryIds,
                    MinPrice = minPrice,
                    MaxPrice = maxPrice,
                    Brand = brand,
                    IsActive = true, // Toujours retourner les produits actifs
                    Page = page,
                    PageSize = pageSize
                };

                var products = await _productService.SearchProductsAsync(searchDto);
                return Ok(products);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = ex.Message });
            }
        }

        [HttpGet("search/popular")]
        [AllowAnonymous]
        public async Task<ActionResult<List<string>>> GetPopularSearches([FromQuery] int count = 10)
        {
            try
            {
                // Pour l'instant, retourner des recherches populaires statiques
                var popularSearches = new List<string>
                {
                    "smartphone", "ordinateur", "vêtements", "chaussures", "livre",
                    "télévision", "casque", "montre", "sac", "parfum"
                };

                return Ok(popularSearches.Take(count).ToList());
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = ex.Message });
            }
        }

        [HttpGet("search/history")]
        [AllowAnonymous]
        public async Task<ActionResult<List<string>>> GetSearchHistory([FromQuery] int count = 10)
        {
            try
            {
                // Pour l'instant, retourner un historique de recherche statique
                var searchHistory = new List<string>
                {
                    "smartphone samsung", "ordinateur portable", "chaussures nike",
                    "livre programmation", "casque bluetooth"
                };

                return Ok(searchHistory.Take(count).ToList());
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = ex.Message });
            }
        }
    }
