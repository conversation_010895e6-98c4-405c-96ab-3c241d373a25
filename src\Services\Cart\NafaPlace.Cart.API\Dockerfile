# Build stage
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copier les fichiers csproj et restaurer les dépendances
COPY ["src/Services/Cart/NafaPlace.Cart.API/NafaPlace.Cart.API.csproj", "Services/Cart/NafaPlace.Cart.API/"]
COPY ["src/Services/Cart/NafaPlace.Cart.Application/NafaPlace.Cart.Application.csproj", "Services/Cart/NafaPlace.Cart.Application/"]
COPY ["src/Services/Cart/NafaPlace.Cart.Domain/NafaPlace.Cart.Domain.csproj", "Services/Cart/NafaPlace.Cart.Domain/"]
COPY ["src/Services/Cart/NafaPlace.Cart.Infrastructure/NafaPlace.Cart.Infrastructure.csproj", "Services/Cart/NafaPlace.Cart.Infrastructure/"]
RUN dotnet restore "Services/Cart/NafaPlace.Cart.API/NafaPlace.Cart.API.csproj"

# Copier le reste des fichiers et construire
COPY ["src/Services/Cart/", "Services/Cart/"]
WORKDIR "/src/Services/Cart/NafaPlace.Cart.API"
RUN dotnet build "NafaPlace.Cart.API.csproj" -c Release -o /app/build

# Publish stage
FROM build AS publish
RUN dotnet publish "NafaPlace.Cart.API.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Final stage
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS final
WORKDIR /app
COPY --from=publish /app/publish .

# Configuration pour le marché africain
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false
ENV TZ=Africa/Dakar
ENV LANG=fr_FR.UTF-8
ENV LANGUAGE=fr_FR.UTF-8
ENV LC_ALL=fr_FR.UTF-8

EXPOSE 80
ENTRYPOINT ["dotnet", "NafaPlace.Cart.API.dll"]