namespace NafaPlace.Reviews.Application.DTOs;
using NafaPlace.Reviews.DTOs;

public class ReviewReportDto
{
    public int Id { get; set; }
    public int ReviewId { get; set; }
    public string UserId { get; set; } = string.Empty;
    public string Reason { get; set; } = string.Empty;
    public string? AdditionalComments { get; set; }
    public DateTime CreatedAt { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime? ResolvedAt { get; set; }
    public string? ResolvedBy { get; set; }
    public string? ResolutionNotes { get; set; }
}

public class CreateReviewReportRequest
{
    public int ReviewId { get; set; }
    public string Reason { get; set; } = string.Empty;
    public string? AdditionalComments { get; set; }
}

public class UpdateReviewReportRequest
{
    public string Status { get; set; } = string.Empty;
    public string? ResolutionNotes { get; set; }
}

public class ReviewReportSummaryDto
{
    public int TotalReportsCount { get; set; }
    public int PendingReportsCount { get; set; }
    public List<ReportedReviewDto> TopReportedReviews { get; set; } = new();
}

public class ReportedReviewDto
{
    public ReviewDto Review { get; set; } = new();
    public int ReportCount { get; set; }
    public List<string> ReportReasons { get; set; } = new();
}
