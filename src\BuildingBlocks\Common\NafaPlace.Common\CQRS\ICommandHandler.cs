using MediatR;
using Microsoft.Extensions.Logging;

namespace NafaPlace.Common.CQRS;

/// <summary>
/// Interface pour les gestionnaires de commandes sans résultat
/// </summary>
/// <typeparam name="TCommand">Type de la commande</typeparam>
public interface ICommandHandler<TCommand> : IRequestHandler<TCommand>
    where TCommand : ICommand
{
}

/// <summary>
/// Interface pour les gestionnaires de commandes avec résultat
/// </summary>
/// <typeparam name="TCommand">Type de la commande</typeparam>
/// <typeparam name="TResult">Type du résultat</typeparam>
public interface ICommandHandler<TCommand, TResult> : IRequestHandler<TCommand, TResult>
    where TCommand : ICommand<TResult>
{
}

/// <summary>
/// Classe de base pour les gestionnaires de commandes
/// </summary>
/// <typeparam name="TCommand">Type de la commande</typeparam>
public abstract class BaseCommandHandler<TCommand> : ICommandHandler<TCommand>
    where TCommand : ICommand
{
    protected readonly ILogger<BaseCommandHandler<TCommand>> Logger;

    protected BaseCommandHandler(ILogger<BaseCommandHandler<TCommand>> logger)
    {
        Logger = logger;
    }

    public async Task Handle(TCommand request, CancellationToken cancellationToken)
    {
        Logger.LogInformation("Handling command {CommandType} with ID {CommandId}", 
            typeof(TCommand).Name, request.CommandId);

        try
        {
            await ValidateCommand(request, cancellationToken);
            await ExecuteCommand(request, cancellationToken);
            
            Logger.LogInformation("Successfully handled command {CommandType} with ID {CommandId}", 
                typeof(TCommand).Name, request.CommandId);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error handling command {CommandType} with ID {CommandId}", 
                typeof(TCommand).Name, request.CommandId);
            throw;
        }
    }

    /// <summary>
    /// Valide la commande avant exécution
    /// </summary>
    /// <param name="command">Commande à valider</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    protected virtual Task ValidateCommand(TCommand command, CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }

    /// <summary>
    /// Exécute la logique métier de la commande
    /// </summary>
    /// <param name="command">Commande à exécuter</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    protected abstract Task ExecuteCommand(TCommand command, CancellationToken cancellationToken);
}

/// <summary>
/// Classe de base pour les gestionnaires de commandes avec résultat
/// </summary>
/// <typeparam name="TCommand">Type de la commande</typeparam>
/// <typeparam name="TResult">Type du résultat</typeparam>
public abstract class BaseCommandHandler<TCommand, TResult> : ICommandHandler<TCommand, TResult>
    where TCommand : ICommand<TResult>
{
    protected readonly ILogger<BaseCommandHandler<TCommand, TResult>> Logger;

    protected BaseCommandHandler(ILogger<BaseCommandHandler<TCommand, TResult>> logger)
    {
        Logger = logger;
    }

    public async Task<TResult> Handle(TCommand request, CancellationToken cancellationToken)
    {
        Logger.LogInformation("Handling command {CommandType} with ID {CommandId}", 
            typeof(TCommand).Name, request.CommandId);

        try
        {
            await ValidateCommand(request, cancellationToken);
            var result = await ExecuteCommand(request, cancellationToken);
            
            Logger.LogInformation("Successfully handled command {CommandType} with ID {CommandId}", 
                typeof(TCommand).Name, request.CommandId);
            
            return result;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error handling command {CommandType} with ID {CommandId}", 
                typeof(TCommand).Name, request.CommandId);
            throw;
        }
    }

    /// <summary>
    /// Valide la commande avant exécution
    /// </summary>
    /// <param name="command">Commande à valider</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    protected virtual Task ValidateCommand(TCommand command, CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }

    /// <summary>
    /// Exécute la logique métier de la commande
    /// </summary>
    /// <param name="command">Commande à exécuter</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Résultat de la commande</returns>
    protected abstract Task<TResult> ExecuteCommand(TCommand command, CancellationToken cancellationToken);
}
