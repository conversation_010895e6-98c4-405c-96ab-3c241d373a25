using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NafaPlace.Identity.Application.Common.Exceptions;
using NafaPlace.Identity.Application.Common.Interfaces;
using NafaPlace.Identity.Application.DTOs.Role;
using Swashbuckle.AspNetCore.Annotations;

namespace NafaPlace.Identity.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize(Roles = "Admin")]
[Produces("application/json")]
[SwaggerTag("Gestion des rôles utilisateur")]
public class RolesController : ControllerBase
{
    private readonly IRoleService _roleService;

    public RolesController(IRoleService roleService)
    {
        _roleService = roleService;
    }

    /// <summary>
    /// Crée un nouveau rôle
    /// </summary>
    /// <param name="request">Informations du rôle à créer</param>
    /// <returns>Le rôle créé</returns>
    /// <response code="200">Rôle créé avec succès</response>
    /// <response code="400">Le nom du rôle existe déjà</response>
    /// <response code="401">Non authentifié</response>
    /// <response code="403">Non autorisé</response>
    [HttpPost]
    [ProducesResponseType(typeof(RoleDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [SwaggerOperation(
        Summary = "Crée un nouveau rôle",
        Description = "Crée un nouveau rôle avec les permissions spécifiées",
        OperationId = "Roles_Create",
        Tags = new[] { "Roles" }
    )]
    public async Task<IActionResult> CreateRole([FromBody] CreateRoleRequest request)
    {
        try
        {
            var role = await _roleService.CreateRoleAsync(request);
            return Ok(role);
        }
        catch (AuthenticationException ex)
        {
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Met à jour un rôle existant
    /// </summary>
    /// <param name="id">ID du rôle</param>
    /// <param name="request">Nouvelles informations du rôle</param>
    /// <returns>Le rôle mis à jour</returns>
    /// <response code="200">Rôle mis à jour avec succès</response>
    /// <response code="400">Le nom du rôle existe déjà ou rôle non trouvé</response>
    /// <response code="401">Non authentifié</response>
    /// <response code="403">Non autorisé</response>
    [HttpPut("{id}")]
    [ProducesResponseType(typeof(RoleDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [SwaggerOperation(
        Summary = "Met à jour un rôle",
        Description = "Met à jour les informations d'un rôle existant",
        OperationId = "Roles_Update",
        Tags = new[] { "Roles" }
    )]
    public async Task<IActionResult> UpdateRole(int id, [FromBody] UpdateRoleRequest request)
    {
        try
        {
            var role = await _roleService.UpdateRoleAsync(id, request);
            return Ok(role);
        }
        catch (AuthenticationException ex)
        {
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Supprime un rôle
    /// </summary>
    /// <param name="id">ID du rôle à supprimer</param>
    /// <returns>Aucun contenu</returns>
    /// <response code="204">Rôle supprimé avec succès</response>
    /// <response code="400">Rôle non trouvé</response>
    /// <response code="401">Non authentifié</response>
    /// <response code="403">Non autorisé</response>
    [HttpDelete("{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [SwaggerOperation(
        Summary = "Supprime un rôle",
        Description = "Supprime un rôle existant et ses associations",
        OperationId = "Roles_Delete",
        Tags = new[] { "Roles" }
    )]
    public async Task<IActionResult> DeleteRole(int id)
    {
        try
        {
            await _roleService.DeleteRoleAsync(id);
            return NoContent();
        }
        catch (AuthenticationException ex)
        {
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Récupère un rôle par son ID
    /// </summary>
    /// <param name="id">ID du rôle</param>
    /// <returns>Le rôle demandé</returns>
    /// <response code="200">Rôle trouvé</response>
    /// <response code="400">Rôle non trouvé</response>
    /// <response code="401">Non authentifié</response>
    /// <response code="403">Non autorisé</response>
    [HttpGet("{id}")]
    [ProducesResponseType(typeof(RoleDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [SwaggerOperation(
        Summary = "Récupère un rôle",
        Description = "Récupère les informations d'un rôle par son ID",
        OperationId = "Roles_GetById",
        Tags = new[] { "Roles" }
    )]
    public async Task<IActionResult> GetRole(int id)
    {
        try
        {
            var role = await _roleService.GetRoleByIdAsync(id);
            return Ok(role);
        }
        catch (AuthenticationException ex)
        {
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Récupère tous les rôles
    /// </summary>
    /// <returns>Liste des rôles</returns>
    /// <response code="200">Liste des rôles récupérée</response>
    /// <response code="401">Non authentifié</response>
    /// <response code="403">Non autorisé</response>
    [HttpGet]
    [ProducesResponseType(typeof(IEnumerable<RoleDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [SwaggerOperation(
        Summary = "Liste tous les rôles",
        Description = "Récupère la liste complète des rôles disponibles",
        OperationId = "Roles_GetAll",
        Tags = new[] { "Roles" }
    )]
    public async Task<IActionResult> GetAllRoles()
    {
        var roles = await _roleService.GetAllRolesAsync();
        return Ok(roles);
    }

    /// <summary>
    /// Assigne un rôle à un utilisateur
    /// </summary>
    /// <param name="userId">ID de l'utilisateur</param>
    /// <param name="roleId">ID du rôle</param>
    /// <returns>Aucun contenu</returns>
    /// <response code="204">Rôle assigné avec succès</response>
    /// <response code="400">Utilisateur ou rôle non trouvé, ou rôle déjà assigné</response>
    /// <response code="401">Non authentifié</response>
    /// <response code="403">Non autorisé</response>
    [HttpPost("users/{userId}/roles/{roleId}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [SwaggerOperation(
        Summary = "Assigne un rôle à un utilisateur",
        Description = "Ajoute un rôle aux permissions d'un utilisateur",
        OperationId = "Roles_AssignToUser",
        Tags = new[] { "Roles" }
    )]
    public async Task<IActionResult> AssignRoleToUser(int userId, int roleId)
    {
        try
        {
            await _roleService.AssignRoleToUserAsync(userId, roleId);
            return NoContent();
        }
        catch (AuthenticationException ex)
        {
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Retire un rôle d'un utilisateur
    /// </summary>
    /// <param name="userId">ID de l'utilisateur</param>
    /// <param name="roleId">ID du rôle</param>
    /// <returns>Aucun contenu</returns>
    /// <response code="204">Rôle retiré avec succès</response>
    /// <response code="400">Utilisateur ou rôle non trouvé, ou rôle non assigné</response>
    /// <response code="401">Non authentifié</response>
    /// <response code="403">Non autorisé</response>
    [HttpDelete("users/{userId}/roles/{roleId}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [SwaggerOperation(
        Summary = "Retire un rôle d'un utilisateur",
        Description = "Supprime un rôle des permissions d'un utilisateur",
        OperationId = "Roles_RemoveFromUser",
        Tags = new[] { "Roles" }
    )]
    public async Task<IActionResult> RemoveRoleFromUser(int userId, int roleId)
    {
        try
        {
            await _roleService.RemoveRoleFromUserAsync(userId, roleId);
            return NoContent();
        }
        catch (AuthenticationException ex)
        {
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Récupère les rôles d'un utilisateur
    /// </summary>
    /// <param name="userId">ID de l'utilisateur</param>
    /// <returns>Liste des rôles de l'utilisateur</returns>
    /// <response code="200">Liste des rôles récupérée</response>
    /// <response code="400">Utilisateur non trouvé</response>
    /// <response code="401">Non authentifié</response>
    /// <response code="403">Non autorisé</response>
    [HttpGet("users/{userId}/roles")]
    [ProducesResponseType(typeof(IEnumerable<RoleDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [SwaggerOperation(
        Summary = "Liste les rôles d'un utilisateur",
        Description = "Récupère la liste des rôles assignés à un utilisateur",
        OperationId = "Roles_GetUserRoles",
        Tags = new[] { "Roles" }
    )]
    public async Task<IActionResult> GetUserRoles(int userId)
    {
        try
        {
            var roles = await _roleService.GetUserRolesAsync(userId);
            return Ok(roles);
        }
        catch (AuthenticationException ex)
        {
            return BadRequest(ex.Message);
        }
    }
}
