# See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
USER app
EXPOSE 8080
EXPOSE 8081

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["src/Services/Wishlist/NafaPlace.Wishlist.API/NafaPlace.Wishlist.API.csproj", "src/Services/Wishlist/NafaPlace.Wishlist.API/"]
COPY ["src/Services/Wishlist/NafaPlace.Wishlist.Application/NafaPlace.Wishlist.Application.csproj", "src/Services/Wishlist/NafaPlace.Wishlist.Application/"]
COPY ["src/Services/Wishlist/NafaPlace.Wishlist.Domain/NafaPlace.Wishlist.Domain.csproj", "src/Services/Wishlist/NafaPlace.Wishlist.Domain/"]
COPY ["src/BuildingBlocks/Common/NafaPlace.Common/NafaPlace.Common.csproj", "src/BuildingBlocks/Common/NafaPlace.Common/"]
COPY ["src/Services/Wishlist/NafaPlace.Wishlist.Infrastructure/NafaPlace.Wishlist.Infrastructure.csproj", "src/Services/Wishlist/NafaPlace.Wishlist.Infrastructure/"]
RUN dotnet restore "./src/Services/Wishlist/NafaPlace.Wishlist.API/NafaPlace.Wishlist.API.csproj"
COPY src/ .
WORKDIR "/src/Services/Wishlist/NafaPlace.Wishlist.API"
RUN dotnet build "./NafaPlace.Wishlist.API.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./NafaPlace.Wishlist.API.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "NafaPlace.Wishlist.API.dll"]
