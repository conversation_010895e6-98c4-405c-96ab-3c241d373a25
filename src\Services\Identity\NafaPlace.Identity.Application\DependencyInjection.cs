using Microsoft.Extensions.DependencyInjection;
using NafaPlace.Identity.Application.Services;
using NafaPlace.Identity.Domain.Services;

namespace NafaPlace.Identity.Application;

public static class DependencyInjection
{
    public static IServiceCollection AddApplication(this IServiceCollection services)
    {
        services.AddScoped<IAuthenticationService, AuthenticationService>();
        services.AddScoped<ITokenService, TokenService>();

        return services;
    }
}
