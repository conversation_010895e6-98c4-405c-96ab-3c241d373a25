namespace NafaPlace.Web.Models.Order
{
    public class OrderCreateDto
    {
        public string UserId { get; set; } = string.Empty;
        public string PaymentMethod { get; set; } = "CashOnDelivery";
        public ShippingAddressDto ShippingAddress { get; set; } = new();
        public string? PhoneNumber { get; set; } // Pour Orange Money

        // Champs pour les coupons
        public string? CouponCode { get; set; }
        public decimal CouponDiscount { get; set; }
        public int? CouponId { get; set; }
    }


}