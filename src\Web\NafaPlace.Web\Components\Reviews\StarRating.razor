@namespace NafaPlace.Web.Components.Reviews

<div class="star-rating @CssClass">
    @if (IsInteractive)
    {
        @for (int i = 1; i <= 5; i++)
        {
            var starIndex = i;
            <i class="@GetStarClass(starIndex) star-interactive" 
               @onclick="() => OnStarClick(starIndex)"
               @onmouseover="() => OnStarHover(starIndex)"
               @onmouseout="OnStarLeave"></i>
        }
    }
    else
    {
        @for (int i = 1; i <= 5; i++)
        {
            <i class="@GetStarClass(i)"></i>
        }
    }
    
    @if (ShowRatingText && Rating > 0)
    {
        <span class="rating-text ms-2">@Rating.ToString("0.0")</span>
    }
</div>

@code {
    [Parameter] public double Rating { get; set; }
    [Parameter] public bool IsInteractive { get; set; } = false;
    [Parameter] public bool ShowRatingText { get; set; } = false;
    [Parameter] public string CssClass { get; set; } = "";
    [Parameter] public EventCallback<int> OnRatingChanged { get; set; }

    private int _hoverRating = 0;

    private string GetStarClass(int starIndex)
    {
        var currentRating = IsInteractive && _hoverRating > 0 ? _hoverRating : Rating;
        
        if (starIndex <= Math.Floor(currentRating))
        {
            return "fas fa-star text-warning";
        }
        else if (starIndex - 0.5 <= currentRating)
        {
            return "fas fa-star-half-alt text-warning";
        }
        else
        {
            return IsInteractive ? "far fa-star text-warning" : "far fa-star text-muted";
        }
    }

    private async Task OnStarClick(int rating)
    {
        if (IsInteractive)
        {
            Rating = rating;
            await OnRatingChanged.InvokeAsync(rating);
        }
    }

    private void OnStarHover(int rating)
    {
        if (IsInteractive)
        {
            _hoverRating = rating;
            StateHasChanged();
        }
    }

    private void OnStarLeave()
    {
        if (IsInteractive)
        {
            _hoverRating = 0;
            StateHasChanged();
        }
    }
}

<style>
    .star-rating {
        display: inline-flex;
        align-items: center;
    }

    .star-interactive {
        cursor: pointer;
        transition: color 0.2s ease;
    }

    .star-interactive:hover {
        transform: scale(1.1);
    }

    .rating-text {
        font-weight: 500;
        color: var(--bs-text-muted);
    }

    .star-rating.small i {
        font-size: 0.8rem;
    }

    .star-rating.large i {
        font-size: 1.5rem;
    }
</style>
