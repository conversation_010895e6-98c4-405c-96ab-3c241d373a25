using System.ComponentModel.DataAnnotations;

namespace NafaPlace.Reviews.Domain.Models;

public class ReviewReport
{
    public int Id { get; set; }
    
    [Required]
    public int ReviewId { get; set; }
    
    [Required]
    public string UserId { get; set; } = string.Empty;
    
    [Required]
    public string Reason { get; set; } = string.Empty;
    
    [StringLength(500)]
    public string? AdditionalComments { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public ReportStatus Status { get; set; } = ReportStatus.Pending;
    
    public DateTime? ResolvedAt { get; set; }
    
    public string? ResolvedBy { get; set; }
    
    public string? ResolutionNotes { get; set; }
    
    // Navigation property
    public virtual Review Review { get; set; } = null!;
}

public enum ReportStatus
{
    Pending,
    Investigating,
    Resolved,
    Dismissed
}
