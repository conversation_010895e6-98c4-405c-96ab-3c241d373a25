using Microsoft.AspNetCore.SignalR;
using Microsoft.AspNetCore.Authorization;
using NafaPlace.Chat.Application.Services;
using NafaPlace.Chat.Application.DTOs;
using System.Security.Claims;

namespace NafaPlace.Chat.Infrastructure.Hubs;

[Authorize]
public class ChatHub : Hub
{
    private readonly IChatService _chatService;
    private readonly ILogger<ChatHub> _logger;
    private static readonly Dictionary<string, string> _userConnections = new();
    private static readonly Dictionary<string, HashSet<string>> _conversationGroups = new();

    public ChatHub(IChatService chatService, ILogger<ChatHub> logger)
    {
        _chatService = chatService;
        _logger = logger;
    }

    public override async Task OnConnectedAsync()
    {
        var userId = GetUserId();
        var connectionId = Context.ConnectionId;

        _logger.LogInformation("Utilisateur {UserId} connecté avec la connexion {ConnectionId}", userId, connectionId);

        // Enregistrer la connexion de l'utilisateur
        _userConnections[userId] = connectionId;

        // Mettre à jour le statut de présence
        await _chatService.UpdateUserPresenceAsync(userId, UserPresenceStatus.Online);

        // Rejoindre le groupe personnel de l'utilisateur
        await Groups.AddToGroupAsync(connectionId, $"User_{userId}");

        // Si c'est un agent, rejoindre le groupe des agents
        if (IsAgent())
        {
            await Groups.AddToGroupAsync(connectionId, "Agents");
        }

        await base.OnConnectedAsync();
    }

    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        var userId = GetUserId();
        var connectionId = Context.ConnectionId;

        _logger.LogInformation("Utilisateur {UserId} déconnecté", userId);

        // Supprimer la connexion de l'utilisateur
        _userConnections.Remove(userId);

        // Mettre à jour le statut de présence
        await _chatService.UpdateUserPresenceAsync(userId, UserPresenceStatus.Offline);

        // Quitter tous les groupes de conversation
        var userGroups = _conversationGroups.Where(kvp => kvp.Value.Contains(connectionId)).ToList();
        foreach (var group in userGroups)
        {
            group.Value.Remove(connectionId);
            if (group.Value.Count == 0)
            {
                _conversationGroups.Remove(group.Key);
            }
        }

        await base.OnDisconnectedAsync(exception);
    }

    // Rejoindre un groupe de conversation
    public async Task JoinConversationGroup(int conversationId)
    {
        var userId = GetUserId();
        var connectionId = Context.ConnectionId;
        var groupName = $"Conversation_{conversationId}";

        _logger.LogInformation("Utilisateur {UserId} rejoint la conversation {ConversationId}", userId, conversationId);

        await Groups.AddToGroupAsync(connectionId, groupName);

        // Enregistrer dans le dictionnaire des groupes
        if (!_conversationGroups.ContainsKey(groupName))
        {
            _conversationGroups[groupName] = new HashSet<string>();
        }
        _conversationGroups[groupName].Add(connectionId);

        // Notifier les autres participants
        await Clients.Group(groupName).SendAsync("UserJoinedConversation", new
        {
            UserId = userId,
            UserName = GetUserName(),
            ConversationId = conversationId,
            Timestamp = DateTime.UtcNow
        });
    }

    // Quitter un groupe de conversation
    public async Task LeaveConversationGroup(int conversationId)
    {
        var userId = GetUserId();
        var connectionId = Context.ConnectionId;
        var groupName = $"Conversation_{conversationId}";

        _logger.LogInformation("Utilisateur {UserId} quitte la conversation {ConversationId}", userId, conversationId);

        await Groups.RemoveFromGroupAsync(connectionId, groupName);

        // Supprimer du dictionnaire des groupes
        if (_conversationGroups.ContainsKey(groupName))
        {
            _conversationGroups[groupName].Remove(connectionId);
            if (_conversationGroups[groupName].Count == 0)
            {
                _conversationGroups.Remove(groupName);
            }
        }

        // Notifier les autres participants
        await Clients.Group(groupName).SendAsync("UserLeftConversation", new
        {
            UserId = userId,
            UserName = GetUserName(),
            ConversationId = conversationId,
            Timestamp = DateTime.UtcNow
        });
    }

    // Envoyer un message via SignalR
    public async Task SendMessage(SendMessageRequest request)
    {
        try
        {
            var userId = GetUserId();
            var userName = GetUserName();

            var messageDto = new SendMessageDto
            {
                ConversationId = request.ConversationId,
                Content = request.Content,
                MessageType = MessageType.Text,
                SenderType = IsAgent() ? SenderType.Agent : SenderType.Customer,
                SenderId = userId,
                SenderName = userName,
                Attachments = request.AttachmentUrl != null ? 
                    new List<MessageAttachmentDto> 
                    { 
                        new() { Url = request.AttachmentUrl, Type = "file" } 
                    } : new List<MessageAttachmentDto>(),
                Metadata = request.Metadata ?? new Dictionary<string, object>()
            };

            var messageId = await _chatService.SendMessageAsync(messageDto, userId);

            if (messageId > 0)
            {
                // Diffuser le message à tous les participants de la conversation
                var groupName = $"Conversation_{request.ConversationId}";
                await Clients.Group(groupName).SendAsync("ReceiveMessage", new
                {
                    Id = messageId,
                    ConversationId = request.ConversationId,
                    Content = request.Content,
                    SenderId = userId,
                    SenderName = userName,
                    SenderType = IsAgent() ? "Agent" : "Customer",
                    Timestamp = DateTime.UtcNow,
                    AttachmentUrl = request.AttachmentUrl
                });

                _logger.LogInformation("Message {MessageId} envoyé dans la conversation {ConversationId}", messageId, request.ConversationId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi du message via SignalR");
            await Clients.Caller.SendAsync("MessageError", "Impossible d'envoyer le message");
        }
    }

    // Envoyer un indicateur de frappe
    public async Task SendTypingIndicator(int conversationId, bool isTyping)
    {
        var userId = GetUserId();
        var userName = GetUserName();
        var groupName = $"Conversation_{conversationId}";

        await Clients.OthersInGroup(groupName).SendAsync("TypingIndicator", new
        {
            UserId = userId,
            UserName = userName,
            ConversationId = conversationId,
            IsTyping = isTyping,
            Timestamp = DateTime.UtcNow
        });
    }

    // Mettre à jour le statut de présence
    public async Task UpdatePresence(bool isOnline)
    {
        var userId = GetUserId();
        var status = isOnline ? UserPresenceStatus.Online : UserPresenceStatus.Away;

        await _chatService.UpdateUserPresenceAsync(userId, status);

        // Notifier les agents si c'est un client
        if (!IsAgent())
        {
            await Clients.Group("Agents").SendAsync("UserPresenceUpdated", new
            {
                UserId = userId,
                UserName = GetUserName(),
                Status = status.ToString(),
                Timestamp = DateTime.UtcNow
            });
        }
    }

    // Demander un agent humain
    public async Task RequestHumanAgent(int conversationId, string reason)
    {
        try
        {
            var success = await _chatService.RequestAgentAssignmentAsync(conversationId, reason);

            if (success)
            {
                // Notifier les agents disponibles
                await Clients.Group("Agents").SendAsync("AgentRequestReceived", new
                {
                    ConversationId = conversationId,
                    UserId = GetUserId(),
                    UserName = GetUserName(),
                    Reason = reason,
                    Timestamp = DateTime.UtcNow
                });

                await Clients.Caller.SendAsync("AgentRequestSent", "Votre demande a été envoyée aux agents disponibles");
            }
            else
            {
                await Clients.Caller.SendAsync("AgentRequestError", "Impossible de traiter votre demande pour le moment");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la demande d'agent");
            await Clients.Caller.SendAsync("AgentRequestError", "Erreur lors de la demande d'agent");
        }
    }

    // Méthodes utilitaires
    private string GetUserId()
    {
        return Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? 
               Context.User?.FindFirst("sub")?.Value ?? 
               "anonymous";
    }

    private string GetUserName()
    {
        return Context.User?.FindFirst(ClaimTypes.Name)?.Value ?? 
               Context.User?.FindFirst("name")?.Value ?? 
               "Utilisateur";
    }

    private bool IsAgent()
    {
        return Context.User?.IsInRole("Agent") == true || Context.User?.IsInRole("Admin") == true;
    }

    // Méthodes statiques pour envoyer des notifications depuis l'extérieur
    public static async Task NotifyConversationUpdate(IHubContext<ChatHub> hubContext, int conversationId, object update)
    {
        var groupName = $"Conversation_{conversationId}";
        await hubContext.Clients.Group(groupName).SendAsync("ConversationUpdated", update);
    }

    public static async Task NotifyAgentAssigned(IHubContext<ChatHub> hubContext, int conversationId, string agentId, string agentName)
    {
        var groupName = $"Conversation_{conversationId}";
        await hubContext.Clients.Group(groupName).SendAsync("AgentAssigned", new
        {
            ConversationId = conversationId,
            AgentId = agentId,
            AgentName = agentName,
            Timestamp = DateTime.UtcNow
        });
    }
}

// DTO pour les requêtes SignalR
public class SendMessageRequest
{
    public int ConversationId { get; set; }
    public string Content { get; set; } = string.Empty;
    public string? AttachmentUrl { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}
