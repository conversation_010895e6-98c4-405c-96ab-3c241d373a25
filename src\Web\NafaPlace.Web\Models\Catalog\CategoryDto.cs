namespace NafaPlace.Web.Models.Catalog;

public class CategoryDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string ImageUrl { get; set; } = string.Empty;
    public int? ParentId { get; set; }
    public string ParentName { get; set; } = string.Empty;
    public int Level { get; set; }
    public bool HasChildren { get; set; }
    public int ProductCount { get; set; }
}
