using NafaPlace.Identity.Application.DTOs;
using NafaPlace.Identity.Application.DTOs.Auth;

namespace NafaPlace.Identity.Application.Common.Interfaces;

public interface IAuthService
{
    Task<AuthResponse> RegisterAsync(RegisterRequest request);
    Task<AuthResponse> RegisterAsync(RegisterRequest request, string role);
    Task<AuthResponse> LoginAsync(LoginRequest request);
    Task<AuthResponse> RefreshTokenAsync(RefreshTokenRequest request);
    Task LogoutAsync(int userId);
    Task<UserDto> GetUserByIdAsync(int userId);
}
