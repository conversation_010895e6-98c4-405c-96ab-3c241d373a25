using CloudinaryDotNet;
using CloudinaryDotNet.Actions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using NafaPlace.Catalog.Application.Interfaces;
using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace NafaPlace.Catalog.Infrastructure.Services
{
    public class CloudinaryService : ICloudinaryService
    {
        private readonly Cloudinary _cloudinary;
        private readonly ILogger<CloudinaryService> _logger;

        public CloudinaryService(IConfiguration configuration, ILogger<CloudinaryService> logger)
        {
            _logger = logger;

            var cloudName = configuration["Cloudinary:CloudName"];
            var apiKey = configuration["Cloudinary:ApiKey"];
            var apiSecret = configuration["Cloudinary:ApiSecret"];

            if (string.IsNullOrEmpty(cloudName) || string.IsNullOrEmpty(apiKey) || string.IsNullOrEmpty(apiSecret))
            {
                throw new ArgumentException("Configuration Cloudinary manquante. Vérifiez CloudName, ApiKey et ApiSecret.");
            }

            var account = new Account(cloudName, apiKey, apiSecret);
            _cloudinary = new Cloudinary(account);
        }

        public async Task<string> UploadImageAsync(Stream imageStream, string fileName, string folder = null)
        {
            try
            {
                var uploadParams = new ImageUploadParams()
                {
                    File = new FileDescription(fileName, imageStream),
                    PublicId = GeneratePublicId(fileName, folder),
                    Folder = folder,
                    Overwrite = true,
                    Format = "jpg"
                };

                var uploadResult = await _cloudinary.UploadAsync(uploadParams);

                if (uploadResult.Error != null)
                {
                    _logger.LogError($"Erreur lors de l'upload Cloudinary: {uploadResult.Error.Message}");
                    throw new Exception($"Erreur lors de l'upload: {uploadResult.Error.Message}");
                }

                return uploadResult.SecureUrl.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de l'upload de l'image {fileName}");
                throw;
            }
        }

        public async Task<string> UploadImageFromBase64Async(string base64Image, string fileName, string folder = null)
        {
            try
            {
                // Nettoyer la chaîne base64 si elle contient le préfixe data:image
                if (base64Image.Contains(","))
                {
                    base64Image = base64Image.Split(',')[1];
                }

                var imageBytes = Convert.FromBase64String(base64Image);
                using var imageStream = new MemoryStream(imageBytes);
                
                return await UploadImageAsync(imageStream, fileName, folder);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de l'upload de l'image base64 {fileName}");
                throw;
            }
        }

        public async Task<string> UploadImageWithTransformationAsync(Stream imageStream, string fileName, int width, int height, string folder = null)
        {
            try
            {
                var uploadParams = new ImageUploadParams()
                {
                    File = new FileDescription(fileName, imageStream),
                    PublicId = GeneratePublicId(fileName, folder),
                    Folder = folder,
                    Overwrite = true,
                    Format = "jpg",
                    Transformation = new Transformation()
                        .Width(width)
                        .Height(height)
                        .Crop("fill")
                        .Gravity("center")
                };

                var uploadResult = await _cloudinary.UploadAsync(uploadParams);

                if (uploadResult.Error != null)
                {
                    _logger.LogError($"Erreur lors de l'upload Cloudinary avec transformation: {uploadResult.Error.Message}");
                    throw new Exception($"Erreur lors de l'upload: {uploadResult.Error.Message}");
                }

                return uploadResult.SecureUrl.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de l'upload de l'image avec transformation {fileName}");
                throw;
            }
        }

        public string GenerateThumbnailUrl(string publicId, int width = 300, int height = 300)
        {
            try
            {
                var transformation = new Transformation()
                    .Width(width)
                    .Height(height)
                    .Crop("fill")
                    .Gravity("center")
                    .Quality("auto:good");

                return _cloudinary.Api.UrlImgUp.Transform(transformation).BuildUrl(publicId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la génération de l'URL thumbnail pour {publicId}");
                throw;
            }
        }

        public async Task<bool> DeleteImageAsync(string publicId)
        {
            try
            {
                var deleteParams = new DeletionParams(publicId)
                {
                    ResourceType = ResourceType.Image
                };

                var result = await _cloudinary.DestroyAsync(deleteParams);
                return result.Result == "ok";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la suppression de l'image {publicId}");
                return false;
            }
        }

        public string ExtractPublicIdFromUrl(string imageUrl)
        {
            try
            {
                if (string.IsNullOrEmpty(imageUrl))
                    return string.Empty;

                // Format typique d'URL Cloudinary: https://res.cloudinary.com/cloud-name/image/upload/v1234567890/folder/public-id.jpg
                var uri = new Uri(imageUrl);
                var segments = uri.AbsolutePath.Split('/');
                
                // Trouver l'index après "upload"
                var uploadIndex = Array.IndexOf(segments, "upload");
                if (uploadIndex == -1 || uploadIndex + 2 >= segments.Length)
                    return string.Empty;

                // Le public ID peut inclure le dossier et le nom du fichier sans l'extension
                var publicIdParts = segments.Skip(uploadIndex + 2).ToArray();
                var publicId = string.Join("/", publicIdParts);
                
                // Retirer l'extension du fichier
                var lastDotIndex = publicId.LastIndexOf('.');
                if (lastDotIndex > 0)
                {
                    publicId = publicId.Substring(0, lastDotIndex);
                }

                return publicId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de l'extraction du public ID de l'URL {imageUrl}");
                return string.Empty;
            }
        }

        private string GeneratePublicId(string fileName, string folder = null)
        {
            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            var randomId = Random.Shared.Next(10000000, 99999999); // 8 chiffres aléatoires
            var nameWithoutExtension = Path.GetFileNameWithoutExtension(fileName);
            
            var publicId = $"{nameWithoutExtension}_{timestamp}_{randomId}";
            
            return folder != null ? $"{folder}/{publicId}" : publicId;
        }
    }
}
